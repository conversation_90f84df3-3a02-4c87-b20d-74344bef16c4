import axios, { AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { sanitizeObject } from '../utils/inputSanitizer';
// import { ErrorHandler, ErrorType } from '../utils/errorHandler';
import Toast from '../components/common/Toast';

// API配置
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryCount: number;
  retryDelay: number;
  enableOfflineMode: boolean;
  enableRequestQueue: boolean;
  enableResponseCache: boolean;
  cacheTTL: number; // 缓存有效期（毫秒）
}

// 默认API配置
const DEFAULT_CONFIG: ApiConfig = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5002/api',
  timeout: 3000, // 减少超时到3秒
  retryCount: 0,  // 禁用全局重试，因为服务已经有了更好的fallback策略
  retryDelay: 50, // 减少重试延迟到50ms（尽管现在禁用了重试）
  enableOfflineMode: true,
  enableRequestQueue: true,
  enableResponseCache: true,
  cacheTTL: 5 * 60 * 1000 // 5分钟缓存
};

// 请求队列
interface QueuedRequest {
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
  retryCount: number;
  timestamp: number;
}

// 响应缓存
interface CachedResponse {
  data: any;
  timestamp: number;
  url: string;
  params?: any;
}

// API服务类
class ApiService {
  private axios = axios.create();
  private config: ApiConfig;
  private isOnline: boolean = navigator.onLine;
  private requestQueue: QueuedRequest[] = [];
  private responseCache: CachedResponse[] = [];
  private processingQueue: boolean = false;
  private csrfToken: string = '';

  constructor(config: Partial<ApiConfig> = {}) {
    // 合并配置
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 初始化Axios实例
    this.axios = axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
      withCredentials: false, // 不携带凭证，避免CORS问题
    });

    // 初始化CSRF令牌
    this.initCSRFToken();

    // 添加请求拦截器
    this.axios.interceptors.request.use(
      this.handleRequest.bind(this),
      this.handleRequestError.bind(this)
    );

    // 添加响应拦截器
    this.axios.interceptors.response.use(
      this.handleResponse.bind(this),
      this.handleResponseError.bind(this)
    );

    // 监听在线/离线状态变化
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));

    // 从本地存储加载请求队列和响应缓存
    this.loadFromStorage();
  }

  /**
   * 初始化CSRF令牌
   */
  private initCSRFToken(): void {
    // 从本地存储获取或生成新的CSRF令牌
    const storedToken = localStorage.getItem('csrf_token');
    if (storedToken) {
      this.csrfToken = storedToken;
    } else {
      this.csrfToken = this.generateCSRFToken();
      localStorage.setItem('csrf_token', this.csrfToken);
    }
  }

  /**
   * 生成CSRF令牌
   */
  private generateCSRFToken(): string {
    // 在实际应用中，这应该从服务器获取
    // 这里我们生成一个随机令牌
    return Math.random().toString(36).substring(2, 15) +
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 处理请求拦截
   */
  private handleRequest(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig {
    // 添加认证令牌
    const token = localStorage.getItem('token');
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 添加CSRF令牌
    config.headers = config.headers || {};
    config.headers['X-CSRF-Token'] = this.csrfToken;

    // 添加请求ID
    const requestId = Date.now().toString(36) + Math.random().toString(36).substring(2);
    config.headers['X-Request-ID'] = requestId;

    // 对POST、PUT、DELETE请求的数据进行净化
    if (['post', 'put', 'patch'].includes(config.method?.toLowerCase() || '') && config.data) {
      config.data = sanitizeObject(config.data);
    }

    // 如果启用了响应缓存，检查是否有缓存
    if (this.config.enableResponseCache && config.method?.toLowerCase() === 'get') {
      const cachedResponse = this.getCachedResponse(config.url || '', config.params);
      if (cachedResponse) {
        // 添加缓存标记
        config.headers['X-From-Cache'] = 'true';
      }
    }

    return config;
  }

  /**
   * 处理请求错误
   */
  private handleRequestError(error: any): Promise<never> {
    return Promise.reject(error);
  }

  /**
   * 处理响应拦截
   */
  private handleResponse(response: AxiosResponse): AxiosResponse {
    // 如果启用了响应缓存，缓存GET请求的响应
    if (this.config.enableResponseCache && response.config.method?.toLowerCase() === 'get') {
      this.cacheResponse(response.config.url || '', response.config.params, response.data);
    }

    return response;
  }

  /**
   * 处理响应错误
   */
  private handleResponseError(error: AxiosError): Promise<any> {
    // 处理 401 错误 - 清除令牌并重定向到登录页面
    if (error.response && error.response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // 使用Toast显示消息
      Toast.error('身份验证失败', '您的登录已过期，请重新登录');

      // 延迟重定向，让用户有时间看到消息
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
    }

    // 处理网络错误
    if (!error.response) {
      // 网络错误或服务器未响应
      console.error('Network error:', error.message);
      Toast.error('网络错误', '无法连接到服务器，请检查您的网络连接');

      // 如果是网络错误或超时，并且启用了离线模式，将请求添加到队列
      if (this.config.enableOfflineMode && this.config.enableRequestQueue &&
          (!error.response || error.code === 'ECONNABORTED')) {
        const config = error.config;
        if (config) {
          // 只有GET请求可以从缓存获取
          if (config.method?.toLowerCase() === 'get' && this.config.enableResponseCache) {
            const cachedResponse = this.getCachedResponse(config.url || '', config.params);
            if (cachedResponse) {
              // 返回缓存的响应
              return Promise.resolve(cachedResponse.data) as Promise<any>;
            }
          }

          // 将请求添加到队列
          if (!this.isOnline) {
            return new Promise((resolve, reject) => {
              this.addToQueue(config, resolve, reject);
            });
          }
        }
      }
    } else {
      // 服务器返回了错误状态码
      const status = error.response.status;
      const data = error.response.data as any;
      const message = data?.message || error.message || '未知错误';

      switch (status) {
        case 400:
          Toast.error('请求错误', `请求参数有误: ${message}`);
          break;
        case 403:
          Toast.error('访问被拒绝', '您没有权限执行此操作');
          break;
        case 404:
          Toast.error('资源不存在', `请求的资源不存在: ${message}`);
          break;
        case 500:
          Toast.error('服务器错误', '服务器内部错误，请稍后再试');
          break;
        default:
          Toast.error('请求失败', `错误 (${status}): ${message}`);
      }
    }

    return Promise.reject(error);
  }

  /**
   * 处理在线事件
   */
  private handleOnline(): void {
    console.log('网络连接已恢复');
    this.isOnline = true;

    // 处理请求队列
    if (this.config.enableRequestQueue) {
      this.processQueue();
    }
  }

  /**
   * 处理离线事件
   */
  private handleOffline(): void {
    console.log('网络连接已断开');
    this.isOnline = false;

    // 显示离线通知
    Toast.warning('网络连接已断开', '您现在处于离线模式，部分功能可能不可用');
  }

  /**
   * 将请求添加到队列
   */
  private addToQueue(config: AxiosRequestConfig, resolve: (value: any) => void, reject: (reason?: any) => void): void {
    // 创建队列项
    const queueItem: QueuedRequest = {
      config,
      resolve,
      reject,
      retryCount: 0,
      timestamp: Date.now()
    };

    // 添加到队列
    this.requestQueue.push(queueItem);

    // 保存队列到本地存储
    this.saveToStorage();

    // 通知用户
    Toast.info('请求已加入队列', '您的操作将在网络恢复后自动完成');
  }

  /**
   * 处理请求队列
   */
  private async processQueue(): Promise<void> {
    if (this.processingQueue || !this.isOnline || this.requestQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      // 显示处理中通知
      if (this.requestQueue.length > 0) {
        Toast.info('正在处理离线请求', `正在处理 ${this.requestQueue.length} 个待处理请求`);
      }

      // 复制队列并清空原队列
      const queue = [...this.requestQueue];
      this.requestQueue = [];

      // 处理每个请求
      for (const item of queue) {
        try {
          const response = await this.axios(item.config);
          item.resolve(response);
        } catch (error) {
          // 如果重试次数未达到最大值，重新加入队列
          if (item.retryCount < this.config.retryCount) {
            item.retryCount++;
            this.requestQueue.push(item);
          } else {
            item.reject(error);
          }
        }
      }

      // 保存更新后的队列
      this.saveToStorage();

      // 如果还有请求，继续处理
      if (this.requestQueue.length > 0) {
        // 延迟一段时间再处理，避免过于频繁的请求
        setTimeout(() => this.processQueue(), this.config.retryDelay);
      } else if (queue.length > 0) {
        // 所有请求处理完成
        Toast.success('离线请求处理完成', '所有待处理的请求已成功完成');
      }
    } catch (error) {
      console.error('处理请求队列时出错:', error);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * 缓存响应
   */
  private cacheResponse(url: string, params: any, data: any): void {
    // 创建缓存项
    const cacheItem: CachedResponse = {
      url,
      params,
      data,
      timestamp: Date.now()
    };

    // 检查是否已存在相同URL和参数的缓存
    const index = this.responseCache.findIndex(
      item => item.url === url && JSON.stringify(item.params) === JSON.stringify(params)
    );

    if (index >= 0) {
      // 更新现有缓存
      this.responseCache[index] = cacheItem;
    } else {
      // 添加新缓存
      this.responseCache.push(cacheItem);
    }

    // 清理过期缓存
    this.cleanCache();

    // 保存缓存到本地存储
    this.saveToStorage();
  }

  /**
   * 获取缓存的响应
   */
  private getCachedResponse(url: string, params: any): CachedResponse | null {
    // 清理过期缓存
    this.cleanCache();

    // 查找匹配的缓存
    const cacheItem = this.responseCache.find(
      item => item.url === url && JSON.stringify(item.params) === JSON.stringify(params)
    );

    if (cacheItem) {
      // 检查缓存是否过期
      const now = Date.now();
      if (now - cacheItem.timestamp <= this.config.cacheTTL) {
        return cacheItem;
      }
    }

    return null;
  }

  /**
   * 清理过期缓存
   */
  private cleanCache(): void {
    const now = Date.now();
    this.responseCache = this.responseCache.filter(
      item => now - item.timestamp <= this.config.cacheTTL
    );
  }

  /**
   * 保存队列和缓存到本地存储
   */
  private saveToStorage(): void {
    try {
      // 只保存必要的信息
      const queueToSave = this.requestQueue.map(item => ({
        config: item.config,
        retryCount: item.retryCount,
        timestamp: item.timestamp
      }));

      ApiService.safeSetItem('api_request_queue', queueToSave);
      ApiService.safeSetItem('api_response_cache', this.responseCache);
      ApiService.safeSetItem('csrf_token', this.csrfToken);
    } catch (error) {
      console.error('保存API状态到本地存储失败:', error);
    }
  }

  /**
   * 清除所有本地存储（增强版）
   */
  public static clearAllLocalStorage(): void {
    try {
      console.log('🧹 Starting comprehensive cache cleanup...');
      
      // 使用原生方法直接清除所有localStorage
      localStorage.clear();
      sessionStorage.clear();
      
      // 清除IndexedDB
      if ('indexedDB' in window) {
        indexedDB.databases?.().then(databases => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name);
            }
          });
        }).catch(console.warn);
      }
      
      // 清除WebSQL (如果存在)
      if ('openDatabase' in window) {
        try {
          const db = (window as any).openDatabase('', '', '', '');
          if (db) {
            db.transaction((tx: any) => {
              tx.executeSql('DROP TABLE IF EXISTS localstorage');
            });
          }
        } catch (e) {
          // WebSQL可能不存在，忽略错误
        }
      }
      
      console.log('✅ Enhanced cache cleanup completed');
    } catch (error) {
      console.error('❌ Cache cleanup failed:', error);
      // 如果清理失败，尝试页面重载
      window.location.reload();
    }
  }

  /**
   * 安全的JSON解析
   */
  private static safeJSONParse(data: string | null): any {
    if (!data || data === 'null' || data === 'undefined') {
      return null;
    }
    
    try {
      // 使用增强的全局JSON.parse
      return JSON.parse(data);
    } catch (error) {
      console.warn('🛡️ safeJSONParse failed, removing corrupted data:', error);
      return null;
    }
  }

  /**
   * 安全的存储获取
   */
  private static safeGetItem(key: string): any {
    // 如果全局安全函数可用，优先使用
    if ((window as any).safeGetItem) {
      return (window as any).safeGetItem(key);
    }
    
    // 回退到本地实现
    try {
      const value = localStorage.getItem(key);
      return this.safeJSONParse(value);
    } catch (error) {
      console.warn('🛡️ safeGetItem failed for key:', key, error);
      localStorage.removeItem(key);
      return null;
    }
  }

  /**
   * 安全的存储设置
   */
  private static safeSetItem(key: string, value: any): void {
    // 如果全局安全函数可用，优先使用
    if ((window as any).safeSetItem) {
      return (window as any).safeSetItem(key, value);
    }
    
    // 回退到本地实现
    try {
      if (value === undefined || value === null) {
        localStorage.removeItem(key);
        return;
      }
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('🛡️ safeSetItem failed for key:', key, error);
    }
  }

  /**
   * 从本地存储加载队列和缓存
   */
  private loadFromStorage(): void {
    try {
      // 等待保护系统初始化
      if (!(window as any).__PROTECTION_ACTIVE__) {
        console.log('🛡️ Waiting for protection system...');
        setTimeout(() => this.loadFromStorage(), 100);
        return;
      }

      console.log('📂 Loading API data from storage...');

      // 加载请求队列
      const queueData = ApiService.safeGetItem('api_request_queue');
      if (Array.isArray(queueData)) {
        this.requestQueue = queueData.map((item: any) => ({
          ...item,
          resolve: () => {},
          reject: () => {}
        }));
        console.log(`📥 Loaded ${this.requestQueue.length} queued requests`);
      }

      // 加载响应缓存
      const cacheData = ApiService.safeGetItem('api_response_cache');
      if (Array.isArray(cacheData)) {
        this.responseCache = cacheData;
        // 清理过期缓存
        this.cleanCache();
        console.log(`🗄️ Loaded ${this.responseCache.length} cached responses`);
      }

      // 加载CSRF令牌
      const csrfToken = ApiService.safeGetItem('csrf_token');
      if (typeof csrfToken === 'string' && csrfToken) {
        this.csrfToken = csrfToken;
      }

      console.log('✅ API data loaded successfully');
    } catch (error) {
      console.warn('⚠️ Failed to load API data from storage:', error);
      // 清理可能损坏的数据
      ['api_request_queue', 'api_response_cache', 'csrf_token'].forEach(key => {
        localStorage.removeItem(key);
      });
    }
  }

  /**
   * 发送GET请求
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'get', url });
  }

  /**
   * 发送POST请求
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'post', url, data });
  }

  /**
   * 发送PUT请求
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'put', url, data });
  }

  /**
   * 发送DELETE请求
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'delete', url });
  }

  /**
   * 发送PATCH请求
   */
  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'patch', url, data });
  }

  /**
   * 发送请求
   */
  private async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    try {
      // 如果离线且启用了离线模式
      if (!this.isOnline && this.config.enableOfflineMode) {
        // 对于GET请求，尝试从缓存获取
        if (config.method?.toLowerCase() === 'get' && this.config.enableResponseCache) {
          const cachedResponse = this.getCachedResponse(config.url || '', config.params);
          if (cachedResponse) {
            return cachedResponse.data as T;
          }
        }

        // 如果启用了请求队列，将请求添加到队列
        if (this.config.enableRequestQueue) {
          return new Promise<T>((resolve, reject) => {
            this.addToQueue(config, resolve, reject);
          });
        }

        // 否则抛出离线错误
        throw new Error('您当前处于离线状态，无法完成此操作');
      }

      // 添加重试逻辑
      let retries = 0;
      const maxRetries = this.config.retryCount;

      while (true) {
        try {
          // 发送请求
          const response = await this.axios.request<T>(config);
          return response.data;
        } catch (error) {
          // 如果已经重试了最大次数，或者错误不是网络错误，则抛出错误
          if (retries >= maxRetries || (error as AxiosError).response) {
            throw error;
          }

          // 增加重试计数
          retries++;

          // 等待一段时间后重试
          console.log(`请求失败，正在进行第 ${retries} 次重试...`);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    } catch (error) {
      // 记录错误
      console.error('请求失败:', error);

      // 重新抛出错误，让拦截器处理
      throw error;
    }
  }
}

// 创建API服务实例
const api = new ApiService();

// 导出默认实例和类
export default api;
export { ApiService };
