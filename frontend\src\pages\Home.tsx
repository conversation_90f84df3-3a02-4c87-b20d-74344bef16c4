import React from 'react';
import { Button, Typography, Row, Col, Card } from 'antd';
import { useNavigate, Link } from 'react-router-dom';
import {
  LoginOutlined,
  UserAddOutlined,
  DashboardOutlined,
  FileTextOutlined,
  DollarOutlined,
  SettingOutlined
} from '@ant-design/icons';
import '../styles/Home.css';

const { Title, Paragraph, Text } = Typography;

const Home: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <DashboardOutlined className="feature-icon" />,
      title: 'Dashboard',
      description: 'Get a comprehensive overview of all your projects and opportunities'
    },
    {
      icon: <FileTextOutlined className="feature-icon" />,
      title: 'Opportunity Management',
      description: 'Track and manage your sales pipeline from lead to cash'
    },
    {
      icon: <DollarOutlined className="feature-icon" />,
      title: 'Finance',
      description: 'Manage revenue, invoices and monitor project profitability'
    },
    {
      icon: <SettingOutlined className="feature-icon" />,
      title: 'System',
      description: 'Configure system settings and user permissions'
    }
  ];

  return (
    <div className="home-container">
      <header className="home-header">
        <div className="logo">SmartLTC</div>
        <div className="nav-buttons">
          <Button
            type="text"
            onClick={() => navigate('/login')}
            className="nav-login-btn"
          >
            Sign In
          </Button>
          <Button
            type="primary"
            onClick={() => navigate('/register')}
            className="nav-register-btn"
          >
            Sign Up
          </Button>
        </div>
      </header>

      <div className="hero-section">
        <div className="hero-content">
          <Title level={1} className="hero-title">
            SmartLTC Management System
          </Title>
          <Paragraph className="hero-description">
            Streamline your project management from <span className="highlight">Lead to Cash</span> with our comprehensive solution
          </Paragraph>
          <div className="hero-buttons">
            <Button
              type="primary"
              size="large"
              icon={<LoginOutlined />}
              onClick={() => navigate('/login')}
              className="login-btn"
            >
              Sign In
            </Button>
            <Button
              size="large"
              icon={<UserAddOutlined />}
              onClick={() => navigate('/register')}
              className="register-btn"
            >
              Sign Up
            </Button>
          </div>
        </div>
        <div className="hero-image">
          {/* Placeholder for hero image */}
          <div className="image-placeholder"></div>
        </div>
      </div>

      <div className="features-section">
        <Title level={2} className="section-title">Key Features</Title>
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card className="feature-card">
                <div className="feature-icon-wrapper">{feature.icon}</div>
                <Title level={4} className="feature-title">{feature.title}</Title>
                <Text className="feature-description">{feature.description}</Text>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      <footer className="home-footer">
        <div className="footer-content">
          <div className="footer-logo">SmartLTC Management System</div>
          <div className="footer-links">
            <Link to="/about">About</Link>
            <Link to="/contact">Contact</Link>
            <Link to="/privacy">Privacy Policy</Link>
            <Link to="/terms">Terms of Service</Link>
          </div>
          <div className="footer-copyright">
            © 2025 SmartLTC Project Management System. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
