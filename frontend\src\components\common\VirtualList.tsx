import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Spin } from 'antd';
import './VirtualList.css';

interface VirtualListProps<T> {
  data: T[];                                  // 数据源
  itemHeight: number;                         // 每项高度
  height?: number | string;                   // 容器高度
  width?: number | string;                    // 容器宽度
  overscan?: number;                          // 可视区域外预渲染的项数
  renderItem: (item: T, index: number) => React.ReactNode; // 渲染项的函数
  itemKey?: (item: T, index: number) => string | number;   // 项的唯一键
  onScroll?: (e: React.UIEvent<HTMLDivElement>) => void;   // 滚动事件处理函数
  onItemsRendered?: (params: {                // 项渲染事件处理函数
    overscanStartIndex: number;
    overscanStopIndex: number;
    visibleStartIndex: number;
    visibleStopIndex: number;
  }) => void;
  className?: string;                         // 自定义类名
  style?: React.CSSProperties;                // 自定义样式
  loading?: boolean;                          // 加载状态
  loadingComponent?: React.ReactNode;         // 自定义加载组件
  emptyComponent?: React.ReactNode;           // 空数据组件
  scrollToIndex?: number;                     // 滚动到指定索引
  scrollToAlignment?: 'start' | 'center' | 'end' | 'auto'; // 滚动对齐方式
  initialScrollOffset?: number;               // 初始滚动偏移量
  direction?: 'vertical' | 'horizontal';      // 滚动方向
}

/**
 * 虚拟列表组件
 * 用于高效渲染大量数据
 */
function VirtualList<T>({
  data,
  itemHeight,
  height = 400,
  width = '100%',
  overscan = 3,
  renderItem,
  itemKey,
  onScroll,
  onItemsRendered,
  className = '',
  style = {},
  loading = false,
  loadingComponent,
  emptyComponent,
  scrollToIndex,
  scrollToAlignment = 'start',
  initialScrollOffset = 0,
  direction = 'vertical'
}: VirtualListProps<T>) {
  // 容器引用
  const containerRef = useRef<HTMLDivElement>(null);
  
  // 状态
  const [scrollOffset, setScrollOffset] = useState(initialScrollOffset);
  const [containerHeight, setContainerHeight] = useState(0);
  
  // 计算可见范围
  const isHorizontal = direction === 'horizontal';
  const itemCount = data.length;
  const totalSize = itemCount * itemHeight;
  
  // 计算可见项的范围
  const getItemRange = useCallback(() => {
    if (!containerRef.current) {
      return { start: 0, end: 0 };
    }
    
    const viewportSize = isHorizontal
      ? containerRef.current.clientWidth
      : containerRef.current.clientHeight;
    
    // 计算可见项的起始索引
    const startIndex = Math.max(0, Math.floor(scrollOffset / itemHeight));
    
    // 计算可见项的结束索引
    const endIndex = Math.min(
      itemCount - 1,
      Math.floor((scrollOffset + viewportSize) / itemHeight)
    );
    
    // 添加overscan
    const overscanStartIndex = Math.max(0, startIndex - overscan);
    const overscanEndIndex = Math.min(itemCount - 1, endIndex + overscan);
    
    return {
      start: overscanStartIndex,
      end: overscanEndIndex
    };
  }, [scrollOffset, itemHeight, itemCount, overscan, isHorizontal]);
  
  // 计算当前可见项
  const { start, end } = getItemRange();
  
  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { current } = containerRef;
    if (current) {
      const newOffset = isHorizontal ? current.scrollLeft : current.scrollTop;
      setScrollOffset(newOffset);
      
      if (onScroll) {
        onScroll(e);
      }
    }
  }, [isHorizontal, onScroll]);
  
  // 滚动到指定索引
  useEffect(() => {
    if (scrollToIndex !== undefined && containerRef.current) {
      const offsetTop = scrollToIndex * itemHeight;
      const containerHeight = containerRef.current.clientHeight;
      
      let scrollTop;
      switch (scrollToAlignment) {
        case 'start':
          scrollTop = offsetTop;
          break;
        case 'center':
          scrollTop = offsetTop - (containerHeight / 2) + (itemHeight / 2);
          break;
        case 'end':
          scrollTop = offsetTop - containerHeight + itemHeight;
          break;
        case 'auto':
        default:
          // 如果项已经在可视区域内，不滚动
          if (
            offsetTop >= scrollOffset &&
            offsetTop + itemHeight <= scrollOffset + containerHeight
          ) {
            return;
          }
          // 否则滚动到顶部
          scrollTop = offsetTop;
          break;
      }
      
      if (isHorizontal) {
        containerRef.current.scrollLeft = scrollTop;
      } else {
        containerRef.current.scrollTop = scrollTop;
      }
    }
  }, [scrollToIndex, itemHeight, scrollToAlignment, scrollOffset, isHorizontal]);
  
  // 测量容器高度
  useEffect(() => {
    if (containerRef.current) {
      setContainerHeight(
        isHorizontal
          ? containerRef.current.clientWidth
          : containerRef.current.clientHeight
      );
    }
  }, [isHorizontal]);
  
  // 通知渲染的项
  useEffect(() => {
    if (onItemsRendered) {
      onItemsRendered({
        overscanStartIndex: start,
        overscanStopIndex: end,
        visibleStartIndex: Math.max(0, Math.floor(scrollOffset / itemHeight)),
        visibleStopIndex: Math.min(
          itemCount - 1,
          Math.floor((scrollOffset + containerHeight) / itemHeight)
        )
      });
    }
  }, [start, end, scrollOffset, containerHeight, itemHeight, itemCount, onItemsRendered]);
  
  // 渲染项
  const items = [];
  for (let i = start; i <= end; i++) {
    const item = data[i];
    const key = itemKey ? itemKey(item, i) : i;
    const itemStyle = {
      position: 'absolute',
      top: isHorizontal ? 0 : i * itemHeight,
      left: isHorizontal ? i * itemHeight : 0,
      height: itemHeight,
      width: isHorizontal ? itemHeight : '100%'
    };
    
    items.push(
      <div key={key} style={itemStyle as React.CSSProperties} className="virtual-list-item">
        {renderItem(item, i)}
      </div>
    );
  }
  
  // 容器样式
  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'auto',
    willChange: 'transform',
    height,
    width,
    ...style
  };
  
  // 内容样式
  const innerStyle: React.CSSProperties = {
    height: isHorizontal ? '100%' : totalSize,
    width: isHorizontal ? totalSize : '100%',
    position: 'relative'
  };
  
  // 渲染加载状态
  const renderLoading = () => {
    if (loading) {
      return loadingComponent || (
        <div className="virtual-list-loading">
          <Spin size="large" />
        </div>
      );
    }
    return null;
  };
  
  // 渲染空状态
  const renderEmpty = () => {
    if (!loading && data.length === 0) {
      return emptyComponent || (
        <div className="virtual-list-empty">
          No data
        </div>
      );
    }
    return null;
  };
  
  return (
    <div
      ref={containerRef}
      style={containerStyle}
      className={`virtual-list ${className} ${isHorizontal ? 'horizontal' : 'vertical'}`}
      onScroll={handleScroll}
    >
      <div style={innerStyle} className="virtual-list-inner">
        {items}
      </div>
      {renderLoading()}
      {renderEmpty()}
    </div>
  );
}

export default VirtualList;
