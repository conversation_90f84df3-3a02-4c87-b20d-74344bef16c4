import React, { Component, ReactNode } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Space } from 'antd';
import { ReloadOutlined, ClearOutlined } from '@ant-design/icons';
import { ApiService } from '../services/api';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // 检查是否是JSON解析错误
    if (error.message.includes('JSON') || error.message.includes('undefined')) {
      console.log('Detected JSON parsing error, attempting cache cleanup...');
      try {
        ApiService.clearAllLocalStorage();
      } catch (clearError) {
        console.error('Failed to clear cache:', clearError);
      }
    }

    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleClearCache = () => {
    try {
      ApiService.clearAllLocalStorage();
      sessionStorage.clear();
      
      // 显示成功消息并刷新
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Failed to clear cache:', error);
      // 如果清理失败，直接刷新页面
      window.location.reload();
    }
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const isJSONError = this.state.error?.message.includes('JSON') || 
                         this.state.error?.message.includes('undefined');

      return (
        <div style={{
          padding: '24px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          background: '#f5f5f5'
        }}>
          <Card 
            style={{ 
              maxWidth: '600px', 
              width: '100%',
              textAlign: 'center' 
            }}
            title="Application Error"
          >
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {isJSONError ? (
                <Alert
                  message="JSON Parsing Error Detected"
                  description="This error is usually caused by corrupted cache data. Clearing the cache should resolve this issue."
                  type="warning"
                  showIcon
                />
              ) : (
                <Alert
                  message="Something went wrong"
                  description="An unexpected error occurred while running the application."
                  type="error"
                  showIcon
                />
              )}

              <div style={{ 
                background: '#f8f8f8', 
                padding: '12px', 
                borderRadius: '4px',
                fontSize: '12px',
                color: '#666',
                textAlign: 'left',
                maxHeight: '150px',
                overflow: 'auto'
              }}>
                <strong>Error Details:</strong>
                <br />
                {this.state.error?.message}
                {this.state.error?.stack && (
                  <>
                    <br /><br />
                    <strong>Stack Trace:</strong>
                    <br />
                    {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                  </>
                )}
              </div>

              <Space wrap>
                {isJSONError && (
                  <Button 
                    type="primary" 
                    icon={<ClearOutlined />}
                    onClick={this.handleClearCache}
                    size="large"
                  >
                    Clear Cache & Reload
                  </Button>
                )}
                
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={this.handleReload}
                  size="large"
                >
                  Reload Page
                </Button>

                <Button 
                  onClick={this.handleReset}
                  size="large"
                >
                  Try Again
                </Button>
              </Space>

              <div style={{ fontSize: '12px', color: '#999' }}>
                If the problem persists, please contact support or clear your browser cache manually.
              </div>
            </Space>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 