.table-skeleton {
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.table-skeleton-header {
  display: flex;
  padding: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.table-skeleton-body {
  padding: 8px 0;
}

.table-skeleton-row {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table-skeleton-row:last-child {
  border-bottom: none;
}

.table-skeleton-cell {
  flex: 1;
  padding: 0 8px;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

/* 卡片骨架屏 */
.card-skeleton {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
}

.card-skeleton-header {
  height: 48px;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-skeleton-body {
  padding: 16px;
}

.card-skeleton-line {
  height: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
}

.card-skeleton-line:last-child {
  margin-bottom: 0;
  width: 60%;
}

/* 列表骨架屏 */
.list-skeleton-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.list-skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 16px;
}

.list-skeleton-content {
  flex: 1;
}

.list-skeleton-title {
  height: 20px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.list-skeleton-description {
  height: 16px;
  width: 70%;
  border-radius: 4px;
}
