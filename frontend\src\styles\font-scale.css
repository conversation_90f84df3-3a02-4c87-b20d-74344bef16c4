/* 全局字体缩小30% (即字体大小变为原来的70%) */

/* 重置根元素，采用更温和的方式 */
html {
  font-size: 14px; /* 基础字体大小从默认16px改为14px */
}

body {
  font-size: 1rem;
}

/* Ant Design组件字体缩放 - 使用更温和的方式 */
.ant-btn {
  font-size: 12px !important;
}

.ant-input,
.ant-select-selection-item,
.ant-select-selection-placeholder {
  font-size: 12px !important;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  font-size: 11px !important;
}

.ant-form-item-label > label {
  font-size: 12px !important;
}

.ant-modal-title {
  font-size: 14px !important;
}

.ant-modal-body {
  font-size: 12px !important;
}

.ant-menu-item {
  font-size: 18px; /* 从14px再扩大25%约为17.5px，四舍五入到18px */
}

.ant-card-head-title {
  font-size: 13px !important;
}

.ant-tag {
  font-size: 11px !important;
}

.ant-pagination-item {
  font-size: 12px !important;
}

/* 分页相关文本 */
.ant-pagination-total-text,
.ant-pagination .ant-pagination-item,
.ant-pagination .ant-pagination-prev,
.ant-pagination .ant-pagination-next,
.ant-pagination .ant-pagination-jump-prev,
.ant-pagination .ant-pagination-jump-next,
.ant-pagination .ant-pagination-options {
  font-size: 12px !important;
}

/* 分页选择器 */
.ant-pagination-options .ant-select-selection-item {
  font-size: 12px !important;
}

/* Input相关文本 */
.ant-input,
.ant-input::placeholder {
  font-size: 12px !important;
}

/* Input组件的suffix和addonAfter */
.ant-input-suffix,
.ant-input-group-addon {
  font-size: 12px !important;
}

/* InputNumber组件的addonAfter */
.ant-input-number-group-addon {
  font-size: 12px !important;
}

/* 页面标题调整 */
h1 { font-size: 18px !important; }
h2 { font-size: 16px !important; }
h3 { font-size: 14px !important; }
h4 { font-size: 13px !important; }
h5 { font-size: 12px !important; }
h6 { font-size: 11px !important; }

/* Select下拉选项 */
.ant-select-dropdown .ant-select-item {
  font-size: 12px !important;
}

/* Input组件内的文字 */
.ant-input-number-input {
  font-size: 12px !important;
}

/* 日期选择器 */
.ant-picker-input > input {
  font-size: 12px !important;
}

/* 文本区域 */
textarea.ant-input {
  font-size: 12px !important;
}

/* Statistic组件统一规范 */
.ant-statistic-title {
  font-size: 12px !important;
  color: #666 !important;
}

.ant-statistic-content {
  font-size: 20px !important;
  font-weight: 600 !important;
}

.ant-statistic-content-value {
  font-size: 20px !important;
}

/* Progress组件 */
.ant-progress-text {
  font-size: 12px !important;
}

/* Tabs组件 */
.ant-tabs-tab {
  font-size: 12px !important;
}

.ant-tabs-tab-btn {
  font-size: 12px !important;
}

/* Modal组件 */
.ant-modal-header .ant-modal-title {
  font-size: 14px !important;
}

/* Tooltip组件 */
.ant-tooltip-inner {
  font-size: 11px !important;
}

/* Message组件 */
.ant-message .ant-message-notice-content {
  font-size: 12px !important;
}

/* Upload组件 */
.ant-upload-text {
  font-size: 12px !important;
}

.ant-upload-hint {
  font-size: 11px !important;
}

/* List组件 */
.ant-list-item-meta-title {
  font-size: 12px !important;
}

.ant-list-item-meta-description {
  font-size: 11px !important;
}

/* Space组件内的文字 */
.ant-space-item {
  font-size: 12px !important;
}

/* Divider组件 */
.ant-divider-inner-text {
  font-size: 12px !important;
}

/* 补充更多组件字体规范 */

/* Alert组件 */
.ant-alert-message {
  font-size: 12px !important;
}

.ant-alert-description {
  font-size: 11px !important;
}

/* Badge组件 */
.ant-badge-count {
  font-size: 11px !important;
}

/* Breadcrumb组件 */
.ant-breadcrumb {
  font-size: 12px !important;
}

/* Card组件内容 */
.ant-card-body {
  font-size: 12px !important;
}

.ant-card-meta-title {
  font-size: 12px !important;
}

.ant-card-meta-description {
  font-size: 11px !important;
}

/* Collapse组件 */
.ant-collapse-header {
  font-size: 12px !important;
}

.ant-collapse-content-box {
  font-size: 12px !important;
}

/* Descriptions组件 */
.ant-descriptions-item-label {
  font-size: 12px !important;
}

.ant-descriptions-item-content {
  font-size: 12px !important;
}

/* Drawer组件 */
.ant-drawer-title {
  font-size: 14px !important;
}

.ant-drawer-body {
  font-size: 12px !important;
}

/* Empty组件 */
.ant-empty-description {
  font-size: 12px !important;
}

/* Image组件 */
.ant-image-preview-img {
  font-size: 12px !important;
}

/* Result组件 */
.ant-result-title {
  font-size: 16px !important;
}

.ant-result-subtitle {
  font-size: 12px !important;
}

/* Skeleton组件 */
.ant-skeleton-title {
  font-size: 12px !important;
}

.ant-skeleton-paragraph {
  font-size: 12px !important;
}

/* Spin组件 */
.ant-spin-text {
  font-size: 12px !important;
}

/* Steps组件 */
.ant-steps-item-title {
  font-size: 12px !important;
}

.ant-steps-item-description {
  font-size: 11px !important;
}

/* Timeline组件 */
.ant-timeline-item-content {
  font-size: 12px !important;
}

/* Typography组件 */
.ant-typography {
  font-size: 12px !important;
}

.ant-typography h1 {
  font-size: 18px !important;
}

.ant-typography h2 {
  font-size: 16px !important;
}

.ant-typography h3 {
  font-size: 14px !important;
}

.ant-typography h4 {
  font-size: 13px !important;
}

.ant-typography h5 {
  font-size: 12px !important;
}

/* Radio和Checkbox组件 */
.ant-radio-wrapper {
  font-size: 12px !important;
}

.ant-checkbox-wrapper {
  font-size: 12px !important;
}

/* Switch组件 */
.ant-switch {
  font-size: 11px !important;
}

/* Rate组件 */
.ant-rate-text {
  font-size: 12px !important;
}

/* Slider组件 */
.ant-slider-mark-text {
  font-size: 11px !important;
}

.ant-slider-handle::after {
  width: 12px !important;
  height: 12px !important;
}

/* Transfer组件 */
.ant-transfer-list-header {
  font-size: 12px !important;
}

.ant-transfer-list-body-search-wrapper input {
  font-size: 12px !important;
}

/* Tree组件 */
.ant-tree-title {
  font-size: 12px !important;
}

/* AutoComplete组件 */
.ant-select-auto-complete .ant-select-selection-item {
  font-size: 12px !important;
}

/* Cascader组件 */
.ant-cascader-menu-item {
  font-size: 12px !important;
}

/* DatePicker组件补充 */
.ant-picker-cell-inner {
  font-size: 11px !important;
}

.ant-picker-header {
  font-size: 12px !important;
}

/* Form组件补充 */
.ant-form-item-explain {
  font-size: 11px !important;
}

.ant-form-item-extra {
  font-size: 11px !important;
}

/* InputNumber组件 */
.ant-input-number {
  font-size: 12px !important;
}

/* Mentions组件 */
.ant-mentions {
  font-size: 12px !important;
}

/* Select组件补充 */
.ant-select-selector {
  font-size: 12px !important;
}

.ant-select-arrow {
  font-size: 11px !important;
}

/* TimePicker组件 */
.ant-picker-time-panel-column > li {
  font-size: 12px !important;
}

/* TreeSelect组件 */
.ant-select-tree-title {
  font-size: 12px !important;
}

/* ===== 下拉菜单、弹出框、Tooltip等浮层组件统一字体规范 ===== */

/* 所有下拉菜单 */
.ant-dropdown-menu {
  font-size: 11px !important;
}

.ant-dropdown-menu-item {
  font-size: 11px !important;
  line-height: 1.2;
  padding: 10px 12px !important;
}

.ant-dropdown-menu-item .anticon {
  font-size: 11px !important;
  margin-right: 4px;
}

.ant-dropdown-menu-item-divider {
  margin: 2px 0 !important;
}

/* 弹出框内容 */
.ant-popover-inner-content {
  font-size: 12px !important;
}

.ant-popover-title {
  font-size: 12px !important;
  font-weight: 600;
}

.ant-popover-message {
  font-size: 12px !important;
}

/* 确认框 */
.ant-popconfirm .ant-popover-message {
  font-size: 12px !important;
}

.ant-popconfirm .ant-popover-buttons {
  font-size: 12px !important;
}

.ant-popconfirm .ant-btn {
  font-size: 11px !important;
}

/* 通知组件 */
.ant-notification-notice {
  font-size: 12px !important;
}

.ant-notification-notice-message {
  font-size: 12px !important;
  font-weight: 600;
}

.ant-notification-notice-description {
  font-size: 11px !important;
}

/* 消息提示 */
.ant-message-notice-content {
  font-size: 12px !important;
}

/* 抽屉组件 */
.ant-drawer-header-title {
  font-size: 14px !important;
}

.ant-drawer-extra {
  font-size: 12px !important;
}

/* 主题切换下拉菜单特殊处理 */
.theme-switcher-dropdown .ant-dropdown-menu-item {
  font-size: 11px !important;
}

/* Notification Panel 特殊处理 */
.notification-panel .ant-list-item-meta-title {
  font-size: 12px !important;
}

.notification-panel .ant-list-item-meta-description {
  font-size: 11px !important;
}

/* 表格操作按钮的Tooltip */
.ant-table .ant-tooltip .ant-tooltip-inner {
  font-size: 10px !important;
}

/* 模态框footer按钮 */
.ant-modal-footer .ant-btn {
  font-size: 12px !important;
}

/* 确认删除等危险操作的模态框 */
.ant-modal-confirm-body {
  font-size: 12px !important;
}

.ant-modal-confirm-title {
  font-size: 13px !important;
}

.ant-modal-confirm-content {
  font-size: 12px !important;
}

/* 上下文菜单 */
.ant-menu-root {
  font-size: 12px !important;
}

/* 级联选择器下拉面板 */
.ant-cascader-menus {
  font-size: 12px !important;
}

/* 时间选择器面板 */
.ant-picker-time-panel {
  font-size: 12px !important;
}

/* 日期选择器面板 */
.ant-picker-panel {
  font-size: 12px !important;
}

/* 颜色选择器 */
.ant-color-picker-panel {
  font-size: 12px !important;
}

/* 提及组件下拉 */
.ant-mentions-dropdown {
  font-size: 12px !important;
}

/* 自动补全下拉 */
.ant-select-dropdown.ant-select-dropdown-empty {
  font-size: 12px !important;
}

/* Transfer穿梭框 */
.ant-transfer-list-content-item {
  font-size: 12px !important;
}

/* 树选择器下拉 */
.ant-tree-select-dropdown {
  font-size: 12px !important;
}

/* 滑动输入条的提示 */
.ant-slider-tooltip .ant-tooltip-inner {
  font-size: 10px !important;
}

/* 进度条的文本提示 */
.ant-progress-circle .ant-progress-text {
  font-size: 11px !important;
}

/* 步骤条的提示气泡 */
.ant-steps .ant-tooltip .ant-tooltip-inner {
  font-size: 10px !important;
}

/* 锚点组件 */
.ant-anchor-link-title {
  font-size: 12px !important;
}

/* 回到顶部按钮 */
.ant-back-top {
  font-size: 12px !important;
}

/* 浮动按钮组 */
.ant-float-btn-group .ant-float-btn {
  font-size: 12px !important;
}

/* QR码组件 */
.ant-qrcode {
  font-size: 12px !important;
}

/* 评分组件的文本 */
.ant-rate .ant-rate-star {
  font-size: 12px !important;
}

/* 分割面板 */
.ant-splitter-panel {
  font-size: 12px !important;
}

/* 水印组件 */
.ant-watermark {
  font-size: 12px !important;
} 