import api from './api';
import { Document } from '../types';

// 本地存储键
const STORAGE_KEY = 'documents';

class DocumentService {
  private documents: Document[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载数据
  private loadFromStorage() {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (data) {
        this.documents = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading documents from localStorage:', error);
      this.documents = [];
    }
  }

  // 保存到localStorage
  private saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.documents));
    } catch (error) {
      console.error('Error saving documents to localStorage:', error);
    }
  }

  async getAllDocuments(): Promise<Document[]> {
    try {
    const response = await api.get<Document[]>('/documents');
    return response;
    } catch (error) {
      console.error('Failed to fetch documents from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      return this.documents;
    }
  }

  async getDocumentById(id: string): Promise<Document> {
    try {
    const response = await api.get<Document>(`/documents/${id}`);
    return response;
    } catch (error) {
      console.error('Failed to fetch document from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      const document = this.documents.find(d => d.id === id);
      if (document) {
        return document;
      }
      throw new Error('Document not found');
    }
  }

  async getProjectDocuments(projectId: string): Promise<Document[]> {
    try {
    const response = await api.get<Document[]>(`/documents/project/${projectId}`);
    return response;
    } catch (error) {
      console.error('Failed to fetch project documents from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      // 使用类型断言来处理projectId字段
      return this.documents.filter(d => (d as any).projectId === projectId);
    }
  }

  async createDocument(document: Partial<Document> & { projectId: string }): Promise<Document> {
    try {
    const response = await api.post<Document>('/documents', document);
    return response;
    } catch (error) {
      console.error('Failed to create document via API:', error);
      // 本地创建文档
      const newDocument: Document = {
        id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: document.name || 'Untitled Document',
        description: document.description,
        fileUrl: document.fileUrl || '',
        fileType: document.fileType || 'application/octet-stream',
        fileSize: document.fileSize || 0,
        category: document.category || 'other',
        uploadedBy: document.uploadedBy || {
          id: 'current_user',
          username: 'Current User',
          email: '<EMAIL>',
          role: 'user',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      this.documents.push(newDocument);
      this.saveToStorage();
      
      return newDocument;
    }
  }

  async updateDocument(id: string, document: Partial<Document>): Promise<Document> {
    try {
    const response = await api.put<Document>(`/documents/${id}`, document);
    return response;
    } catch (error) {
      console.error('Failed to update document via API:', error);
      // 本地更新文档
      const documentIndex = this.documents.findIndex(d => d.id === id);
      if (documentIndex !== -1) {
        this.documents[documentIndex] = {
          ...this.documents[documentIndex],
          ...document,
          updatedAt: new Date().toISOString()
        };
        this.saveToStorage();
        return this.documents[documentIndex];
      }
      throw new Error('Document not found');
    }
  }

  async deleteDocument(id: string): Promise<void> {
    try {
    await api.delete(`/documents/${id}`);
    } catch (error) {
      console.error('Failed to delete document via API:', error);
      // 本地删除文档
      this.documents = this.documents.filter(d => d.id !== id);
      this.saveToStorage();
    }
  }

  // 文件上传函数
  async uploadFile(file: File): Promise<{ fileUrl: string; fileType: string; fileSize: number }> {
    try {
    // 这里应该实现文件上传逻辑，可以使用FormData
    // 这是一个简化的示例，实际实现可能需要更复杂的逻辑
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
    } catch (error) {
      console.error('Failed to upload file via API:', error);
      // 本地模拟文件上传
      const mockFileUrl = `data:${file.type};base64,${btoa('mock file content')}`;
      return {
        fileUrl: mockFileUrl,
        fileType: file.type,
        fileSize: file.size
      };
    }
  }
}

export default new DocumentService();
