{"name": "backend", "version": "1.0.0", "description": "LTC Project Management System Backend", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "typeorm": "ts-node ./node_modules/typeorm/cli.js", "test": "jest"}, "keywords": ["project-management", "typescript", "express", "typeorm"], "author": "", "license": "ISC", "dependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.12", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^8.5.8", "@types/node": "^17.0.21", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.17.3", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "pg": "^8.7.3", "reflect-metadata": "^0.1.13", "sqlite3": "^5.1.7", "typeorm": "^0.2.45", "typescript": "^4.6.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^17.0.21", "nodemon": "^2.0.15", "ts-node-dev": "^1.1.8"}}