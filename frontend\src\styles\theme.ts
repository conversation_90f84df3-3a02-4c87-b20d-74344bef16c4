// 现代简约风格主题配置
export const theme = {
  // 主色调
  colors: {
    primary: '#6366F1', // 紫蓝色作为主色调
    primaryLight: '#EEF2FF',
    secondary: '#10B981', // 绿色作为辅助色
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#3B82F6',
    
    // 文本颜色
    textPrimary: '#1F2023',
    textSecondary: '#4B5563',
    textLight: '#9CA3AF',
    
    // 背景颜色
    background: '#F9FAFB',
    cardBackground: '#FFFFFF',
    
    // 边框颜色
    border: '#E5E7EB',
    borderFocus: '#6366F1',
    
    // 灰度
    grayLight: '#F3F4F6',
    gray: '#9CA3AF',
    grayDark: '#4B5563',
    
    // 悬停效果
    hover: '#EEF2FF',
  },
  
  // 圆角
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    circle: '50%',
  },
  
  // 阴影
  shadows: {
    small: '0 1px 2px rgba(0, 0, 0, 0.05)',
    medium: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
    large: '0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05)',
  },
  
  // 间距
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  // 字体
  typography: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
    fontSizes: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px',
      xxxl: '30px',
    },
    fontWeights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
  },
  
  // 过渡效果
  transitions: {
    fast: '0.15s ease-in-out',
    normal: '0.25s ease-in-out',
    slow: '0.35s ease-in-out',
  },
  
  // 暗色主题
  dark: {
    colors: {
      primary: '#818CF8',
      primaryLight: '#2D3748',
      secondary: '#34D399',
      success: '#34D399',
      warning: '#FBBF24',
      danger: '#F87171',
      info: '#60A5FA',
      
      textPrimary: '#F9FAFB',
      textSecondary: '#D1D5DB',
      textLight: '#6B7280',
      
      background: '#111827',
      cardBackground: '#1F2937',
      
      border: '#374151',
      borderFocus: '#818CF8',
      
      grayLight: '#1F2937',
      gray: '#6B7280',
      grayDark: '#D1D5DB',
      
      hover: '#2D3748',
    },
    shadows: {
      small: '0 1px 2px rgba(0, 0, 0, 0.2)',
      medium: '0 4px 6px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.3)',
      large: '0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.2)',
    },
  }
};

export default theme;
