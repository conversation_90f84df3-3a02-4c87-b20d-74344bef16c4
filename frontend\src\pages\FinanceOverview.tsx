import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Row, Col, Statistic, Progress, Table, Button, Tag, Spin, message } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import projectService from '../services/new-project.service';
import projectStageService from '../services/project-stage.service';
import { Project, ProjectStage } from '../types';
import moment from 'moment';
import '../styles/finance-module.css';

const FinanceOverview: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [project, setProject] = useState<Project | null>(null);
  const [stages, setStages] = useState<ProjectStage[]>([]);

  useEffect(() => {
    if (id) {
      fetchProjectData();
    }
  }, [id]);

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      const projectData = await projectService.getProjectById(id!);
      
      if (!projectData) {
        console.warn('Project data is null, redirecting to opportunity list');
        message.error('Project not found');
        navigate('/opportunity');
        return;
      }
      
      setProject(projectData);

      // 获取项目阶段
      const stagesData = await projectStageService.getStagesByProjectId(id!);
      setStages(stagesData);
    } catch (error) {
      console.error('Error fetching project data:', error);
      
      // 只在正确的路径下显示错误信息
      const currentPath = window.location.pathname;
      if (currentPath.includes('/finance/') || currentPath.includes('/opportunity/')) {
        message.error('Failed to load project data (FinanceOverview)');
        navigate('/opportunity');
      }
    } finally {
      setLoading(false);
    }
  };

  // 模拟财务数据
  const invoices = [
    {
      id: '1',
      number: 'INV-2023-001',
      date: '2023-06-01',
      dueDate: '2023-07-01',
      description: 'Initial Payment (30%)',
      amount: 3600,
      status: 'paid'
    },
    {
      id: '2',
      number: 'INV-2023-002',
      date: '2023-07-15',
      dueDate: '2023-08-15',
      description: 'Planning Phase Completion (30%)',
      amount: 3600,
      status: 'paid'
    },
    {
      id: '3',
      number: 'INV-2023-003',
      date: '2023-09-01',
      dueDate: '2023-10-01',
      description: 'Migration Completion (30%)',
      amount: 3600,
      status: 'pending'
    },
    {
      id: '4',
      number: 'INV-2023-004',
      date: '2023-10-15',
      dueDate: '2023-11-15',
      description: 'Final Acceptance (10%)',
      amount: 1200,
      status: 'upcoming'
    }
  ];

  // 计算财务摘要
  const calculateFinancialSummary = () => {
    const totalValue = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const receivedValue = invoices.filter(invoice => invoice.status === 'paid').reduce((sum, invoice) => sum + invoice.amount, 0);
    const pendingValue = invoices.filter(invoice => invoice.status === 'pending').reduce((sum, invoice) => sum + invoice.amount, 0);
    const upcomingValue = invoices.filter(invoice => invoice.status === 'upcoming').reduce((sum, invoice) => sum + invoice.amount, 0);

    return {
      totalValue,
      receivedValue,
      pendingValue,
      upcomingValue,
      receivedPercentage: Math.round((receivedValue / totalValue) * 100)
    };
  };

  const summary = calculateFinancialSummary();

  // 表格列定义
  const invoiceColumns = [
    {
      title: 'Invoice Number',
      dataIndex: 'number',
      key: 'number',
    },
    {
      title: 'Invoice Date',
      dataIndex: 'date',
      key: 'date',
      render: (text: string) => moment(text).format('YYYY-MM-DD')
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (text: string) => moment(text).format('YYYY-MM-DD')
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `€${amount.toLocaleString()}`
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        let text = '';
        switch (status) {
          case 'paid':
            color = 'green';
            text = 'Paid';
            break;
          case 'pending':
            color = 'orange';
            text = 'Pending';
            break;
          case 'overdue':
            color = 'red';
            text = 'Overdue';
            break;
          case 'upcoming':
            color = 'blue';
            text = 'Upcoming';
            break;
          default:
            color = 'default';
            text = status;
        }
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <>
          <Button type="link" icon={<EyeOutlined />} size="small">View</Button>
          <Button type="link" icon={<EditOutlined />} size="small">Edit</Button>
          <Button type="link" icon={<DownloadOutlined />} size="small">Download</Button>
        </>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Button
        icon={<ArrowLeftOutlined />}
        style={{ marginBottom: 16 }}
        onClick={() => navigate(`/opportunity/${id}`)}
      >
        Back to Project
      </Button>

      <Card title={`Finance Overview - ${project?.name || 'Project'}`}>
        <Row gutter={16}>
          <Col span={6}>
            <Card className="finance-module-card">
              <Statistic
                title="Total Receivable"
                value={summary.totalValue}
                precision={0}
                valueStyle={{ color: '#3f8600' }}
                prefix="€"
                formatter={(value) => `${value.toLocaleString()}`}
              />
              <Progress percent={summary.receivedPercentage} size="small" status="active" style={{ marginTop: 8 }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="finance-module-card">
              <Statistic
                title="Received"
                value={summary.receivedValue}
                precision={0}
                valueStyle={{ color: '#3f8600' }}
                prefix="€"
                formatter={(value) => `${value.toLocaleString()}`}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="finance-module-card">
              <Statistic
                title="Pending"
                value={summary.pendingValue}
                precision={0}
                valueStyle={{ color: '#faad14' }}
                prefix="€"
                formatter={(value) => `${value.toLocaleString()}`}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card className="finance-module-card">
              <Statistic
                title="Upcoming"
                value={summary.upcomingValue}
                precision={0}
                valueStyle={{ color: '#1890ff' }}
                prefix="€"
                formatter={(value) => `${value.toLocaleString()}`}
              />
            </Card>
          </Col>
        </Row>

        <Card
          title="Invoice Management"
          className="finance-module-card"
          style={{ marginTop: 16 }}
          extra={<Button type="primary">Create Invoice</Button>}
        >
          <Table
            columns={invoiceColumns}
            dataSource={invoices}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </Card>
    </div>
  );
};

export default FinanceOverview;
