/* 统一表单样式 */

/* ===== 表单容器 ===== */
.form-container {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  margin-bottom: var(--spacing-lg);
}

/* 表单标题 */
.form-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border);
}

/* 表单描述 */
.form-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* 表单分组 */
.form-section {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border);
}

/* 表单分组标题 */
.form-section-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* 表单分组描述 */
.form-section-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* 表单行 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  margin-bottom: var(--spacing-md);
}

/* 表单列 */
.form-col {
  padding: 0 8px;
  margin-bottom: var(--spacing-sm);
}

.form-col-12 {
  width: 100%;
}

.form-col-6 {
  width: 50%;
}

.form-col-4 {
  width: 33.33%;
}

.form-col-3 {
  width: 25%;
}

/* 响应式表单列 */
@media (max-width: 768px) {
  .form-col-6,
  .form-col-4,
  .form-col-3 {
    width: 100%;
  }
}

/* ===== 表单项 ===== */
.form-item {
  margin-bottom: var(--spacing-md);
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

/* 必填标记 */
.form-label-required::after {
  content: '*';
  color: var(--danger);
  margin-left: 4px;
}

/* 表单输入框 */
.form-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--font-size-sm);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  background-color: var(--card-background);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 表单帮助文本 */
.form-help-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 表单错误文本 */
.form-error-text {
  font-size: var(--font-size-xs);
  color: var(--danger);
  margin-top: var(--spacing-xs);
}

/* 表单操作区 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border);
}

/* ===== 特殊表单组件 ===== */

/* 复选框组 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* 单选框组 */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* 开关 */
.form-switch {
  display: flex;
  align-items: center;
}

.form-switch-label {
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* 滑块 */
.form-slider {
  padding: var(--spacing-sm) 0;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
  background-color: var(--gray-light);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.upload-area:hover {
  border-color: var(--primary);
  background-color: var(--primary-light);
}

.upload-icon {
  font-size: 24px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.upload-text {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 文件列表 */
.file-list {
  margin-top: var(--spacing-md);
}

.file-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xs);
  background-color: var(--card-background);
}

.file-icon {
  margin-right: var(--spacing-sm);
  color: var(--text-secondary);
}

.file-name {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.file-size {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-right: var(--spacing-sm);
}

.file-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 日期选择器 */
.date-picker {
  width: 100%;
}

/* 时间选择器 */
.time-picker {
  width: 100%;
}

/* 颜色选择器 */
.color-picker {
  width: 100%;
}

/* 标签输入框 */
.tag-input {
  width: 100%;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background-color: var(--primary-light);
  color: var(--primary);
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-xs);
}

.tag-close {
  margin-left: 4px;
  cursor: pointer;
}

/* 表单验证状态 */
.form-input-success {
  border-color: var(--success);
}

.form-input-warning {
  border-color: var(--warning);
}

.form-input-error {
  border-color: var(--danger);
}

/* 表单禁用状态 */
.form-input:disabled,
.form-input[disabled] {
  background-color: var(--gray-light);
  color: var(--text-light);
  cursor: not-allowed;
}

/* 表单只读状态 */
.form-input[readonly] {
  background-color: var(--gray-light);
  cursor: default;
}

/* 表单占位符 */
.form-input::placeholder {
  color: var(--text-light);
}
