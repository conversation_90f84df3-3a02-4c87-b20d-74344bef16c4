import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import { createConnection } from 'typeorm';
import dotenv from 'dotenv';
import projectRoutes from './routes/project.routes';
import authRoutes from './routes/auth.routes';
import projectStageRoutes from './routes/project-stage.routes';
import teamMemberRoutes from './routes/team-member.routes';
import documentRoutes from './routes/document.routes';
import notificationRoutes from './routes/notification.routes';
import config from './config/database';

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'x-request-id',
    'X-Requested-With',
    'x-csrf-token'
  ]
}));
app.use(express.json());

// Routes
app.use('/api/projects', projectRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/project-stages', projectStageRoutes);
app.use('/api/team-members', teamMemberRoutes);
app.use('/api/documents', documentRoutes);
app.use('/api/notifications', notificationRoutes);

// Database connection
createConnection(config)
  .then(() => {
    console.log('Database connected successfully');

    // Start server
    const PORT = process.env.PORT || 5002; // 使用不同的端口
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  })
  .catch((error) => {
    console.error('Error connecting to database:', error);
  });

export default app;