import api from './api';
import { ErrorHandler } from '../utils/errorHandler';

// 定义财务数据类型
export interface NrcItem {
  id: string;
  name: string;
  description: string;
  currency: string;
  amount: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  notes: string;
  paymentStatus: string;
}

export interface MrcItem {
  id: string;
  name: string;
  description: string;
  unitPrice: number;
  vatRate: number;
  totalUnitPrice: number;
  startDate: string;
  endDate: string;
  cycle: string;
  totalCycleRevenue: number;
  received: number;
  pending: number;
  status: string;
  paymentStatus: string;
  currency: string;
}

export interface Invoice {
  id: string;
  number: string;
  revenueItem: string;
  type: string;
  amount: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  invoiceDate: string;
  dueDate: string;
  status: string;
  projectId: string;
}

export interface VendorInvoice {
  id: string;
  number: string;
  vendor: string;
  type: string;
  amount: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  invoiceDate: string;
  dueDate: string;
  status: string;
  projectId: string;
}

export interface FinancialSummary {
  totalValue: number;
  receivedValue: number;
  pendingValue: number;
  upcomingValue: number;
  receivedPercentage: number;
}

// 存储键名
const STORAGE_KEYS = {
  NRC_ITEMS: 'nrc_items',
  MRC_ITEMS: 'mrc_items',
  INVOICES: 'invoices',
  VENDOR_INVOICES: 'vendor_invoices'
};

class FinanceService {
  // API服务器可用性检查缓存
  private apiServerAvailable: boolean | null = null;
  private lastApiCheck: number = 0;
  private readonly API_CHECK_INTERVAL = 30000; // 30秒检查一次

  // 检查API服务器是否可用
  private async isApiServerAvailable(): Promise<boolean> {
    const now = Date.now();
    
    // 如果30秒内已经检查过，直接返回缓存结果
    if (this.apiServerAvailable !== null && (now - this.lastApiCheck) < this.API_CHECK_INTERVAL) {
      console.log(`🔄 Using cached API availability: ${this.apiServerAvailable}`);
      return this.apiServerAvailable;
    }

    console.log('🔍 Checking API server availability...');
    try {
      // 使用一个轻量级的健康检查端点，超时时间更短
      await api.get('/health', { timeout: 200 });
      this.apiServerAvailable = true;
      this.lastApiCheck = now;
      console.log('✅ API server is available');
      return true;
    } catch (error) {
      this.apiServerAvailable = false;
      this.lastApiCheck = now;
      console.log('❌ API server is not available');
      return false;
    }
  }

  // NRC项目相关方法
  async getNrcItems(projectId: string): Promise<NrcItem[]> {
    return ErrorHandler.withErrorHandling(async () => {
      // 首先立即从localStorage获取数据，提供快速响应
      const data = localStorage.getItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`);
      const localData: NrcItem[] = data ? JSON.parse(data) : [];

      // 如果有本地数据，立即返回
      if (localData.length > 0) {
        // 🔧 开发环境下禁用后台自动更新，避免覆盖用户删除操作
        if (process.env.NODE_ENV !== 'development') {
          this.updateNrcItemsInBackground(projectId);
        }
        return localData;
      }

      // 如果没有本地数据，检查是否应该尝试API
      if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
        console.log('🔧 Development mode: No local NRC data and API server not available, returning empty array');
        return localData; // 返回空数组
      }

      // 尝试从API获取（使用更短的超时）
      try {
        const response = await api.get<NrcItem[]>(`/finance/${projectId}/nrc`, {
          timeout: 2000 // 减少超时到2秒
        });
        
        // 保存到localStorage
        localStorage.setItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`, JSON.stringify(response));
        return response;
      } catch (error) {
        console.error('Error fetching NRC items from API:', error);
        return localData; // 返回本地数据（可能为空）
      }
    }, '获取NRC项目失败') as Promise<NrcItem[]>;
  }

  // 后台更新NRC数据
  private async updateNrcItemsInBackground(projectId: string): Promise<void> {
    // 检查是否在开发环境且API服务器不可用
    if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
      console.log('🔧 Development mode: Skipping background API update (API server not available)');
      return;
    }

    try {
      const response = await api.get<NrcItem[]>(`/finance/${projectId}/nrc`, {
        timeout: 2000
      });
      localStorage.setItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`, JSON.stringify(response));
      console.log('✅ Background update completed for NRC items');
    } catch (error) {
      // 后台更新失败不影响用户体验，只记录简单日志
      console.log('Background update failed for NRC items:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async createNrcItem(projectId: string, item: Omit<NrcItem, 'id'>): Promise<NrcItem> {
    return ErrorHandler.withErrorHandling(async () => {
      try {
        // 尝试通过API创建
        const response = await api.post<NrcItem>(`/finance/${projectId}/nrc`, item);
        return response;
      } catch (error) {
        console.error('Error creating NRC item via API:', error);

        // 从localStorage直接获取，避免通过getNrcItems导致重复
        const data = localStorage.getItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`);
        const existingItems: NrcItem[] = data ? JSON.parse(data) : [];

        // 创建新项目
        const newItem: NrcItem = {
          ...item as any,
          id: Date.now().toString(),
          currency: item.currency || 'EUR', // 添加默认货币
        };

        // 保存到本地存储
        const updatedItems = [...existingItems, newItem];
        localStorage.setItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
        
        // 🔧 FIX: 也更新合并的revenue数据，确保UI能立即显示
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        const combinedItems = combinedData ? JSON.parse(combinedData) : [];
        
        // 添加到合并数据中
        const combinedItem = {
          ...newItem,
          type: 'nrc'
        };
        combinedItems.push(combinedItem);
        localStorage.setItem(combinedKey, JSON.stringify(combinedItems));
        console.log('✅ NRC item also saved to combined revenue data:', newItem.id);

        return newItem;
      }
    }, '创建NRC项目失败') as Promise<NrcItem>;
  }

  async updateNrcItem(projectId: string, itemId: string, item: Omit<NrcItem, 'id'>): Promise<NrcItem> {
    return ErrorHandler.withErrorHandling(async () => {
      try {
        const response = await api.put<NrcItem>(`/finance/${projectId}/nrc/${itemId}`, item);
        return response;
      } catch (error) {
        console.error('Error updating NRC item via API:', error);
        
        // Update NRC items storage
        const data = localStorage.getItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`);
        const existingItems: NrcItem[] = data ? JSON.parse(data) : [];
        const updatedItem = { ...item, id: itemId };
        const updatedItems = existingItems.map(i => i.id === itemId ? updatedItem : i);
        localStorage.setItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
        
        // 🔧 FIX: Also update combined revenue data for UI consistency
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        if (combinedData) {
          const combinedItems = JSON.parse(combinedData);
          const itemIndex = combinedItems.findIndex((i: any) => i.id === itemId);
          if (itemIndex !== -1) {
            // Update the combined item with new data
            const combinedItem = {
              ...updatedItem,
              type: 'nrc'
            };
            combinedItems[itemIndex] = combinedItem;
            localStorage.setItem(combinedKey, JSON.stringify(combinedItems));
            console.log('✅ NRC item also updated in combined revenue data:', itemId);
          }
        }
        
        return updatedItem;
      }
    }, '更新NRC项目失败') as Promise<NrcItem>;
  }

  // MRC项目相关方法
  async getMrcItems(projectId: string): Promise<MrcItem[]> {
    return ErrorHandler.withErrorHandling(async () => {
      // 首先立即从localStorage获取数据，提供快速响应
      const data = localStorage.getItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`);
      const localData: MrcItem[] = data ? JSON.parse(data) : [];

      // 如果有本地数据，立即返回
      if (localData.length > 0) {
        // 🔧 开发环境下禁用后台自动更新，避免覆盖用户删除操作
        if (process.env.NODE_ENV !== 'development') {
          this.updateMrcItemsInBackground(projectId);
        }
        return localData;
      }

      // 如果没有本地数据，检查是否应该尝试API
      if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
        console.log('🔧 Development mode: No local MRC data and API server not available, returning empty array');
        return localData; // 返回空数组
      }

      // 尝试从API获取（使用更短的超时）
      try {
        const response = await api.get<MrcItem[]>(`/finance/${projectId}/mrc`, {
          timeout: 2000 // 减少超时到2秒
        });
        
        // 保存到localStorage
        localStorage.setItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`, JSON.stringify(response));
        return response;
      } catch (error) {
        console.error('Error fetching MRC items from API:', error);
        return localData; // 返回本地数据（可能为空）
      }
    }, '获取MRC项目失败') as Promise<MrcItem[]>;
  }

  // 后台更新MRC数据
  private async updateMrcItemsInBackground(projectId: string): Promise<void> {
    // 检查是否在开发环境且API服务器不可用
    if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
      console.log('🔧 Development mode: Skipping background API update (API server not available)');
      return;
    }

    try {
      const response = await api.get<MrcItem[]>(`/finance/${projectId}/mrc`, {
        timeout: 2000
      });
      localStorage.setItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`, JSON.stringify(response));
      console.log('✅ Background update completed for MRC items');
    } catch (error) {
      // 后台更新失败不影响用户体验，只记录简单日志
      console.log('Background update failed for MRC items:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async createMrcItem(projectId: string, item: Omit<MrcItem, 'id'>): Promise<MrcItem> {
    return ErrorHandler.withErrorHandling(async () => {
      try {
        // 尝试通过API创建
        const response = await api.post<MrcItem>(`/finance/${projectId}/mrc`, item);
        return response;
      } catch (error) {
        console.error('Error creating MRC item via API:', error);

        // 从localStorage直接获取，避免通过getMrcItems导致重复
        const data = localStorage.getItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`);
        const existingItems: MrcItem[] = data ? JSON.parse(data) : [];

        // 创建新项目
        const newItem: MrcItem = {
          ...item as any,
          id: Date.now().toString(),
          currency: item.currency || 'EUR', // 添加默认货币
        };

        // 保存到本地存储
        const updatedItems = [...existingItems, newItem];
        localStorage.setItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
        
        // 🔧 FIX: 也更新合并的revenue数据，确保UI能立即显示
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        const combinedItems = combinedData ? JSON.parse(combinedData) : [];
        
        // 添加到合并数据中
        const combinedItem = {
          ...newItem,
          type: 'mrc',
          amount: newItem.unitPrice,
          totalAmount: newItem.totalCycleRevenue * (1 + (newItem.vatRate || 0) / 100)
        };
        combinedItems.push(combinedItem);
        localStorage.setItem(combinedKey, JSON.stringify(combinedItems));
        console.log('✅ MRC item also saved to combined revenue data:', newItem.id);

        return newItem;
      }
    }, '创建MRC项目失败') as Promise<MrcItem>;
  }

  async updateMrcItem(projectId: string, itemId: string, item: Omit<MrcItem, 'id'>): Promise<MrcItem> {
    return ErrorHandler.withErrorHandling(async () => {
      try {
        const response = await api.put<MrcItem>(`/finance/${projectId}/mrc/${itemId}`, item);
        return response;
      } catch (error) {
        console.error('Error updating MRC item via API:', error);
        
        // Update MRC items storage
        const data = localStorage.getItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`);
        const existingItems: MrcItem[] = data ? JSON.parse(data) : [];
        const updatedItem = { ...item, id: itemId };
        const updatedItems = existingItems.map(i => i.id === itemId ? updatedItem : i);
        localStorage.setItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
        
        // 🔧 FIX: Also update combined revenue data for UI consistency
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        if (combinedData) {
          const combinedItems = JSON.parse(combinedData);
          const itemIndex = combinedItems.findIndex((i: any) => i.id === itemId);
          if (itemIndex !== -1) {
            // Update the combined item with new data
            const combinedItem = {
              ...updatedItem,
              type: 'mrc',
              amount: updatedItem.unitPrice,
              totalAmount: updatedItem.totalCycleRevenue * (1 + (updatedItem.vatRate || 0) / 100)
            };
            combinedItems[itemIndex] = combinedItem;
            localStorage.setItem(combinedKey, JSON.stringify(combinedItems));
            console.log('✅ MRC item also updated in combined revenue data:', itemId);
          }
        }
        
        return updatedItem;
      }
    }, '更新MRC项目失败') as Promise<MrcItem>;
  }

  // 删除NRC项目
  async deleteNrcItem(projectId: string, itemId: string): Promise<boolean> {
    return ErrorHandler.withErrorHandling(async () => {
      // 🚀 优化：优先进行localStorage删除，确保快速响应
      const data = localStorage.getItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`);
      const existingItems: NrcItem[] = data ? JSON.parse(data) : [];

      // 过滤掉要删除的项目
      const updatedItems = existingItems.filter(item => item.id !== itemId);
      
      // 立即保存到localStorage
      localStorage.setItem(`${STORAGE_KEYS.NRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
      console.log('✅ NRC item deleted from localStorage:', itemId);

      // 🚀 优化：在开发环境下跳过API调用，或异步执行API调用
      if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
        console.log('🔧 Development mode: Skipping API delete (API server not available)');
        return true;
      }

      // 🚀 优化：在后台异步尝试API删除，不阻塞用户操作
      this.deleteNrcItemInBackground(projectId, itemId);

      return true;
    }, '删除NRC项目失败') as Promise<boolean>;
  }

  // 后台异步删除NRC项目（不阻塞用户操作）
  private async deleteNrcItemInBackground(projectId: string, itemId: string): Promise<void> {
    try {
      await api.delete(`/finance/${projectId}/nrc/${itemId}`);
      console.log('✅ NRC item deleted via API (background):', itemId);
    } catch (error) {
      console.log('Background API delete failed for NRC item:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // 删除MRC项目
  async deleteMrcItem(projectId: string, itemId: string): Promise<boolean> {
    return ErrorHandler.withErrorHandling(async () => {
      // 🚀 优化：优先进行localStorage删除，确保快速响应
      const data = localStorage.getItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`);
      const existingItems: MrcItem[] = data ? JSON.parse(data) : [];

      // 过滤掉要删除的项目
      const updatedItems = existingItems.filter(item => item.id !== itemId);
      
      // 立即保存到localStorage
      localStorage.setItem(`${STORAGE_KEYS.MRC_ITEMS}_${projectId}`, JSON.stringify(updatedItems));
      console.log('✅ MRC item deleted from localStorage:', itemId);

      // 🚀 优化：在开发环境下跳过API调用，或异步执行API调用
      if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
        console.log('🔧 Development mode: Skipping API delete (API server not available)');
        return true;
      }

      // 🚀 优化：在后台异步尝试API删除，不阻塞用户操作
      this.deleteMrcItemInBackground(projectId, itemId);

      return true;
    }, '删除MRC项目失败') as Promise<boolean>;
  }

  // 后台异步删除MRC项目（不阻塞用户操作）
  private async deleteMrcItemInBackground(projectId: string, itemId: string): Promise<void> {
    try {
      await api.delete(`/finance/${projectId}/mrc/${itemId}`);
      console.log('✅ MRC item deleted via API (background):', itemId);
    } catch (error) {
      console.log('Background API delete failed for MRC item:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // 发票相关方法
  async getInvoices(projectId: string): Promise<Invoice[]> {
    return ErrorHandler.withErrorHandling(async () => {
      // 首先立即从localStorage获取数据，提供快速响应
      const data = localStorage.getItem(`${STORAGE_KEYS.INVOICES}_${projectId}`);
      const localData: Invoice[] = data ? JSON.parse(data) : [];

      // 如果有本地数据，立即返回；同时在后台尝试更新
      if (localData.length > 0) {
        // 后台尝试从API获取最新数据
        this.updateInvoicesInBackground(projectId);
        return localData;
      }

      // 如果没有本地数据，检查是否应该尝试API
      if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
        console.log('🔧 Development mode: No local invoice data and API server not available, returning empty array');
        return localData; // 返回空数组
      }

      // 尝试从API获取（使用更短的超时）
      try {
        const response = await api.get<Invoice[]>(`/finance/${projectId}/invoices`, {
          timeout: 2000 // 减少超时到2秒
        });
        
        // 保存到localStorage
        localStorage.setItem(`${STORAGE_KEYS.INVOICES}_${projectId}`, JSON.stringify(response));
        return response;
      } catch (error) {
        console.error('Error fetching invoices from API:', error);
        return localData; // 返回本地数据（可能为空）
      }
    }, '获取发票失败') as Promise<Invoice[]>;
  }

  // 后台更新Invoice数据
  private async updateInvoicesInBackground(projectId: string): Promise<void> {
    // 检查是否在开发环境且API服务器不可用
    if (process.env.NODE_ENV === 'development' && !await this.isApiServerAvailable()) {
      console.log('🔧 Development mode: Skipping background API update (API server not available)');
      return;
    }

    try {
      const response = await api.get<Invoice[]>(`/finance/${projectId}/invoices`, {
        timeout: 2000
      });
      localStorage.setItem(`${STORAGE_KEYS.INVOICES}_${projectId}`, JSON.stringify(response));
      console.log('✅ Background update completed for invoices');
    } catch (error) {
      // 后台更新失败不影响用户体验，只记录简单日志
      console.log('Background update failed for invoices:', error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async createInvoice(projectId: string, invoice: Omit<Invoice, 'id' | 'number'>): Promise<Invoice> {
    return ErrorHandler.withErrorHandling(async () => {
      try {
        // 尝试通过API创建
        const response = await api.post<Invoice>(`/finance/${projectId}/invoices`, invoice);
        return response;
      } catch (error) {
        console.error('Error creating invoice via API:', error);

        // 从本地存储获取现有发票
        const existingInvoices = await this.getInvoices(projectId);

        // 创建新发票
        const newInvoice: Invoice = {
          ...invoice as any,
          id: Date.now().toString(),
          number: `INV-${new Date().getFullYear()}-${(existingInvoices.length + 1).toString().padStart(3, '0')}`,
          projectId
        };

        // 保存到本地存储
        const updatedInvoices = [...existingInvoices, newInvoice];
        localStorage.setItem(`${STORAGE_KEYS.INVOICES}_${projectId}`, JSON.stringify(updatedInvoices));

        return newInvoice;
      }
    }, '创建发票失败') as Promise<Invoice>;
  }

  // 计算财务摘要
  async getFinancialSummary(projectId: string): Promise<FinancialSummary> {
    const invoices = await this.getInvoices(projectId);

    const totalValue = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const receivedValue = invoices.filter(invoice => invoice.status === 'Paid').reduce((sum, invoice) => sum + invoice.amount, 0);
    const pendingValue = invoices.filter(invoice => invoice.status === 'Pending').reduce((sum, invoice) => sum + invoice.amount, 0);
    const upcomingValue = invoices.filter(invoice => invoice.status === 'Upcoming').reduce((sum, invoice) => sum + invoice.amount, 0);

    return {
      totalValue,
      receivedValue,
      pendingValue,
      upcomingValue,
      receivedPercentage: totalValue > 0 ? Math.round((receivedValue / totalValue) * 100) : 0
    };
  }
}

export default new FinanceService();
