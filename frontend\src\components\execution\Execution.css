/* Execution Component Styles */
.execution-container {
  width: 100%;
}

.progress-card {
  margin-bottom: 24px;
}

.progress-container {
  text-align: center;
  padding: 20px 0;
}

.progress-info {
  margin-top: 16px;
}

.progress-info h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.progress-info p {
  color: rgba(0, 0, 0, 0.65);
}

.execution-tabs {
  margin-top: 16px;
}

.module-card {
  margin-bottom: 24px;
}

.form-actions {
  text-align: right;
  margin-top: 16px;
}

/* Status Tag Styles */
.ant-tag-success {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-processing {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* Table Styles */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* <PERSON><PERSON> Styles */
.ant-btn-link {
  padding: 0 8px;
}

.ant-btn-link:hover {
  background-color: transparent;
}

/* Form Styles */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-card-head-title {
  font-weight: 600;
}

/* Gantt Chart Styles */
.gantt-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fe;
  border-radius: 8px;
  overflow: hidden;
}

.gantt-header {
  display: flex;
  border-bottom: 1px solid #eaedf7;
}

.gantt-task-list {
  width: 300px;
  flex-shrink: 0;
  border-right: 1px solid #eaedf7;
}

.gantt-task-header {
  padding: 12px 16px;
  font-weight: 600;
  color: #333;
}

.gantt-timeline-header {
  display: flex;
  flex-grow: 1;
}

.gantt-day {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-weight: 500;
  color: #666;
  border-right: 1px solid #eaedf7;
}

.gantt-body {
  display: flex;
  flex-direction: column;
}

.gantt-row {
  display: flex;
  border-bottom: 1px solid #eaedf7;
  height: 80px;
  position: relative;
}

.gantt-task-info {
  width: 300px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-right: 1px solid #eaedf7;
  background-color: white;
}

.gantt-task-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.gantt-task-details {
  flex-grow: 1;
  overflow: hidden;
}

.gantt-task-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gantt-task-description {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gantt-task-more {
  margin-left: auto;
}

.gantt-timeline {
  flex-grow: 1;
  position: relative;
  display: flex;
  background-image: linear-gradient(to right, #eaedf7 1px, transparent 1px);
  background-size: 10% 100%;
}

.gantt-bar {
  position: absolute;
  height: 40px;
  top: 20px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gantt-progress {
  height: 100%;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
}

.gantt-progress-label {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.gantt-task-toggle {
  margin-left: auto;
}
