// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// 项目类型
export interface Project {
  id: string;              // 项目主键ID
  projectId: string;       // 业务ID (如: PROJ-2025-001)
  name: string;
  client: string;
  country: string;
  tier: 'S' | 'V' | 'B' | 'A';  // 统一tier类型定义
  level: string;
  category: string;
  revenue: number;
  stage: string;
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';
  probability: string | number;
  owner: string;
  description: string;
  background?: string;
  closeDate?: string;
  stages?: ProjectStage[];
  teamMembers?: TeamMember[];
  documents?: Document[];
  createdAt: string;
  updatedAt: string;
  startDate?: string;
}

// 项目状态
export interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  loading: boolean;
  error: string | null;
}

// 项目阶段类型
export interface ProjectStage {
  id: string;
  projectId: string;
  name: string;
  type: 'basic_info' | 'proposal' | 'contract' | 'execution' | 'acceptance' | 'finance' | 'closeout' | 'after_sales';
  status: 'not_started' | 'in_progress' | 'completed' | 'pending' | 'approved' | 'rejected';
  startDate?: string;
  endDate?: string;
  notes?: string;
  data?: string;
  createdAt: string;
  updatedAt: string;
}

// 项目阶段状态
export interface ProjectStageState {
  stages: ProjectStage[];
  currentStage: ProjectStage | null;
  loading: boolean;
  error: string | null;
}

// 团队成员角色
export enum TeamRole {
  OWNER = 'owner',
  ADMIN = 'admin', 
  MEMBER = 'member',
  VIEWER = 'viewer'
}

// 成员状态
export enum MemberStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  INACTIVE = 'inactive'
}

// 团队成员接口
export interface TeamMember {
  id: string;
  userId: string;
  projectId: string;
  email: string;
  name: string;
  avatar?: string;
  role: TeamRole;
  status: MemberStatus;
  invitedBy: string;
  invitedAt: string;
  joinedAt?: string;
  lastActiveAt?: string;
  position?: string;
  department?: string;
  phone?: string;
  bio?: string;
  createdAt?: string;
  permissions: {
    canViewProject: boolean;
    canEditProject: boolean;
    canDeleteProject: boolean;
    canManageTeam: boolean;
    canManageFinance: boolean;
    canExportData: boolean;
    canViewReports: boolean;
  };
}

// 团队邀请接口
export interface TeamInvitation {
  id: string;
  projectId: string;
  email: string;
  role: TeamRole;
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  token: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
}

// 权限模板
export interface PermissionTemplate {
  role: TeamRole;
  permissions: TeamMember['permissions'];
  description: string;
}

// 团队统计
export interface TeamStats {
  totalMembers: number;
  activeMembers: number;
  pendingInvitations: number;
  roleDistribution: {
    [key in TeamRole]: number;
  };
}

// 团队成员状态
export interface TeamMemberState {
  teamMembers: TeamMember[];
  currentTeamMember: TeamMember | null;
  loading: boolean;
  error: string | null;
}

// 文档类型
export interface Document {
  id: string;
  name: string;
  description?: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  category: 'contract' | 'proposal' | 'report' | 'invoice' | 'other';
  uploadedBy: User;
  createdAt: string;
  updatedAt: string;
}

// 文档状态
export interface DocumentState {
  documents: Document[];
  currentDocument: Document | null;
  loading: boolean;
  error: string | null;
}

// 客户类型
export interface Client {
  id: string;
  clientId: string;
  name: string;
  email: string;
  phone?: string;
  industry: string;
  country: string;
  address?: string;
  website?: string;
  tier: 'S' | 'V' | 'B' | 'A';  // 统一使用Project的tier类型
  status: 'active' | 'inactive' | 'potential' | 'churned';
  contactPerson: string;
  contactEmail?: string;
  contactPhone?: string;
  totalRevenue: number;
  totalProjects: number;
  lastContactDate?: string;
  notes?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

// 客户状态
export interface ClientState {
  clients: Client[];
  currentClient: Client | null;
  loading: boolean;
  error: string | null;
}

// 供应商类型
export interface Vendor {
  id: string;
  vendorId: string;
  name: string;
  email: string;
  phone?: string;
  category: 'product' | 'service' | 'material' | 'software' | 'consulting' | 'logistics' | 'other';
  industry: string;
  country: string;
  address?: string;
  website?: string;
  contactPerson: string;
  contactEmail?: string;
  contactPhone?: string;
  paymentTerms: 'net_15' | 'net_30' | 'net_45' | 'net_60' | 'net_90' | 'prepaid' | 'cod';
  creditRating: 'AAA' | 'AA' | 'A' | 'BBB' | 'BB' | 'B' | 'CCC' | 'CC' | 'C' | 'D';
  status: 'active' | 'inactive' | 'potential' | 'blacklisted' | 'suspended';
  totalSpend: number;
  contractStartDate?: string;
  contractEndDate?: string;
  lastOrderDate?: string;
  tags: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 供应商状态
export interface VendorState {
  vendors: Vendor[];
  currentVendor: Vendor | null;
  loading: boolean;
  error: string | null;
}

export interface ProjectFilters {
  searchText: string;
  status: string;
  country: string;
  tier: string;
  category: string;
  revenue: string;
  probability: string;
  level: string;
  createDate: string;
}

export interface ProjectService {
  getAllProjects: (forceRefresh?: boolean) => Promise<Project[]>;
  getProject: (id: string) => Promise<Project>;
  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Project>;
  updateProject: (id: string, project: Partial<Project>) => Promise<Project>;
  deleteProject: (id: string) => Promise<void>;
}

export interface ApiError extends Error {
  code?: string;
  status?: number;
  details?: any;
}

// 通知系统类型定义
export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'mention' | 'team' | 'system';
  isRead: boolean;
  isStarred: boolean;
  createdAt: string;
  projectId?: string;
  projectName?: string;
  actionUrl?: string;
  icon?: string;
  priority: 'low' | 'medium' | 'high';
}

export interface NotificationStats {
  unreadCount: number;
  totalCount: number;
  lastReadAt?: string;
}

// Transaction类型 - 全局财务流水
export interface Transaction {
  id: string;
  transactionId: string; // 业务ID (如: TXN-2025-001)
  type: 'income' | 'expense';
  category: string;
  description: string;
  amount: number; // 不含税金额
  vatRate: number;
  vatAmount: number;
  totalAmount: number; // 含税金额
  date: string;
  paymentMethod: string;
  status: 'completed' | 'pending' | 'cancelled';
  
  // 关联字段
  projectId?: string; // 关联项目
  clientId?: string; // 关联客户
  vendorId?: string; // 关联供应商
  invoiceId?: string; // 关联发票
  revenueItem?: string; // 关联收入项 (如: "NRC-WEB")
  budgetItem?: string; // 关联预算项
  
  // 货币信息
  currency?: string; // 当前显示货币
  originalCurrency?: string; // 原始货币
  
  // 其他信息
  reference?: string; // 参考号
  notes?: string;
  tags?: string[];
  attachments?: any[];
  
  // 循环交易
  isRecurring?: boolean;
  recurringFrequency?: 'monthly' | 'quarterly' | 'annually';
  recurringEndDate?: string;
  
  createdAt: string;
  updatedAt: string;
}

// Sales Invoice类型 - 销售发票
export interface SalesInvoice {
  id: string;
  invoiceNumber: string; // 发票号 (如: INV-2025-001)
  projectId: string; // 所属项目
  clientId?: string; // 客户ID
  revenueItem: string; // 关联收入项 (如: "NRC-WEB")
  
  // 金额信息
  amount: number; // 不含税金额
  vatRate: number;
  vatAmount: number;
  totalAmount: number; // 含税金额
  
  // 日期信息
  invoiceDate: string;
  dueDate: string;
  
  // 状态信息
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  
  // 回款信息 (基于Transaction自动计算)
  paidAmount: number; // 已回款金额
  pendingAmount: number; // 待回款金额
  collectionProgress: number; // 回款进度 (0-100%)
  
  // 其他信息
  description?: string;
  notes?: string;
  terms?: string; // 付款条款
  
  createdAt: string;
  updatedAt: string;
}

// Expense Invoice类型 - 费用发票
export interface ExpenseInvoice {
  id: string;
  invoiceNumber: string; // 发票号 (如: PUR-2025-001)
  projectId?: string; // 所属项目 (可选)
  vendorId?: string; // 供应商ID
  budgetItem?: string; // 关联预算项
  
  // 基本信息
  category: string; // 费用类别
  description: string;
  
  // 金额信息
  amount: number; // 不含税金额
  vatRate: number;
  vatAmount: number;
  totalAmount: number; // 含税金额
  
  // 日期信息
  invoiceDate: string;
  dueDate: string;
  
  // 状态信息
  status: 'pending' | 'approved' | 'paid' | 'rejected' | 'cancelled';
  paymentStatus: 'unpaid' | 'partial' | 'paid';
  
  // 付款信息 (基于Transaction自动计算)
  paidAmount: number; // 已付款金额
  pendingAmount: number; // 待付款金额
  paymentProgress: number; // 付款进度 (0-100%)
  
  // 审批信息
  approvedBy?: string;
  approvedDate?: string;
  
  // 其他信息
  reference?: string;
  notes?: string;
  
  createdAt: string;
  updatedAt: string;
}

// Revenue Item类型 - 收入项 (项目级)
export interface RevenueItem {
  id: string;
  projectId: string;
  name: string; // 如: "NRC-WEB", "MRC-MONTHLY"
  description: string;
  type: 'nrc' | 'mrc'; // 一次性收入 / 循环收入
  
  // 金额信息
  amount: number; // 合同确认金额 (不含税)
  vatRate: number;
  vatAmount: number;
  totalAmount: number; // 含税金额
  currency: string;
  
  // MRC专用字段
  unitPrice?: number; // 单价 (不含税)
  totalUnitPrice?: number; // 单价 (含税)
  cycle?: string; // 计费周期
  startDate?: string;
  endDate?: string;
  totalCycleRevenue?: number; // 周期总收入
  
  // 收款状态 (基于SalesInvoice和Transaction自动计算)
  totalReceived: number; // 已收款金额 (不含税)
  totalPending: number; // 待收款金额 (不含税)
  collectionProgress: number; // 收款进度 (0-100%)
  paymentStatus: 'pending' | 'partial' | 'paid' | 'overdue';
  
  // 其他信息
  notes?: string;
  
  createdAt: string;
  updatedAt: string;
}

// Transaction状态
export interface TransactionState {
  transactions: Transaction[];
  currentTransaction: Transaction | null;
  loading: boolean;
  error: string | null;
}

// Invoice状态
export interface InvoiceState {
  salesInvoices: SalesInvoice[];
  expenseInvoices: ExpenseInvoice[];
  currentInvoice: SalesInvoice | ExpenseInvoice | null;
  loading: boolean;
  error: string | null;
}

// Revenue状态
export interface RevenueState {
  revenueItems: RevenueItem[];
  currentItem: RevenueItem | null;
  loading: boolean;
  error: string | null;
}

// 财务数据同步结果
export interface FinanceSyncResult {
  success: boolean;
  updated: number;
  total: number;
  errors: string[];
  syncedAt: string;
}

// 财务数据统计
export interface FinanceStatistics {
  // Revenue统计
  totalRevenue: number;
  receivedRevenue: number;
  pendingRevenue: number;
  collectionRatio: number;
  
  // Invoice统计
  totalInvoiced: number;
  paidInvoices: number;
  overdueInvoices: number;
  pendingInvoices: number;
  
  // Transaction统计
  totalTransactions: number;
  incomeTransactions: number;
  expenseTransactions: number;
  pendingTransactions: number;
  
  // 项目级统计
  projectRevenue?: number;
  projectExpenses?: number;
  projectProfit?: number;
  
  lastUpdated: string;
}
