import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Button, Typography, Space, message, Tabs, Statistic } from 'antd';
import {
  ProjectOutlined,
  UserOutlined,
  DollarOutlined,
  PlusOutlined,
  ClockCircleOutlined,
  StarOutlined,
  ToolOutlined,
  WarningOutlined,
  PercentageOutlined,
  CloudUploadOutlined,
  UserAddOutlined,
  Bar<PERSON><PERSON>Outlined,
  ShopOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import {
  BarChart, Bar, XAxis, YAxis, Tooltip, CartesianGrid, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, Legend
} from 'recharts';
import projectService from '../services/new-project.service';
import clientService from '../services/client.service';
import { Project, Client } from '../types';
import '../styles/dashboard.css';

const { Text } = Typography;
const { TabPane } = Tabs;

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [timeView, setTimeView] = useState('monthly');

  // 快捷操作配置 - 可扩展的系统功能
  // 如何自定义：
  // 1. 添加新操作：在数组中添加新的配置对象
  // 2. 控制显示：修改 enabled 字段
  // 3. 设置优先级：primary 类型会显示为主按钮，default 类型显示为小按钮
  const quickActions = [
    {
      id: 'new_opportunity',
      label: 'New Opportunity',
      icon: <PlusOutlined />,
      path: '/opportunity/create',
      type: 'default' as const,
      enabled: true
    },
    {
      id: 'new_client',
      label: 'New Client',
      icon: <UserAddOutlined />,
      path: '/client/create',
      type: 'default' as const,
      enabled: true
    },
    {
      id: 'add_payment',
      label: 'New Transaction',
      icon: <DollarOutlined />,
      path: '/finance',
      type: 'default' as const,
      enabled: true
    },
    {
      id: 'new_vendor',
      label: 'New Vendor',
      icon: <ShopOutlined />,
      path: '/vendor/create',
      type: 'default' as const,
      enabled: true
    },
    {
      id: 'upload_contract',
      label: 'Upload Contract',
      icon: <CloudUploadOutlined style={{ fontSize: '16px', color: '#FF7A00' }} />,
      path: '/opportunity',
      type: 'default' as const,
      enabled: false // 可以通过配置控制显示
    },
    {
      id: 'view_reports',
      label: 'View Reports',
      icon: <BarChartOutlined />,
      path: '/reports',
      type: 'default' as const,
      enabled: false
    }
  ];

  // 获取启用的快捷操作
  const enabledActions = quickActions.filter(action => action.enabled);

  useEffect(() => {
    // 清除任何旧的错误消息
    message.destroy();
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [projectsData, clientsData] = await Promise.all([
        projectService.getAllProjects(),
        clientService.getAllClients()
      ]);
      setProjects(projectsData || []);
      setClients(clientsData || []);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      message.error('Failed to load dashboard data');
    }
  };

  // 计算统计数据
  const activeProjects = projects.filter(p => p.status === 'in_progress').length;
  const totalProjects = projects.length;
  const activeClients = clients.filter(c => c.status === 'active').length;
  const totalRevenue = projects.reduce((sum, p) => sum + (p.revenue || 0), 0);
  const netProfit = Math.round(totalRevenue * 0.22); // 假设22%利润率
  const newOpportunities = projects.filter(p => p.status === 'planning').length;

  // 图表数据
  const projectStageData = [
    { stage: 'Proposal', count: projects.filter(p => p.status === 'planning').length },
    { stage: 'Contract', count: projects.filter(p => p.status === 'on_hold').length },
    { stage: 'Execution', count: projects.filter(p => p.status === 'in_progress').length },
    { stage: 'Completed', count: projects.filter(p => p.status === 'completed').length }
  ];

  const clientLevelData = [
    { level: 'S', value: clients.filter(c => c.tier === 'S').length },
    { level: 'V', value: clients.filter(c => c.tier === 'V').length },
    { level: 'B', value: clients.filter(c => ['BA', 'B'].includes(c.tier)).length },
    { level: 'A', value: clients.filter(c => ['A', 'C'].includes(c.tier)).length }
  ].filter(item => item.value > 0);

  // 时间数据生成函数
  const generateTimeData = (view: string) => {
    const currentDate = new Date();
    
    if (view === 'quarterly') {
      // 最近8个季度数据
      return Array.from({ length: 8 }, (_, i) => {
        const quarterNames = ['Q1', 'Q2', 'Q3', 'Q4'];
        const currentQuarter = Math.floor((currentDate.getMonth()) / 3);
        const quarterIndex = (currentQuarter - (7 - i) + 32) % 4;
        const year = currentDate.getFullYear() - Math.floor((7 - i - currentQuarter) / 4);
        
        const revenue = Math.round(totalRevenue * (0.2 + Math.random() * 0.3) / 1000);
        const cost = Math.round(revenue * (0.7 + Math.random() * 0.2));
        
        return {
          time: `${quarterNames[quarterIndex]} ${year}`,
          revenue,
          cost,
          profit: revenue - cost
        };
      });
    } else if (view === 'yearly') {
      // 最近5年数据
      return Array.from({ length: 5 }, (_, i) => {
        const year = currentDate.getFullYear() - (4 - i);
        
        const revenue = Math.round(totalRevenue * (0.8 + Math.random() * 0.4) / 1000);
        const cost = Math.round(revenue * (0.65 + Math.random() * 0.2));
        
        return {
          time: year.toString(),
          revenue,
          cost,
          profit: revenue - cost
        };
      });
    } else {
      // 月度数据 - 最近12个月
      return Array.from({ length: 12 }, (_, i) => {
        const date = new Date(currentDate);
        date.setMonth(date.getMonth() - (11 - i));
        
        const revenue = Math.round(totalRevenue * (0.06 + Math.random() * 0.08) / 1000);
        const cost = Math.round(revenue * (0.65 + Math.random() * 0.2));
        
        return {
          time: date.toLocaleDateString('en-GB', { month: '2-digit', year: 'numeric' }),
          revenue,
          cost,
          profit: revenue - cost
        };
      });
    }
  };

  const revenueData = generateTimeData(timeView);

  // 使用系统统一主题颜色 - 80%透明度
  const CHART_COLORS = [
    'rgba(255, 122, 0, 0.8)',   // 主色橙色 80%透明度
    'rgba(16, 185, 129, 0.8)',  // 成功绿色 80%透明度  
    'rgba(59, 130, 246, 0.8)',  // 信息蓝色 80%透明度
    'rgba(245, 158, 11, 0.8)',  // 警告黄色 80%透明度
    'rgba(156, 163, 175, 0.8)'  // 灰色 80%透明度
  ];

  // 延期项目
  const delayedProjects = projects.filter(p => p.status === 'on_hold').length;

  // 高价值项目
  const highValueProjects = projects.filter(p => (p.revenue || 0) > 10000).length;

  // 执行阶段项目
  const executionProjects = projects.filter(p => p.status === 'in_progress').length;

  // VIP客户
  const vipClients = clients.filter(c => ['VIP', 'SVIP'].includes(c.tier));

  // 汇总卡片数据 - 使用Statistic组件，与Finance页面保持一致
  const summaryCards = [
    {
      title: 'Active Projects',
      value: activeProjects,
      suffix: null, // 不使用suffix
      customValue: `${activeProjects}/${totalProjects}`, // 使用自定义格式
      color: '#3B82F6', // 系统info蓝色
      prefix: <ProjectOutlined style={{ fontSize: '0.7em', color: '#3B82F6' }} />,
      clickPath: '/opportunity' // Lead to Cash 项目列表页
    },
    {
      title: 'Active Clients',
      value: activeClients,
      suffix: ' Clients',
      color: '#10B981', // 系统success绿色
      prefix: <UserOutlined style={{ fontSize: '0.7em', color: '#10B981' }} />,
      clickPath: '/client' // Client 管理页面
    },
    {
      title: 'Total Revenue',
      value: (totalRevenue / 1000),
      precision: 0,
      suffix: 'K',
      color: '#FF7A00', // 系统主色橙色
      prefix: <span style={{ fontSize: '0.7em' }}>€</span>,
      clickPath: '/finance' // Finance 管理页面
    },
    {
      title: 'Net Profit',
      value: (netProfit / 1000),
      precision: 0,
      suffix: 'K',
      color: '#10B981', // 系统success绿色
      prefix: <span style={{ fontSize: '0.7em' }}>€</span>,
      clickPath: '/finance' // Finance 管理页面
    },
    {
      title: 'New Opportunities',
      value: newOpportunities,
      color: '#4B5563', // 系统灰色
      prefix: <StarOutlined style={{ fontSize: '0.7em', color: '#4B5563' }} />,
      clickPath: '/opportunity' // Lead to Cash 项目列表页
    }
  ];

  return (
    <div style={{ 
      padding: '24px', 
      backgroundColor: '#F9FAFB', 
      minHeight: '100vh' 
    }}>


      {/* 汇总卡片 */}
      <div style={{ 
        display: 'flex', 
        gap: '12px', 
        marginBottom: '32px',
        flexWrap: 'wrap'
      }}>
        {summaryCards.map((card, index) => (
          <div 
            key={index}
            style={{ 
              flex: '1 1 calc(20% - 10px)',
              minWidth: '200px'
            }}
          >
            <Card 
              hoverable
              onClick={() => navigate(card.clickPath)}
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB',
                height: '100px',
                width: '100%',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              bodyStyle={{ padding: '16px' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 1px 2px rgba(0,0,0,0.05)';
              }}
            >
              {card.customValue ? (
                // 使用标准Statistic组件，但用自定义的value显示
                <Statistic
                  title={card.title}
                  value={card.customValue}
                  prefix={card.prefix}
                  valueStyle={{ color: card.color, fontSize: '2.0em' }}
                />
              ) : (
                // 标准Statistic组件显示
                <Statistic
                  title={card.title}
                  value={card.value}
                  precision={card.precision}
                  prefix={card.prefix}
                  suffix={card.suffix}
                  valueStyle={{ color: card.color, fontSize: '2.0em' }}
                  className="dashboard-statistic-small-suffix"
                />
              )}
            </Card>
          </div>
        ))}
      </div>

      {/* 主要内容区域 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        {/* 项目焦点 */}
        <Col xs={24} md={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Project Focus</span>
              </div>
            }
            style={{ 
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB',
              height: '200px'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ClockCircleOutlined style={{ color: '#F59E0B', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • <strong>{delayedProjects}</strong> Delayed Projects
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <StarOutlined style={{ color: '#FF7A00', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • <strong>{highValueProjects}</strong> High Value Projects
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ToolOutlined style={{ color: '#10B981', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • <strong>{executionProjects}</strong> Projects in Execution Phase
                </Text>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 客户焦点 */}
        <Col xs={24} md={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Customer Focus</span>
              </div>
            }
            style={{ 
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB',
              height: '200px'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              {vipClients.slice(0, 3).map((client, index) => {
                const statusIcons = ['Pending Visit', '90% Paid', 'Contract Pending'];
                return (
                  <div key={client.id} style={{ display: 'flex', alignItems: 'center' }}>
                    <UserOutlined style={{ 
                      color: client.tier === 'S' ? '#FF7A00' : '#3B82F6', 
                      marginRight: '8px' 
                    }} />
                    <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                      • {client.name} ({client.tier}) – {statusIcons[index] || 'Active'}
                    </Text>
                  </div>
                );
              })}
              {vipClients.length === 0 && (
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  No VIP clients available
                </Text>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 底部区域 */}
      <Row gutter={[24, 24]}>
        {/* 财务概览 */}
        <Col xs={24} md={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Finance Overview</span>
              </div>
            }
            style={{ 
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB',
              height: '200px'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <DollarOutlined style={{ color: '#10B981', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • This Month: Revenue €{(totalRevenue/12/1000).toFixed(0)}K | Net Profit €{(netProfit/12/1000).toFixed(0)}K
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <WarningOutlined style={{ color: '#EF4444', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • Overdue Invoices: 2 × €12,000
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <PercentageOutlined style={{ color: '#10B981', marginRight: '8px' }} />
                <Text style={{ fontSize: '14px', color: '#4B5563' }}>
                  • Profit Margin: 22%
                </Text>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 快速操作 */}
        <Col xs={24} md={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Quick Actions</span>
              </div>
            }
            style={{ 
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB',
              height: '200px'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
              gap: '8px' 
            }}>
              {enabledActions.map(action => (
                <Button 
                  key={action.id}
                  icon={action.icon}
                  size="small"
                  style={{ 
                    height: '32px',
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}
                  onClick={() => navigate(action.path)}
                >
                  {action.label}
                </Button>
              ))}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        {/* 项目阶段分布图 */}
        <Col xs={24} md={12}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Project Stage Distribution</span>
              </div>
            }
            style={{
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <ResponsiveContainer width="100%" height={240}>
              <BarChart data={projectStageData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis dataKey="stage" tick={{ fontSize: 12, fill: '#4B5563' }} />
                <YAxis tick={{ fontSize: 12, fill: '#4B5563' }} />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#FFFFFF', 
                    border: '1px solid #E5E7EB',
                    borderRadius: '4px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
                  }} 
                />
                <Bar dataKey="count" fill="#FF7A00" barSize={40} />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        {/* 客户等级分布饼图 */}
        <Col xs={24} md={12}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Client Level Distribution</span>
              </div>
            }
            style={{
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB'
            }}
            bodyStyle={{ padding: '16px' }}
          >
            <ResponsiveContainer width="100%" height={240}>
              <PieChart>
                <Pie
                  data={clientLevelData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ level, percent }) => `${level} (${(percent * 100).toFixed(0)}%)`}
                  outerRadius={80}
                  innerRadius={30}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="level"
                  stroke="#FFFFFF"
                  strokeWidth={2}
                >
                  {clientLevelData.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={CHART_COLORS[index % CHART_COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name) => [`${value} clients`, name]}
                  labelFormatter={(label) => `Level: ${label}`}
                  contentStyle={{ 
                    backgroundColor: '#FFFFFF', 
                    border: '1px solid #E5E7EB',
                    borderRadius: '4px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                    fontSize: '12px'
                  }}
                />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  wrapperStyle={{ 
                    fontSize: '12px', 
                    color: '#4B5563',
                    paddingTop: '10px'
                  }}
                  formatter={(value) => value}
                />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 收入与成本趋势图 - 股票风格 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{ fontSize: '16px', fontWeight: '500', color: '#1F2023' }}>Monthly Revenue & Cost Trend</span>
                </div>
                <Tabs 
                  activeKey={timeView} 
                  onChange={setTimeView}
                  size="small"
                  style={{ marginBottom: 0 }}
                >
                  <TabPane tab="Monthly" key="monthly" />
                  <TabPane tab="Quarterly" key="quarterly" />
                  <TabPane tab="Yearly" key="yearly" />
                </Tabs>
              </div>
            }
            style={{
              borderRadius: '8px',
              boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
              border: '1px solid #E5E7EB'
            }}
            bodyStyle={{ padding: '16px', backgroundColor: '#FAFAFA' }}
          >
            {/* 股票风格统计信息 */}
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-around', 
              marginBottom: '16px',
              padding: '12px',
              backgroundColor: '#FFFFFF',
              borderRadius: '6px',
              border: '1px solid #E5E7EB'
            }}>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Revenue</Text>
                <Text style={{ fontSize: '16px', fontWeight: '600', color: '#10B981' }}>
                  €{revenueData.reduce((sum, item) => sum + item.revenue, 0)}K
                </Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Cost</Text>
                <Text style={{ fontSize: '16px', fontWeight: '600', color: '#EF4444' }}>
                  €{revenueData.reduce((sum, item) => sum + item.cost, 0)}K
                </Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Net Profit</Text>
                <Text style={{ fontSize: '16px', fontWeight: '600', color: '#3B82F6' }}>
                  €{revenueData.reduce((sum, item) => sum + item.profit, 0)}K
                </Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Profit Margin</Text>
                <Text style={{ fontSize: '16px', fontWeight: '600', color: '#FF7A00' }}>
                  {(
                    (revenueData.reduce((sum, item) => sum + item.profit, 0) / 
                     revenueData.reduce((sum, item) => sum + item.revenue, 0)) * 100
                  ).toFixed(1)}%
                </Text>
              </div>
            </div>

            <ResponsiveContainer width="100%" height={320}>
              <LineChart 
                data={revenueData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <defs>
                  <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.05}/>
                  </linearGradient>
                  <linearGradient id="costGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#EF4444" stopOpacity={0.05}/>
                  </linearGradient>
                  <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05}/>
                  </linearGradient>
                </defs>
                <CartesianGrid 
                  strokeDasharray="1 1" 
                  stroke="#E5E7EB" 
                  strokeOpacity={0.5}
                  horizontal={true}
                  vertical={false}
                />
                <XAxis 
                  dataKey="time" 
                  tick={{ fontSize: 11, fill: '#6B7280' }}
                  axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                  tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                  interval={0}
                />
                <YAxis 
                  tick={{ fontSize: 11, fill: '#6B7280' }}
                  axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                  tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                  domain={['dataMin - 2', 'dataMax + 2']}
                />
                <Tooltip 
                  formatter={(value, name) => [
                    `€${value}K`, 
                    name === 'revenue' ? 'Revenue' : 
                    name === 'cost' ? 'Cost' : 'Profit'
                  ]}
                  labelStyle={{ color: '#374151', fontWeight: '500' }}
                  contentStyle={{ 
                    backgroundColor: '#FFFFFF', 
                    border: '1px solid #D1D5DB',
                    borderRadius: '6px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    fontSize: '12px'
                  }}
                />
                <Legend 
                  wrapperStyle={{ 
                    color: '#6B7280', 
                    fontSize: '12px',
                    paddingTop: '16px'
                  }}
                />
                {/* Revenue Line with Area */}
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#10B981" 
                  strokeWidth={2.5}
                  name="Revenue"
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, fill: '#10B981', strokeWidth: 2, stroke: '#FFFFFF' }}
                  fill="url(#revenueGradient)"
                />
                {/* Cost Line with Area */}
                <Line 
                  type="monotone" 
                  dataKey="cost" 
                  stroke="#EF4444" 
                  strokeWidth={2.5}
                  name="Cost"
                  strokeDasharray="0"
                  dot={{ fill: '#EF4444', strokeWidth: 2, r: 3 }}
                  activeDot={{ r: 5, fill: '#EF4444', strokeWidth: 2, stroke: '#FFFFFF' }}
                  fill="url(#costGradient)"
                />
                {/* Profit Line */}
                <Line 
                  type="monotone" 
                  dataKey="profit" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  name="Profit"
                  strokeDasharray="5 5"
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 2 }}
                  activeDot={{ r: 4, fill: '#3B82F6', strokeWidth: 2, stroke: '#FFFFFF' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
