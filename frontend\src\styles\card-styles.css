/* 卡片样式 */
.project-card {
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  border-color: #d9d9d9 !important;
}

.card-action-button {
  opacity: 0.7;
}

.card-action-button:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.04);
}

/* 编辑按钮悬停效果 */
.project-card .ant-btn-text:hover {
  background-color: rgba(24, 144, 255, 0.1);
}

/* 删除按钮悬停效果 */
.project-card .ant-btn-text.ant-btn-dangerous:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

/* 确保卡片内的链接样式正确 */
.project-card a {
  text-decoration: none;
}

.project-card a:hover {
  text-decoration: underline;
}
