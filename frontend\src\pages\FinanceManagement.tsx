import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Card, Statistic, Row, Col, Input, Select, Modal, message, Tag, Tooltip, DatePicker, Image } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, DollarOutlined, CreditCardOutlined, BankOutlined, SearchOutlined, PaperClipOutlined, EyeOutlined } from '@ant-design/icons';
import type { Dayjs } from 'dayjs';
import { useNavigate } from 'react-router-dom';
import '../styles/finance-page.css';
import { enableTableColumnResize, clearTableColumnWidths } from '../utils/tableResizer';
import authService from '../services/auth.service';
import notificationService from '../services/notification.service';
import { useCurrency } from '../contexts/CurrencyContext';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface Transaction {
  id: string;
  date: string;
  type: 'income' | 'expense';
  category: string;
  description: string;
  amount: number;
  paymentMethod: string;
  reference: string;
  status: 'completed' | 'pending' | 'cancelled';
  tags: string[];
  transactionId?: string;
  totalAmount?: number;
  client?: string;
  project?: string;
  currency?: string; // 新增：当前选择的货币
  originalCurrency?: string; // 新增：原始货币信息
  attachments?: any[];
  vatAmount?: number;
  vatRate?: number;
}

const FinanceManagement: React.FC = () => {
  const navigate = useNavigate();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const { selectedCurrency, convertAmount, formatAmount } = useCurrency();
  const [attachmentPreviewVisible, setAttachmentPreviewVisible] = useState(false);
  const [previewAttachments, setPreviewAttachments] = useState<any[]>([]);
  const [previewTransactionRef, setPreviewTransactionRef] = useState<string>('');

  // 货币检测和转换函数
  const detectAndConvertAmount = (amount: number, description: string, record?: any): { convertedAmount: number; originalCurrency: string } => {
    // 优先使用currency字段
    let detectedCurrency = record?.currency || record?.originalCurrency || 'EUR';

    // 如果没有currency和originalCurrency，则从描述中检测
    if (!record?.currency && !record?.originalCurrency && description && typeof description === 'string') {
      const desc = description.toLowerCase();
      if (desc.includes('cny') || desc.includes('人民币') || desc.includes('rmb') || desc.includes('¥')) {
        detectedCurrency = 'CNY';
      } else if (desc.includes('usd') || desc.includes('美元') || desc.includes('$')) {
        detectedCurrency = 'USD';
      } else if (desc.includes('gbp') || desc.includes('英镑') || desc.includes('£')) {
        detectedCurrency = 'GBP';
      } else if (desc.includes('jpy') || desc.includes('日元')) {
        detectedCurrency = 'JPY';
      }
    }

    // 转换金额到系统选定的货币
    const convertedAmount = convertAmount(amount, detectedCurrency, selectedCurrency);

    // 添加调试日志
    console.log(`🔄 Currency conversion: ${detectedCurrency} ${amount} → ${selectedCurrency} ${convertedAmount}`, {
      record: record,
      hasRecordCurrency: !!record?.currency,
      hasOriginalCurrency: !!record?.originalCurrency
    });

    return { convertedAmount, originalCurrency: detectedCurrency };
  };

  // 新系统从空白状态开始，不再设置示例数据
  useEffect(() => {
    const txKey = 'finance_transactions';
    const data = localStorage.getItem(txKey);
    let txList = [];
    if (data) {
      try { 
        const rawList = JSON.parse(data);
        // 数据适配：将CreateTransaction格式转换为FinanceManagement格式
        txList = rawList.map((item: any, index: number) => {
          // 确保amount是数字类型
          const rawAmount = item.totalAmount || item.amount || 0;
          const numericAmount = typeof rawAmount === 'string' ? parseFloat(rawAmount.replace(/[^\d.-]/g, '')) : rawAmount;
          
          return {
            id: item.transactionId || `tx_${Date.now()}_${index}`,
            date: item.date,
            type: item.type,
            category: item.category || '',
            description: item.description || '',
            amount: isNaN(numericAmount) ? 0 : numericAmount,
            paymentMethod: item.paymentMethod || '',
            reference: item.transactionId || `REF-${index}`,
            status: item.status || 'pending',
            tags: item.tags || [],
            transactionId: item.transactionId,
            totalAmount: item.totalAmount,
            client: item.client,
            project: item.project,
            currency: item.currency, // 保存currency字段
            originalCurrency: item.originalCurrency, // 关键：保留原始货币信息
            attachments: item.attachments || [],
            vatAmount: item.vatAmount,
            vatRate: item.vatRate
          };
        });

        // 🔧 按日期和创建时间排序，最新的在前面
        txList.sort((a: any, b: any) => {
          // 首先按日期排序（最新的在前）
          const dateA = new Date(a.date).getTime();
          const dateB = new Date(b.date).getTime();
          if (dateA !== dateB) {
            return dateB - dateA;
          }

          // 如果日期相同，按transactionId排序（假设ID包含时间信息）
          const idA = a.transactionId || a.reference || '';
          const idB = b.transactionId || b.reference || '';
          return idB.localeCompare(idA);
        });

        console.log('✅ Loaded and sorted transactions from localStorage:', txList.length, txList);
      } catch (e) {
        console.error('❌ Failed to parse transactions:', e);
        txList = [];
      }
    }
    setTransactions(txList);
  }, []);

  // 初始化表格列宽调整功能
  useEffect(() => {
    let resizer: any = null;
    const timer = setTimeout(() => {
      resizer = enableTableColumnResize('.finance-management .ant-table');
    }, 200);
    
    return () => {
      clearTimeout(timer);
      if (resizer) {
        resizer.destroy();
      }
    };
  }, [transactions]);

  // 计算统计数据 - 使用转换后的金额
  const getStatistics = () => {
    // 🔧 业务逻辑修复：只统计已完成/确认的收入，排除cancelled和pending
    const totalRevenue = transactions
      .filter(tx => tx.type === 'income' && !['cancelled', 'pending'].includes(tx.status.toLowerCase()))
      .reduce((sum, tx) => {
        // 检测并转换货币
        const { convertedAmount } = detectAndConvertAmount(tx.amount, tx.description, tx);
        
        // 计算不含VAT的金额
        let revenueAmount = convertedAmount;
        if (typeof tx.vatAmount === 'number') {
          revenueAmount = convertedAmount - tx.vatAmount;
        } else if (typeof tx.vatRate === 'number') {
          revenueAmount = convertedAmount / (1 + tx.vatRate / 100);
        }
        
        return sum + revenueAmount;
      }, 0);
    
    // 🔧 业务逻辑修复：只统计已确认的支出，排除cancelled状态
    const totalExpenses = transactions
      .filter(t => t.type === 'expense' && t.status.toLowerCase() !== 'cancelled')
      .reduce((sum, t) => {
        const { convertedAmount } = detectAndConvertAmount(t.amount, t.description, t);
        return sum + convertedAmount;
      }, 0);
    
    // 统计VAT总和 - 同样排除cancelled状态
    const totalVat = transactions
      .filter(tx => tx.type === 'expense' && tx.status.toLowerCase() !== 'cancelled')
      .reduce((sum, tx) => {
        const { convertedAmount } = detectAndConvertAmount(tx.amount, tx.description, tx);
        
        if (typeof tx.vatAmount === 'number') {
          return sum + tx.vatAmount;
        } else if (typeof tx.vatRate === 'number') {
          return sum + (convertedAmount * tx.vatRate / 100);
        }
        return sum;
      }, 0);
      
    // Net Profit = Total Revenue - Total Expenses
    const netProfit = totalRevenue - totalExpenses;
    
    // 🔧 Pending Amount：只统计真正pending的交易，排除cancelled，使用不含税金额
    const pendingAmount = transactions
      .filter(t => t.status.toLowerCase() === 'pending')
      .reduce((sum, t) => {
        const { convertedAmount } = detectAndConvertAmount(t.amount, t.description, t);
        
        // 🔑 修复：计算不含税的pending金额，与Total Revenue逻辑保持一致
        let pendingExclVat = convertedAmount;
        if (typeof t.vatAmount === 'number') {
          pendingExclVat = convertedAmount - t.vatAmount;
        } else if (typeof t.vatRate === 'number') {
          pendingExclVat = convertedAmount / (1 + t.vatRate / 100);
        }
        
        return sum + pendingExclVat;
      }, 0);

    console.log('📊 Statistics calculated:', {
      totalRevenue: `${formatAmount(totalRevenue)} (excluded cancelled/pending)`,
      totalExpenses: `${formatAmount(totalExpenses)} (excluded cancelled)`,
      netProfit: `${formatAmount(netProfit)}`,
      pendingAmount: `${formatAmount(pendingAmount)}`,
      totalVat: `${formatAmount(totalVat)}`
    });

    return {
      totalRevenue,
      totalExpenses,
      netProfit,
      pendingAmount,
      totalVat
    };
  };

  const stats = getStatistics();

  // 过滤数据
  const getFilteredTransactions = () => {
    return transactions.filter(transaction => {
      const matchesSearch = transaction.description.toLowerCase().includes(searchText.toLowerCase()) ||
                          transaction.category.toLowerCase().includes(searchText.toLowerCase()) ||
                          transaction.reference.toLowerCase().includes(searchText.toLowerCase());
      
      const matchesType = selectedType === '' || transaction.type === selectedType;
      const matchesStatus = selectedStatus === '' || transaction.status === selectedStatus;
      const matchesPaymentMethod = selectedPaymentMethod === '' || transaction.paymentMethod === selectedPaymentMethod;
      
      // 日期范围筛选
      let matchesDateRange = true;
      if (dateRange && dateRange[0] && dateRange[1]) {
        const transactionDate = new Date(transaction.date);
        const startDate = dateRange[0].toDate();
        const endDate = dateRange[1].toDate();
        matchesDateRange = transactionDate >= startDate && transactionDate <= endDate;
      }
      
      return matchesSearch && matchesType && matchesStatus && matchesPaymentMethod && matchesDateRange;
    });
  };

  const handleNewTransaction = () => {
    navigate('/finance/create');
  };

  const handleEdit = (record: Transaction) => {
    navigate(`/finance/edit/${record.id}`);
  };

  const handleDelete = (record: Transaction) => {
    Modal.confirm({
      title: 'Delete Transaction',
      content: 'Are you sure you want to delete this transaction?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk() {
        // 过滤掉要删除的交易记录
        const updatedTransactions = transactions.filter(t => t.id !== record.id);
        
        // 更新React状态
        setTransactions(updatedTransactions);
        
        // 同步更新localStorage - 关键修复
        try {
          const txKey = 'finance_transactions';
          localStorage.setItem(txKey, JSON.stringify(updatedTransactions));
          console.log('✅ Transaction deleted and localStorage updated:', record.id);
        } catch (error) {
          console.error('❌ Failed to update localStorage after deletion:', error);
        }
        
        message.success('Transaction deleted successfully');
      },
    });
  };

  const renderStatus = (status: string) => {
    const statusConfig = {
      completed: { color: 'green', text: 'Completed' },
      pending: { color: 'orange', text: 'Pending' },
      cancelled: { color: 'red', text: 'Cancelled' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color} style={{ fontSize: '12px' }}>{config.text}</Tag>;
  };

  const renderType = (type: string) => {
    return type === 'income' ? 
      <Tag color="green" style={{ fontSize: '12px' }}>Income</Tag> : 
      <Tag color="red" style={{ fontSize: '12px' }}>Expense</Tag>;
  };

  // 打开附件预览
  const handleAttachmentPreview = (attachments: any[], transactionRef: string) => {
    setPreviewAttachments(attachments || []);
    setPreviewTransactionRef(transactionRef);
    setAttachmentPreviewVisible(true);
  };

  // 处理单个文件预览
  const handleFilePreview = (file: any) => {
    console.log('🔍 handleFilePreview called with file:', file);
    console.log('📋 File properties:', {
      name: file.name,
      type: file.type,
      size: file.size,
      url: file.url,
      thumbUrl: file.thumbUrl,
      uid: file.uid
    });
    
    // 根据文件类型处理预览
    const fileType = file.type || '';
    console.log('🎯 Detected file type:', fileType);
    
    if (fileType.startsWith('image/')) {
      console.log('🖼️ Processing image file');
      // 图片文件：使用Image组件预览
      if (file.thumbUrl || file.url) {
        const imageUrl = file.thumbUrl || file.url;
        console.log('📸 Image URL:', imageUrl);
        Modal.info({
          title: `Preview: ${file.name}`,
          content: (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <img 
                src={imageUrl} 
                alt={file.name}
                style={{ 
                  maxWidth: '100%', 
                  maxHeight: '400px',
                  objectFit: 'contain'
                }}
              />
            </div>
          ),
          width: 600,
          icon: null
        });
      } else {
        console.log('⚠️ Image file has no thumbUrl or url - using file reader for preview');
        // 在开发环境中，如果没有URL，显示文件信息和提示
        Modal.info({
          title: `File Information: ${file.name}`,
          content: (
            <div style={{ padding: '20px' }}>
              <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                <div style={{ fontSize: '48px', marginBottom: '8px' }}>🖼️</div>
                <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                  {file.name}
                </div>
              </div>
              <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
                <div><strong>File Type:</strong> {file.type}</div>
                <div><strong>File Size:</strong> {(file.size / 1024).toFixed(1)} KB</div>
                {file.lastModified && (
                  <div><strong>Last Modified:</strong> {new Date(file.lastModified).toLocaleString()}</div>
                )}
              </div>
              <div style={{ 
                marginTop: '16px', 
                padding: '12px', 
                backgroundColor: '#e6f7ff', 
                borderRadius: '6px',
                border: '1px solid #91d5ff'
              }}>
                <div style={{ color: '#1890ff', fontSize: '14px' }}>
                  💡 <strong>Development Mode:</strong> This is a simulated file attachment. 
                  In production, the actual image would be displayed here.
                </div>
              </div>
            </div>
          ),
          width: 500,
          icon: null
        });
      }
    } else if (fileType === 'application/pdf') {
      // PDF文件：在新窗口打开
      if (file.url) {
        window.open(file.url, '_blank');
      } else {
        console.log('📄 PDF file without URL - showing info');
        Modal.info({
          title: `PDF File: ${file.name}`,
          content: (
            <div style={{ padding: '20px' }}>
              <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                <div style={{ fontSize: '48px', marginBottom: '8px' }}>📄</div>
                <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                  {file.name}
                </div>
              </div>
              <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
                <div><strong>File Type:</strong> {file.type}</div>
                <div><strong>File Size:</strong> {(file.size / 1024).toFixed(1)} KB</div>
                {file.lastModified && (
                  <div><strong>Last Modified:</strong> {new Date(file.lastModified).toLocaleString()}</div>
                )}
              </div>
              <div style={{ 
                marginTop: '16px', 
                padding: '12px', 
                backgroundColor: '#e6f7ff', 
                borderRadius: '6px',
                border: '1px solid #91d5ff'
              }}>
                <div style={{ color: '#1890ff', fontSize: '14px' }}>
                  💡 <strong>Development Mode:</strong> This is a simulated PDF attachment. 
                  In production, the PDF would open in a new window.
                </div>
              </div>
            </div>
          ),
          width: 500,
          icon: null
        });
      }
    } else if (fileType.startsWith('text/') || fileType.includes('document')) {
      // 文本或文档文件：在新窗口打开
      if (file.url) {
        window.open(file.url, '_blank');
      } else {
        console.log('📝 Document file without URL - showing info');
        Modal.info({
          title: `Document: ${file.name}`,
          content: (
            <div style={{ padding: '20px' }}>
              <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                <div style={{ fontSize: '48px', marginBottom: '8px' }}>📝</div>
                <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                  {file.name}
                </div>
              </div>
              <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
                <div><strong>File Type:</strong> {file.type}</div>
                <div><strong>File Size:</strong> {(file.size / 1024).toFixed(1)} KB</div>
                {file.lastModified && (
                  <div><strong>Last Modified:</strong> {new Date(file.lastModified).toLocaleString()}</div>
                )}
              </div>
              <div style={{ 
                marginTop: '16px', 
                padding: '12px', 
                backgroundColor: '#e6f7ff', 
                borderRadius: '6px',
                border: '1px solid #91d5ff'
              }}>
                <div style={{ color: '#1890ff', fontSize: '14px' }}>
                  💡 <strong>Development Mode:</strong> This is a simulated document attachment. 
                  In production, the document would open in a new window.
                </div>
              </div>
            </div>
          ),
          width: 500,
          icon: null
        });
      }
    } else {
      // 其他文件类型：显示文件信息
      console.log('📎 Other file type without URL - showing info');
      Modal.info({
        title: `File: ${file.name}`,
        content: (
          <div style={{ padding: '20px' }}>
            <div style={{ marginBottom: '16px', textAlign: 'center' }}>
              <div style={{ fontSize: '48px', marginBottom: '8px' }}>📎</div>
              <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                {file.name}
              </div>
            </div>
            <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '6px' }}>
              <div><strong>File Type:</strong> {fileType || 'Unknown'}</div>
              <div><strong>File Size:</strong> {(file.size / 1024).toFixed(1)} KB</div>
              {file.lastModified && (
                <div><strong>Last Modified:</strong> {new Date(file.lastModified).toLocaleString()}</div>
              )}
            </div>
            <div style={{ 
              marginTop: '16px', 
              padding: '12px', 
              backgroundColor: '#e6f7ff', 
              borderRadius: '6px',
              border: '1px solid #91d5ff'
            }}>
              <div style={{ color: '#1890ff', fontSize: '14px' }}>
                💡 <strong>Development Mode:</strong> This is a simulated file attachment. 
                In production, appropriate actions would be available based on the file type.
              </div>
            </div>
          </div>
        ),
        width: 500,
        icon: null
      });
    }
    
    // Debug: 确保函数执行完成
    console.log('✅ handleFilePreview completed');
  };

  const columns = [
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
      width: 130,
      sorter: (a: Transaction, b: Transaction) => a.reference.localeCompare(b.reference),
      render: (reference: string) => (
        <span style={{ fontSize: '12px' }}>
          {reference}
        </span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      sorter: (a: Transaction, b: Transaction) => a.type.localeCompare(b.type),
      render: renderType
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 150,
      sorter: (a: Transaction, b: Transaction) => a.category.localeCompare(b.category),
      render: (category: string) => (
        <span style={{ fontSize: '12px' }}>
          {category}
        </span>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      sorter: (a: Transaction, b: Transaction) => a.description.localeCompare(b.description),
      render: (description: string) => (
        <span style={{ fontSize: '12px' }}>
          {description}
        </span>
      )
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 130,
      align: 'right' as const,
      sorter: (a: Transaction, b: Transaction) => {
        const aConverted = detectAndConvertAmount(a.amount, a.description, a).convertedAmount;
        const bConverted = detectAndConvertAmount(b.amount, b.description, b).convertedAmount;
        return aConverted - bConverted;
      },
      render: (amount: number, record: Transaction) => {
        let displayAmount = amount;
        if (record.type === 'income') {
          if (typeof record.vatAmount === 'number') displayAmount = amount - record.vatAmount;
          else if (typeof record.vatRate === 'number') displayAmount = amount / (1 + record.vatRate / 100);
        }
        const { convertedAmount, originalCurrency } = detectAndConvertAmount(displayAmount, record.description, record);
        const showConversion = originalCurrency !== selectedCurrency;
        
        return (
          <div style={{ fontSize: '12px' }}>
            <span style={{ 
              color: record.type === 'income' ? '#52c41a' : '#ff4d4f',
              fontWeight: 'bold'
            }}>
              {record.type === 'income' ? '+' : '-'}{formatAmount(Number(convertedAmount.toFixed(1)))}
            </span>
            {record.type === 'income' && (
              <Tooltip title={`Gross: ${formatAmount(amount)} (含VAT)`}>
                <div style={{ fontSize: '10px', color: '#8c8c8c', fontStyle: 'italic' }}>
                  (excl. VAT)
                </div>
              </Tooltip>
            )}
            {showConversion && (
              <Tooltip title={`Original: ${originalCurrency} ${Number(displayAmount).toFixed(1)}`}>
                <div style={{ 
                  fontSize: '10px', 
                  color: '#8c8c8c',
                  fontStyle: 'italic'
                }}>
                  (~{originalCurrency} {Number(displayAmount).toFixed(1)})
                </div>
              </Tooltip>
            )}
          </div>
        );
      }
    },
    {
      title: 'Payment Method',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      width: 140,
      sorter: (a: Transaction, b: Transaction) => a.paymentMethod.localeCompare(b.paymentMethod),
      render: (method: string) => (
        <span style={{ fontSize: '12px' }}>
          {method === 'Bank Transfer' && <BankOutlined style={{ marginRight: 4, fontSize: '12px' }} />}
          {method === 'Credit Card' && <CreditCardOutlined style={{ marginRight: 4, fontSize: '12px' }} />}
          {method === 'Company Card' && <CreditCardOutlined style={{ marginRight: 4, fontSize: '12px' }} />}
          {method}
        </span>
      )
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      sorter: (a: Transaction, b: Transaction) => new Date(a.date).getTime() - new Date(b.date).getTime(),
      render: (date: string) => (
        <span style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleDateString()}
        </span>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 110,
      sorter: (a: Transaction, b: Transaction) => a.status.localeCompare(b.status),
      render: renderStatus
    },
    {
      title: 'Attachment',
      dataIndex: 'attachments',
      key: 'attachments',
      width: 90,
      align: 'center' as const,
      render: (attachments: any[] = [], record: Transaction) => {
        if (!attachments || !attachments.length) return null;
        
        return (
          <span 
            style={{ cursor: 'pointer' }} 
            onClick={() => handleAttachmentPreview(attachments, record.reference)}
          >
            <Tooltip title={`${attachments.length} attachment(s) - Click to preview`}>
              <PaperClipOutlined 
                style={{ 
                  color: '#FF7A00', 
                  fontSize: 16, 
                  verticalAlign: 'middle' 
                }} 
              />
            </Tooltip>
          </span>
        );
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: Transaction) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined style={{ fontSize: '12px' }} />} 
              onClick={() => handleEdit(record)}
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined style={{ fontSize: '12px' }} />} 
              onClick={() => handleDelete(record)}
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和右上角按钮 */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between', 
        marginBottom: '24px',
      }}>
        <div style={{ fontSize: '16px', fontWeight: 600, color: '#000' }}>
          Transaction Management
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={handleNewTransaction}
          size="large"
          style={{ 
            backgroundColor: '#FF7A00', 
            borderColor: '#FF7A00', 
            fontSize: '15px', 
            borderRadius: '6px',
            height: '40px',
            minWidth: '170px',
            fontWeight: 500
          }}
        >
          New Transaction
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={Number(stats.totalRevenue).toFixed(1)}
              prefix={<span style={{ fontSize: '0.7em' }}>{selectedCurrency === 'EUR' ? '€' : selectedCurrency}</span>}
              valueStyle={{ color: '#3f8600', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Expenses"
              value={Number(stats.totalExpenses).toFixed(1)}
              prefix={<span style={{ fontSize: '0.7em' }}>{selectedCurrency === 'EUR' ? '€' : selectedCurrency}</span>}
              valueStyle={{ color: '#cf1322', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Net Profit"
              value={Number(stats.netProfit).toFixed(1)}
              prefix={<span style={{ fontSize: '0.7em' }}>{selectedCurrency === 'EUR' ? '€' : selectedCurrency}</span>}
              valueStyle={{ color: stats.netProfit >= 0 ? '#3f8600' : '#cf1322', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Pending Amount"
              value={Number(stats.pendingAmount).toFixed(1)}
              prefix={<span style={{ fontSize: '0.7em' }}>{selectedCurrency === 'EUR' ? '€' : selectedCurrency}</span>}
              valueStyle={{ color: '#fa8c16', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Space wrap style={{ width: '100%', justifyContent: 'flex-start' }}>
          <Input
            placeholder="Search transactions..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200, fontSize: '12px' }}
            allowClear
          />

          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ fontSize: '12px' }}
            placeholder={['Start Date', 'End Date']}
          />

          <Select
            style={{ width: 100, fontSize: '12px' }}
            value={selectedType}
            onChange={setSelectedType}
            placeholder="Type"
          >
            <Option value="">All Types</Option>
            <Option value="income">Income</Option>
            <Option value="expense">Expense</Option>
          </Select>

          <Select
            style={{ width: 100, fontSize: '12px' }}
            value={selectedStatus}
            onChange={setSelectedStatus}
            placeholder="Status"
          >
            <Option value="">All Status</Option>
            <Option value="completed">Completed</Option>
            <Option value="pending">Pending</Option>
            <Option value="cancelled">Cancelled</Option>
          </Select>

          <Select
            style={{ width: 130, fontSize: '12px' }}
            value={selectedPaymentMethod}
            onChange={setSelectedPaymentMethod}
            placeholder="Payment Method"
          >
            <Option value="">All Methods</Option>
            <Option value="Bank Transfer">Bank Transfer</Option>
            <Option value="Credit Card">Credit Card</Option>
            <Option value="Company Card">Company Card</Option>
            <Option value="Cash">Cash</Option>
            <Option value="Check">Check</Option>
          </Select>

          <Button onClick={() => {
            setSearchText('');
            setSelectedType('');
            setSelectedStatus('');
            setSelectedPaymentMethod('');
            setDateRange(null);
          }} style={{ fontSize: '12px' }}>
            Clear Filters
          </Button>

          <Button onClick={() => {
            clearTableColumnWidths('.finance-management .ant-table');
            window.location.reload(); // 重新加载页面以应用默认列宽
          }} style={{ fontSize: '12px' }}>
            Reset Column Widths
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card>
        <div className="finance-management">
          <Table
            columns={columns}
            dataSource={getFilteredTransactions()}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `${range[0]}-${range[1]} of ${total} transactions`,
              defaultPageSize: 20,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
            scroll={{ x: 1200 }}
            size="middle"
            bordered
          />
        </div>
      </Card>

      {/* 附件预览Modal */}
      <Modal
        title={`Attachments - ${previewTransactionRef}`}
        open={attachmentPreviewVisible}
        onCancel={() => setAttachmentPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setAttachmentPreviewVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {previewAttachments.length > 0 ? (
            <div style={{ display: 'grid', gap: '16px' }}>
              {previewAttachments.map((file, index) => (
                <div 
                  key={file.uid || file.name || index}
                  style={{
                    border: '1px solid #f0f0f0',
                    borderRadius: '8px',
                    padding: '12px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    <PaperClipOutlined style={{ color: '#FF7A00', marginRight: '8px', fontSize: '16px' }} />
                    <div style={{ flex: 1 }}>
                      <div 
                        style={{ 
                          fontWeight: 'bold', 
                          fontSize: '14px', 
                          cursor: 'pointer',
                          color: '#1890ff',
                          textDecoration: 'underline'
                        }}
                        onClick={() => handleFilePreview(file)}
                      >
                        {file.name}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {file.size ? `${(file.size / 1024).toFixed(1)} KB` : 'Unknown size'} • 
                        {file.type || 'Unknown type'}
                        {file.lastModified && (
                          <span> • {new Date(file.lastModified).toLocaleString()}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* 如果是图片，显示预览 */}
                  {file.type && file.type.startsWith('image/') && file.thumbUrl && (
                    <div style={{ textAlign: 'center', marginTop: '8px' }}>
                      <Image
                        src={file.thumbUrl}
                        alt={file.name}
                        style={{ maxWidth: '100%', maxHeight: '200px' }}
                        preview={{
                          mask: <div><EyeOutlined /> Preview</div>
                        }}
                      />
                    </div>
                  )}
                  
                  {/* 文件操作按钮 */}
                  <div style={{ marginTop: '8px', textAlign: 'right' }}>
                    {file.url && (
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => window.open(file.url, '_blank')}
                      >
                        <EyeOutlined /> View
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
              No attachments found
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default FinanceManagement; 