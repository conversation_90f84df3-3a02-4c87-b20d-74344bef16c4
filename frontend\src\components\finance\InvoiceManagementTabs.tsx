import React, { useState } from 'react';
import { Tabs } from 'antd';

const { TabPane } = Tabs;

interface InvoiceManagementTabsProps {
  // 可以根据需要添加属性
}

const InvoiceManagementTabs: React.FC<InvoiceManagementTabsProps> = () => {
  const [activeTab, setActiveTab] = useState('sales');

  return (
    <div className="invoice-management-tabs">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-semibold">Invoice Management</h2>
      </div>

      {/* Invoice Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {[
          { label: 'Total Invoiced', value: '€1,200,000', color: 'text-green-700' },
          { label: 'Paid', value: '€500,000', color: 'text-green-600' },
          { label: 'Pending', value: '€300,000', color: 'text-yellow-500' },
          { label: 'Overdue', value: '€400,000', color: 'text-red-600' },
          { label: 'Payment Ratio', value: '42%', color: 'text-purple-600', progress: true, progressValue: 42 }
        ].map((item, idx) => (
          <div key={idx} className="bg-white shadow rounded-lg p-4">
            <div className="text-sm text-gray-500">{item.label}</div>
            <div className={`text-xl font-bold ${item.color}`}>{item.value}</div>
            {(item.label === 'Total Invoiced' || item.progress) && (
              <div className="mt-2 w-full bg-gray-200 h-2 rounded-full">
                <div
                  className={`h-2 rounded-full ${item.label === 'Payment Ratio' ? 'bg-purple-500' : 'bg-green-500'}`}
                  style={{ width: item.progressValue ? `${item.progressValue}%` : '30%' }}
                ></div>
              </div>
            )}
          </div>
        ))}
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
        className="invoice-main-tabs"
      >
        {/* Sales Tab */}
        <TabPane tab="Sales" key="sales">
          <div className="flex items-center justify-end mb-3">
            <div className="flex space-x-2">
              <button className="bg-teal-600 text-white px-3 py-1.5 rounded text-sm hover:bg-teal-700 flex items-center">
                <span className="mr-1">📊</span> Export Excel
              </button>
              <button className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700 flex items-center">
                <span className="mr-1">+</span> Create Invoice
              </button>
            </div>
          </div>

          <div className="mb-3">
            <Tabs defaultActiveKey="all" type="card" className="invoice-tabs">
              <TabPane tab={<span><span className="mr-1">📁</span> All</span>} key="all">
                <div className="overflow-x-auto bg-white shadow rounded-lg">
                  <table className="min-w-full text-sm text-left">
                    <thead className="bg-gray-100 text-gray-700">
                      <tr>
                        <th className="p-3">Invoice #</th>
                        <th className="p-3">Revenue Item</th>
                        <th className="p-3">Type</th>
                        <th className="p-3">Amount(Excl. VAT)</th>
                        <th className="p-3">VAT (%)</th>
                        <th className="p-3">Total Amount</th>
                        <th className="p-3">Invoice Date</th>
                        <th className="p-3">Due Date</th>
                        <th className="p-3">Status</th>
                        <th className="p-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-001</td>
                        <td className="p-3">Setup Fee</td>
                        <td className="p-3"><span className="px-2 py-1 text-purple-700 bg-purple-100 rounded">NRC</span></td>
                        <td className="p-3">€2,000</td>
                        <td className="p-3">€420 (21%)</td>
                        <td className="p-3">€2,420</td>
                        <td className="p-3">2023-09-15</td>
                        <td className="p-3">2023-10-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-002</td>
                        <td className="p-3">SaaS License (Jan)</td>
                        <td className="p-3"><span className="px-2 py-1 text-blue-700 bg-blue-100 rounded">MRC</span></td>
                        <td className="p-3">€500</td>
                        <td className="p-3">€105 (21%)</td>
                        <td className="p-3">€605</td>
                        <td className="p-3">2023-01-01</td>
                        <td className="p-3">2023-01-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-003</td>
                        <td className="p-3">Migration Completion</td>
                        <td className="p-3"><span className="px-2 py-1 text-purple-700 bg-purple-100 rounded">NRC</span></td>
                        <td className="p-3">€450,000</td>
                        <td className="p-3">€94,500 (21%)</td>
                        <td className="p-3">€544,500</td>
                        <td className="p-3">2024-03-15</td>
                        <td className="p-3">2024-04-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-yellow-700 bg-yellow-100 rounded">Upcoming</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-004</td>
                        <td className="p-3">SaaS License (Feb)</td>
                        <td className="p-3"><span className="px-2 py-1 text-blue-700 bg-blue-100 rounded">MRC</span></td>
                        <td className="p-3">€500</td>
                        <td className="p-3">€105 (21%)</td>
                        <td className="p-3">€605</td>
                        <td className="p-3">2023-02-01</td>
                        <td className="p-3">2023-02-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-red-700 bg-red-100 rounded">Overdue</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TabPane>

              <TabPane tab={<span><span className="mr-1">🟣</span> NRC</span>} key="nrc">
                <div className="overflow-x-auto bg-white shadow rounded-lg">
                  <table className="min-w-full text-sm text-left">
                    <thead className="bg-gray-100 text-gray-700">
                      <tr>
                        <th className="p-3">Invoice #</th>
                        <th className="p-3">Revenue Item</th>
                        <th className="p-3">Type</th>
                        <th className="p-3">Amount(Excl. VAT)</th>
                        <th className="p-3">VAT (%)</th>
                        <th className="p-3">Total Amount</th>
                        <th className="p-3">Invoice Date</th>
                        <th className="p-3">Due Date</th>
                        <th className="p-3">Status</th>
                        <th className="p-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-001</td>
                        <td className="p-3">Setup Fee</td>
                        <td className="p-3"><span className="px-2 py-1 text-purple-700 bg-purple-100 rounded">NRC</span></td>
                        <td className="p-3">€2,000</td>
                        <td className="p-3">€420 (21%)</td>
                        <td className="p-3">€2,420</td>
                        <td className="p-3">2023-09-15</td>
                        <td className="p-3">2023-10-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-003</td>
                        <td className="p-3">Migration Completion</td>
                        <td className="p-3"><span className="px-2 py-1 text-purple-700 bg-purple-100 rounded">NRC</span></td>
                        <td className="p-3">€450,000</td>
                        <td className="p-3">€94,500 (21%)</td>
                        <td className="p-3">€544,500</td>
                        <td className="p-3">2024-03-15</td>
                        <td className="p-3">2024-04-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-yellow-700 bg-yellow-100 rounded">Upcoming</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TabPane>

              <TabPane tab={<span><span className="mr-1">🔵</span> MRC</span>} key="mrc">
                <div className="overflow-x-auto bg-white shadow rounded-lg">
                  <table className="min-w-full text-sm text-left">
                    <thead className="bg-gray-100 text-gray-700">
                      <tr>
                        <th className="p-3">Invoice #</th>
                        <th className="p-3">Revenue Item</th>
                        <th className="p-3">Type</th>
                        <th className="p-3">Amount(Excl. VAT)</th>
                        <th className="p-3">VAT (%)</th>
                        <th className="p-3">Total Amount</th>
                        <th className="p-3">Invoice Date</th>
                        <th className="p-3">Due Date</th>
                        <th className="p-3">Status</th>
                        <th className="p-3">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-002</td>
                        <td className="p-3">SaaS License (Jan)</td>
                        <td className="p-3"><span className="px-2 py-1 text-blue-700 bg-blue-100 rounded">MRC</span></td>
                        <td className="p-3">€500</td>
                        <td className="p-3">€105 (21%)</td>
                        <td className="p-3">€605</td>
                        <td className="p-3">2023-01-01</td>
                        <td className="p-3">2023-01-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-3">INV-2023-004</td>
                        <td className="p-3">SaaS License (Feb)</td>
                        <td className="p-3"><span className="px-2 py-1 text-blue-700 bg-blue-100 rounded">MRC</span></td>
                        <td className="p-3">€500</td>
                        <td className="p-3">€105 (21%)</td>
                        <td className="p-3">€605</td>
                        <td className="p-3">2023-02-01</td>
                        <td className="p-3">2023-02-15</td>
                        <td className="p-3"><span className="px-2 py-1 text-red-700 bg-red-100 rounded">Overdue</span></td>
                        <td className="p-3 space-x-2">
                          <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                          <button className="text-purple-600 bg-transparent border-none cursor-pointer">Send</button>
                          <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </TabPane>
            </Tabs>
          </div>
        </TabPane>

        {/* Payables Tab */}
        <TabPane tab="Payables" key="purchase">
          <div className="flex items-center justify-end mb-3">
            <div className="flex space-x-2">
              <button className="bg-teal-600 text-white px-3 py-1.5 rounded text-sm hover:bg-teal-700 flex items-center">
                <span className="mr-1">📊</span> Export Excel
              </button>
              <button className="bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700 flex items-center">
                <span className="mr-1">+</span> Create Invoice
              </button>
            </div>
          </div>

          <div className="mb-3">
            <div className="overflow-x-auto bg-white shadow rounded-lg">
              <table className="min-w-full text-sm text-left">
                <thead className="bg-gray-100 text-gray-700">
                  <tr>
                    <th className="p-3">Invoice #</th>
                    <th className="p-3">Vendor</th>
                    <th className="p-3">Type</th>
                    <th className="p-3">Amount(Excl. VAT)</th>
                    <th className="p-3">VAT (%)</th>
                    <th className="p-3">Total Amount</th>
                    <th className="p-3">Invoice Date</th>
                    <th className="p-3">Due Date</th>
                    <th className="p-3">Status</th>
                    <th className="p-3">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-3">PUR-2023-001</td>
                    <td className="p-3">Hardware Supplier Co.</td>
                    <td className="p-3"><span className="px-2 py-1 text-purple-700 bg-purple-100 rounded">Equipment</span></td>
                    <td className="p-3">€5,000</td>
                    <td className="p-3">€1,050 (21%)</td>
                    <td className="p-3">€6,050</td>
                    <td className="p-3">2023-08-10</td>
                    <td className="p-3">2023-09-10</td>
                    <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                    <td className="p-3 space-x-2">
                      <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-3">PUR-2023-002</td>
                    <td className="p-3">Office Supplies Ltd.</td>
                    <td className="p-3"><span className="px-2 py-1 text-orange-700 bg-orange-100 rounded">Supplies</span></td>
                    <td className="p-3">€800</td>
                    <td className="p-3">€168 (21%)</td>
                    <td className="p-3">€968</td>
                    <td className="p-3">2023-09-05</td>
                    <td className="p-3">2023-10-05</td>
                    <td className="p-3"><span className="px-2 py-1 text-green-700 bg-green-100 rounded">Paid</span></td>
                    <td className="p-3 space-x-2">
                      <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-3">PUR-2023-003</td>
                    <td className="p-3">Software Solutions Inc.</td>
                    <td className="p-3"><span className="px-2 py-1 text-blue-700 bg-blue-100 rounded">Software</span></td>
                    <td className="p-3">€12,000</td>
                    <td className="p-3">€2,520 (21%)</td>
                    <td className="p-3">€14,520</td>
                    <td className="p-3">2023-10-15</td>
                    <td className="p-3">2023-11-15</td>
                    <td className="p-3"><span className="px-2 py-1 text-yellow-700 bg-yellow-100 rounded">Pending</span></td>
                    <td className="p-3 space-x-2">
                      <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-3">PUR-2023-004</td>
                    <td className="p-3">Consulting Group XYZ</td>
                    <td className="p-3"><span className="px-2 py-1 text-pink-700 bg-pink-100 rounded">Services</span></td>
                    <td className="p-3">€3,500</td>
                    <td className="p-3">€735 (21%)</td>
                    <td className="p-3">€4,235</td>
                    <td className="p-3">2023-09-20</td>
                    <td className="p-3">2023-10-20</td>
                    <td className="p-3"><span className="px-2 py-1 text-red-700 bg-red-100 rounded">Overdue</span></td>
                    <td className="p-3 space-x-2">
                      <button className="text-blue-600 bg-transparent border-none cursor-pointer">View</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Edit</button>
                      <button className="text-gray-500 bg-transparent border-none cursor-pointer">Status</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default InvoiceManagementTabs;
