"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const typeorm_1 = require("typeorm");
const Project_1 = require("../entities/Project");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authMiddleware);
router.get('/', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const projects = await projectRepository.find();
        res.json(projects);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching projects', error });
    }
});
router.get('/:id', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        res.json(project);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching project', error });
    }
});
router.post('/', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = projectRepository.create(req.body);
        const result = await projectRepository.save(project);
        res.status(201).json(result);
    }
    catch (error) {
        res.status(500).json({ message: 'Error creating project', error });
    }
});
router.put('/:id', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        projectRepository.merge(project, req.body);
        const result = await projectRepository.save(project);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ message: 'Error updating project', error });
    }
});
router.delete('/:id', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        await projectRepository.remove(project);
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: 'Error deleting project', error });
    }
});
router.get('/:id/team/members', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        const mockMembers = [
            {
                id: '1',
                userId: 'user1',
                projectId: req.params.id,
                email: '<EMAIL>',
                name: 'John Doe',
                avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=random',
                role: 'owner',
                status: 'active',
                invitedBy: 'system',
                invitedAt: new Date().toISOString(),
                joinedAt: new Date().toISOString(),
                lastActiveAt: new Date().toISOString(),
                permissions: {
                    canViewProject: true,
                    canEditProject: true,
                    canDeleteProject: true,
                    canManageTeam: true,
                    canManageFinance: true,
                    canExportData: true,
                    canViewReports: true,
                }
            },
            {
                id: '2',
                userId: 'user2',
                projectId: req.params.id,
                email: '<EMAIL>',
                name: 'Jane Smith',
                avatar: 'https://ui-avatars.com/api/?name=Jane+Smith&background=random',
                role: 'admin',
                status: 'active',
                invitedBy: 'user1',
                invitedAt: new Date().toISOString(),
                joinedAt: new Date().toISOString(),
                lastActiveAt: new Date().toISOString(),
                permissions: {
                    canViewProject: true,
                    canEditProject: true,
                    canDeleteProject: false,
                    canManageTeam: true,
                    canManageFinance: true,
                    canExportData: true,
                    canViewReports: true,
                }
            }
        ];
        res.json(mockMembers);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching team members', error });
    }
});
router.get('/:id/team/stats', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        const mockStats = {
            totalMembers: 2,
            activeMembers: 2,
            pendingInvitations: 0,
            roleDistribution: {
                owner: 1,
                admin: 1,
                member: 0,
                viewer: 0
            }
        };
        res.json(mockStats);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching team stats', error });
    }
});
router.get('/:id/team/my-role', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        res.json({ role: 'owner' });
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching user role', error });
    }
});
router.get('/:id/team/invitations', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        res.json([]);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching invitations', error });
    }
});
router.post('/:id/team/invite', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        const { email, role, message } = req.body;
        const mockInvitation = {
            id: Date.now().toString(),
            projectId: req.params.id,
            email,
            role,
            invitedBy: 'current-user',
            invitedAt: new Date().toISOString(),
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            token: 'mock-token',
            status: 'pending'
        };
        res.status(201).json(mockInvitation);
    }
    catch (error) {
        res.status(500).json({ message: 'Error sending invitation', error });
    }
});
router.delete('/:id/team/members/:memberId', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: 'Error removing member', error });
    }
});
router.put('/:id/team/members/:memberId/role', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        const { role } = req.body;
        const mockUpdatedMember = {
            id: req.params.memberId,
            userId: 'user1',
            projectId: req.params.id,
            email: '<EMAIL>',
            name: 'Example User',
            role,
            status: 'active',
            permissions: {
                canViewProject: true,
                canEditProject: role !== 'viewer',
                canDeleteProject: role === 'owner',
                canManageTeam: ['owner', 'admin'].includes(role),
                canManageFinance: ['owner', 'admin'].includes(role),
                canExportData: role !== 'viewer',
                canViewReports: true,
            }
        };
        res.json(mockUpdatedMember);
    }
    catch (error) {
        res.status(500).json({ message: 'Error updating member role', error });
    }
});
router.put('/:id/team/members/:memberId/permissions', async (req, res) => {
    try {
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(req.params.id);
        if (!project) {
            return res.status(404).json({ message: 'Project not found' });
        }
        const { permissions } = req.body;
        const mockUpdatedMember = {
            id: req.params.memberId,
            userId: 'user1',
            projectId: req.params.id,
            email: '<EMAIL>',
            name: 'Example User',
            role: 'member',
            status: 'active',
            permissions
        };
        res.json(mockUpdatedMember);
    }
    catch (error) {
        res.status(500).json({ message: 'Error updating member permissions', error });
    }
});
exports.default = router;
//# sourceMappingURL=project.routes.js.map