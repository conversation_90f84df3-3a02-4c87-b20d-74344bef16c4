import { Router } from 'express';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

// 使用认证中间件保护所有路由
router.use(authMiddleware);

// 获取用户通知列表
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, unreadOnly = false } = req.query;
    
    // 模拟通知数据
    const mockNotifications = [
      {
        id: '1',
        title: 'New team member joined',
        message: '<PERSON> has joined the "Web Development" project team.',
        type: 'team',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
        projectId: 'proj-1',
        projectName: 'Web Development',
        actionUrl: '/projects/proj-1/team',
        priority: 'medium'
      },
      {
        id: '2',
        title: 'Project milestone completed',
        message: 'Phase 1 of the "Mobile App" project has been completed successfully.',
        type: 'success',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        projectId: 'proj-2',
        projectName: 'Mobile App',
        actionUrl: '/projects/proj-2',
        priority: 'high'
      },
      {
        id: '3',
        title: 'Document requires review',
        message: 'The contract document for "Enterprise Solution" needs your review.',
        type: 'warning',
        isRead: false,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
        projectId: 'proj-3',
        projectName: 'Enterprise Solution',
        actionUrl: '/projects/proj-3/documents',
        priority: 'medium'
      },
      {
        id: '4',
        title: 'System maintenance scheduled',
        message: 'System maintenance is scheduled for tonight from 2:00 AM to 4:00 AM.',
        type: 'system',
        isRead: true,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString(),
        priority: 'low'
      },
      {
        id: '5',
        title: 'You were mentioned in a comment',
        message: '@admin Please review the latest changes in the design section.',
        type: 'mention',
        isRead: true,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        projectId: 'proj-1',
        projectName: 'Web Development',
        actionUrl: '/projects/proj-1/comments',
        priority: 'medium'
      }
    ];

    // 过滤未读通知
    let filteredNotifications = unreadOnly === 'true' 
      ? mockNotifications.filter(n => !n.isRead)
      : mockNotifications;

    // 分页
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex);

    // 统计信息
    const unreadCount = mockNotifications.filter(n => !n.isRead).length;

    res.json({
      notifications: paginatedNotifications,
      stats: {
        unreadCount,
        totalCount: mockNotifications.length,
        lastReadAt: new Date().toISOString()
      },
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: filteredNotifications.length,
        hasNext: endIndex < filteredNotifications.length
      }
    });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching notifications', error });
  }
});

// 获取通知统计信息
router.get('/stats', async (_req, res) => {
  try {
    // 模拟统计数据
    const mockStats = {
      unreadCount: 3,
      totalCount: 5,
      lastReadAt: new Date().toISOString(),
      categories: {
        team: 1,
        success: 1,
        warning: 1,
        system: 1,
        mention: 1,
        error: 0,
        info: 0
      }
    };

    res.json(mockStats);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching notification stats', error });
  }
});

// 标记通知为已读
router.put('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟标记为已读
    console.log(`Marking notification ${id} as read for user`);
    
    res.json({ 
      message: 'Notification marked as read',
      notificationId: id,
      readAt: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ message: 'Error marking notification as read', error });
  }
});

// 标记所有通知为已读
router.put('/read-all', async (_req, res) => {
  try {
    // 模拟标记所有为已读
    console.log('Marking all notifications as read for user');
    
    res.json({ 
      message: 'All notifications marked as read',
      readAt: new Date().toISOString(),
      count: 3
    });
  } catch (error) {
    res.status(500).json({ message: 'Error marking all notifications as read', error });
  }
});

// 删除通知
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 模拟删除通知
    console.log(`Deleting notification ${id} for user`);
    
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Error deleting notification', error });
  }
});

// 创建新通知（系统内部使用）
router.post('/', async (req, res) => {
  try {
    const { title, message, type, projectId, actionUrl, priority = 'medium' } = req.body;
    
    // 模拟创建通知
    const newNotification = {
      id: Date.now().toString(),
      title,
      message,
      type,
      isRead: false,
      createdAt: new Date().toISOString(),
      projectId,
      actionUrl,
      priority
    };
    
    console.log('Creating new notification:', newNotification);
    
    res.status(201).json(newNotification);
  } catch (error) {
    res.status(500).json({ message: 'Error creating notification', error });
  }
});

export default router; 