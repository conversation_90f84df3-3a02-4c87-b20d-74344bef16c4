/* 表格行间距优化 - Lead to Cash, Client, Finance 管理页面 */

/* 防止表头文字换行 */
.ant-table-thead > tr > th {
  white-space: nowrap !important;
}

.no-wrap-header {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 通用表格行间距调整 - 覆盖modern-table.css中的16px padding */
.ant-table-thead > tr > th {
  padding: 12px !important;
  height: auto !important;
  line-height: 1.2 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.ant-table-tbody > tr > td {
  padding: 10px 12px !important;
  height: auto !important;
  line-height: 1.4 !important;
  font-size: 12px !important;
}

/* 项目列表容器特定调整 - 优先级更高，覆盖modern-table.css */
.project-list-container .ant-table-thead > tr > th {
  padding: 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  height: auto !important;
  line-height: 1.2 !important;
  background: #fafafa !important;
  color: #000 !important;
}

.project-list-container .ant-table-tbody > tr > td {
  padding: 10px 12px !important;
  font-size: 12px !important;
  height: auto !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
  color: #000 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

/* 客户列表页面特定调整 */
.client-list-container .ant-table-thead > tr > th {
  padding: 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.client-list-container .ant-table-tbody > tr > td {
  padding: 10px 12px !important;
  font-size: 12px !important;
  height: auto !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
}

/* 财务管理页面特定调整 */
.finance-management-container .ant-table-thead > tr > th {
  padding: 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  height: auto !important;
  line-height: 1.2 !important;
}

.finance-management-container .ant-table-tbody > tr > td {
  padding: 10px 12px !important;
  font-size: 12px !important;
  height: auto !important;
  line-height: 1.4 !important;
  vertical-align: middle !important;
}

/* 联系信息区域的特殊间距调整 */
.ant-table-tbody > tr > td .ant-space {
  gap: 4px !important;
}

.ant-table-tbody > tr > td .ant-space-item {
  margin-bottom: 2px !important;
}

/* 确保按钮和图标在紧凑布局中正常显示 */
.ant-table-tbody > tr > td .ant-btn {
  height: 24px !important;
  padding: 0 6px !important;
  font-size: 11px !important;
  line-height: 22px !important;
}

.ant-table-tbody > tr > td .anticon {
  font-size: 12px !important;
  line-height: 1 !important;
}

/* 标签组件在紧凑布局中的调整 */
.ant-table-tbody > tr > td .ant-tag {
  padding: 2px 6px !important;
  font-size: 11px !important;
  line-height: 1.2 !important;
  margin: 1px 2px !important;
}

/* 表格操作列的特殊调整 */
.ant-table-tbody > tr > td:last-child .ant-space {
  gap: 2px !important;
}

/* 确保表格在紧凑模式下仍然易读 */
.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5 !important;
}

/* 针对特定表格的微调 */
.ant-table-size-middle .ant-table-thead > tr > th {
  padding: 12px !important;
}

.ant-table-size-middle .ant-table-tbody > tr > td {
  padding: 10px 12px !important;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .ant-table-thead > tr > th {
    padding: 10px 8px !important;
    font-size: 11px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px !important;
    font-size: 11px !important;
  }
}

@media (max-width: 768px) {
  .ant-table-thead > tr > th {
    padding: 8px 6px !important;
    font-size: 10px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 6px !important;
    font-size: 10px !important;
  }
}

/* 专门针对客户列表的空白行修复 */
.client-list-container .ant-table-thead > tr:nth-child(n+2) {
  display: none !important;
  height: 0 !important;
  min-height: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

.client-list-container .ant-table-filter-row,
.client-list-container .ant-table thead tr:not(:first-child),
.client-list-container .ant-table-thead tr:not(:first-child) {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  min-height: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
}

/* 强制确保客户列表表头和数据之间无间隙 */
.client-list-container .ant-table-tbody {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.client-list-container .ant-table-tbody > tr:first-child {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.client-list-container .ant-table-tbody > tr:first-child > td {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 10px !important;
}

/* 彻底隐藏Ant Design Table的测量行 */
.ant-table-measure-row,
tr.ant-table-measure-row,
.ant-table-tbody > tr[aria-hidden="true"],
.ant-table-tbody > tr:first-child[aria-hidden="true"] {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  min-height: 0 !important;
  max-height: 0 !important;
  line-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  position: absolute !important;
  left: -9999px !important;
  overflow: hidden !important;
}

.ant-table-measure-row > td,
tr.ant-table-measure-row > td,
.ant-table-tbody > tr[aria-hidden="true"] > td {
  display: none !important;
  visibility: hidden !important;
  height: 0 !important;
  min-height: 0 !important;
  max-height: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  position: absolute !important;
  left: -9999px !important;
}

/* 统一所有管理页面表格的优秀网格线样式（基于Vendor页面样式） */
.client-list-container .ant-table,
.project-list-container .ant-table,
.finance-management .ant-table,
.vendor-list-container .ant-table {
  border: 1px solid #f0f0f0 !important;
  border-radius: 6px !important;
}

.client-list-container .ant-table-container,
.project-list-container .ant-table-container,
.finance-management .ant-table-container,
.vendor-list-container .ant-table-container {
  border: none !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

/* 表头样式 - 统一网格线 */
.client-list-container .ant-table-thead > tr > th,
.project-list-container .ant-table-thead > tr > th,
.finance-management .ant-table-thead > tr > th,
.vendor-list-container .ant-table-thead > tr > th {
  border-bottom: 1px solid #f0f0f0 !important;
  border-right: 1px solid #f0f0f0 !important;
  border-left: none !important;
  background-color: #fafafa !important;
}

/* 最后一列表头不需要右边框 */
.client-list-container .ant-table-thead > tr > th:last-child,
.project-list-container .ant-table-thead > tr > th:last-child,
.finance-management .ant-table-thead > tr > th:last-child,
.vendor-list-container .ant-table-thead > tr > th:last-child {
  border-right: none !important;
}

/* 表体单元格样式 - 统一网格线 */
.client-list-container .ant-table-tbody > tr > td,
.project-list-container .ant-table-tbody > tr > td,
.finance-management .ant-table-tbody > tr > td,
.vendor-list-container .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f5f5f5 !important;
  border-right: 1px solid #f5f5f5 !important;
  border-left: none !important;
}

/* 最后一列表体不需要右边框 */
.client-list-container .ant-table-tbody > tr > td:last-child,
.project-list-container .ant-table-tbody > tr > td:last-child,
.finance-management .ant-table-tbody > tr > td:last-child,
.vendor-list-container .ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* 最后一行不需要下边框 */
.client-list-container .ant-table-tbody > tr:last-child > td,
.project-list-container .ant-table-tbody > tr:last-child > td,
.finance-management .ant-table-tbody > tr:last-child > td,
.vendor-list-container .ant-table-tbody > tr:last-child > td {
  border-bottom: none !important;
}

/* 确保Finance页面表格样式与Client页面完全一致 */
.finance-management .ant-table-tbody {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.finance-management .ant-table-tbody > tr:first-child {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.finance-management .ant-table-tbody > tr:first-child > td {
  border-top: none !important;
  margin-top: 0 !important;
  padding-top: 10px !important;
}

/* 修复排序按钮与表头文字重叠问题 - 包含Vendor页面 */
.client-list-container .ant-table-column-sorter,
.project-list-container .ant-table-column-sorter,
.finance-management .ant-table-column-sorter,
.vendor-list-container .ant-table-column-sorter {
  transform: scale(0.7) !important;
  transform-origin: center right !important;
  margin-left: 8px !important;
}

.client-list-container .ant-table-column-sorter-up,
.client-list-container .ant-table-column-sorter-down,
.project-list-container .ant-table-column-sorter-up,
.project-list-container .ant-table-column-sorter-down,
.finance-management .ant-table-column-sorter-up,
.finance-management .ant-table-column-sorter-down,
.vendor-list-container .ant-table-column-sorter-up,
.vendor-list-container .ant-table-column-sorter-down {
  font-size: 9px !important;
}

.client-list-container .ant-table-column-sorters,
.project-list-container .ant-table-column-sorters,
.finance-management .ant-table-column-sorters,
.vendor-list-container .ant-table-column-sorters {
  padding-right: 20px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* 确保表头文字有足够空间 - 包含Vendor页面 */
.client-list-container .ant-table-column-title,
.project-list-container .ant-table-column-title,
.finance-management .ant-table-column-title,
.vendor-list-container .ant-table-column-title {
  flex: 1 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  margin-right: 8px !important;
}

/* 统一四个管理页面的表格样式 - 基于Finance页面的样式标准 */
.client-list-container .ant-table,
.project-list-container .ant-table,
.finance-management .ant-table,
.vendor-list-container .ant-table {
  font-size: 12px !important;
}

/* 统一表头样式 */
.client-list-container .ant-table-thead > tr > th,
.project-list-container .ant-table-thead > tr > th,
.finance-management .ant-table-thead > tr > th,
.vendor-list-container .ant-table-thead > tr > th {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #595959 !important;
  padding: 12px 16px !important;
}

/* 统一表格数据行样式 */
.client-list-container .ant-table-tbody > tr > td,
.project-list-container .ant-table-tbody > tr > td,
.finance-management .ant-table-tbody > tr > td,
.vendor-list-container .ant-table-tbody > tr > td {
  font-size: 12px !important;
  padding: 12px 16px !important;
  color: #262626 !important;
}

/* 统一悬停效果 */
.client-list-container .ant-table-tbody > tr:hover > td,
.project-list-container .ant-table-tbody > tr:hover > td,
.finance-management .ant-table-tbody > tr:hover > td,
.vendor-list-container .ant-table-tbody > tr:hover > td {
  background-color: #fafafa !important;
}

/* 统一分页器样式 */
.client-list-container .ant-pagination,
.project-list-container .ant-pagination,
.finance-management .ant-pagination,
.vendor-list-container .ant-pagination {
  font-size: 12px !important;
}

/* 统一Tag标签样式 */
.client-list-container .ant-tag,
.project-list-container .ant-tag,
.finance-management .ant-tag,
.vendor-list-container .ant-tag {
  font-size: 12px !important;
  padding: 2px 8px !important;
  border-radius: 4px !important;
}

/* 统一按钮样式 */
.client-list-container .ant-btn,
.project-list-container .ant-btn,
.finance-management .ant-btn,
.vendor-list-container .ant-btn {
  font-size: 12px !important;
}

/* 统一操作按钮样式 */
.client-list-container .ant-btn[type="text"],
.project-list-container .ant-btn[type="text"],
.finance-management .ant-btn[type="text"],
.vendor-list-container .ant-btn[type="text"] {
  width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 统一Finance页面统计卡片字体大小 */
.finance-management .ant-statistic-title {
  font-size: 12px !important;
}

.finance-management .ant-statistic-content {
  font-size: 12px !important;
}

.finance-management .ant-statistic-content-value {
  font-size: 12px !important;
}

/* 统一Finance页面所有Card标题字体大小 */
.finance-management .ant-card-head-title {
  font-size: 12px !important;
}

/* 统一Finance页面所有输入框和选择器字体大小 */
.finance-management .ant-input,
.finance-management .ant-select-selector,
.finance-management .ant-select-selection-item {
  font-size: 12px !important;
}

/* 统一Finance页面DatePicker字体大小 */
.finance-management .ant-picker,
.finance-management .ant-picker-input > input,
.finance-management .ant-picker-range-separator {
  font-size: 12px !important;
}

.finance-management .ant-picker-suffix,
.finance-management .ant-picker-clear {
  font-size: 12px !important;
}

/* 统一Lead to Cash页面字体大小 */
.project-list-container .ant-input,
.project-list-container .ant-select-selector,
.project-list-container .ant-select-selection-item,
.project-list-container .ant-btn {
  font-size: 12px !important;
}

.project-list-container .ant-drawer .ant-drawer-title,
.project-list-container .ant-drawer .ant-drawer-body,
.project-list-container .ant-checkbox-wrapper {
  font-size: 12px !important;
}

/* 统一Client页面字体大小 */
.client-list-container .ant-input,
.client-list-container .ant-select-selector,
.client-list-container .ant-select-selection-item,
.client-list-container .ant-btn {
  font-size: 12px !important;
}

.client-list-container .ant-drawer .ant-drawer-title,
.client-list-container .ant-drawer .ant-drawer-body,
.client-list-container .ant-checkbox-wrapper {
  font-size: 12px !important;
}

/* 统一Vendor页面字体大小 */
.vendor-list-container .ant-input,
.vendor-list-container .ant-select-selector,
.vendor-list-container .ant-select-selection-item,
.vendor-list-container .ant-btn {
  font-size: 12px !important;
}

.vendor-list-container .ant-drawer .ant-drawer-title,
.vendor-list-container .ant-drawer .ant-drawer-body,
.vendor-list-container .ant-checkbox-wrapper {
  font-size: 12px !important;
}

/* 表格列宽可拖拽调整功能 */
.client-list-container .ant-table-thead > tr > th,
.project-list-container .ant-table-thead > tr > th,
.finance-management .ant-table-thead > tr > th,
.vendor-list-container .ant-table-thead > tr > th {
  position: relative !important;
  border-right: 1px solid #f0f0f0 !important;
  user-select: none !important;
}

/* 列边框拖拽区域 - 改进版本 */
.client-list-container .ant-table-thead > tr > th::after,
.project-list-container .ant-table-thead > tr > th::after,
.finance-management .ant-table-thead > tr > th::after,
.vendor-list-container .ant-table-thead > tr > th::after {
  content: '';
  position: absolute;
  right: -4px;
  top: 0;
  bottom: 0;
  width: 8px;
  background: transparent;
  cursor: col-resize;
  z-index: 10;
  transition: background-color 0.2s ease;
}

/* 悬停时的轻微视觉反馈 */
.client-list-container .ant-table-thead > tr > th:hover::after,
.project-list-container .ant-table-thead > tr > th:hover::after,
.finance-management .ant-table-thead > tr > th:hover::after,
.vendor-list-container .ant-table-thead > tr > th:hover::after {
  background: rgba(0, 0, 0, 0.05);
}

/* 防止最后一列（Actions列）显示拖拽区域 */
.client-list-container .ant-table-thead > tr > th:last-child::after,
.project-list-container .ant-table-thead > tr > th:last-child::after,
.finance-management .ant-table-thead > tr > th:last-child::after,
.vendor-list-container .ant-table-thead > tr > th:last-child::after {
  display: none !important;
}

/* 确保表格支持动态列宽 */
.client-list-container .ant-table,
.project-list-container .ant-table,
.finance-management .ant-table,
.vendor-list-container .ant-table {
  table-layout: fixed !important;
}

.client-list-container .ant-table-container,
.project-list-container .ant-table-container,
.finance-management .ant-table-container,
.vendor-list-container .ant-table-container {
  overflow-x: auto !important;
}

 