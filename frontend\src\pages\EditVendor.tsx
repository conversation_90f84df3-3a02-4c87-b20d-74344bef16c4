import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  message,
  DatePicker,
  InputNumber,
  Divider,
  Spin
} from 'antd';
import {
  ArrowLeftOutlined,
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  BankOutlined,
  ShopOutlined,
  GlobalOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import vendorService from '../services/vendor.service';
import { Vendor } from '../types';
import dayjs from 'dayjs';
import { useCurrency } from '../contexts/CurrencyContext';

const { Option } = Select;
const { TextArea } = Input;

const EditVendor: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const { selectedCurrency, convertAmount } = useCurrency();

  // 加载供应商数据
  const loadVendor = async () => {
    if (!id) {
      message.error('Vendor ID not found');
      navigate('/vendor');
      return;
    }

    try {
      setInitialLoading(true);
      const vendorData = await vendorService.getVendor(id);
      setVendor(vendorData);
      
      // 设置表单初始值
      form.setFieldsValue({
        ...vendorData,
        contractStartDate: vendorData.contractStartDate ? dayjs(vendorData.contractStartDate) : null,
        contractEndDate: vendorData.contractEndDate ? dayjs(vendorData.contractEndDate) : null,
        lastOrderDate: vendorData.lastOrderDate ? dayjs(vendorData.lastOrderDate) : null,
      });
    } catch (error) {
      console.error('Failed to load vendor:', error);
      message.error('Failed to load vendor');
      navigate('/vendor');
    } finally {
      setInitialLoading(false);
    }
  };

  useEffect(() => {
    loadVendor();
  }, [id]);

  const handleSubmit = async (values: any) => {
    if (!id) return;

    try {
      setLoading(true);
      
      const vendorData: Partial<Vendor> = {
        name: values.name,
        email: values.email,
        phone: values.phone,
        category: values.category,
        industry: values.industry,
        country: values.country,
        address: values.address,
        website: values.website,
        contactPerson: values.contactPerson,
        contactEmail: values.contactEmail,
        contactPhone: values.contactPhone,
        paymentTerms: values.paymentTerms,
        creditRating: values.creditRating,
        status: values.status,
        totalSpend: values.totalSpend || 0,
        contractStartDate: values.contractStartDate?.format('YYYY-MM-DD'),
        contractEndDate: values.contractEndDate?.format('YYYY-MM-DD'),
        lastOrderDate: values.lastOrderDate?.format('YYYY-MM-DD'),
        tags: values.tags || [],
        notes: values.notes
      };

      await vendorService.updateVendor(id, vendorData);
      message.success('Vendor updated successfully');
      navigate('/vendor');
    } catch (error) {
      console.error('Failed to update vendor:', error);
      message.error('Failed to update vendor');
    } finally {
      setLoading(false);
    }
  };

  // 自动生成供应商ID（递增记忆）
  const generateVendorId = () => {
    const year = new Date().getFullYear();
    const counterKey = `vendor_id_counter_${year}`;
    let counter = 1;
    const stored = localStorage.getItem(counterKey);
    if (stored && !isNaN(Number(stored))) {
      counter = Number(stored) + 1;
    }
    const numStr = String(counter).padStart(3, '0');
    return `VEN-${year}-${numStr}`;
  };

  // 重新生成ID的处理函数
  const handleRegenerateId = () => {
    const newId = generateVendorId();
    form.setFieldsValue({
      vendorId: newId
    });
    message.success('New Vendor ID generated successfully!');
  };

  // 自动统计Total Spend（多币种打通Finance）
  const [autoTotalSpend, setAutoTotalSpend] = useState(0);
  useEffect(() => {
    async function fetchTotalSpend() {
      let total = 0;
      try {
        // 读取所有finance_transactions
        const txData = localStorage.getItem('finance_transactions');
        const txList = txData ? JSON.parse(txData) : [];
        const vendorName = form.getFieldValue('name');
        if (Array.isArray(txList) && vendorName) {
          total = txList
            .filter(tx => tx.type === 'expense' && tx.vendor && tx.vendor === vendorName)
            .reduce((sum, tx) => {
              // 检测原始货币
              let detectedCurrency = tx.currency || tx.originalCurrency || 'EUR';
              if (!tx.currency && !tx.originalCurrency && tx.description && typeof tx.description === 'string') {
                const desc = tx.description.toLowerCase();
                if (desc.includes('cny') || desc.includes('人民币') || desc.includes('rmb') || desc.includes('¥')) {
                  detectedCurrency = 'CNY';
                } else if (desc.includes('usd') || desc.includes('美元') || desc.includes('$')) {
                  detectedCurrency = 'USD';
                } else if (desc.includes('gbp') || desc.includes('英镑') || desc.includes('£')) {
                  detectedCurrency = 'GBP';
                } else if (desc.includes('jpy') || desc.includes('日元')) {
                  detectedCurrency = 'JPY';
                }
              }
              // 金额字段兼容totalAmount/amount
              const rawAmount = tx.totalAmount || tx.amount || 0;
              const numericAmount = typeof rawAmount === 'string' ? parseFloat(rawAmount.replace(/[^\d.-]/g, '')) : rawAmount;
              // 统一转换为当前系统货币
              const converted = convertAmount(Math.abs(numericAmount), detectedCurrency, selectedCurrency);
              return sum + converted;
            }, 0);
        }
      } catch (e) { /* 忽略错误 */ }
      setAutoTotalSpend(total);
      form.setFieldsValue({ totalSpend: total });
    }
    fetchTotalSpend();
    // 监听Vendor Name和系统货币变化
  }, [form, form.getFieldValue('name'), selectedCurrency]);

  if (initialLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '400px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/vendor')}
          style={{ marginRight: '16px' }}
        >
          Back
        </Button>
        <h1 style={{
          fontSize: '20px',
          fontWeight: 600,
          margin: 0,
          textAlign: 'center',
          flex: 1
        }}>
          Edit Vendor - {vendor?.name}
        </h1>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          status: 'active',
          paymentTerms: 'net_30',
          creditRating: 'A',
          totalSpend: 0
        }}
      >
        <Row gutter={[24, 24]}>
          {/* 左侧列 */}
          <Col xs={24} lg={12}>
            {/* 基本信息 */}
            <Card 
              title={
                <span>
                  <ShopOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  Basic Information
                </span>
              } 
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={<span>Vendor ID <span style={{ color: 'red' }}>*</span></span>}
                name="vendorId"
                rules={[
                  { required: true, message: 'Please enter vendor ID' },
                  { pattern: /^VEN-\d{4}-\d{3}$/, message: 'Vendor ID format should be VEN-YYYY-XXX' }
                ]}
                tooltip="Unique identifier for the vendor. Format: VEN-YEAR-XXX"
              >
                <Input 
                  placeholder="VEN-2025-001"
                  addonAfter={
                    <Button
                      type="text"
                      size="small"
                      onClick={handleRegenerateId}
                      title="Generate new ID"
                      style={{ padding: '0 8px' }}
                    >
                      🔄
                    </Button>
                  }
                  disabled={true}
                />
              </Form.Item>

              <Form.Item
                label={<span>Vendor Name <span style={{ color: 'red' }}>*</span></span>}
                name="name"
                rules={[{ required: true, message: 'Please enter vendor name' }]}
              >
                <Input 
                  prefix={<ShopOutlined />} 
                  placeholder="Enter vendor name"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={<span>Category <span style={{ color: 'red' }}>*</span></span>}
                    name="category"
                    rules={[{ required: true, message: 'Please select category' }]}
                  >
                    <Select placeholder="Select category">
                      <Option value="product">Product</Option>
                      <Option value="service">Service</Option>
                      <Option value="material">Material</Option>
                      <Option value="software">Software</Option>
                      <Option value="consulting">Consulting</Option>
                      <Option value="logistics">Logistics</Option>
                      <Option value="other">Other</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span>Industry <span style={{ color: 'red' }}>*</span></span>}
                    name="industry"
                    rules={[{ required: true, message: 'Please enter industry' }]}
                  >
                    <Input 
                      placeholder="Enter industry"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={<span>Status <span style={{ color: 'red' }}>*</span></span>}
                    name="status"
                    rules={[{ required: true, message: 'Please select status' }]}
                  >
                    <Select placeholder="Select status">
                      <Option value="active">Active</Option>
                      <Option value="inactive">Inactive</Option>
                      <Option value="potential">Potential</Option>
                      <Option value="blacklisted">Blacklisted</Option>
                      <Option value="suspended">Suspended</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span>Country/Region <span style={{ color: 'red' }}>*</span></span>}
                    name="country"
                    rules={[{ required: true, message: 'Please enter country' }]}
                  >
                    <Input 
                      placeholder="Enter country"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Address"
                name="address"
              >
                <TextArea 
                  rows={3} 
                  placeholder="Enter full address"
                />
              </Form.Item>

              <Form.Item
                label="Website"
                name="website"
              >
                <Input 
                  prefix={<GlobalOutlined />}
                  placeholder="https://example.com"
                />
              </Form.Item>
            </Card>

            {/* 财务信息 */}
            <Card 
              title={
                <span>
                  <BankOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                  Financial Information
                </span>
              } 
              style={{ marginBottom: '24px' }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={<span>Payment Terms <span style={{ color: 'red' }}>*</span></span>}
                    name="paymentTerms"
                    rules={[{ required: true, message: 'Please select payment terms' }]}
                  >
                    <Select placeholder="Select payment terms">
                      <Option value="net_15">Net 15</Option>
                      <Option value="net_30">Net 30</Option>
                      <Option value="net_45">Net 45</Option>
                      <Option value="net_60">Net 60</Option>
                      <Option value="net_90">Net 90</Option>
                      <Option value="prepaid">Prepaid</Option>
                      <Option value="cod">COD</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span>Credit Rating <span style={{ color: 'red' }}>*</span></span>}
                    name="creditRating"
                    rules={[{ required: true, message: 'Please select credit rating' }]}
                  >
                    <Select placeholder="Select credit rating">
                      <Option value="AAA">AAA</Option>
                      <Option value="AA">AA</Option>
                      <Option value="A">A</Option>
                      <Option value="BBB">BBB</Option>
                      <Option value="BB">BB</Option>
                      <Option value="B">B</Option>
                      <Option value="CCC">CCC</Option>
                      <Option value="CC">CC</Option>
                      <Option value="C">C</Option>
                      <Option value="D">D</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label={<span>Total Spend (€)</span>}
                name="totalSpend"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0"
                  min={0}
                  value={autoTotalSpend}
                  readOnly
                  disabled
                  formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => {
                    if (!value) return 0;
                    const numericValue = value.replace(/€\s?|(,*)/g, '');
                    const parsed = parseFloat(numericValue);
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Card>
          </Col>

          {/* 右侧列 */}
          <Col xs={24} lg={12}>
            {/* 联系信息 */}
            <Card 
              title={
                <span>
                  <UserOutlined style={{ marginRight: '8px', color: '#fa8c16' }} />
                  Contact Information
                </span>
              } 
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                name="email"
                label={<span>Primary Email</span>}
                rules={[
                  { type: 'email', message: 'Please enter a valid email address' }
                ]}
              >
                <Input 
                  placeholder="<EMAIL>"
                  prefix={<MailOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Primary Phone"
                name="phone"
              >
                <Input 
                  prefix={<PhoneOutlined />}
                  placeholder="******-123-4567"
                />
              </Form.Item>

              <Divider />

              <div style={{ marginBottom: '16px', fontWeight: 500 }}>
                Alternative Contact
              </div>

              <Form.Item
                label={<span>Contact Person <span style={{ color: 'red' }}>*</span></span>}
                name="contactPerson"
                rules={[{ required: true, message: 'Please enter contact person' }]}
              >
                <Input 
                  prefix={<UserOutlined />}
                  placeholder="Contact person name"
                />
              </Form.Item>

              <Form.Item
                label="Contact Person Email"
                name="contactEmail"
                rules={[
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input 
                  prefix={<MailOutlined />}
                  placeholder="<EMAIL>"
                />
              </Form.Item>

              <Form.Item
                label="Contact Person Phone"
                name="contactPhone"
              >
                <Input 
                  prefix={<PhoneOutlined />}
                  placeholder="******-123-4567"
                />
              </Form.Item>
            </Card>

            {/* 合同信息 */}
            <Card 
              title={
                <span>
                  <CalendarOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
                  Contract Information
                </span>
              } 
              style={{ marginBottom: '24px' }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Contract Start Date"
                    name="contractStartDate"
                  >
                    <DatePicker 
                      style={{ width: '100%' }}
                      placeholder="Select start date"
                      format="DD/MM/YYYY"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Contract End Date"
                    name="contractEndDate"
                  >
                    <DatePicker 
                      style={{ width: '100%' }}
                      placeholder="Select end date"
                      format="DD/MM/YYYY"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Last Order Date"
                name="lastOrderDate"
              >
                <DatePicker 
                  style={{ width: '100%' }}
                  placeholder="Select last order date"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Card>

            {/* 其他信息 */}
            <Card 
              title={
                <span>
                  <BankOutlined style={{ marginRight: '8px', color: '#13c2c2' }} />
                  Additional Information
                </span>
              }
            >
              <Form.Item
                label="Tags"
                name="tags"
              >
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  placeholder="Add tags..."
                  tokenSeparators={[',']}
                  options={[
                    { value: 'reliable', label: 'Reliable' },
                    { value: 'premium', label: 'Premium' },
                    { value: 'international', label: 'International' },
                    { value: 'strategic', label: 'Strategic' },
                    { value: 'preferred', label: 'Preferred' },
                    { value: 'bulk', label: 'Bulk' },
                    { value: 'quality', label: 'Quality' },
                    { value: 'competitive', label: 'Competitive' }
                  ]}
                />
              </Form.Item>

              <Form.Item
                label="Notes"
                name="notes"
              >
                <TextArea 
                  rows={4} 
                  placeholder="Additional notes about the vendor..."
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Card style={{ marginTop: '24px' }}>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
            <Button 
              size="large" 
              onClick={() => navigate('/vendor')}
            >
              Cancel
            </Button>
            <Button 
              type="primary" 
              size="large" 
              htmlType="submit"
              loading={loading}
            >
              Update Vendor
            </Button>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default EditVendor; 