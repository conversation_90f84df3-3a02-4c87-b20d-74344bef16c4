const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const cluster = require('cluster');
const os = require('os');

// 配置
const config = {
  port: process.env.PORT || 5002,
  logDirectory: path.join(__dirname, 'logs'),
  enableClustering: false, // 设置为true启用多进程
  numWorkers: os.cpus().length,
  maxMemoryRestart: 200, // MB，超过此内存使用量时重启
};

// 确保日志目录存在
if (!fs.existsSync(config.logDirectory)) {
  fs.mkdirSync(config.logDirectory, { recursive: true });
}

// 创建日志文件流
const accessLogStream = fs.createWriteStream(
  path.join(config.logDirectory, `access-${new Date().toISOString().split('T')[0]}.log`),
  { flags: 'a' }
);

const errorLogStream = fs.createWriteStream(
  path.join(config.logDirectory, `error-${new Date().toISOString().split('T')[0]}.log`),
  { flags: 'a' }
);

// 日志函数
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
  
  console.log(logMessage);
  
  if (type === 'error') {
    errorLogStream.write(logMessage);
  } else {
    accessLogStream.write(logMessage);
  }
}

// 内存监控
function monitorMemory() {
  const memoryUsage = process.memoryUsage();
  const memoryUsageMB = Math.round(memoryUsage.rss / 1024 / 1024);
  
  log(`Memory usage: ${memoryUsageMB}MB`);
  
  if (memoryUsageMB > config.maxMemoryRestart) {
    log(`Memory usage exceeded limit (${config.maxMemoryRestart}MB). Restarting...`, 'error');
    process.exit(1); // 进程将被PM2重启
  }
}

// 如果启用了集群模式且是主进程
if (config.enableClustering && cluster.isMaster) {
  log(`Master process ${process.pid} is running`);
  
  // 创建工作进程
  for (let i = 0; i < config.numWorkers; i++) {
    cluster.fork();
  }
  
  // 监听工作进程退出事件
  cluster.on('exit', (worker, code, signal) => {
    log(`Worker ${worker.process.pid} died with code ${code} and signal ${signal}`, 'error');
    log('Starting a new worker...');
    cluster.fork();
  });
} else {
  // 工作进程代码
  startServer();
}

function startServer() {
  // 创建Express应用
  const app = express();
  
  // 中间件
  app.use(cors());
  app.use(express.json());
  
  // 请求日志中间件
  app.use((req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      log(`${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`);
    });
    
    next();
  });
  
  // 内存数据存储
  const projects = [];
  const stages = [];
  
  // 健康检查端点
  app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', message: '服务器正常运行', version: '1.0.0' });
  });
  
  // 登录端点
  app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    log(`登录请求: ${email}`);
    
    // 简单验证
    if (email === '<EMAIL>' && password === 'admin123') {
      // 返回成功响应
      res.json({
        user: {
          id: '703732e7-5236-4b5d-a46f-40bc6afa801e',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          createdAt: '2025-05-16 08:14:42',
          updatedAt: '2025-05-16 08:14:42'
        },
        token: 'dummy-token-for-testing'
      });
    } else {
      // 返回错误响应
      res.status(401).json({ message: '邮箱或密码错误' });
    }
  });
  
  // 项目相关端点
  app.get('/api/projects', (req, res) => {
    res.json(projects);
  });
  
  app.get('/api/projects/:id', (req, res) => {
    const project = projects.find(p => p.id === req.params.id);
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    res.json(project);
  });
  
  app.post('/api/projects', (req, res) => {
    const newProject = {
      ...req.body,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    projects.push(newProject);
    res.status(201).json(newProject);
  });
  
  app.put('/api/projects/:id', (req, res) => {
    const index = projects.findIndex(p => p.id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    projects[index] = {
      ...projects[index],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    res.json(projects[index]);
  });
  
  // 项目阶段相关端点
  app.get('/api/project-stages/project/:projectId', (req, res) => {
    const projectStages = stages.filter(s => s.projectId === req.params.projectId);
    res.json(projectStages);
  });
  
  app.get('/api/project-stages/:id', (req, res) => {
    const stage = stages.find(s => s.id === req.params.id);
    if (!stage) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }
    res.json(stage);
  });
  
  app.post('/api/project-stages', (req, res) => {
    log(`收到创建项目阶段请求: ${JSON.stringify(req.body)}`);
    
    const newStage = {
      ...req.body,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    stages.push(newStage);
    log(`创建的项目阶段: ${newStage.id}`);
    
    res.status(201).json(newStage);
  });
  
  app.put('/api/project-stages/:id', (req, res) => {
    const index = stages.findIndex(s => s.id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }
    
    stages[index] = {
      ...stages[index],
      ...req.body,
      updatedAt: new Date().toISOString()
    };
    
    res.json(stages[index]);
  });
  
  app.delete('/api/project-stages/:id', (req, res) => {
    const index = stages.findIndex(s => s.id === req.params.id);
    if (index === -1) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }
    
    stages.splice(index, 1);
    res.status(204).send();
  });
  
  // 错误处理中间件
  app.use((err, req, res, next) => {
    log(`错误: ${err.message}\n${err.stack}`, 'error');
    res.status(500).json({ message: '服务器内部错误', error: err.message });
  });
  
  // 启动服务器
  const server = app.listen(config.port, '0.0.0.0', () => {
    log(`增强版服务器运行在端口 ${config.port}`);
    log(`访问 http://localhost:${config.port}/api/health 测试服务器是否正常运行`);
  });
  
  // 优雅关闭
  process.on('SIGTERM', () => {
    log('收到SIGTERM信号，正在优雅关闭...');
    server.close(() => {
      log('HTTP服务器已关闭');
      process.exit(0);
    });
  });
  
  process.on('SIGINT', () => {
    log('收到SIGINT信号，正在优雅关闭...');
    server.close(() => {
      log('HTTP服务器已关闭');
      process.exit(0);
    });
  });
  
  // 未捕获的异常处理
  process.on('uncaughtException', (err) => {
    log(`未捕获的异常: ${err.message}\n${err.stack}`, 'error');
    
    // 尝试优雅关闭，但确保在一定时间后强制退出
    server.close(() => {
      log('由于未捕获的异常，服务器已关闭');
      process.exit(1);
    });
    
    // 如果无法在5秒内优雅关闭，则强制退出
    setTimeout(() => {
      log('无法在5秒内优雅关闭，强制退出', 'error');
      process.exit(1);
    }, 5000);
  });
  
  // 未处理的Promise拒绝
  process.on('unhandledRejection', (reason, promise) => {
    log(`未处理的Promise拒绝: ${reason}`, 'error');
  });
  
  // 定期监控内存使用
  setInterval(monitorMemory, 60000); // 每分钟检查一次
}
