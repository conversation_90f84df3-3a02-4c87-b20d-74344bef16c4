import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Upload,
  Modal,
  Form,
  Input,
  Select,
  message,
  Progress,
  Tooltip,
  DatePicker,
  Row,
  Col,
  Typography,
  Space
} from 'antd';
import {
  CloudUploadOutlined,
  RightOutlined,
  CloseOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  EditFilled,
  CheckCircleOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';
import './Contract.css';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

interface ContractProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

const Contract: React.FC<ContractProps> = ({ project, stage, onSave, onProceed }) => {
  // 🔧 修复：初始化为空数组，避免默认数据覆盖localStorage数据
  const [versions, setVersions] = useState<any[]>([]);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // 上传对话框状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [form] = Form.useForm();

  // Actions模态窗口状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [editForm] = Form.useForm();

  // 🔧 新增：合同签证日期相关状态
  const [contractSigningDate, setContractSigningDate] = useState<string | null>(null);
  const [signingStatus, setSigningStatus] = useState<'not_signed' | 'signed' | 'pending'>('not_signed');
  const [signedBy, setSignedBy] = useState<string>('');
  const [signedByCompany, setSignedByCompany] = useState<string>('');
  const [isEditingSigningInfo, setIsEditingSigningInfo] = useState(false);
  const [signingForm] = Form.useForm();

  // 🔧 修复：改进数据保存逻辑，只在数据加载完成后保存
  useEffect(() => {
    if (!isDataLoaded) return; // 避免在初始化阶段保存空数据
    
    try {
      // 多种方式获取projectId
      const projectId = project?.id || project?.projectId || stage?.projectId || 
                        window.location.pathname.split('/').pop();
      
      if (projectId && projectId !== 'create' && versions.length > 0) {
        localStorage.setItem(`contract_versions_${projectId}`, JSON.stringify(versions));
        console.log('💾 Contract: Saved versions to localStorage:', versions.length, 'versions');
        
        // 移除onSave调用，避免无限循环
        // if (onSave) {
        //   onSave({
        //     type: 'contract',
        //     status: 'in_progress',
        //     versions: versions,
        //     updatedAt: new Date().toISOString()
        //   });
        // }
      }
    } catch (error) {
      console.error('❌ Contract: Error saving contract versions:', error);
    }
  }, [versions, project?.id, project?.projectId, stage?.projectId, isDataLoaded]); // 移除onSave依赖

  // 🔧 新增：签证日期数据的保存
  useEffect(() => {
    if (!isDataLoaded) return;
    
    try {
      const projectId = project?.id || project?.projectId || stage?.projectId || 
                        window.location.pathname.split('/').pop();
      
      if (projectId && projectId !== 'create') {
        const signingData = {
          contractSigningDate,
          signingStatus,
          signedBy,
          signedByCompany
        };
        localStorage.setItem(`contract_signing_${projectId}`, JSON.stringify(signingData));
        console.log('💾 Contract: Saved signing data to localStorage');
      }
    } catch (error) {
      console.error('❌ Contract: Error saving contract signing data:', error);
    }
  }, [contractSigningDate, signingStatus, signedBy, signedByCompany, project?.id, project?.projectId, stage?.projectId, isDataLoaded]);

  // 🔧 修复：优化数据加载逻辑，确保正确的初始化顺序
  useEffect(() => {
    const initializeData = () => {
      try {
        // 多种方式获取projectId
        const projectId = project?.id || project?.projectId || stage?.projectId || 
                          window.location.pathname.split('/').pop();
        
        if (projectId && projectId !== 'create') {
          let loadedVersions = null;
          
          // 优先从localStorage加载合同文档
          const savedVersions = localStorage.getItem(`contract_versions_${projectId}`);
          if (savedVersions) {
            loadedVersions = JSON.parse(savedVersions);
            console.log('✅ Contract: Loaded versions from localStorage:', loadedVersions.length, 'versions');
          }
          
          // 其次尝试从stage.data加载
          if (!loadedVersions && stage?.data) {
            try {
              const stageData = typeof stage.data === 'string' ? JSON.parse(stage.data) : stage.data;
              if (stageData && stageData.versions && Array.isArray(stageData.versions)) {
                loadedVersions = stageData.versions;
                console.log('✅ Contract: Loaded versions from stage data:', loadedVersions.length, 'versions');
              }
            } catch (parseError) {
              console.warn('⚠️ Contract: Failed to parse stage data:', parseError);
            }
          }
          
          // 如果都没有数据，使用空数组
          if (!loadedVersions) {
            loadedVersions = [];
            console.log('📋 Contract: Using empty versions data');
          }
          
          setVersions(loadedVersions);

          // 🔧 新增：加载签证日期数据
          const savedSigningData = localStorage.getItem(`contract_signing_${projectId}`);
          if (savedSigningData) {
            try {
              const signingData = JSON.parse(savedSigningData);
              setContractSigningDate(signingData.contractSigningDate || null);
              setSigningStatus(signingData.signingStatus || 'not_signed');
              setSignedBy(signingData.signedBy || '');
              setSignedByCompany(signingData.signedByCompany || '');
              console.log('✅ Contract: Loaded signing data from localStorage');
            } catch (parseError) {
              console.warn('⚠️ Contract: Failed to parse signing data:', parseError);
            }
          }
        } else {
          console.log('⚠️ Contract: No projectId found, using empty versions');
        }
        
        setIsDataLoaded(true);
      } catch (error) {
        console.error('❌ Contract: Error loading contract data:', error);
        setIsDataLoaded(true);
      }
    };

    initializeData();
  }, [project?.id, project?.projectId, stage?.projectId, stage?.data]);

  // 自定义上传请求
  const customUploadRequest = (options: any) => {
    const { file, onSuccess, onError } = options;
    
    setTimeout(() => {
      if (Math.random() > 0.1) {
        onSuccess({
          fileName: file.name,
          fileSize: file.size,
          uploadDate: new Date().toISOString(),
          fileUrl: file.type.startsWith('image/') 
            ? URL.createObjectURL(file)
            : 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
        });
      } else {
        onError(new Error('Upload failed'));
      }
    }, 1000);

    return {
      abort() {
        console.log('Upload aborted');
      }
    };
  };

  // 🔧 修复：改进文件上传处理逻辑，确保数据持久性
  const handleUploadChange = (info: any) => {
    console.log('📁 Contract: Handling file upload:', info.file.status, info.file.name);

    if (info.file.status === 'done') {
      // 检查文件是否已存在于列表中
      const isFileAlreadyAdded = versions.some(v => v.notes === info.file.name && v.uid === info.file.uid);
      if (isFileAlreadyAdded) {
        console.log(`⚠️ Contract: File "${info.file.name}" already added. Skipping.`);
        return;
      }
      
      console.log(`✅ Contract: File "${info.file.name}" uploaded successfully.`);
      
      const newVersion = {
        key: info.file.uid, // 使用文件的唯一ID作为key
        uid: info.file.uid, // 保存uid以便去重
        version: `v${versions.length + 1}.0`,
        type: 'other',
        date: new Date().toISOString().split('T')[0],
        uploadedBy: 'Current User',
        notes: info.file.name,
        status: 'submitted',
        fileName: info.file.name,
        fileUrl: info.file.response?.fileUrl || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        uploadedAt: new Date().toISOString()
      };

      // 🔧 修复：新文件添加到数组开头
      setVersions(prevVersions => [newVersion, ...prevVersions]);
      
      message.success(`"${info.file.name}" uploaded successfully!`);

    } else if (info.file.status === 'error') {
      console.error(`❌ Contract: File "${info.file.name}" upload failed.`);
      message.error(`"${info.file.name}" upload failed.`);
    }
  };

  // 编辑合同
  const handleEdit = (record: any) => {
    setSelectedRecord(record);
    editForm.setFieldsValue({
      version: record.version,
      type: record.type,
      date: dayjs(record.date),
      uploadedBy: record.uploadedBy,
      notes: record.notes,
      status: record.status
    });
    setEditModalVisible(true);
  };

  // 删除合同
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure to delete contract ${record.version}? This operation cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        const newVersions = versions.filter(item => item.key !== record.key);
        setVersions(newVersions);
        message.success('Contract deleted successfully');
      },
    });
  };

  // 保存编辑
  const handleEditSave = () => {
    if (!selectedRecord) return;
    
    editForm.validateFields().then(values => {
      const newVersions = versions.map(item => 
        item.key === selectedRecord.key 
          ? {
              ...item,
              version: values.version,
              type: values.type,
              date: values.date.format('YYYY-MM-DD'),
              uploadedBy: values.uploadedBy,
              notes: values.notes,
              status: values.status
            }
          : item
      );
      setVersions(newVersions);
      setEditModalVisible(false);
      message.success('Contract information updated successfully');
    });
  };

  // 关闭上传对话框
  const closeUploadModal = () => {
    setUploadModalVisible(false);
  };

  // 模拟上传进度
  const simulateUpload = () => {
    setUploading(true);
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);

      if (progress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          setUploadModalVisible(false);
          setUploading(false);

          // 🔧 修复：Modal上传成功后的处理，确保数据持久性
          const values = form.getFieldsValue();
          const newVersion = {
            key: String(Date.now()),
            version: values.version || `v${versions.length + 1}.0`,
            type: values.type || 'other',
            date: new Date().toISOString().split('T')[0],
            uploadedBy: 'Current User',
            notes: values.description || 'Uploaded via modal',
            status: 'submitted',
            uploadedAt: new Date().toISOString()
          };

          // 🔧 关键修复：确保状态更新触发保存
          setVersions(prev => {
            const updated = [newVersion, ...prev];
            console.log('💾 Contract: Updated versions via modal upload:', updated.length, 'total versions');
            return updated;
          });
          
          message.success('Contract uploaded successfully!');
          form.resetFields();
        }, 500);
      }
    }, 200);
  };

  // 处理表单提交
  const handleUploadSubmit = () => {
    form.validateFields()
      .then(values => {
        simulateUpload();
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  // 处理提交并继续
  const handleSubmitAndProceed = () => {
    message.loading('Submitting...', 1.5)
      .then(() => {
        message.success('Submitted successfully! Proceeding to the next stage.');
        if (onSave) {
          onSave({
            status: 'completed',
            versions: versions
          });
        }
        // 自动跳转到下一个阶段
        if (onProceed) {
          onProceed('execution');
        }
      });
  };

  // 🔧 新增：处理签证信息编辑
  const handleEditSigningInfo = () => {
    signingForm.setFieldsValue({
      signingDate: contractSigningDate ? dayjs(contractSigningDate) : null,
      signedBy: signedBy,
      signedByCompany: signedByCompany
    });
    setIsEditingSigningInfo(true);
  };

  // 🔧 新增：保存签证信息
  const handleSaveSigningInfo = () => {
    signingForm.validateFields().then(values => {
      setContractSigningDate(values.signingDate ? values.signingDate.format('YYYY-MM-DD') : null);
      setSignedBy(values.signedBy || '');
      setSignedByCompany(values.signedByCompany || '');
      setIsEditingSigningInfo(false);
      message.success('Contract signing information updated successfully!');
    }).catch(info => {
      console.log('Validate Failed:', info);
    });
  };

  // 🔧 新增：取消编辑签证信息
  const handleCancelSigningInfo = () => {
    setIsEditingSigningInfo(false);
    signingForm.resetFields();
  };

  // 表格列定义
  const columns = [
    {
      title: 'Contract',
      dataIndex: 'version',
      key: 'version',
      render: (text: string) => (
        <span className="version-link">{text}</span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => {
        const typeMap: Record<string, { color: string, text: string }> = {
          'hardware': { color: 'blue', text: 'Hardware' },
          'software': { color: 'green', text: 'Software' },
          'service': { color: 'purple', text: 'Service' },
          'other': { color: 'default', text: 'Other' }
        };
        const type = text || 'other';
        const { color, text: typeText } = typeMap[type] || typeMap.other;
        return <span className={`contract-type ${color}`}>{typeText}</span>;
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Uploaded By',
      dataIndex: 'uploadedBy',
      key: 'uploadedBy',
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let className = '';
        let text = '';

        if (status === 'submitted') {
          className = 'status-submitted';
          text = 'Submitted';
        } else if (status === 'internal') {
          className = 'status-internal';
          text = 'Internal';
        }

        return <span className={`status ${className}`}>{text}</span>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: any) => (
        <div className="action-icons">
          <Tooltip title="Edit Contract">
            <EditOutlined 
              className="action-icon edit-icon" 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Delete Contract">
            <DeleteOutlined 
              className="action-icon delete-icon" 
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </div>
      )
    }
  ];

  return (
    <div className="contract-container">
      {/* Contract Signing Information Card */}
      <Card 
        className="contract-card" 
        title="Contract Signed Information"
        extra={
          !isEditingSigningInfo && (
            <Button 
              type="text" 
              icon={<EditFilled />} 
              onClick={handleEditSigningInfo}
              style={{ color: '#FF7A00' }}
            >
              Edit
            </Button>
          )
        }
      >
        {!isEditingSigningInfo ? (
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div className="signing-info-item">
                <Text strong style={{ color: '#666', fontSize: '14px' }}>Signed Date:</Text>
                <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center' }}>
                  <CalendarOutlined style={{ color: '#FF7A00', marginRight: '8px', fontSize: '16px' }} />
                  <Text style={{ fontSize: '16px' }}>
                    {contractSigningDate ? dayjs(contractSigningDate).format('DD/MM/YYYY') : 'Not set'}
                  </Text>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="signing-info-item">
                <Text strong style={{ color: '#666', fontSize: '14px' }}>Signed By (Client):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ fontSize: '16px' }}>
                    {signedBy || 'Not specified'}
                  </Text>
                </div>
              </div>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <div className="signing-info-item">
                <Text strong style={{ color: '#666', fontSize: '14px' }}>Signed By (Company):</Text>
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ fontSize: '16px' }}>
                    {signedByCompany || 'Not specified'}
                  </Text>
                </div>
              </div>
            </Col>
          </Row>
        ) : (
          <Form
            form={signingForm}
            layout="vertical"
            onFinish={handleSaveSigningInfo}
          >
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="signingDate"
                  label="Signed Date"
                  rules={[{ required: false, message: 'Please select the signed date' }]}
                >
                  <DatePicker 
                    style={{ width: '100%' }} 
                    format="DD/MM/YYYY"
                    placeholder="Select signed date"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="signedBy"
                  label="Signed By (Client)"
                  rules={[{ required: false, message: 'Please enter who signed the contract (Client)' }]}
                >
                  <Input placeholder="Enter client signatory name" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="signedByCompany"
                  label="Signed By (Company)"
                  rules={[{ required: false, message: 'Please enter who signed the contract (Company)' }]}
                >
                  <Input placeholder="Enter company signatory name" />
                </Form.Item>
              </Col>
            </Row>
            <Row justify="end" style={{ marginTop: '16px' }}>
              <Space>
                <Button onClick={handleCancelSigningInfo}>
                  Cancel
                </Button>
                <Button type="primary" htmlType="submit" style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}>
                  Save
                </Button>
              </Space>
            </Row>
          </Form>
        )}
      </Card>

      {/* Contract Documents Card */}
      <Card className="contract-card" title="Contract Documents (All Versions)" style={{ marginTop: '24px' }}>
        <div className="upload-container" style={{ marginBottom: '24px' }}>
          <Upload.Dragger
            name="files"
            multiple={true}
            customRequest={customUploadRequest}
            onChange={handleUploadChange}
            showUploadList={false}
            className="upload-dropzone"
          >
            <p className="ant-upload-drag-icon">
              <CloudUploadOutlined style={{ fontSize: '48px', color: '#FF7A00' }} />
            </p>
            <p className="ant-upload-text">Drag & drop multiple files here or click to browse</p>
            <p className="ant-upload-hint">Maximum file size 20MB each</p>
          </Upload.Dragger>
        </div>
        {versions.length > 0 && (
          <Table 
            columns={columns} 
            dataSource={[...versions].reverse()} 
            rowKey="key" 
            pagination={false} 
          />
        )}
      </Card>

      {/* Workflow Actions */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleSubmitAndProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>

      {/* Upload Dialog */}
      <Modal
        title="Upload Contract"
        open={uploadModalVisible}
        onCancel={closeUploadModal}
        footer={null}
        className="upload-modal"
      >
        <Upload.Dragger
          name="files"
          multiple
          customRequest={customUploadRequest}
          onChange={handleUploadChange}
          className="contract-upload-area"
          beforeUpload={(file) => {
            if (file.size > 20 * 1024 * 1024) {
              message.error('File size cannot exceed 20MB!');
              return Upload.LIST_IGNORE;
            }
            return true;
          }}
        >
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined style={{ color: '#FF7A00' }} />
          </p>
          <p className="ant-upload-text">Click or drag files to this area to upload</p>
          <p className="ant-upload-hint">Support for a single or bulk upload.</p>
        </Upload.Dragger>
        {uploading && (
          <div className="upload-progress">
            <Progress percent={uploadProgress} status="active" showInfo={false} />
          </div>
        )}
        <Form form={form} layout="vertical">
          <Form.Item
            name="version"
            label="Version"
            rules={[{ required: true, message: 'Please input the version' }]}
          >
            <Input placeholder="e.g., v1.0" />
          </Form.Item>
          <Form.Item
            name="type"
            label="Contract Type"
            rules={[{ required: true, message: 'Please select the contract type' }]}
          >
            <Select placeholder="Select contract type">
              <Select.Option value="hardware">Hardware</Select.Option>
              <Select.Option value="software">Software</Select.Option>
              <Select.Option value="service">Service</Select.Option>
              <Select.Option value="other">Other</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please input a description' }]}
          >
            <TextArea placeholder="Brief description of this document" rows={4} />
          </Form.Item>
          <div className="upload-dialog-footer">
            <Button onClick={closeUploadModal}>Cancel</Button>
            <Button type="primary" onClick={handleUploadSubmit} loading={uploading}>
              Upload
            </Button>
          </div>
        </Form>
      </Modal>

      {/* View Details Modal */}
      <Modal
        title="Contract Details"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            Close
          </Button>
        ]}
        width={600}
      >
        {selectedRecord && (
          <div className="contract-detail">
            <div className="detail-item">
              <span className="detail-label">Contract Version:</span>
              <span className="detail-value">{selectedRecord.version}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Contract Type:</span>
              <span className="detail-value">
                {selectedRecord.type === 'service' ? 'Service Contract' : 
                 selectedRecord.type === 'hardware' ? 'Hardware Contract' :
                 selectedRecord.type === 'software' ? 'Software Contract' : 'Other Contract'}
              </span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Upload Date:</span>
              <span className="detail-value">{selectedRecord.date}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Uploaded By:</span>
              <span className="detail-value">{selectedRecord.uploadedBy}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Status:</span>
              <span className={`status status-${selectedRecord.status}`}>
                {selectedRecord.status === 'submitted' ? 'Submitted' : 'Internal'}
              </span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Notes:</span>
              <span className="detail-value">{selectedRecord.notes}</span>
            </div>
          </div>
        )}
      </Modal>

      {/* Edit Contract Modal */}
      <Modal
        title="Edit Contract Information"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleEditSave}
        okText="Save"
        cancelText="Cancel"
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          name="editContractForm"
        >
          <Form.Item
            name="version"
            label="Contract Version"
            rules={[{ required: true, message: 'Please enter contract version' }]}
          >
            <Input placeholder="Please enter contract version" />
          </Form.Item>

          <Form.Item
            name="type"
            label="Contract Type"
            rules={[{ required: true, message: 'Please select contract type' }]}
          >
            <Select placeholder="Please select contract type">
              <Option value="hardware">Hardware Contract</Option>
              <Option value="software">Software Contract</Option>
              <Option value="service">Service Contract</Option>
              <Option value="other">Other Contract</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="date"
            label="Upload Date"
            rules={[{ required: true, message: 'Please select upload date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="uploadedBy"
            label="Uploaded By"
            rules={[{ required: true, message: 'Please enter uploader name' }]}
          >
            <Input placeholder="Please enter uploader name" />
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select placeholder="Please select status">
              <Option value="submitted">Submitted</Option>
              <Option value="internal">Internal</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea 
              rows={4} 
              placeholder="Please enter notes"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Contract;
