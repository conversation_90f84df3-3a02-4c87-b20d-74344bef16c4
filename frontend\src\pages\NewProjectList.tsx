import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Card, 
  Tag, 
  message,
  Popconfirm,
  Tooltip,
  DatePicker,
  Modal,
  Upload
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  TeamOutlined,
  ShareAltOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  UserOutlined,
  DownloadOutlined,
  MenuOutlined
} from '@ant-design/icons';
import { Checkbox, Drawer } from 'antd';
import projectService, { initializeData } from '../services/new-project.service';
import { Project } from '../types';

import TeamManagementModal from '../components/TeamManagement/TeamManagementModal';
import '../styles/table-row-spacing.css';
import { enableTableColumnResize } from '../utils/tableResizer';
import type { Dayjs } from 'dayjs';
// import { Eye, Grid3X3, List } from 'lucide-react'; // 临时注释，修复导入错误

const { Option } = Select;

const NewProjectList: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [countryFilter, setCountryFilter] = useState('all');
  const [tierFilter, setTierFilter] = useState('all');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [columnSettingsVisible, setColumnSettingsVisible] = useState(false);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>(() => {
    // 从本地存储恢复视图模式
    const savedViewMode = localStorage.getItem('project_list_view_mode');
    return (savedViewMode === 'grid' || savedViewMode === 'table') ? savedViewMode : 'table';
  });
  const [teamModalVisible, setTeamModalVisible] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [visibleColumns, setVisibleColumns] = useState<{[key: string]: boolean}>({
    projectId: true,
    name: true,
    client: true,
    country: true,
    tier: true,
    revenue: true,
    stage: true,
    probability: true,
    owner: true,
    status: true,
    createdAt: true,
    actions: true
  });
  const [spaceModal, setSpaceModal] = useState<{visible: boolean, project: Project | null}>({visible: false, project: null});
  const [activeSpaceTab, setActiveSpaceTab] = useState('user');
  const [userSpaceFiles, setUserSpaceFiles] = useState<any[]>([]);
  const [clientSpaceFiles, setClientSpaceFiles] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 获取项目列表
  const fetchProjects = async (forceRefresh: boolean = false) => {
    console.log('🔄 fetchProjects called, forceRefresh:', forceRefresh);
    
    // 🔒 **紧急修复：确保数据加载的稳定性**
    setLoading(true);
    try {
      // 第一步：直接检查localStorage确保数据存在
      const directCheck = localStorage.getItem('edm_projects');
      if (directCheck && directCheck !== 'null' && directCheck !== 'undefined') {
        try {
          const directProjects = JSON.parse(directCheck);
          if (Array.isArray(directProjects) && directProjects.length > 0) {
            console.log('🔒 Direct localStorage check found projects:', directProjects.length);
            console.log('🔒 Direct check project names:', directProjects.map((p: any) => p.name));
            
            // 立即设置到state，确保UI显示
            setProjects(directProjects);
            console.log('✅ Set projects to state directly from localStorage');
          }
        } catch (parseError) {
          console.error('❌ Direct localStorage parse error:', parseError);
        }
      }

      // 第二步：通过服务层获取数据（作为验证和同步）
      const startTime = performance.now();
      const data = await projectService.getAllProjects(forceRefresh);
      const endTime = performance.now();
      
      console.log(`⏱️ fetchProjects completed in ${endTime - startTime}ms`);
      console.log('📊 Fetched projects count:', data.length);
      if (data.length > 0) {
        console.log('📋 Project IDs:', data.map(p => p.id));
        console.log('📋 Project names:', data.map(p => p.name));
      }

      // 第三步：更新state（可能覆盖第一步的直接设置，但确保数据一致性）
      setProjects(data);
      
      // 验证state设置是否成功
      console.log('🔍 State verification - projects count in state:', data.length);
      
      // 如果数据为空但localStorage有数据，强制再次设置
      if (data.length === 0 && directCheck) {
        console.log('🚨 Emergency: Service returned empty but localStorage has data, using direct data');
        try {
          const emergencyProjects = JSON.parse(directCheck);
          if (Array.isArray(emergencyProjects) && emergencyProjects.length > 0) {
            setProjects(emergencyProjects);
            console.log('🚨 Emergency fallback successful:', emergencyProjects.length);
          }
        } catch (error) {
          console.error('🚨 Emergency fallback failed:', error);
        }
      }
      
      setError(null);
    } catch (error) {
      console.error('❌ fetchProjects error:', error);
      setError('Failed to load opportunities');
      
      // 错误情况下的最后保护：尝试直接从localStorage加载
      console.log('🛡️ Error recovery: attempting direct localStorage load...');
      try {
        const recoveryData = localStorage.getItem('edm_projects');
        if (recoveryData && recoveryData !== 'null' && recoveryData !== 'undefined') {
          const recoveryProjects = JSON.parse(recoveryData);
          if (Array.isArray(recoveryProjects) && recoveryProjects.length > 0) {
            setProjects(recoveryProjects);
            setError(null); // 清除错误，因为我们从localStorage恢复了数据
            console.log('🛡️ Error recovery successful:', recoveryProjects.length);
          }
        }
      } catch (recoveryError) {
        console.error('🛡️ Error recovery failed:', recoveryError);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 🛡️ 检查用户删除日志，如果用户删除了项目则不初始化数据
    const checkAndInitialize = () => {
      try {
        const deletedLog = JSON.parse(localStorage.getItem('deleted_projects_log') || '[]');
        if (deletedLog.length > 0) {
          console.log('🚫 NewProjectList: User deletion log found, skipping data initialization');
          console.log('🚫 Deleted projects:', deletedLog.map((log: any) => log.projectId));
          return;
        }
        
        // 只有在没有删除记录时才初始化
        console.log('✅ NewProjectList: No deletion log found, safe to initialize data');
        initializeData();
      } catch (error) {
        console.warn('⚠️ Error checking deletion log, proceeding with caution:', error);
        // 出错时不初始化，保持保守策略
      }
    };
    
    checkAndInitialize();

    // 清除任何旧的错误消息
    message.destroy();
    
    // 页面加载时验证localStorage数据完整性
    console.log('🔍 Page load - Verifying localStorage data integrity...');
    const rawData = localStorage.getItem('edm_projects');
    if (rawData) {
      try {
        const projects = JSON.parse(rawData);
        console.log('✅ localStorage verification: Found', projects.length, 'projects');
        console.log('📋 Project names:', projects.map((p: any) => p.name));
        
        // 确保数据格式正确
        if (!Array.isArray(projects)) {
          console.warn('⚠️ localStorage data is not an array, resetting...');
          localStorage.setItem('edm_projects', JSON.stringify([]));
        }
      } catch (error) {
        console.error('❌ localStorage data corrupted:', error);
        // 不自动清除，让用户决定
        message.warning('localStorage数据可能损坏，如果遇到问题请联系技术支持');
      }
    } else {
      console.log('⚠️ No localStorage data found on page load');
    }
    
    fetchProjects();
  }, []);

  // 监听路由状态变化，处理强制刷新
  useEffect(() => {
    console.log('🔍 Route state changed:', location.state);
    if (location.state?.forceRefresh && !location.state?.processed) {
      console.log('🔄 Detected forceRefresh from navigation state, reloading projects...');
      console.log('🔄 Last created project ID:', location.state?.lastCreatedProjectId);
      console.log('🔄 Timestamp:', location.state?.timestamp);
      
      // 标记为已处理，防止重复执行
      const processedState = { ...location.state, processed: true };
      
      // 立即获取项目列表查看当前状态
      console.log('📊 Before forceRefresh - checking current projects...');
      projectService.getAllProjects(false).then(currentProjects => {
        console.log('📊 Current projects before forceRefresh:', currentProjects.length);
        const foundProject = currentProjects.find(p => p.id === location.state?.lastCreatedProjectId);
        console.log('🔍 Last created project found before refresh:', !!foundProject);
        
        // 现在进行强制刷新
        fetchProjects(true); // 传递forceRefresh = true
      });
      
      // 清除state以避免重复刷新，但保留processed标记
      navigate('/opportunity', { replace: true, state: processedState });
    }
  }, [location.state?.forceRefresh, location.state?.timestamp, navigate]);

  // 初始化表格列宽调整功能
  useEffect(() => {
    const timer = setTimeout(() => {
      enableTableColumnResize('.project-list-container .ant-table');
    }, 200);
    
    return () => clearTimeout(timer);
  }, [projects]);

  // 获取唯一的国家列表
  const uniqueCountries = Array.from(new Set(projects.map(p => p.country).filter(Boolean)));

  // 过滤数据
  const filteredProjects = projects.filter(project => {
    const matchesSearch = !searchText || 
        project.name.toLowerCase().includes(searchText.toLowerCase()) ||
        project.client.toLowerCase().includes(searchText.toLowerCase()) ||
      project.projectId.toLowerCase().includes(searchText.toLowerCase());

      const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
      const matchesCountry = countryFilter === 'all' || project.country === countryFilter;
      const matchesTier = tierFilter === 'all' || project.tier === tierFilter;

    // 日期范围过滤
    let matchesDateRange = true;
    if (dateRange && dateRange[0] && dateRange[1]) {
      const projectDate = new Date(project.createdAt);
      const startDate = dateRange[0].toDate();
      const endDate = dateRange[1].toDate();
      // 设置结束日期为当天的23:59:59
      endDate.setHours(23, 59, 59, 999);
      matchesDateRange = projectDate >= startDate && projectDate <= endDate;
    }

    return matchesSearch && matchesStatus && matchesCountry && matchesTier && matchesDateRange;
  });

  // 🔍 **调试日志：检查过滤结果**
  console.log('🔍 DEBUG: Filtering projects...');
  console.log('🔍 DEBUG: Original projects count:', projects.length);
  console.log('🔍 DEBUG: Filtered projects count:', filteredProjects.length);
  console.log('🔍 DEBUG: Current filters:');
  console.log('  - searchText:', searchText);
  console.log('  - statusFilter:', statusFilter);
  console.log('  - countryFilter:', countryFilter);
  console.log('  - tierFilter:', tierFilter);
  console.log('  - dateRange:', dateRange);
  
  if (projects.length > 0 && filteredProjects.length === 0) {
    console.log('🚨 WARNING: Projects exist but filteredProjects is empty!');
    console.log('🚨 Checking first project filtering results:');
    const firstProject = projects[0];
    console.log('🚨 First project:', firstProject);
    
    const matchesSearch = !searchText || 
      firstProject.name.toLowerCase().includes(searchText.toLowerCase()) ||
      firstProject.client.toLowerCase().includes(searchText.toLowerCase()) ||
      firstProject.projectId.toLowerCase().includes(searchText.toLowerCase());
    console.log('🚨 Matches search:', matchesSearch);
    
    const matchesStatus = statusFilter === 'all' || firstProject.status === statusFilter;
    console.log('🚨 Matches status:', matchesStatus, `(${firstProject.status} vs ${statusFilter})`);
    
    const matchesCountry = countryFilter === 'all' || firstProject.country === countryFilter;
    console.log('🚨 Matches country:', matchesCountry, `(${firstProject.country} vs ${countryFilter})`);
    
    const matchesTier = tierFilter === 'all' || firstProject.tier === tierFilter;
    console.log('🚨 Matches tier:', matchesTier, `(${firstProject.tier} vs ${tierFilter})`);
  }

  // 删除项目
  const handleDelete = async (id: string) => {
    try {
      await projectService.deleteProject(id);
      message.success('Opportunity deleted successfully');
      fetchProjects(); // 刷新列表
    } catch (error) {
      console.error('Failed to delete project:', error);
      message.error('Failed to delete opportunity');
    }
  };

  // 团队成员/协同管理
  const handleTeamManagement = async (id: string, project: Project) => {
    try {
      setSelectedProject(project);
      setTeamModalVisible(true);
    } catch (error) {
      console.error('Failed to open team management:', error);
      message.error('Failed to open team management');
    }
  };

  // 分享按钮弹窗逻辑
  // 简约设计：简化分享功能
  const handleShareModal = (project: Project) => {
    message.info(`分享功能开发中：${project.name}`, 2);
  };

  // 项目空间弹窗逻辑
  const handleSpaceModal = (project: Project) => {
    setSpaceModal({visible: true, project});
  };



  // 用户空间文件上传
  const handleUserSpaceUpload = ({file, fileList}: any) => {
    setUserSpaceFiles([...fileList]);
  };

  // 客户空间文件上传
  const handleClientSpaceUpload = ({file, fileList}: any) => {
    setClientSpaceFiles([...fileList]);
  };

  // 复制空间链接
  const handleCopySpaceLink = (spaceType: 'user' | 'client') => {
    const baseUrl = `${window.location.origin}/project-space/${spaceModal.project?.id}`;
    const spaceUrl = `${baseUrl}/${spaceType}`;
    navigator.clipboard.writeText(spaceUrl);
    message.success(`${spaceType === 'user' ? 'User' : 'Client'} space link copied to clipboard`);
  };



  // 保存视图模式到本地存储
  const handleViewModeChange = (mode: 'table' | 'grid') => {
    setViewMode(mode);
    localStorage.setItem('project_list_view_mode', mode);
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'planning': { color: 'blue', text: 'Planning' },
      'in_progress': { color: 'orange', text: 'In Progress' },
      'completed': { color: 'green', text: 'Completed' },
      'on_hold': { color: 'red', text: 'On Hold' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color} style={{ fontSize: '12px' }}>{config.text}</Tag>;
  };

  // 规范化Probability显示格式
  const formatProbability = (probability: string | number | undefined) => {
    if (!probability) return '-';
    
    // 如果是字符串，直接转换
    if (typeof probability === 'string') {
      switch (probability.toUpperCase()) {
        case 'A': return 'A (80-100%)';
        case 'B': return 'B (60-80%)';
        case 'C': return 'C (40-60%)';
        case 'D': return 'D (<40%)';
        default: return probability;
      }
    }
    
    // 如果是数字，根据范围判断等级
    if (typeof probability === 'number') {
      if (probability >= 80) return 'A (80-100%)';
      if (probability >= 60) return 'B (60-80%)';
      if (probability >= 40) return 'C (40-60%)';
      return 'D (<40%)';
    }
    
    return '-';
  };

  // 表格列定义
  const allColumns = [
    {
      title: 'Opportunity ID',
      dataIndex: 'projectId',
      key: 'projectId',
      width: 160,
      sorter: (a: Project, b: Project) => (a.projectId || a.id || '').localeCompare(b.projectId || b.id || ''),
      className: 'no-wrap-header',
      render: (text: string, record: Project) => (
        <span style={{ fontSize: '12px' }}>{text || record.id || '-'}</span>
      ),
    },
    {
      title: 'Opportunity Name',
      dataIndex: 'name',
      key: 'name',
      width: 250,
      sorter: (a: Project, b: Project) => a.name.localeCompare(b.name),
      className: 'no-wrap-header',
      render: (text: string, record: Project) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/opportunity/${record.id}`)} 
          style={{ padding: 0, height: 'auto', fontSize: '12px' }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: 'Client',
      dataIndex: 'client',
      key: 'client',
      width: 180,
      sorter: (a: Project, b: Project) => a.client.localeCompare(b.client),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      width: 120,
      sorter: (a: Project, b: Project) => (a.country || '').localeCompare(b.country || ''),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text || '-'}</span>
      ),
    },
    {
      title: 'Tier',
      dataIndex: 'tier',
      key: 'tier',
      width: 100,
      sorter: (a: Project, b: Project) => (a.tier || '').localeCompare(b.tier || ''),
      render: (text: string) => {
        if (!text || text === '-') return <span style={{ fontSize: '12px' }}>-</span>;
        const colors = { 'SVIP': 'purple', 'VIP': 'gold', 'BA': 'blue', 'A': 'green' };
        return <Tag color={colors[text as keyof typeof colors] || 'default'} style={{ fontSize: '12px' }}>{text}</Tag>;
      },
    },
    {
      title: 'Revenue',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 120,
      align: 'right' as const,
      sorter: (a: Project, b: Project) => (a.revenue || 0) - (b.revenue || 0),
      render: (value: number) => (
        <span style={{ fontSize: '12px', color: value ? '#52c41a' : '#999' }}>
          {value ? `€${value.toLocaleString()}` : '-'}
        </span>
      ),
    },
    {
      title: 'Stage',
      dataIndex: 'stage',
      key: 'stage',
      width: 120,
      sorter: (a: Project, b: Project) => (a.stage || '').localeCompare(b.stage || ''),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text || '-'}</span>
      ),
    },
    {
      title: 'Probability',
      dataIndex: 'probability',
      key: 'probability',
      width: 110,
      align: 'right' as const,
      sorter: (a: Project, b: Project) => {
        const probA = typeof a.probability === 'string' ? 
          parseInt(a.probability.replace('%', '')) : (a.probability || 0);
        const probB = typeof b.probability === 'string' ? 
          parseInt(b.probability.replace('%', '')) : (b.probability || 0);
        return probA - probB;
      },
      render: (value: string | number) => {
        if (!value) return <span style={{ fontSize: '12px' }}>-</span>;
        const formattedProb = formatProbability(value);
        
        // 提取等级用于颜色判断
        const level = formattedProb.charAt(0);
        let color = 'default';
        switch (level) {
          case 'A': color = 'green'; break;
          case 'B': color = 'blue'; break;
          case 'C': color = 'orange'; break;
          case 'D': color = 'red'; break;
        }
        
        return <Tag color={color} style={{ fontSize: '12px' }}>{formattedProb}</Tag>;
      },
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      width: 120,
      sorter: (a: Project, b: Project) => (a.owner || '').localeCompare(b.owner || ''),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text || '-'}</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      sorter: (a: Project, b: Project) => a.status.localeCompare(b.status),
      render: (status: string) => renderStatusTag(status),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      sorter: (a: Project, b: Project) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      render: (date: string) => (
        <span style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleDateString()}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 160,
      render: (_: any, record: Project) => (
        <Space size="small">
          <Tooltip title="Team">
            <Button
              type="text"
              size="small"
              icon={<TeamOutlined style={{ fontSize: '12px' }} />}
              onClick={() => handleTeamManagement(record.id, record)}
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Tooltip title="Project Space">
            <Button
              type="text"
              size="small"
              icon={<CloudUploadOutlined style={{ fontSize: '12px' }} />}
              onClick={(e) => {
                e.stopPropagation();
                handleSpaceModal(record);
              }}
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => navigate(`/opportunity/edit/${record.id}`)}
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Popconfirm
              title="Delete opportunity"
              description="Are you sure you want to delete this opportunity?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                style={{ fontSize: '12px' }}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 过滤可见列
  const columns = allColumns.filter(column => visibleColumns[column.key as string]);

  // 列设置变更处理
  const handleColumnVisibilityChange = (columnKey: string, checked: boolean) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: checked
    }));
  };

  // 导出功能
  const handleExport = () => {
    // 创建CSV数据
    const headers = columns.map(col => col.title).join(',');
    const csvData = filteredProjects.map(project => 
      columns.map(col => {
        const key = col.dataIndex as keyof Project;
        let value = project[key];
        
        // 处理特殊字段的显示格式
        if (key === 'revenue' && value) {
          value = `€${(value as number).toLocaleString()}`;
        } else if (key === 'createdAt') {
          value = new Date(value as string).toLocaleDateString();
        } else if (key === 'probability' && value) {
          value = formatProbability(value as string | number);
        }
        
        return `"${value || '-'}"`;
      }).join(',')
    ).join('\n');

    const csvContent = `${headers}\n${csvData}`;
    
    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `opportunities_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('Data exported successfully');
  };

  // 渲染卡片视图
  const renderGridView = () => {
    return (
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
        gap: '20px',
        padding: '16px 0'
      }}>
        {filteredProjects.map((project) => (
          <Card
            key={project.id}
            size="small"
            hoverable
            className="project-card"
            onClick={() => navigate(`/opportunity/${project.id}`)}
            style={{
              cursor: 'pointer',
              border: '1px solid var(--border, #e8e8e8)',
              borderRadius: 'var(--radius-md, 8px)',
              transition: 'all 0.2s ease',
              minHeight: '260px',
              backgroundColor: '#fafafa',
              fontFamily: 'var(--font-family)'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            {/* 头部信息 */}
            <div style={{ marginBottom: '16px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'flex-start',
                marginBottom: '8px'
              }}>
                <h4 style={{ 
                  margin: 0,
                  fontSize: 'var(--font-size-md, 16px)',
                  fontWeight: 600,
                  color: 'var(--text-primary, #2c3e50)',
                  lineHeight: '1.4',
                  flex: 1,
                  marginRight: '12px',
                  fontFamily: 'var(--font-family)'
                }}>
                  {project.name}
                </h4>
                <Tooltip title="Share Project">
                  <Button
                    type="text"
                    size="small"
                    icon={<ShareAltOutlined style={{ color: '#1890ff' }} />}
                    onClick={e => { e.stopPropagation(); handleShareModal(project); }}
                    style={{ 
                      width: '24px',
                      height: '24px',
                      minWidth: '24px',
                      padding: 0,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  />
                </Tooltip>
              </div>
              
              <div style={{ 
                fontSize: 'var(--font-size-xs, 12px)',
                color: 'var(--text-secondary, #7f8c8d)',
                fontWeight: 500,
                letterSpacing: '0.5px',
                fontFamily: 'var(--font-family)'
              }}>
                {project.projectId}
              </div>
            </div>

            {/* Main Information */}
            <div style={{ marginBottom: '16px' }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '8px'
              }}>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  color: 'var(--text-secondary, #7f8c8d)',
                  fontFamily: 'var(--font-family)'
                }}>
                  Client
                </span>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  fontWeight: 500,
                  color: 'var(--text-primary, #2c3e50)',
                  fontFamily: 'var(--font-family)'
                }}>
                  {project.client}
                </span>
              </div>
              
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '8px'
              }}>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  color: 'var(--text-secondary, #7f8c8d)',
                  fontFamily: 'var(--font-family)'
                }}>
                  Country
                </span>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  fontWeight: 500,
                  color: 'var(--text-primary, #2c3e50)',
                  fontFamily: 'var(--font-family)'
                }}>
                  {project.country || '-'}
                </span>
              </div>

              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '8px'
              }}>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  color: 'var(--text-secondary, #7f8c8d)',
                  fontFamily: 'var(--font-family)'
                }}>
                  Status
                </span>
                <div>
                  {renderStatusTag(project.status)}
                </div>
              </div>

              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: '8px'
              }}>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  color: 'var(--text-secondary, #7f8c8d)',
                  fontFamily: 'var(--font-family)'
                }}>
                  Probability
                </span>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  fontWeight: 500,
                  color: 'var(--text-primary, #2c3e50)',
                  fontFamily: 'var(--font-family)'
                }}>
                  {formatProbability(project.probability)}
                </span>
              </div>

              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between'
              }}>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  color: 'var(--text-secondary, #7f8c8d)',
                  fontFamily: 'var(--font-family)'
                }}>
                  Revenue
                </span>
                <span style={{ 
                  fontSize: 'var(--font-size-sm, 14px)',
                  fontWeight: 500,
                  color: project.revenue && project.revenue > 0 ? '#27ae60' : 'var(--text-tertiary, #95a5a6)',
                  fontFamily: 'var(--font-family)'
                }}>
                  {project.revenue && project.revenue > 0 ? `€${project.revenue.toLocaleString()}` : '-'}
                </span>
              </div>
            </div>

            {/* Bottom Action Area */}
            <div style={{ 
              marginTop: '16px',
              paddingTop: '12px',
              borderTop: '1px solid var(--border, #ecf0f1)',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div style={{ 
                fontSize: 'var(--font-size-xs, 12px)', 
                color: 'var(--text-tertiary, #95a5a6)',
                fontFamily: 'var(--font-family)'
              }}>
                {project.owner || 'Unassigned'}
              </div>
              
              <Space size="small">
                <Tooltip title="Team">
                  <Button
                    type="text"
                    size="small"
                    icon={<TeamOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTeamManagement(project.id, project);
                    }}
                    style={{
                      fontSize: 'var(--font-size-xs, 12px)',
                      fontFamily: 'var(--font-family)'
                    }}
                  />
                </Tooltip>
                <Tooltip title="Project Space">
                  <Button
                    type="text"
                    size="small"
                    icon={<CloudUploadOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSpaceModal(project);
                    }}
                    style={{
                      fontSize: 'var(--font-size-xs, 12px)',
                      fontFamily: 'var(--font-family)'
                    }}
                  />
                </Tooltip>
                <Tooltip title="Edit">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/opportunity/edit/${project.id}`);
                    }}
                    style={{
                      fontSize: 'var(--font-size-xs, 12px)',
                      fontFamily: 'var(--font-family)'
                    }}
                  />
                </Tooltip>
                <Tooltip title="Delete">
                  <Popconfirm
                    title="Delete opportunity"
                    description="Are you sure you want to delete this opportunity?"
                    onConfirm={(e) => {
                      e?.stopPropagation();
                      handleDelete(project.id);
                    }}
                    okText="Yes"
                    cancelText="No"
                    onCancel={(e) => e?.stopPropagation()}
                  >
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => e.stopPropagation()}
                      style={{
                        fontSize: 'var(--font-size-xs, 12px)',
                        fontFamily: 'var(--font-family)'
                      }}
                    />
                  </Popconfirm>
                </Tooltip>
              </Space>
            </div>
          </Card>
        ))}
      </div>
    );
  };



  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和新建按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '16px',
          fontWeight: 600,
          margin: 0,
          color: 'var(--text-primary)',
          fontFamily: 'var(--font-family)'
        }}>
          Lead to Cash
        </h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/opportunity/create')}
          size="large"
        >
          New Opportunity
        </Button>
      </div>

      {/* 搜索和筛选区域 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space wrap size="middle">
            <Input
            placeholder="Search opportunities..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
            />

            <DatePicker.RangePicker
              style={{ width: 250 }}
              value={dateRange}
              onChange={setDateRange}
              placeholder={['Start Date', 'End Date']}
              allowClear
            />

            <Select
              style={{ width: 140 }}
              value={statusFilter}
            onChange={setStatusFilter}
            placeholder="All Status"
            >
            <Option value="all">All Status</Option>
              <Option value="planning">Planning</Option>
              <Option value="in_progress">In Progress</Option>
              <Option value="completed">Completed</Option>
              <Option value="on_hold">On Hold</Option>
            </Select>

            <Select
              style={{ width: 140 }}
              value={countryFilter}
            onChange={setCountryFilter}
            placeholder="All Countries"
            >
              <Option value="all">All Countries</Option>
            {uniqueCountries.map(country => (
                <Option key={country} value={country}>{country}</Option>
              ))}
            </Select>

            <Select
              style={{ width: 140 }}
              value={tierFilter}
            onChange={setTierFilter}
            placeholder="All Tiers"
            >
              <Option value="all">All Tiers</Option>
              <Option value="SVIP">SVIP</Option>
              <Option value="VIP">VIP</Option>
              <Option value="BA">BA</Option>
              <Option value="A">A</Option>
            </Select>

          <Button onClick={() => {
            setSearchText('');
            setStatusFilter('all');
            setCountryFilter('all');
            setTierFilter('all');
            setDateRange(null);
          }}>
            Clear Filters
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '16px', fontWeight: 600 }}>Opportunities</span>
            
            {/* 功能快捷键 */}
            <Space size="small">
              <Tooltip title="Refresh">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchProjects(true)}
                  loading={loading}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Export">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Column Settings">
                <Button
                  icon={<MenuOutlined />}
                  onClick={() => setColumnSettingsVisible(true)}
                  size="small"
                />
              </Tooltip>

              {/* 视图切换按钮 */}
              <Space.Compact size="small">
                <Tooltip title="Table View">
                  <Button
                    icon={<UnorderedListOutlined />}
                    type={viewMode === 'table' ? 'primary' : 'default'}
                    onClick={() => handleViewModeChange('table')}
                  />
                </Tooltip>
                <Tooltip title="Grid View">
                  <Button
                    icon={<AppstoreOutlined />}
                    type={viewMode === 'grid' ? 'primary' : 'default'}
                    onClick={() => handleViewModeChange('grid')}
                  />
                </Tooltip>
              </Space.Compact>
            </Space>
          </div>
        }
      >
        {viewMode === 'table' ? (
          <div className="project-list-container">
            <Table
              columns={columns}
              dataSource={filteredProjects}
              rowKey={(record) => record.id || record.projectId || Math.random().toString()}
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} of ${total} opportunities`,
                defaultPageSize: 20,
                pageSizeOptions: ['10', '20', '50', '100'],
              }}
              scroll={{ x: 1400 }}
              size="middle"
              bordered
            />
          </div>
        ) : (
          renderGridView()
        )}
      </Card>

      {/* 列设置抽屉 */}
      <Drawer
        title="Column Settings"
        placement="right"
        onClose={() => setColumnSettingsVisible(false)}
        open={columnSettingsVisible}
        width={300}
      >
        <div style={{ padding: '8px 0' }}>
          <div style={{ marginBottom: '16px', fontWeight: 500 }}>
            Select columns to display:
                        </div>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {allColumns.map(column => (
              <Checkbox
                key={column.key}
                checked={visibleColumns[column.key as string]}
                onChange={(e) => handleColumnVisibilityChange(column.key as string, e.target.checked)}
                disabled={column.key === 'actions'} // Actions列始终显示
              >
                {column.title}
              </Checkbox>
            ))}
          </Space>
          <div style={{ marginTop: '24px', padding: '16px 0', borderTop: '1px solid #f0f0f0' }}>
            <Button 
              onClick={() => {
                const defaultVisible = Object.keys(visibleColumns).reduce((acc, key) => {
                  acc[key] = true;
                  return acc;
                }, {} as {[key: string]: boolean});
                setVisibleColumns(defaultVisible);
              }}
              style={{ marginRight: '8px' }}
            >
              Show All
            </Button>
            <Button 
              onClick={() => {
                const minimalVisible = Object.keys(visibleColumns).reduce((acc, key) => {
                  acc[key] = ['name', 'client', 'status', 'actions'].includes(key);
                  return acc;
                }, {} as {[key: string]: boolean});
                setVisibleColumns(minimalVisible);
              }}
            >
              Minimal View
            </Button>
          </div>
        </div>
      </Drawer>

      {/* 团队管理模态框 */}
      {selectedProject && (
        <TeamManagementModal
          visible={teamModalVisible}
          onCancel={() => {
            setTeamModalVisible(false);
            setSelectedProject(null);
          }}
          project={selectedProject}
        />
      )}

      {/* 项目空间Modal */}
      <Modal
        open={spaceModal.visible}
        title={`Project Space - ${spaceModal.project?.name || ''}`}
        onCancel={() => setSpaceModal({visible: false, project: null})}
        footer={null}
        width={700}
      >
        {/* Space Tabs */}
        <div style={{ marginTop: 36, marginBottom: 24 }}>
          <Space size={0}>
            <Button 
              type={activeSpaceTab === 'user' ? 'primary' : 'text'}
              onClick={() => setActiveSpaceTab('user')}
              icon={<UserOutlined />}
            >
              User Space
            </Button>
            <Button 
              type={activeSpaceTab === 'client' ? 'primary' : 'text'}
              onClick={() => setActiveSpaceTab('client')}
              icon={<TeamOutlined />}
            >
              Client Space
            </Button>
          </Space>
        </div>

        {/* Space Content */}
        {activeSpaceTab === 'user' && (
          <div>
            {/* User Space Header */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              marginBottom: 16,
              padding: '12px 16px',
              backgroundColor: '#f8f9fa',
              borderRadius: 8
            }}>
              <div>
                <div style={{ fontWeight: 500, fontSize: 14 }}>User Space (Internal)</div>
                <div style={{ fontSize: 12, color: '#666' }}>For internal team collaboration and documents</div>
              </div>
              <Button 
                type="primary" 
                size="small"
                icon={<CopyOutlined />}
                onClick={() => handleCopySpaceLink('user')}
              >
                Share Link
              </Button>
            </div>

            {/* User Space Upload */}
            <Upload.Dragger
              fileList={userSpaceFiles}
              onChange={handleUserSpaceUpload}
              multiple
              showUploadList
              beforeUpload={(file) => {
                const isLt20M = file.size / 1024 / 1024 < 20;
                if (!isLt20M) {
                  message.error('File must be smaller than 20MB!');
                  return false;
                }
                return true;
              }}
              customRequest={({file, onSuccess}) => setTimeout(() => onSuccess && onSuccess('ok'), 500)}
              style={{ marginBottom: 16 }}
            >
              <p className="ant-upload-drag-icon">
                <CloudUploadOutlined style={{ color: '#FF7A00', fontSize: 48 }} />
              </p>
              <p className="ant-upload-text">Click or drag files to upload internal documents (最大上传文件大小 20MB)</p>
              <p className="ant-upload-hint">Support multiple files, max 20MB each</p>
            </Upload.Dragger>

            {/* User Space Files */}
            <div>
              <h4 style={{ marginBottom: 12 }}>Uploaded Documents</h4>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {userSpaceFiles.length === 0 && (
                  <li style={{ color: '#888', fontStyle: 'italic', padding: '8px 0' }}>No documents uploaded yet</li>
                )}
                {userSpaceFiles.map(file => (
                  <li key={file.uid} style={{ marginBottom: 8, padding: '8px 12px', backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                    <a href={file.url || '#'} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                      📄 {file.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {activeSpaceTab === 'client' && (
          <div>
            {/* Client Space Header */}
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              marginBottom: 16,
              padding: '12px 16px',
              backgroundColor: '#f0f9ff',
              borderRadius: 8
            }}>
              <div>
                <div style={{ fontWeight: 500, fontSize: 14 }}>Client Space (External)</div>
                <div style={{ fontSize: 12, color: '#666' }}>For sharing documents with clients</div>
              </div>
              <Button 
                type="primary" 
                size="small"
                icon={<CopyOutlined />}
                onClick={() => handleCopySpaceLink('client')}
              >
                Share Link
              </Button>
            </div>

            {/* Client Space Upload */}
            <Upload.Dragger
              fileList={clientSpaceFiles}
              onChange={handleClientSpaceUpload}
              multiple
              showUploadList
              beforeUpload={(file) => {
                const isLt20M = file.size / 1024 / 1024 < 20;
                if (!isLt20M) {
                  message.error('File must be smaller than 20MB!');
                  return false;
                }
                return true;
              }}
              customRequest={({file, onSuccess}) => setTimeout(() => onSuccess && onSuccess('ok'), 500)}
              style={{ marginBottom: 16 }}
            >
              <p className="ant-upload-drag-icon">
                <CloudUploadOutlined style={{ color: '#FF7A00', fontSize: 48 }} />
              </p>
              <p className="ant-upload-text">Click or drag files to upload client documents (最大上传文件大小 20MB)</p>
              <p className="ant-upload-hint">Support multiple files, max 20MB each</p>
            </Upload.Dragger>

            {/* Client Space Files */}
            <div>
              <h4 style={{ marginBottom: 12 }}>Shared Documents</h4>
              <ul style={{ listStyle: 'none', padding: 0 }}>
                {clientSpaceFiles.length === 0 && (
                  <li style={{ color: '#888', fontStyle: 'italic', padding: '8px 0' }}>No documents shared yet</li>
                )}
                {clientSpaceFiles.map(file => (
                  <li key={file.uid} style={{ marginBottom: 8, padding: '8px 12px', backgroundColor: '#f0f9ff', borderRadius: 4 }}>
                    <a href={file.url || '#'} target="_blank" rel="noopener noreferrer" style={{ textDecoration: 'none' }}>
                      📄 {file.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default NewProjectList;
