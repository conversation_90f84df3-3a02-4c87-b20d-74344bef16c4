import { Router } from 'express';
import { getRepository } from 'typeorm';
import { User } from '../entities/User';
import jwt from 'jsonwebtoken';
import { verifyGoogleToken } from '../services/google-auth.service';
import { verifyAppleToken } from '../services/apple-auth.service';
import { verifyFacebookToken } from '../services/facebook-auth.service';

const router = Router();

// Register new user
/*
router.post('/register', async (req, res) => {
  try {
    const userRepository = getRepository(User);

    // Check if user already exists
    const existingUser = await userRepository.findOne({ where: { email: req.body.email } });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // 只取单个对象
    const body = Array.isArray(req.body) ? req.body[0] : req.body;
    if (!body || typeof body !== 'object') {
      return res.status(400).json({ message: 'Invalid registration data' });
    }
    const user = userRepository.create(body);
    if (user && typeof (user as any).hashPassword === 'function') {
      (user as any).hashPassword();
    }
    const savedUser = await userRepository.save(user);
    if (!savedUser) {
      return res.status(500).json({ message: 'Failed to save user' });
    }
    // Generate JWT token
    const token = jwt.sign(
      { userId: (savedUser as any).id, email: (savedUser as any).email },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
    res.status(201).json({ user: savedUser, token });
    return;
  } catch (error) {
    res.status(500).json({ message: 'Error registering user', error });
    return;
  }
});
*/

// Login user
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    const userRepository = getRepository(User);

    // Check if user exists
    const user = await userRepository.findOne({ where: { email } });
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Check password
    if (!user.checkPassword || !user.checkPassword(password)) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 创建用户对象（不包含密码）
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({ user: userResponse, token });
    return;
  } catch (error) {
    res.status(500).json({ message: 'Error logging in', error });
    return;
  }
});

// Google 登录
router.post('/google', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // 验证 Google 令牌
    const googleUserInfo = await verifyGoogleToken(token);

    if (!googleUserInfo.verified) {
      return res.status(401).json({ message: 'Email not verified with Google' });
    }

    const userRepository = getRepository(User);

    // 查找是否已存在使用此 Google ID 的用户
    let user = await userRepository.findOne({ where: { googleId: googleUserInfo.googleId } });

    // 如果没有找到使用 Google ID 的用户，尝试通过邮箱查找
    if (!user) {
      user = await userRepository.findOne({ where: { email: googleUserInfo.email } });

      // 如果找到了邮箱匹配的用户，更新其 Google ID
      if (user) {
        user.googleId = googleUserInfo.googleId;
        await userRepository.save(user);
      } else {
        // 如果用户不存在，创建新用户
        user = userRepository.create({
          username: googleUserInfo.username,
          email: googleUserInfo.email,
          googleId: googleUserInfo.googleId,
          // 为 Google 登录用户生成随机密码
          password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
        });

        if (user && typeof user.hashPassword === 'function') {
          user.hashPassword();
        }
        await userRepository.save(user);
      }
    }

    if (!user) {
      return res.status(500).json({ message: 'Failed to create or find user' });
    }

    // 生成 JWT 令牌
    const jwtToken = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 创建用户响应对象（不包含密码）
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({ user: userResponse, token: jwtToken });
    return;
  } catch (error) {
    console.error('Google login error:', error);
    res.status(500).json({ message: 'Error during Google login', error: error.message });
    return;
  }
});

// Apple ID 登录
router.post('/apple', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // 验证 Apple ID 令牌
    const appleUserInfo = await verifyAppleToken(token);

    const userRepository = getRepository(User);

    // 查找是否已存在使用此 Apple ID 的用户
    let user = await userRepository.findOne({ where: { appleId: appleUserInfo.appleId } });

    // 如果没有找到使用 Apple ID 的用户，尝试通过邮箱查找
    if (!user && appleUserInfo.email) {
      user = await userRepository.findOne({ where: { email: appleUserInfo.email } });

      // 如果找到了邮箱匹配的用户，更新其 Apple ID
      if (user) {
        user.appleId = appleUserInfo.appleId;
        await userRepository.save(user);
      } else {
        // 如果用户不存在，创建新用户
        user = userRepository.create({
          username: appleUserInfo.username,
          email: appleUserInfo.email,
          appleId: appleUserInfo.appleId,
          // 为 Apple ID 登录用户生成随机密码
          password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
        });

        if (user && typeof user.hashPassword === 'function') {
          user.hashPassword();
        }
        await userRepository.save(user);
      }
    }

    if (!user) {
      return res.status(500).json({ message: 'Failed to create or find user' });
    }

    // 生成 JWT 令牌
    const jwtToken = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 创建用户响应对象（不包含密码）
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({ user: userResponse, token: jwtToken });
    return;
  } catch (error) {
    console.error('Apple login error:', error);
    res.status(500).json({ message: 'Error during Apple login', error: error.message });
    return;
  }
});

// Facebook 登录
router.post('/facebook', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({ message: 'Token is required' });
    }

    // 验证 Facebook 令牌
    const facebookUserInfo = await verifyFacebookToken(token);

    const userRepository = getRepository(User);

    // 查找是否已存在使用此 Facebook ID 的用户
    let user = await userRepository.findOne({ where: { facebookId: facebookUserInfo.facebookId } });

    // 如果没有找到使用 Facebook ID 的用户，尝试通过邮箱查找
    if (!user && facebookUserInfo.email) {
      user = await userRepository.findOne({ where: { email: facebookUserInfo.email } });

      // 如果找到了邮箱匹配的用户，更新其 Facebook ID
      if (user) {
        user.facebookId = facebookUserInfo.facebookId;
        await userRepository.save(user);
      } else {
        // 如果用户不存在，创建新用户
        user = userRepository.create({
          username: facebookUserInfo.username,
          email: facebookUserInfo.email,
          facebookId: facebookUserInfo.facebookId,
          // 为 Facebook 登录用户生成随机密码
          password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
        });

        if (user && typeof user.hashPassword === 'function') {
          user.hashPassword();
        }
        await userRepository.save(user);
      }
    }

    if (!user) {
      return res.status(500).json({ message: 'Failed to create or find user' });
    }

    // 生成 JWT 令牌
    const jwtToken = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // 创建用户响应对象（不包含密码）
    const userResponse = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.json({ user: userResponse, token: jwtToken });
    return;
  } catch (error) {
    console.error('Facebook login error:', error);
    res.status(500).json({ message: 'Error during Facebook login', error: error.message });
    return;
  }
});

export default router;