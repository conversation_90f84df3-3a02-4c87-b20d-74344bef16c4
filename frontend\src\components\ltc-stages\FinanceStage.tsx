import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Row, 
  Col, 
  Button, 
  Table, 
  Tag, 
  Statistic, 
  Progress, 
  Upload, 
  Tabs,
  Space,
  Modal,
  Typography,
  List,
  Divider,
  Timeline
} from 'antd';
import { 
  CloudUploadOutlined, 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  ExclamationCircleOutlined, 
  RightOutlined,
  DollarOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  BankOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  MoneyCollectOutlined,
  DownloadOutlined,
  CheckCircleOutlined,

  CreditCardOutlined,
  CalendarOutlined,
  RiseOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Text } = Typography;

interface FinanceStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

// 发票接口
interface Invoice {
  id: string;
  number: string;
  date: string;
  amount: number;
  status: string;
  dueDate: string;
  description: string;
  customer: string;
  paymentDate: string | null;
}

// 付款记录接口
interface Payment {
  id: string;
  invoiceNumber: string;
  amount: number;
  date: string;
  method: string;
  reference: string;
  status: string;
}

// 成本项接口
interface Cost {
  id: string;
  category: string;
  description: string;
  amount: number;
  percentage: number;
}

const FinanceStage: React.FC<FinanceStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [invoiceModalVisible, setInvoiceModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  // 财务数据 - 新项目从空白开始
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  // 付款记录 - 新项目从空白开始
  const [payments, setPayments] = useState<Payment[]>([]);

  // 成本分解 - 新项目从空白开始
  const [costs, setCosts] = useState<Cost[]>([]);

  const handleSave = (values: any) => {
    if (onSave) {
      onSave({
        ...values,
        invoices,
        payments,
        costs,
        type: 'finance',
        status: 'active',
        updatedAt: new Date().toISOString()
      });
    }
  };

  const handleProceed = () => {
    if (onProceed) {
      onProceed('completion');
    }
  };

  // 计算财务摘要
  const calculateFinancialSummary = () => {
    const totalValue = invoices.reduce((sum, invoice) => sum + invoice.amount, 0);
    const receivedValue = invoices.filter(invoice => invoice.status === 'paid').reduce((sum, invoice) => sum + invoice.amount, 0);
    const pendingValue = invoices.filter(invoice => invoice.status === 'pending').reduce((sum, invoice) => sum + invoice.amount, 0);
    const upcomingValue = invoices.filter(invoice => invoice.status === 'upcoming').reduce((sum, invoice) => sum + invoice.amount, 0);
    const totalCosts = costs.reduce((sum, cost) => sum + cost.amount, 0);
    const profit = totalValue - totalCosts;
    
    // 修复：防止除零错误
    const profitMargin = totalValue > 0 ? ((profit / totalValue) * 100).toFixed(1) : '0.0';
    const receivedPercentage = totalValue > 0 ? Math.round((receivedValue / totalValue) * 100) : 0;

    return {
      totalValue,
      receivedValue,
      pendingValue,
      upcomingValue,
      totalCosts,
      profit,
      profitMargin,
      receivedPercentage
    };
  };

  const summary = calculateFinancialSummary();

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'paid':
      case 'confirmed':
        return <Tag color="success">Paid</Tag>;
      case 'pending':
        return <Tag color="warning">Pending</Tag>;
      case 'overdue':
        return <Tag color="error">Overdue</Tag>;
      case 'upcoming':
        return <Tag color="default">Upcoming</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const invoiceColumns = [
    {
      title: 'Invoice #',
      dataIndex: 'number',
      key: 'number',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `€${amount.toLocaleString()}`,
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} size="small">View</Button>
          <Button type="link" icon={<EditOutlined />} size="small">Edit</Button>
        </Space>
      ),
    },
  ];

  const paymentColumns = [
    {
      title: 'Invoice #',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `€${amount.toLocaleString()}`,
    },
    {
      title: 'Payment Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Method',
      dataIndex: 'method',
      key: 'method',
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_: any, record: any) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} size="small">View</Button>
          <Button type="link" icon={<EditOutlined />} size="small">Edit</Button>
        </Space>
      ),
    },
  ];

  const costColumns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `€${amount.toLocaleString()}`,
    },
    {
      title: 'Percentage',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage: number) => `${percentage}%`,
    },
  ];

  return (
    <div className="ltc-stage-content">
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h2 className="ltc-stage-title">Finance Management</h2>
      </div>

      {/* Tab导航 */}
      <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
        {/* 财务概览标签页 */}
        <TabPane 
          tab={
            <span>
              <BarChartOutlined />
              Overview
            </span>
          } 
          key="overview"
        >
          {/* 关键财务指标 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Total Revenue"
                  value={summary.totalValue}
                  precision={0}
                  prefix="€"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                />
                <Progress 
                  percent={summary.receivedPercentage} 
                  size="small" 
                  status="active" 
                  style={{ marginTop: 8 }} 
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Received"
                  value={summary.receivedValue}
                  precision={0}
                  prefix="€"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Pending"
                  value={summary.pendingValue}
                  precision={0}
                  prefix="€"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Profit Margin"
                  value={summary.profitMargin}
                  precision={1}
                  suffix="%"
                />
              </Card>
            </Col>
          </Row>

          {/* 收入 vs 成本对比 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={12}>
              <Card className="ltc-stage-card" title="Revenue Breakdown">
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Total Contract Value: €{summary.totalValue.toLocaleString()}</Text>
                </div>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text>Received: €{summary.receivedValue.toLocaleString()}</Text>
                    <Progress percent={Math.round((summary.receivedValue / summary.totalValue) * 100)} />
                  </div>
                  <div>
                    <Text>Pending: €{summary.pendingValue.toLocaleString()}</Text>
                    <Progress percent={Math.round((summary.pendingValue / summary.totalValue) * 100)} />
                  </div>
                  <div>
                    <Text>Upcoming: €{summary.upcomingValue.toLocaleString()}</Text>
                    <Progress percent={Math.round((summary.upcomingValue / summary.totalValue) * 100)} />
                  </div>
                </Space>
              </Card>
            </Col>
            <Col xs={24} sm={12}>
              <Card className="ltc-stage-card" title="Cost Analysis">
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Total Project Cost: €{summary.totalCosts.toLocaleString()}</Text>
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>Estimated Profit: €{summary.profit.toLocaleString()}</Text>
                </div>
                                 <Timeline>
                   {costs.slice(0, 3).map(cost => (
                     <Timeline.Item key={cost.id}>
                       <Text>{cost.category}: €{cost.amount.toLocaleString()} ({cost.percentage}%)</Text>
                     </Timeline.Item>
                   ))}
                 </Timeline>
              </Card>
            </Col>
          </Row>

          {/* 最近财务活动 */}
          <Card className="ltc-stage-card" title="Recent Financial Activities">
            <Timeline>
              <Timeline.Item color="green">
                <Text strong>Payment Received</Text>
                <br />
                <Text type="secondary">€450,000 received for INV-2024-001 - 5 days ago</Text>
              </Timeline.Item>
              <Timeline.Item color="blue">
                <Text strong>Invoice Generated</Text>
                <br />
                <Text type="secondary">INV-2024-002 created for planning phase - 10 days ago</Text>
              </Timeline.Item>
              <Timeline.Item>
                <Text strong>Cost Updated</Text>
                <br />
                <Text type="secondary">Infrastructure costs revised - 15 days ago</Text>
              </Timeline.Item>
            </Timeline>
          </Card>
        </TabPane>

        {/* 发票管理标签页 */}
        <TabPane 
          tab={
            <span>
              <FileTextOutlined />
              Invoices
            </span>
          } 
          key="invoices"
        >
          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setInvoiceModalVisible(true)}>
                Create Invoice
              </Button>
              <Button onClick={handleSave}>Save Changes</Button>
            </Space>
          </div>
          
          <Card className="ltc-stage-card">
            <Table
              columns={invoiceColumns}
              dataSource={invoices}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        {/* 支付记录标签页 */}
        <TabPane 
          tab={
            <span>
              <MoneyCollectOutlined />
              Payments
            </span>
          } 
          key="payments"
        >
          <div style={{ marginBottom: '16px' }}>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={() => setPaymentModalVisible(true)}>
                Record Payment
              </Button>
              <Button onClick={handleSave}>Save Changes</Button>
            </Space>
          </div>
          
          <Card className="ltc-stage-card">
            <Table
              columns={paymentColumns}
              dataSource={payments}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        {/* 成本分析标签页 */}
        <TabPane 
          tab={
            <span>
              <DollarOutlined />
              Cost Analysis
            </span>
          } 
          key="costs"
        >
          <Card className="ltc-stage-card" title="Project Cost Breakdown">
            <Table
              columns={costColumns}
              dataSource={costs}
              rowKey="id"
              pagination={false}
              size="small"
              summary={() => (
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2}>
                    <Text strong>Total</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <Text strong>€{summary.totalCosts.toLocaleString()}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3}>
                    <Text strong>100%</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 进入下一阶段按钮 */}
      <div style={{ marginTop: '24px', textAlign: 'center' }}>
        <Button
          type="primary"
          size="large"
          icon={<RightOutlined />}
          onClick={handleProceed}
          className="ltc-proceed-button"
        >
          Complete Project
        </Button>
      </div>

      {/* 创建发票模态框 */}
      <Modal
        title="Create Invoice"
        visible={invoiceModalVisible}
        onCancel={() => setInvoiceModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form layout="vertical">
          <Form.Item name="customer" label="Customer" rules={[{ required: true }]}>
            <Select placeholder="Select customer">
              <Option value="Acme Corporation">Acme Corporation</Option>
              <Option value="TechStart Inc">TechStart Inc</Option>
              <Option value="Global Systems">Global Systems</Option>
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description" rules={[{ required: true }]}>
            <Input placeholder="Enter invoice description" />
          </Form.Item>
          <Form.Item name="amount" label="Amount (€)" rules={[{ required: true }]}>
            <Input type="number" placeholder="Enter amount" />
          </Form.Item>
          <Form.Item name="dueDate" label="Due Date" rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Additional notes" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary">Create Invoice</Button>
              <Button onClick={() => setInvoiceModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 记录支付模态框 */}
      <Modal
        title="Record Payment"
        visible={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form layout="vertical">
          <Form.Item name="invoiceNumber" label="Invoice Number" rules={[{ required: true }]}>
            <Select placeholder="Select invoice">
              {invoices.filter(inv => inv.status === 'pending').map(inv => (
                <Option key={inv.id} value={inv.number}>{inv.number} - €{inv.amount.toLocaleString()}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="amount" label="Payment Amount (€)" rules={[{ required: true }]}>
            <Input type="number" placeholder="Enter payment amount" />
          </Form.Item>
          <Form.Item name="date" label="Payment Date" rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="method" label="Payment Method" rules={[{ required: true }]}>
            <Select placeholder="Select payment method">
              <Option value="Bank Transfer">Bank Transfer</Option>
              <Option value="Wire Transfer">Wire Transfer</Option>
              <Option value="Credit Card">Credit Card</Option>
              <Option value="Check">Check</Option>
            </Select>
          </Form.Item>
          <Form.Item name="reference" label="Reference Number">
            <Input placeholder="Enter reference number" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary">Record Payment</Button>
              <Button onClick={() => setPaymentModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FinanceStage;
