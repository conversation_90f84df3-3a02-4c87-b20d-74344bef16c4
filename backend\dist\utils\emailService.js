"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendInvitationEmail = void 0;
async function sendInvitationEmail(email, projectName, invitationToken, message) {
    console.log(`
====== 邀请邮件 ======
收件人: ${email}
项目: ${projectName}
邀请令牌: ${invitationToken}
消息: ${message || '无'}
邀请链接: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/invitation?token=${invitationToken}
====================
  `);
    await new Promise(resolve => setTimeout(resolve, 100));
}
exports.sendInvitationEmail = sendInvitationEmail;
//# sourceMappingURL=emailService.js.map