import React, { useState, useEffect } from 'react';
import './AcceptanceStage.css';
import dayjs from 'dayjs';
import {
  Card,
  Row,
  Col,
  Progress,
  Button,
  Table,
  Tag,
  Tabs,
  Form,
  Input,
  Select,
  DatePicker,
  Space,
  Modal,
  message,
  Tooltip,
  Upload,
  Steps,
  AutoComplete
} from 'antd';
import {
  RightOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
  EditOutlined,
  EyeOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined,
  BugOutlined,
  DeleteOutlined,
  SaveOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;
const { Step } = Steps;

interface AcceptanceStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

// 验收项接口
interface AcceptanceItem {
  id: string;
  category: string;
  description: string;
  criteria: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected';
  reviewer: string;
  testDate?: string;
  comments?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// 缺陷项接口
interface DefectItem {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assignedTo: string;
  reportedBy: string;
  reportedDate: string;
  resolvedDate?: string;
  category: string;
}

// 文档接口
interface AcceptanceDocument {
  id: string;
  name: string;
  type: string;
  uploadDate: string;
  uploadedBy: string;
  status: 'draft' | 'under_review' | 'approved' | 'rejected';
  version: string;
  size: string;
  notes?: string;
  description?: string;
  fileName?: string;
  fileUrl?: string;
}

// 团队成员接口 - 复用ExecutionStage的结构
interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  workload: number;
  availability: 'available' | 'busy' | 'unavailable';
}

const AcceptanceStage: React.FC<AcceptanceStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [form] = Form.useForm();
  const [criteriaForm] = Form.useForm();
  const [defectForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('testing');
  
  // Progress流转状态管理
  const [currentProgressStep, setCurrentProgressStep] = useState(0); // 0: Test Planning, 1: Test Execution, 2: Defect Resolution, 3: Final Approval
  
  // 模态框状态管理
  const [criteriaModalVisible, setCriteriaModalVisible] = useState(false);
  const [editingCriteria, setEditingCriteria] = useState<AcceptanceItem | null>(null);
  const [defectModalVisible, setDefectModalVisible] = useState(false);
  const [editingDefect, setEditingDefect] = useState<DefectItem | null>(null);

  // 状态管理 - 新项目从空白开始
  const [acceptanceItems, setAcceptanceItems] = useState<AcceptanceItem[]>([]);
  const [defects, setDefects] = useState<DefectItem[]>([]);
  const [documents, setDocuments] = useState<AcceptanceDocument[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  // 1. 在组件顶部useState定义弹窗状态和当前编辑文档：
  const [docEditModalVisible, setDocEditModalVisible] = useState(false);
  const [editingDoc, setEditingDoc] = useState<AcceptanceDocument | null>(null);

  // 文档预览状态
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<{fileName: string, fileUrl: string} | null>(null);
  const [docForm] = Form.useForm();

  // 🔧 加载团队成员数据 - 从ExecutionStage获取
  useEffect(() => {
    if (project?.id) {
      const getStorageKey = (type: string) => `execution_stage_${type}_${project.id}`;
      
      try {
        const savedTeamMembers = localStorage.getItem(getStorageKey('teamMembers'));
        if (savedTeamMembers) {
          const parsedTeamMembers = JSON.parse(savedTeamMembers);
          if (parsedTeamMembers.length > 0) {
            setTeamMembers(parsedTeamMembers);
            console.log('✅ Loaded team members for AcceptanceStage:', parsedTeamMembers);
          }
        }
      } catch (error) {
        console.error('Failed to load team members:', error);
      }
    }
  }, [project?.id]);

  // localStorage持久化
  useEffect(() => {
    if (project?.id) {
      const saved = localStorage.getItem(`acceptance_documents_${project.id}`);
      if (saved) {
        try {
          const savedDocuments = JSON.parse(saved);
          setDocuments(savedDocuments);
        } catch (error) {
          console.error('Failed to load saved acceptance documents:', error);
        }
      }
    }
  }, [project?.id]);

  useEffect(() => {
    if (project?.id && documents.length > 0) {
      try {
        localStorage.setItem(`acceptance_documents_${project.id}`, JSON.stringify(documents));
      } catch (error) {
        console.error('Failed to save acceptance documents:', error);
      }
    }
  }, [documents, project?.id]);

  // 🔧 添加acceptanceItems和defects的数据持久化
  useEffect(() => {
    if (project?.id) {
      try {
        // 加载验收项数据
        const savedAcceptanceItems = localStorage.getItem(`acceptance_items_${project.id}`);
        if (savedAcceptanceItems) {
          const parsedItems = JSON.parse(savedAcceptanceItems);
          setAcceptanceItems(parsedItems);
          console.log('✅ Loaded acceptance items:', parsedItems);
        }

        // 加载缺陷数据
        const savedDefects = localStorage.getItem(`acceptance_defects_${project.id}`);
        if (savedDefects) {
          const parsedDefects = JSON.parse(savedDefects);
          setDefects(parsedDefects);
          console.log('✅ Loaded defects:', parsedDefects);
        }

        // 加载当前步骤状态
        const savedProgressStep = localStorage.getItem(`acceptance_progress_${project.id}`);
        if (savedProgressStep) {
          const stepIndex = parseInt(savedProgressStep, 10);
          if (!isNaN(stepIndex)) {
            setCurrentProgressStep(stepIndex);
            console.log('✅ Loaded progress step:', stepIndex);
          }
        }
      } catch (error) {
        console.error('Failed to load acceptance stage data:', error);
      }
    }
  }, [project?.id]);

  // 保存验收项数据
  useEffect(() => {
    if (project?.id && acceptanceItems.length >= 0) {
      try {
        localStorage.setItem(`acceptance_items_${project.id}`, JSON.stringify(acceptanceItems));
        console.log('💾 Saved acceptance items:', acceptanceItems.length);
      } catch (error) {
        console.error('Failed to save acceptance items:', error);
      }
    }
  }, [acceptanceItems, project?.id]);

  // 保存缺陷数据
  useEffect(() => {
    if (project?.id && defects.length >= 0) {
      try {
        localStorage.setItem(`acceptance_defects_${project.id}`, JSON.stringify(defects));
        console.log('💾 Saved defects:', defects.length);
      } catch (error) {
        console.error('Failed to save defects:', error);
      }
    }
  }, [defects, project?.id]);

  // 保存进度步骤
  useEffect(() => {
    if (project?.id) {
      try {
        localStorage.setItem(`acceptance_progress_${project.id}`, currentProgressStep.toString());
        console.log('💾 Saved progress step:', currentProgressStep);
      } catch (error) {
        console.error('Failed to save progress step:', error);
      }
    }
  }, [currentProgressStep, project?.id]);

  // 计算统计信息
  const getAcceptanceStats = () => {
    const total = acceptanceItems.length;
    const approved = acceptanceItems.filter(item => item.status === 'approved').length;
    const pending = acceptanceItems.filter(item => item.status === 'pending').length;
    const rejected = acceptanceItems.filter(item => item.status === 'rejected').length;
    const inReview = acceptanceItems.filter(item => item.status === 'in_review').length;
    
    const openDefects = defects.filter(d => d.status === 'open').length;
    const criticalDefects = defects.filter(d => d.severity === 'critical' && d.status !== 'resolved' && d.status !== 'closed').length;
    
    const approvalRate = total > 0 ? Math.round((approved / total) * 100) : 0;
    
    return { total, approved, pending, rejected, inReview, openDefects, criticalDefects, approvalRate };
  };

  const stats = getAcceptanceStats();

  // Progress步骤定义
  const progressSteps = [
    {
      title: 'Test Planning',
      description: 'Test plans and criteria defined',
      requirements: [], // 没有前置要求
      key: 'test_planning'
    },
    {
      title: 'Test Execution',
      description: 'Running acceptance tests',
      requirements: ['test_planning'], // 需要完成测试计划
      key: 'test_execution'
    },
    {
      title: 'Defect Resolution',
      description: 'Resolve identified issues',
      requirements: ['test_execution'], // 需要完成测试执行
      key: 'defect_resolution'
    },
    {
      title: 'Final Approval',
      description: 'Stakeholder sign-off',
      requirements: ['defect_resolution'], // 需要完成缺陷解决
      key: 'final_approval'
    }
  ];

  // 验收标准管理函数
  const handleAddCriteria = () => {
    setEditingCriteria(null);
    criteriaForm.resetFields();
    setCriteriaModalVisible(true);
  };

  const handleEditCriteria = (record: AcceptanceItem) => {
    setEditingCriteria(record);
    criteriaForm.setFieldsValue({
      ...record,
      testDate: record.testDate ? dayjs(record.testDate) : null
    });
    setCriteriaModalVisible(true);
  };

  const handleDeleteCriteria = (id: string) => {
    setAcceptanceItems(items => items.filter(item => item.id !== id));
    message.success('Acceptance criteria deleted successfully');
  };

  // 缺陷管理函数
  const handleAddDefect = () => {
    setEditingDefect(null);
    defectForm.resetFields();
    setDefectModalVisible(true);
  };

  const handleEditDefect = (record: DefectItem) => {
    setEditingDefect(record);
    defectForm.setFieldsValue({
      ...record,
      reportedDate: record.reportedDate ? dayjs(record.reportedDate) : null,
      resolvedDate: record.resolvedDate ? dayjs(record.resolvedDate) : null
    });
    setDefectModalVisible(true);
  };

  const handleDeleteDefect = (id: string) => {
    setDefects(prevDefects => prevDefects.filter(defect => defect.id !== id));
    message.success('Defect deleted successfully');
  };

  const handleSaveDefect = async (values: any) => {
    try {
      const defectData = {
        ...values,
        reportedDate: values.reportedDate ? values.reportedDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        resolvedDate: values.resolvedDate ? values.resolvedDate.format('YYYY-MM-DD') : undefined,
        reportedBy: values.reportedBy || 'Current User' // 默认当前用户
      };

      if (editingDefect) {
        // 编辑现有缺陷
        setDefects(prevDefects => 
          prevDefects.map(defect => 
            defect.id === editingDefect.id 
              ? { ...defect, ...defectData }
              : defect
          )
        );
        message.success('Defect updated successfully');
      } else {
        // 添加新缺陷
        const newDefect: DefectItem = {
          id: `D${String(defects.length + 1).padStart(3, '0')}`,
          ...defectData
        };
        setDefects(prevDefects => [...prevDefects, newDefect]);
        message.success('New defect reported successfully');
      }

      setDefectModalVisible(false);
      defectForm.resetFields();
      setEditingDefect(null);
    } catch (error) {
      message.error('Failed to save defect');
    }
  };

  const handleSaveCriteria = async (values: any) => {
    try {
      const newCriteria: AcceptanceItem = {
        id: editingCriteria?.id || Date.now().toString(),
        category: values.category,
        description: values.description,
        criteria: values.criteria,
        status: values.status || 'pending',
        reviewer: values.reviewer,
        testDate: values.testDate ? values.testDate.format('YYYY-MM-DD') : undefined,
        comments: values.comments,
        priority: values.priority
      };

      if (editingCriteria) {
        // 编辑现有项目
        setAcceptanceItems(items => 
          items.map(item => item.id === editingCriteria.id ? newCriteria : item)
        );
        message.success('Acceptance criteria updated successfully');
      } else {
        // 添加新项目
        setAcceptanceItems(items => [...items, newCriteria]);
        message.success('New acceptance criteria added successfully');
      }

      setCriteriaModalVisible(false);
      criteriaForm.resetFields();
      setEditingCriteria(null);
    } catch (error) {
      message.error('Failed to save acceptance criteria');
    }
  };

  // 检查步骤是否可以推进
  const canProceedToStep = (stepIndex: number) => {
    switch (stepIndex) {
      case 1: // Test Execution
        return true; // Test Planning 总是可以完成
      case 2: // Defect Resolution
        return stats.approved > 0; // 至少需要有一些已批准的项目
      case 3: // Final Approval
        return stats.openDefects === 0 && stats.criticalDefects === 0; // 需要没有开放的缺陷
      default:
        return true;
    }
  };

  // 获取步骤状态
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentProgressStep) return 'finish';
    if (stepIndex === currentProgressStep) return 'process';
    return 'wait';
  };

  // 跳转到指定步骤
  const handleProgressJump = (stepIndex: number) => {
    if (stepIndex <= currentProgressStep) {
      // 可以跳转到当前步骤或之前的步骤
      setCurrentProgressStep(stepIndex);
      message.info(`Switched to: ${progressSteps[stepIndex].title}`);
    } else if (canProceedToStep(stepIndex)) {
      // 如果满足条件，可以跳转到后续步骤
      setCurrentProgressStep(stepIndex);
      message.success(`Advanced to: ${progressSteps[stepIndex].title}`);
    } else {
      // 不满足条件时显示具体要求
      let errorMessage = '';
      switch (stepIndex) {
        case 2:
          errorMessage = 'Need at least some approved acceptance items to enter defect resolution stage';
          break;
        case 3:
          errorMessage = 'Need to resolve all open defects and critical defects to enter final approval stage';
          break;
        default:
          errorMessage = 'Requirements not met for this step';
      }
      message.warning(errorMessage);
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'approved': { color: 'success', text: 'Approved', icon: <CheckCircleOutlined /> },
      'pending': { color: 'default', text: 'Pending', icon: <ClockCircleOutlined /> },
      'rejected': { color: 'error', text: 'Rejected', icon: <CloseOutlined /> },
      'in_review': { color: 'processing', text: 'In Review', icon: <ClockCircleOutlined /> },
      'open': { color: 'error', text: 'Open', icon: <ExclamationCircleOutlined /> },
      'in_progress': { color: 'processing', text: 'In Progress', icon: <ClockCircleOutlined /> },
      'resolved': { color: 'success', text: 'Resolved', icon: <CheckOutlined /> },
      'closed': { color: 'default', text: 'Closed', icon: <CheckCircleOutlined /> },
      'under_review': { color: 'processing', text: 'Under Review', icon: <ClockCircleOutlined /> },
      'draft': { color: 'default', text: 'Draft', icon: <EditOutlined /> }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['pending'];
        return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 渲染严重程度标签
  const renderSeverityTag = (severity: string) => {
    const severityConfig = {
      'critical': { color: 'red', text: 'Critical' },
      'high': { color: 'orange', text: 'High' },
      'medium': { color: 'blue', text: 'Medium' },
      'low': { color: 'green', text: 'Low' }
    };

    const config = severityConfig[severity as keyof typeof severityConfig] || severityConfig['medium'];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 验收项表格列定义
  const acceptanceColumns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (text: string, record: AcceptanceItem) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
            {record.criteria}
          </div>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => renderStatusTag(status)
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => renderSeverityTag(priority)
    },
    {
      title: 'Reviewer',
      dataIndex: 'reviewer',
      key: 'reviewer',
      width: 120
    },
    {
      title: 'Test Date',
      dataIndex: 'testDate',
      key: 'testDate',
      width: 100,
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: any, record: AcceptanceItem) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small" 
              onClick={() => handleEditCriteria(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
              onClick={() => {
                Modal.confirm({
                  title: 'Delete Acceptance Criteria',
                  content: 'Are you sure you want to delete this acceptance criteria?',
                  okText: 'Yes',
                  okType: 'danger',
                  cancelText: 'No',
                  onOk: () => handleDeleteCriteria(record.id)
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 缺陷表格列定义
  const defectColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: DefectItem) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '11px', color: '#666', marginTop: '2px' }}>
            {record.description}
          </div>
        </div>
      )
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => renderSeverityTag(severity)
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => renderStatusTag(status)
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 120
    },
    {
      title: 'Reported',
      dataIndex: 'reportedDate',
      key: 'reportedDate',
      width: 100,
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: any, record: DefectItem) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small" 
              onClick={() => handleEditDefect(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
              onClick={() => {
                Modal.confirm({
                  title: 'Delete Defect',
                  content: 'Are you sure you want to delete this defect?',
                  okText: 'Yes',
                  okType: 'danger',
                  cancelText: 'No',
                  onOk: () => handleDeleteDefect(record.id)
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 自定义上传请求
  const customUploadRequest = (options: any) => {
    const { file, onSuccess, onError } = options;
    
    setTimeout(() => {
      if (Math.random() > 0.1) {
        onSuccess({
          fileName: file.name,
          fileSize: file.size,
          uploadDate: new Date().toISOString(),
          fileUrl: file.type.startsWith('image/') 
            ? URL.createObjectURL(file)
            : 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
        });
      } else {
        onError(new Error('Upload failed'));
      }
    }, 1000);

    return {
      abort() {
        console.log('Upload aborted');
      }
    };
  };

  // 处理文件上传
  const handleFileUpload = (info: any) => {
    // 处理多文件上传
    if (info.fileList && info.fileList.length > 0) {
      const uploadedFiles = info.fileList.filter((file: any) => file.status === 'done');
      
      if (uploadedFiles.length > 0) {
        const newDocuments = uploadedFiles.map((file: any, index: number) => ({
          id: String(Date.now() + index),
          name: file.name,
          type: 'Acceptance Document',
          uploadDate: new Date().toISOString(),
          uploadedBy: 'Current User',
          status: 'draft' as const,
          version: `v${documents.length + index + 1}.0`,
          size: (file.size / 1024).toFixed(1) + ' KB',
          notes: file.name,
          fileName: file.name,
          fileUrl: file.response?.fileUrl || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
        }));

        setDocuments(prev => [...newDocuments, ...prev]);
        
        // 根据上传文件数量显示相应的成功消息
        if (uploadedFiles.length === 1) {
          console.log('File uploaded successfully:', uploadedFiles[0].name);
        } else {
          console.log(`${uploadedFiles.length} files uploaded successfully`);
        }
      }
      
      // 检查是否有上传失败的文件
      const failedFiles = info.fileList.filter((file: any) => file.status === 'error');
      if (failedFiles.length > 0) {
        failedFiles.forEach((file: any) => {
          message.error(`${file.name} upload failed.`);
        });
      }
    }
  };

  // 查看文档预览
  const handleViewDoc = (record: AcceptanceDocument) => {
    const fileName = (record as any).fileName || record.name;
    const fileUrl = (record as any).fileUrl || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    
    setPreviewDocument({
      fileName,
      fileUrl
    });
    setPreviewModalVisible(true);
  };

  // 渲染文档预览内容
  const renderDocumentPreview = () => {
    if (!previewDocument) return null;

    const { fileName, fileUrl } = previewDocument;
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'pdf':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <iframe
              src={fileUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                borderRadius: '4px'
              }}
              title={`Preview: ${fileName}`}
            />
          </div>
        );

      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '20px',
            maxHeight: '600px',
            overflow: 'auto'
          }}>
            <img
              src={fileUrl}
              alt={fileName}
              style={{
                maxWidth: '100%',
                maxHeight: '550px',
                objectFit: 'contain',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              onError={(e) => {
                console.error('Image failed to load:', fileUrl);
                e.currentTarget.src = 'https://via.placeholder.com/400x300?text=Image+Not+Available';
              }}
            />
          </div>
        );

      case 'txt':
      case 'md':
        return (
          <div style={{ 
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            overflow: 'auto'
          }}>
            <iframe
              src={fileUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                backgroundColor: 'white'
              }}
              title={`Preview: ${fileName}`}
            />
          </div>
        );

      case 'doc':
      case 'docx':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '20px', color: '#1890ff' }}>
              📄
            </div>
            <h3 style={{ marginBottom: '16px', color: '#333' }}>
              {fileName}
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              This file type requires external application to view.
            </p>
            <Button 
              type="primary" 
              onClick={() => {
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download File
            </Button>
          </div>
        );

      default:
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '20px', color: '#ff4d4f' }}>
              ❓
            </div>
            <h3 style={{ marginBottom: '16px', color: '#333' }}>
              Unsupported File Type
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              Cannot preview this file type. Please download to view.
            </p>
            <Button 
              type="primary" 
              onClick={() => {
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download File
            </Button>
          </div>
        );
    }
  };

  // 2. 编辑、删除函数：
  const handleEditDoc = (record: AcceptanceDocument) => {
    setEditingDoc(record);
    docForm.setFieldsValue(record);
    setDocEditModalVisible(true);
  };

  const handleDeleteDoc = (record: AcceptanceDocument) => {
    Modal.confirm({
      title: 'Delete Document',
      content: `Are you sure you want to delete "${record.name}"?`,
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: () => {
        setDocuments(docs => docs.filter(doc => doc.id !== record.id));
        message.success('Document deleted successfully');
      }
    });
  };

  const handleSaveDoc = async () => {
    try {
      const values = await docForm.validateFields();
      setDocuments(docs => docs.map(doc => doc.id === editingDoc?.id ? { ...doc, ...values } : doc));
      setDocEditModalVisible(false);
      setEditingDoc(null);
      message.success('Document updated successfully');
    } catch {}
  };

  // 保存处理
  const handleSave = async (values: any) => {
    try {
      const acceptanceData = {
        ...values,
        acceptanceItems,
        defects,
        documents,
        stats,
        type: 'acceptance',
        status: stats.approvalRate >= 90 ? 'completed' : 'in_progress',
        updatedAt: new Date().toISOString()
      };

      if (onSave) {
        onSave(acceptanceData);
      }

      message.success('验收阶段数据保存成功');
    } catch (error) {
      console.error('Save error:', error);
      message.error('保存失败，请重试');
    }
  };

  // 进入下一阶段
  const handleProceed = async () => {
    if (stats.criticalDefects > 0) {
      message.error('请先解决所有关键缺陷后再继续');
      return;
    }

    if (stats.approvalRate < 90) {
      message.error('验收通过率需达到90%以上才能进入下一阶段');
      return;
    }

    try {
      await handleSave(form.getFieldsValue());
      
      setTimeout(() => {
        message.success('验收阶段完成！正在进入项目收尾阶段...');
        if (onProceed) {
          onProceed('closeout');
        }
      }, 1000);
    } catch (error) {
      message.error('保存失败，无法进入下一阶段');
    }
  };

  // 在组件内添加：
  const acceptanceDocColumns = [
    {
      title: 'Document',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: AcceptanceDocument) => (
        <span className="version-link">{text}</span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (text: string) => <span className="contract-type purple">{text}</span>
    },
    {
      title: 'Date',
      dataIndex: 'uploadDate',
      key: 'uploadDate',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Uploaded By',
      dataIndex: 'uploadedBy',
      key: 'uploadedBy',
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      render: (_: any, record: AcceptanceDocument) => record.notes || record.description || ''
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let className = '';
        let text = '';
        if (status === 'approved') {
          className = 'status-submitted';
          text = 'Approved';
        } else if (status === 'under_review') {
          className = 'status-internal';
          text = 'Under Review';
        } else if (status === 'draft') {
          className = 'status-internal';
          text = 'Draft';
        } else if (status === 'rejected') {
          className = 'status-internal';
          text = 'Rejected';
        }
        return <span className={`status ${className}`}>{text}</span>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: AcceptanceDocument) => (
        <div className="action-icons">
          <Tooltip title="View Document">
            <EyeOutlined 
              className="action-icon view-icon" 
              onClick={() => handleViewDoc(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <EditOutlined className="action-icon edit-icon" onClick={() => handleEditDoc(record)} />
          </Tooltip>
          <Tooltip title="删除">
            <DeleteOutlined className="action-icon delete-icon" onClick={() => handleDeleteDoc(record)} />
          </Tooltip>
        </div>
      )
    }
  ];

  return (
    <div className="ltc-stage-content">
      {/* 添加/编辑验收标准模态框 */}
      <Modal
        title={editingCriteria ? 'Edit Acceptance Criteria' : 'Add New Acceptance Criteria'}
        open={criteriaModalVisible}
        onCancel={() => {
          setCriteriaModalVisible(false);
          criteriaForm.resetFields();
          setEditingCriteria(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={criteriaForm}
          layout="vertical"
          onFinish={handleSaveCriteria}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="Criteria Name"
              >
                <Input placeholder="Enter category (e.g., Functional Testing, Performance Testing)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priority"
                label={<span>Priority <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please select priority' }]}
              >
                <Select placeholder="Select priority">
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <Input placeholder="Brief description of the test item" />
          </Form.Item>
          
          <Form.Item
            name="criteria"
            label="Acceptance Criteria"
          >
            <TextArea 
              rows={3}
              placeholder="Detailed acceptance criteria that must be met"
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="reviewer"
                label={<span>Reviewer <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please enter reviewer name' }]}
              >
                <Input placeholder="Assigned reviewer" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
              >
                <Select placeholder="Select status" defaultValue="pending">
                  <Option value="pending">Pending</Option>
                  <Option value="in_review">In Review</Option>
                  <Option value="approved">Approved</Option>
                  <Option value="rejected">Rejected</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="testDate"
                label="Test Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="comments"
                label="Comments"
              >
                <Input placeholder="Additional comments" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCriteriaModalVisible(false);
                criteriaForm.resetFields();
                setEditingCriteria(null);
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                {editingCriteria ? 'Update' : 'Save'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加/编辑缺陷模态框 */}
      <Modal
        title={editingDefect ? 'Edit Defect' : 'Report New Defect'}
        open={defectModalVisible}
        onCancel={() => {
          setDefectModalVisible(false);
          defectForm.resetFields();
          setEditingDefect(null);
        }}
        footer={null}
        width={700}
      >
        <Form
          form={defectForm}
          layout="vertical"
          onFinish={handleSaveDefect}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label={<span>Defect Title <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please enter defect title' }]}
              >
                <Input placeholder="Brief title describing the defect" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="severity"
                label={<span>Severity <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please select severity' }]}
              >
                <Select placeholder="Select severity level">
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea 
              rows={4}
              placeholder="Detailed description of the defect, including steps to reproduce and expected vs actual behavior"
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="Category"
              >
                <Input placeholder="Enter category (e.g., UI/UX, Performance, Security, Functionality)" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
              >
                <Select placeholder="Select status" defaultValue="open">
                  <Option value="open">Open</Option>
                  <Option value="in_progress">In Progress</Option>
                  <Option value="resolved">Resolved</Option>
                  <Option value="closed">Closed</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="assignedTo"
                label={<span>Assigned To <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please assign to someone' }]}
              >
                <AutoComplete
                  placeholder="Select team member or enter custom name"
                  options={[
                    // 团队成员选项
                    ...teamMembers.map(member => ({
                      value: member.name,
                      label: `${member.name} (${member.role})`
                    })),
                    // 默认选项
                    { value: 'Development Team', label: 'Development Team' },
                    { value: 'QA Team', label: 'QA Team' }
                  ]}
                  filterOption={(inputValue, option) =>
                    option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="reportedBy"
                label="Reported By"
              >
                <Input placeholder="Reporter name (defaults to current user)" />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="reportedDate"
                label="Reported Date"
              >
                <DatePicker style={{ width: '100%' }} defaultValue={dayjs()} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="resolvedDate"
                label="Resolved Date"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setDefectModalVisible(false);
                defectForm.resetFields();
                setEditingDefect(null);
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                {editingDefect ? 'Update Defect' : 'Report Defect'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 className="ltc-stage-title">
          Acceptance & Testing
        </h1>
      </div>

      {/* 状态卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Approved Items</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{stats.approved}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Pending Review</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ClockCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>{stats.pending + stats.inReview}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#fff2e8',
            border: '1px solid #ffbb96',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Open Defects</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <BugOutlined style={{ fontSize: '24px', color: '#ff4d4f' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>{stats.openDefects}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#fff7e6',
            border: '1px solid #ffd591',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Critical Issues</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ExclamationCircleOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>{stats.criticalDefects}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <div className="ltc-progress-circle" style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              percent={stats.approvalRate}
              size={80}
              strokeColor={stats.approvalRate >= 90 ? "#52c41a" : "#fa8c16"}
            />
            <div className="ltc-progress-label" style={{ marginTop: '8px', fontSize: '14px', fontWeight: 500 }}>
              Acceptance Rate
            </div>
          </div>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card className="ltc-card-content">
        <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
          {/* 验收测试 */}
          <TabPane 
            tab={
              <span>
                <CheckCircleOutlined />
                Acceptance Testing
              </span>
            } 
            key="testing"
          >
            {/* Acceptance Progress 和 Key Metrics */}
            <Row gutter={[24, 16]} style={{ marginBottom: '24px' }}>
              <Col span={12}>
                <Card 
                  title="Acceptance Progress" 
                  size="small"
                >
                  <Steps 
                    direction="vertical" 
                    size="small" 
                    current={currentProgressStep}
                    onChange={handleProgressJump}
                    style={{ cursor: 'pointer' }}
                  >
                    {progressSteps.map((step, index) => (
                      <Step 
                        key={step.key}
                        title={step.title} 
                        status={getStepStatus(index)}
                        description={
                          <div>
                            <div>{step.description}</div>
                            {index === currentProgressStep && (
                              <div style={{ 
                                marginTop: '4px', 
                                fontSize: '10px', 
                                color: '#1890ff', 
                                fontWeight: 500 
                              }}>
                                Current Stage
                              </div>
                            )}
                            {index > currentProgressStep && !canProceedToStep(index) && (
                              <div style={{ 
                                marginTop: '4px', 
                                fontSize: '10px', 
                                color: '#ff4d4f' 
                              }}>
                                Requirements Not Met
                              </div>
                            )}
                          </div>
                        }
                        style={{
                          cursor: (index <= currentProgressStep || canProceedToStep(index)) ? 'pointer' : 'not-allowed'
                        }}
                      />
                    ))}
                  </Steps>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="Key Metrics" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Acceptance Rate: </span>
                      <strong style={{ color: stats.approvalRate >= 90 ? '#52c41a' : '#fa8c16' }}>
                        {stats.approvalRate}%
                      </strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Total Items: </span>
                      <strong>{stats.total}</strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Approved: </span>
                      <strong style={{ color: '#52c41a' }}>{stats.approved}</strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Rejected: </span>
                      <strong style={{ color: '#ff4d4f' }}>{stats.rejected}</strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Open Defects: </span>
                      <strong style={{ color: '#ff4d4f' }}>{stats.openDefects}</strong>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>

            <Form form={form} layout="vertical" onFinish={handleSave}>
              <Row gutter={[24, 16]}>
            <Col span={24}>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <h3>Acceptance Criteria & Plan</h3>
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />}
                      onClick={handleAddCriteria}
                    >
                      Add New Criteria
                    </Button>
                  </div>
              <Table
                    columns={acceptanceColumns}
                    dataSource={acceptanceItems}
                    pagination={{ pageSize: 10 }}
              />
            </Col>
          </Row>
            </Form>
          </TabPane>

          {/* 缺陷管理 */}
          <TabPane 
            tab={
              <span>
                <BugOutlined />
                Defect Management
              </span>
            } 
            key="defects"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  <h3>Defect Tracking</h3>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={handleAddDefect}
                  >
                    Report New Defect
                  </Button>
        </div>
                <Table
                  columns={defectColumns}
                  dataSource={defects}
                  pagination={{ pageSize: 10 }}
                />
              </Col>
            </Row>
          </TabPane>

          {/* 文档管理 */}
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                Documentation
              </span>
            } 
            key="documents"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Card className="contract-card" title="Acceptance Documents (All Versions)">
                  {/* 文件上传区域 */}
                  <Upload.Dragger
                    name="files"
                    customRequest={customUploadRequest}
                    onChange={handleFileUpload}
                    className="contract-upload-area"
                    beforeUpload={(file) => {
                      const isLt20M = file.size / 1024 / 1024 < 20;
                      if (!isLt20M) {
                        message.error('File must be smaller than 20MB!');
                        return false;
                      }
                      return true;
                    }}
                    multiple={true}
                  >
                    <p className="ant-upload-drag-icon">
                      <CloudUploadOutlined style={{ fontSize: '96px', color: '#FF7A00' }} />
                    </p>
                    <p className="ant-upload-text">Drag & drop multiple files here or click to browse (Maximum file size 20MB each)</p>
                  </Upload.Dragger>
                  {/* 文档表格 */}
                  <Table
                    columns={acceptanceDocColumns}
                    dataSource={documents}
                    pagination={false}
                    className="version-table"
                    rowKey="id"
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* Save & Proceed 按钮 */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>

      {/* 编辑文档Modal */}
      <Modal
        title="Edit Document"
        open={docEditModalVisible}
        onCancel={() => { setDocEditModalVisible(false); setEditingDoc(null); }}
        onOk={handleSaveDoc}
        okText="Save"
        cancelText="Cancel"
        width={500}
      >
        <Form form={docForm} layout="vertical">
          <Form.Item name="name" label="Document Name" rules={[{ required: true, message: 'Please enter document name' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true, message: 'Please enter type' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item name="status" label="Status" rules={[{ required: true, message: 'Please select status' }]}> 
            <Select>
              <Option value="approved">Approved</Option>
              <Option value="under_review">Under Review</Option>
              <Option value="draft">Draft</Option>
              <Option value="rejected">Rejected</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文档预览Modal */}
      <Modal
        title="Document Preview"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
        width={900}
        style={{ top: 20 }}
      >
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          <strong>{previewDocument?.fileName}</strong>
        </div>
        {renderDocumentPreview()}
      </Modal>
    </div>
  );
};

export default AcceptanceStage;
