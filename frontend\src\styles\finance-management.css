/* Finance Management 页面样式优化 */
.finance-management-page {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

.finance-management-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(600px circle at 20% 20%, rgba(255, 122, 0, 0.05) 0%, transparent 50%),
    radial-gradient(400px circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
    radial-gradient(800px circle at 50% 50%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.finance-management-page > * {
  position: relative;
  z-index: 1;
}

/* 页面标题样式 */
.finance-page-title {
  font-size: 19.2px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  background: linear-gradient(135deg, #ff7a00 0%, #e66500 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 2px 4px rgba(255, 122, 0, 0.1);
}

/* 新建按钮样式 */
.finance-new-btn {
  height: 36px !important;
  padding: 0 16px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border-radius: 8px !important;
  background: linear-gradient(135deg, #ff7a00 0%, #e66500 100%) !important;
  border: none !important;
  box-shadow: 0 4px 16px rgba(255, 122, 0, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.finance-new-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.finance-new-btn:hover::before {
  left: 100%;
}

.finance-new-btn:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 0 8px 24px rgba(255, 122, 0, 0.4) !important;
}

.finance-new-btn:active {
  transform: translateY(0) scale(0.98) !important;
}

/* 统计卡片样式 */
.finance-stat-card {
  border-radius: 16px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.finance-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff7a00, #10b981, #3b82f6, #f59e0b);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.finance-stat-card:hover::before {
  opacity: 1;
}

.finance-stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15) !important;
}

/* 收入卡片 */
.income-card::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%) !important;
}

.income-card:hover {
  border-color: rgba(16, 185, 129, 0.3) !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
}

/* 支出卡片 */
.expense-card::before {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%) !important;
}

.expense-card:hover {
  border-color: rgba(239, 68, 68, 0.3) !important;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
}

/* 利润卡片 */
.profit-card::before {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.profit-card:hover {
  border-color: rgba(59, 130, 246, 0.3) !important;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
}

/* 待收款卡片 */
.pending-card::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%) !important;
}

.pending-card:hover {
  border-color: rgba(245, 158, 11, 0.3) !important;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
}

/* 统计卡片背景图标 */
.stat-card-bg-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 48px;
  opacity: 0.1;
  transition: all 0.3s ease;
  transform: rotate(-10deg);
}

.finance-stat-card:hover .stat-card-bg-icon {
  opacity: 0.2;
  transform: rotate(0deg) scale(1.1);
}

/* 筛选卡片样式 */
.finance-filter-card {
  border-radius: 16px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.finance-filter-card .ant-card-body {
  padding: 20px 24px !important;
}

/* 表格卡片样式 */
.finance-table-card {
  border-radius: 16px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden;
}

.finance-table-title {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #1f2937 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 操作按钮样式 */
.finance-action-btn {
  border-radius: 8px !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  color: #6b7280 !important;
  transition: all 0.3s ease !important;
}

.finance-action-btn:hover {
  background: linear-gradient(135deg, #ff7a00 0%, #e66500 100%) !important;
  border-color: transparent !important;
  color: white !important;
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.3) !important;
}

/* 表格样式优化 */
.finance-table-container {
  border-radius: 12px;
  overflow: hidden;
}

.finance-table .ant-table {
  background: transparent !important;
}

.finance-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.1) 0%, rgba(255, 122, 0, 0.05) 100%) !important;
  border-bottom: 2px solid rgba(255, 122, 0, 0.2) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  color: #374151 !important;
  padding: 12px !important;
}

.finance-table .ant-table-tbody > tr {
  transition: all 0.3s ease !important;
}

.finance-table .ant-table-tbody > tr:hover {
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.03) 0%, rgba(255, 122, 0, 0.01) 100%) !important;
  transform: scale(1.001);
}

.finance-table .ant-table-tbody > tr > td {
  padding: 12px !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  font-size: 14px !important;
}

/* 抽屉样式 */
.finance-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #ff7a00 0%, #e66500 100%) !important;
  border-bottom: none !important;
}

.finance-drawer .ant-drawer-title {
  color: white !important;
  font-weight: 600 !important;
}

.finance-drawer .ant-drawer-close {
  color: white !important;
}

.finance-drawer .ant-drawer-body {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

/* Statistic组件文字大小优化 */
.finance-stat-card .ant-statistic-title {
  font-size: 12px !important;
  font-weight: 400 !important;
  color: #6b7280 !important;
  margin-bottom: 8px !important;
}

.finance-stat-card .ant-statistic-content {
  font-size: 20px !important;
  font-weight: 600 !important;
}

.finance-stat-card .ant-statistic-content-value {
  font-size: 20px !important;
  font-weight: 600 !important;
}

.finance-stat-card .ant-statistic-content-prefix {
  font-size: 16px !important;
  margin-right: 8px !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .finance-management-page {
    padding: 16px;
  }
  
  .finance-page-title {
    font-size: 18px !important;
  }
  
  .finance-new-btn {
    height: 32px !important;
    padding: 0 12px !important;
    font-size: 12px !important;
  }
  
  .finance-stat-card {
    min-height: 100px;
  }
  
  .stat-card-bg-icon {
    font-size: 36px;
    top: 12px;
    right: 12px;
  }
}

@media (max-width: 480px) {
  .finance-management-page {
    padding: 12px;
  }
  
  .finance-page-title {
    font-size: 16px !important;
  }
  
  .finance-stat-card {
    min-height: 90px;
  }
  
  .stat-card-bg-icon {
    font-size: 32px;
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.finance-stat-card {
  animation: slideInUp 0.6s ease-out;
}

.finance-stat-card:nth-child(1) { animation-delay: 0.1s; }
.finance-stat-card:nth-child(2) { animation-delay: 0.2s; }
.finance-stat-card:nth-child(3) { animation-delay: 0.3s; }
.finance-stat-card:nth-child(4) { animation-delay: 0.4s; }

.finance-filter-card {
  animation: slideInUp 0.6s ease-out 0.5s both;
}

.finance-table-card {
  animation: slideInUp 0.6s ease-out 0.6s both;
} 