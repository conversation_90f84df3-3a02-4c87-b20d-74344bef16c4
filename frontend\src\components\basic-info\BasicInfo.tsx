import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber,
  Select, 
  Row, 
  Col, 
  Button, 
  message, 
  Typography,
  Divider,
  Slider,
  Switch,
  Tag,
  Alert
} from 'antd';
import { 
  AudioOutlined, 
  SaveOutlined,
  RightOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';
import './BasicInfo.css';

const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface BasicInfoProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
  id?: string;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ project, stage, onSave, onProceed, id }) => {
  const [clientForm] = Form.useForm();
  const [opportunityForm] = Form.useForm();
  const [competitorForm] = Form.useForm();
  const [otherForm] = Form.useForm();

  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [activeModule, setActiveModule] = useState('client');
  const [winProbabilityValue, setWinProbabilityValue] = useState(75);
  const [hasCompetitors, setHasCompetitors] = useState(false);
  const [competitorTags, setCompetitorTags] = useState<string[]>([]);

  // 自动保存相关状态
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 自动保存函数
  const autoSave = useCallback(async (showMessage = false) => {
    if (!id || isSaving) return;

    try {
      setIsSaving(true);
      
      // 读取localStorage已有数据
      let localStageData = {};
      const storageKey = `project_stages_${id}`;
      const localStagesData = localStorage.getItem(storageKey);
      if (localStagesData) {
        try {
          const parsed = JSON.parse(localStagesData);
          const basicInfoStage = parsed.find((stage: any) => stage.type === 'basic_info');
          if (basicInfoStage && basicInfoStage.data) {
            localStageData = typeof basicInfoStage.data === 'string' ? JSON.parse(basicInfoStage.data) : basicInfoStage.data;
          }
        } catch {}
      }

      // 合并所有表单内容
      const allFormData = {
        ...localStageData,
        ...clientForm.getFieldsValue(),
        ...opportunityForm.getFieldsValue(),
        ...competitorForm.getFieldsValue(),
        ...otherForm.getFieldsValue(),
        activeModule, // 保存当前活跃模块
        winProbabilityValue,
        hasCompetitors,
        competitorTags
      };

      // 保存到localStorage
      const stageData = [{
        id: `local_basic_info_${Date.now()}`,
        projectId: id,
        name: 'Basic Information',
        type: 'basic_info',
        status: 'in_progress',
        data: allFormData,
        updatedAt: new Date().toISOString()
      }];
      
      localStorage.setItem(storageKey, JSON.stringify(stageData));
      
      // 调用外部保存函数
      if (onSave) {
        await onSave(allFormData);
      }
      
      setLastSaveTime(new Date());
      setHasUnsavedChanges(false);
      
      if (showMessage) {
        message.success('数据已自动保存');
      }
      
      console.log('✅ Auto-save completed:', new Date().toLocaleTimeString());
    } catch (error) {
      console.error('❌ Auto-save failed:', error);
      if (showMessage) {
        message.error('自动保存失败');
      }
    } finally {
      setIsSaving(false);
    }
  }, [id, clientForm, opportunityForm, competitorForm, otherForm, activeModule, winProbabilityValue, hasCompetitors, competitorTags, onSave, isSaving]);

  // 防抖保存函数
  const debouncedAutoSave = useCallback(() => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }
    
    setHasUnsavedChanges(true);
    
    saveTimeoutRef.current = setTimeout(() => {
      autoSave(false);
    }, 2000); // 2秒后自动保存
  }, [autoSave]);

  // 表单字段变化处理
  const handleFieldChange = useCallback(() => {
    debouncedAutoSave();
  }, [debouncedAutoSave]);

  // 定时自动保存 - 每30秒保存一次
  useEffect(() => {
    if (!id) return;
    
    autoSaveTimerRef.current = setInterval(() => {
      if (hasUnsavedChanges) {
        autoSave(false);
      }
    }, 30000); // 30秒

    return () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }
    };
  }, [id, hasUnsavedChanges, autoSave]);

  // 页面卸载时自动保存
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        autoSave(false);
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    };

    const handleUnload = () => {
      if (hasUnsavedChanges) {
        autoSave(false);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
      
      // 清理定时器
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }
    };
  }, [hasUnsavedChanges, autoSave]);

  // 1. 只在首次加载或id变化时初始化表单
  useEffect(() => {
    if (!id) return;
    // 只在首次加载时执行
    let initialized = false;
    if (initialized) return;
    initialized = true;

    // 读取localStorage
    const storageKey = `project_stages_${id}`;
    const localStagesData = localStorage.getItem(storageKey);
    let localStageData = {};
    if (localStagesData) {
      try {
        const parsed = JSON.parse(localStagesData);
        const basicInfoStage = parsed.find((stage: any) => stage.type === 'basic_info');
        if (basicInfoStage && basicInfoStage.data) {
          localStageData = typeof basicInfoStage.data === 'string' ? JSON.parse(basicInfoStage.data) : basicInfoStage.data;
        }
      } catch {}
    }

    // 项目数据映射
    let projectData = {};
    if (project) {
      projectData = {
        clientName: project.client || '',
        clientTier: project.tier === 'S' ? 'a' : project.tier === 'V' ? 'a' : project.tier === 'B' ? 'a' : project.tier || 'a',
        address: '',
        industry: project.category === 'Software' ? 'technology' : project.category === 'Service' ? 'finance' : project.category === 'Product' ? 'manufacturing' : 'technology',
        keyStakeholder: '',
        primaryContact: project.owner,
        contactPhone: '',
        email: ''
      };
    }

    // 合并优先级：localStorage > 项目数据 > 空
    const merged = { ...projectData, ...localStageData };
    clientForm.setFieldsValue(merged);
    opportunityForm.setFieldsValue(merged);
    competitorForm.setFieldsValue(merged);
    otherForm.setFieldsValue(merged);
  }, [id, project]);

  // 项目数据自动映射
  useEffect(() => {
    console.log('=== BasicInfo useEffect triggered ===');
    console.log('Stage:', stage);
    console.log('Project:', project);
    console.log('ID:', id);
    console.log('Active Module:', activeModule);

    // 🔧 首先强制添加CSS样式启用所有输入框
    const styleId = 'basic-info-force-enable-style';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* 强制启用Basic Info页面的所有输入框 */
        #basic-info-form .ant-input,
        #basic-info-form .ant-input-number-input,
        #basic-info-form .ant-select-selector,
        #basic-info-form textarea,
        #basic-info-form .form-input {
          pointer-events: auto !important;
          cursor: text !important;
          background-color: white !important;
          color: rgba(0, 0, 0, 0.88) !important;
          border-color: #d9d9d9 !important;
          opacity: 1 !important;
        }
        
        #basic-info-form .ant-input[disabled],
        #basic-info-form .ant-input-number-input[disabled],
        #basic-info-form .form-input[disabled],
        #basic-info-form textarea[disabled] {
          pointer-events: auto !important;
          cursor: text !important;
          background-color: white !important;
          color: rgba(0, 0, 0, 0.88) !important;
          opacity: 1 !important;
        }
        
        #basic-info-form .ant-input:hover,
        #basic-info-form .ant-input:focus,
        #basic-info-form textarea:hover,
        #basic-info-form textarea:focus {
          border-color: #4096ff !important;
          box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1) !important;
        }
      `;
      document.head.appendChild(style);
      console.log('✅ Force enable CSS styles added to document head');
    }

    // 数据优先级：localStorage > 项目数据 > 空值
    let initialData = {};

    // 1. 首先从项目数据自动映射基础信息（作为默认值）
    if (project) {
      console.log('Auto-mapping project data to form fields...');
      
      // 客户信息映射
      const clientData = {
        clientName: project.client,
        clientTier: project.tier === 'S' ? 'svip' : 
                   project.tier === 'V' ? 'vip' : 
                   project.tier === 'B' ? 'ba' : 
                   project.tier === 'A' ? 'a' : 
                   project.tier || 'a',
        address: '', // 地址信息需要用户手动填写
        industry: project.category === 'Software' ? 'Technology' :
                 project.category === 'Service' ? 'Financial Services' :
                 project.category === 'Product' ? 'Manufacturing' : 'Technology',
        keyStakeholder: '', // 关键利益相关者需要用户手动填写
        primaryContact: project.owner,
        contactPhone: '', // 联系电话需要用户手动填写
        email: '' // 邮箱需要用户手动填写
      };

      // 机会信息映射
      const opportunityData = {
        opportunityName: project.name || '', // 项目名称映射到机会名称
        totalRevenue: project.revenue || 0, // 使用项目的 revenue 字段
        currency: 'EUR', // 默认欧元
        winProbability: typeof project.probability === 'string' ? 
          (project.probability === 'A' ? 90 :
           project.probability === 'B' ? 75 :
           project.probability === 'C' ? 55 :
           project.probability === 'D' ? 30 : 75) :
          (typeof project.probability === 'number' ? project.probability : 75),
        currentStage: project.stage || 'qualification',
        background: '' // 背景描述需要用户手动填写
      };

      initialData = { ...clientData, ...opportunityData };
      console.log('Auto-mapped initial data:', initialData);

      // 设置Win Probability滑块的值
      if (opportunityData.winProbability) {
        setWinProbabilityValue(opportunityData.winProbability);
      }

      console.log('✅ Project data auto-mapping completed');
    }

    // 2. 设置初始数据到表单（项目数据作为默认值）
    if (Object.keys(initialData).length > 0) {
      console.log('Setting initial project data to forms...');
      console.log('Initial data being set:', initialData);
      
      clientForm.setFieldsValue(initialData);
      opportunityForm.setFieldsValue(initialData);
      competitorForm.setFieldsValue(initialData);
      otherForm.setFieldsValue(initialData);
      
      console.log('Opportunity form values after setting:', opportunityForm.getFieldsValue());
    }

    // 3. 检查localStorage数据并覆盖（localStorage数据优先级最高）
    if (id) {
      try {
        const stageStorageKey = `project_stages_${id}`;
        const localStagesData = localStorage.getItem(stageStorageKey);
        if (localStagesData) {
          const parsedLocalStages = JSON.parse(localStagesData);
          const basicInfoStage = parsedLocalStages.find((stage: any) => stage.type === 'basic_info');
          
          if (basicInfoStage && basicInfoStage.data) {
            console.log('Loading data from localStorage basicInfoStage:', basicInfoStage);
            const localStageData = typeof basicInfoStage.data === 'string' ? JSON.parse(basicInfoStage.data) : basicInfoStage.data;
            console.log('Parsed localStorage stage data:', localStageData);

            // 合并数据：localStorage优先，项目数据作为fallback
            const mergedData = { ...initialData, ...localStageData };

            // 设置所有模块的表单数据
            clientForm.setFieldsValue(mergedData);
            opportunityForm.setFieldsValue(mergedData);
            competitorForm.setFieldsValue(mergedData);
            otherForm.setFieldsValue(mergedData);

            // 恢复状态
            if (localStageData.winProbability !== undefined) {
              setWinProbabilityValue(localStageData.winProbability);
            } else if ((initialData as any).winProbability !== undefined) {
              setWinProbabilityValue((initialData as any).winProbability);
            }
            
            if (localStageData.hasCompetitors !== undefined) {
              setHasCompetitors(localStageData.hasCompetitors);
            }
            if (localStageData.competitorTags && Array.isArray(localStageData.competitorTags)) {
              setCompetitorTags(localStageData.competitorTags);
            }

            console.log('✅ Successfully loaded and merged data from localStorage');
            return; // 数据加载完成
          }
        }
      } catch (localStorageError) {
        console.warn('Failed to load from localStorage:', localStorageError);
      }
    }

    // 3. 如果有阶段数据，则覆盖项目数据（阶段数据优先级更高）
    if (stage?.data) {
      try {
        console.log('Loading existing stage data...');
        const stageData = JSON.parse(stage.data);
        console.log('Parsed stage data:', stageData);
        
        // 合并数据：阶段数据覆盖自动映射的数据
        const mergedData = { ...initialData, ...stageData };
        
        // 恢复所有表单数据
        clientForm.setFieldsValue(mergedData);
        opportunityForm.setFieldsValue(mergedData);
        competitorForm.setFieldsValue(mergedData);
        otherForm.setFieldsValue(mergedData);
        
        // 恢复活跃模块状态
        if (mergedData.activeModule) {
          setActiveModule(mergedData.activeModule);
        }
        
        // 恢复其他状态
        if (mergedData.winProbabilityValue !== undefined) {
          setWinProbabilityValue(mergedData.winProbabilityValue);
        }
        if (mergedData.hasCompetitors !== undefined) {
          setHasCompetitors(mergedData.hasCompetitors);
        }
        if (mergedData.competitorTags && Array.isArray(mergedData.competitorTags)) {
          setCompetitorTags(mergedData.competitorTags);
        }

        console.log('✅ Successfully loaded existing stage data');
      } catch (error) {
        console.error('❌ Error parsing stage data:', error);
      }
    }

    // 4. 确保表单字段可编辑和强制更新
    setTimeout(() => {
      console.log('=== FORCE ENABLING FORM FIELDS ===');
      const allInputs = document.querySelectorAll('input[disabled], textarea[disabled]');
      console.log('Found disabled inputs:', allInputs.length);
      allInputs.forEach((input: any) => {
        input.disabled = false;
        input.readOnly = false;
        input.style.pointerEvents = 'auto';
        input.style.cursor = 'text';
      });
      
      // 强制重新渲染表单
      clientForm.validateFields().catch(() => {});
      opportunityForm.validateFields().catch(() => {});
      console.log('Forms should be properly initialized now');
    }, 100);

    // 5. 如果既没有阶段数据也没有项目数据，显示提示信息
    if (!project && !stage?.data) {
      console.log('ℹ️ No existing data found, forms will be empty');
    }

  }, [stage, project, id]); // 移除Form实例依赖，避免无限循环

  const handleSubmit = async (values: any) => {
    try {
      // 读取localStorage已有数据
      let localStageData = {};
      if (id) {
        const storageKey = `project_stages_${id}`;
        const localStagesData = localStorage.getItem(storageKey);
        if (localStagesData) {
          try {
            const parsed = JSON.parse(localStagesData);
            const basicInfoStage = parsed.find((stage: any) => stage.type === 'basic_info');
            if (basicInfoStage && basicInfoStage.data) {
              localStageData = typeof basicInfoStage.data === 'string' ? JSON.parse(basicInfoStage.data) : basicInfoStage.data;
            }
          } catch {}
        }
      }
      // 合并所有表单内容
      const allFormData = {
        ...localStageData,
        ...clientForm.getFieldsValue(),
        ...opportunityForm.getFieldsValue(),
        ...competitorForm.getFieldsValue(),
        ...otherForm.getFieldsValue(),
        ...values
      };
      // 保存到localStorage
      if (id) {
        const storageKey = `project_stages_${id}`;
        const stageData = [{
          id: `local_basic_info_${Date.now()}`,
          projectId: id,
          name: 'Basic Information',
          type: 'basic_info',
          status: 'completed',
          data: allFormData,
          updatedAt: new Date().toISOString()
        }];
        localStorage.setItem(storageKey, JSON.stringify(stageData));
      }
      if (onSave) await onSave(allFormData);
      
      // 更新保存状态
      setLastSaveTime(new Date());
      setHasUnsavedChanges(false);
      
      message.success('Information saved successfully!');
    } catch (error) {
      message.error('Failed to save information.');
    }
  };

  const handleProceed = async () => {
    // 保存前先自动保存
    if (hasUnsavedChanges) {
      await autoSave(true);
    }
    
    if (onProceed) {
      onProceed('proposal'); // 下一个阶段是Proposal
    }
  };

  const handleVoiceButtonClick = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      setTranscript('Recording started, please describe your project...');
      message.info('Recording started, please describe your project...');
      
      // 模拟AI语音识别和自动填充
      setTimeout(() => {
        setIsRecording(false);
        const mockTranscript = "This is a cloud migration project for Acme Corporation, estimated revenue €1.5M, 80% success probability, 6-month timeline.";
        setTranscript(mockTranscript);

        // AI自动填充表单
        clientForm.setFieldsValue({
          clientName: 'Acme Corporation',
          clientTier: 'vip',
          industry: 'technology',
          primaryContact: 'Jane Doe (CTO)',
          email: '<EMAIL>',
          contactPhone: '+****************',
        });

        opportunityForm.setFieldsValue({
          opportunityName: 'Cloud Migration Project',
          totalRevenue: 1500000,
          currency: 'EUR',
          currentStage: 'Discovery',
          keyRequirements: 'Cloud migration with AWS/Azure, microservices architecture',
          decisionCriteria: 'Cost effectiveness, scalability, reliability'
        });

        setWinProbabilityValue(80);

        message.success('AI successfully extracted key information and filled the form');
      }, 2000);
    } else {
      setTranscript('Voice recording stopped.');
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#F9FAFB', 
      minHeight: '100vh' 
    }}>
      {/* 自动保存状态显示 */}
      <div style={{ 
        position: 'fixed', 
        top: '80px', 
        right: '20px', 
        zIndex: 1000,
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '8px 12px',
        backgroundColor: 'white',
        borderRadius: '6px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        border: '1px solid #E5E7EB',
        fontSize: '12px'
      }}>
        {isSaving ? (
          <>
            <ClockCircleOutlined style={{ color: '#FF7A00' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>正在保存...</Text>
          </>
        ) : hasUnsavedChanges ? (
          <>
            <ClockCircleOutlined style={{ color: '#faad14' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>有未保存更改</Text>
          </>
        ) : lastSaveTime ? (
          <>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              已保存 {lastSaveTime.toLocaleTimeString()}
            </Text>
          </>
        ) : null}
      </div>

      {/* AI Voice Collection Card */}
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
          border: '1px solid #E5E7EB',
          marginBottom: '16px'
        }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button
            type="primary"
            shape="circle"
            icon={<AudioOutlined />}
            size="large"
            onClick={handleVoiceButtonClick}
            danger={isRecording}
            style={{
              width: '40px',
              height: '40px',
              fontSize: '18px'
            }}
          />
          <div style={{ flex: 1 }}>
            <Title level={5} style={{ margin: 0, marginBottom: '4px' }}>
              🎤 AI Voice Information Collection
            </Title>
            <Text style={{ fontSize: '14px' }}>
              Click the microphone to record your project. Key information will be extracted automatically by AI.
            </Text>
            {transcript && (
              <div style={{ 
                marginTop: '12px', 
                padding: '12px', 
                backgroundColor: '#F3F4F6', 
                borderRadius: '6px'
              }}>
                {transcript}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Module Navigation */}
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
          border: '1px solid #E5E7EB',
          marginBottom: '16px'
        }}
        bodyStyle={{ padding: '12px' }}
      >
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          {[
            { key: 'client', label: 'Client Information', color: '#3B82F6' },
            { key: 'opportunity', label: 'Opportunity Information', color: '#FF7A00' },
            { key: 'competitor', label: 'Competitor Information', color: '#10B981' },
            { key: 'other', label: 'Other Information', color: '#6B7280' }
          ].map(item => (
            <Button
              key={item.key}
              type={activeModule === item.key ? 'primary' : 'default'}
              onClick={async () => {
                console.log(`Switching to module: ${item.key}`);
                
                // 切换前先自动保存当前模块数据
                if (hasUnsavedChanges) {
                  await autoSave(false);
                }
                
                setActiveModule(item.key);
                
                // 如果切换到client模块，确保表单可编辑
                if (item.key === 'client') {
                  console.log('Enabling client form for editing');
                  // 强制重置表单状态
                  setTimeout(() => {
                    const formFields = clientForm.getFieldsValue();
                    console.log('Current client form values:', formFields);
                  }, 50);
                }
              }}
              style={{
                borderRadius: '6px',
                height: '32px',
                fontSize: '13px',
                ...(activeModule === item.key ? { backgroundColor: item.color, borderColor: item.color } : {})
              }}
            >
              {item.label}
            </Button>
          ))}
        </div>
      </Card>

      {/* Client Information Module */}
      {activeModule === 'client' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>👤 Client Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form 
            form={clientForm} 
            layout="vertical" 
            onFinish={handleSubmit}
            disabled={false}
            id="basic-info-form"
            style={{
              pointerEvents: 'auto'
            }}
            onFieldsChange={handleFieldChange}
          >
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="clientName" label="Client Name">
                  <Input 
                    placeholder="e.g. Acme Corporation" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text',
                      backgroundColor: 'white',
                      color: 'rgba(0, 0, 0, 0.88)'
                    }}
                    onChange={(e) => console.log('Client Name changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                    autoComplete="off"
                    key="client-name-force-editable"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="clientTier" label="Client Tier">
                  <Select placeholder="Select client tier" style={{ borderRadius: '6px' }}>
                    <Option value="SVIP">SVIP - Strategic VIP</Option>
                    <Option value="VIP">VIP - Very Important</Option>
                    <Option value="BA">BA - Business Account</Option>
                    <Option value="A">A - Standard</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="address" label="Address">
                  <Input 
                    placeholder="e.g. 123 Main Street, San Francisco, CA 94105" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text',
                      backgroundColor: 'white',
                      color: 'rgba(0, 0, 0, 0.88)'
                    }}
                    onChange={(e) => console.log('Address changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                    autoComplete="off"
                    key="address-force-editable"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="industry" label="Industry">
                  <Select 
                    placeholder="Type to search industry..." 
                    showSearch
                    allowClear
                    style={{ borderRadius: '6px' }}
                    filterOption={(input, option) =>
                      String(option?.children || '').toLowerCase().includes(input.toLowerCase())
                    }
                    optionFilterProp="children"
                  >
                    <Option value="Technology">💻 Technology</Option>
                    <Option value="Financial Services">🏦 Financial Services</Option>
                    <Option value="Healthcare">🏥 Healthcare</Option>
                    <Option value="Manufacturing">🏭 Manufacturing</Option>
                    <Option value="Retail">🛍️ Retail</Option>
                    <Option value="Education">🎓 Education</Option>
                    <Option value="Food & Beverage">🍽️ Food & Beverage</Option>
                    <Option value="Hospitality">🏨 Hospitality</Option>
                    <Option value="Real Estate">🏢 Real Estate</Option>
                    <Option value="Construction">🏗️ Construction</Option>
                    <Option value="Transportation">🚛 Transportation</Option>
                    <Option value="Energy">⚡ Energy</Option>
                    <Option value="Telecommunications">📡 Telecommunications</Option>
                    <Option value="Media & Entertainment">🎬 Media & Entertainment</Option>
                    <Option value="Government">🏛️ Government</Option>
                    <Option value="Non-profit">❤️ Non-profit</Option>
                    <Option value="Agriculture">🌾 Agriculture</Option>
                    <Option value="Automotive">🚗 Automotive</Option>
                    <Option value="Aerospace">✈️ Aerospace</Option>
                    <Option value="Consulting">💼 Consulting</Option>
                    <Option value="Legal">⚖️ Legal</Option>
                    <Option value="Insurance">🛡️ Insurance</Option>
                    <Option value="Pharmaceuticals">💊 Pharmaceuticals</Option>
                    <Option value="Chemicals">🧪 Chemicals</Option>
                    <Option value="Textiles">👕 Textiles</Option>
                    <Option value="Mining">⛏️ Mining</Option>
                    <Option value="Sports & Fitness">🏃 Sports & Fitness</Option>
                    <Option value="Beauty & Personal Care">💄 Beauty & Personal Care</Option>
                    <Option value="Gaming">🎮 Gaming</Option>
                    <Option value="E-commerce">🛒 E-commerce</Option>
                    <Option value="Other">📋 Other</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="keyStakeholder" label="Key Stakeholder">
                  <Input 
                    placeholder="e.g. John Smith (CEO)" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Key Stakeholder changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="primaryContact" label="Primary Contact">
                  <Input 
                    placeholder="e.g. Jane Doe (CTO)" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Primary Contact changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="email" label="Email">
                  <Input 
                    placeholder="e.g. <EMAIL>" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Email changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="contactPhone" label="Contact Phone">
                  <Input 
                    placeholder="e.g. +****************" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Contact Phone changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Client Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Opportunity Information Module */}
      {activeModule === 'opportunity' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>💼 Opportunity Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form 
            form={opportunityForm} 
            layout="vertical" 
            onFinish={handleSubmit}
            onFieldsChange={handleFieldChange}
          >
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="opportunityName" label="Opportunity Name">
                  <Input placeholder="e.g. Digital Transformation Project" style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="totalRevenue" label="Total Revenue">
                  <InputNumber 
                    placeholder="100000" 
                    style={{ borderRadius: '6px', width: '100%' }}
                    addonBefore="€"
                    min={0}
                    precision={0}
                    formatter={(value) => {
                      if (!value) return '';
                      return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    }}
                    parser={(value: any) => {
                      if (!value) return 0;
                      return value.replace(/[€\s,]/g, '');
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="winProbability" label={`Win Probability: ${winProbabilityValue}%`}>
                  <Slider
                    min={0}
                    max={100}
                    value={winProbabilityValue}
                    onChange={(value) => {
                      setWinProbabilityValue(value);
                      handleFieldChange();
                    }}
                    marks={{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="currentStage" label="Current Stage">
                  <Select placeholder="Select stage" style={{ borderRadius: '6px' }}>
                    <Option value="qualification">Qualification</Option>
                    <Option value="proposal">Proposal</Option>
                    <Option value="negotiation">Negotiation</Option>
                    <Option value="closed_won">Closed Won</Option>
                    <Option value="closed_lost">Closed Lost</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item name="background" label="Background">
                  <TextArea rows={4} placeholder="Describe the opportunity background..." style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Opportunity Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Competitor Information Module */}
      {activeModule === 'competitor' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>🏆 Competitor Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form 
            form={competitorForm} 
            layout="vertical" 
            onFinish={handleSubmit}
            onFieldsChange={handleFieldChange}
          >
            <Row gutter={[16, 8]}>
              {/* Competitor Existence Toggle */}
              <Col xs={24}>
                <Form.Item label="Do you have competitors?" style={{ marginBottom: '20px' }}>
                  <Switch
                    checked={hasCompetitors}
                    onChange={(checked) => {
                      setHasCompetitors(checked);
                      if (!checked) {
                        // Clear competitor data when "No" is selected
                        setCompetitorTags([]);
                        competitorForm.setFieldsValue({
                          competitorNames: [],
                          competitorNotes: ''
                        });
                      }
                      handleFieldChange();
                    }}
                    checkedChildren="Yes"
                    unCheckedChildren="No"
                    style={{
                      backgroundColor: hasCompetitors ? '#52c41a' : '#d9d9d9'
                    }}
                  />
                  <span style={{ marginLeft: '12px', color: '#666', fontSize: '14px' }}>
                    {hasCompetitors ? 'Yes, we have competitors' : 'No, no direct competitors'}
                  </span>
                </Form.Item>
              </Col>

              {/* Competitor Names - Only show if hasCompetitors is true */}
              {hasCompetitors && (
                <Col xs={24}>
                  <Form.Item name="competitorNames" label="Competitor Names">
                    <Select
                      mode="tags"
                      placeholder="Enter competitor names (press Enter to add each one)"
                      style={{ borderRadius: '6px' }}
                      value={competitorTags}
                      onChange={(tags) => {
                        setCompetitorTags(tags);
                        handleFieldChange();
                      }}
                      tokenSeparators={[',']}
                      maxTagCount={10}
                      maxTagTextLength={50}
                    >
                      {/* Pre-defined common competitors */}
                      <Select.Option value="Microsoft">Microsoft</Select.Option>
                      <Select.Option value="Google">Google</Select.Option>
                      <Select.Option value="Amazon">Amazon</Select.Option>
                      <Select.Option value="IBM">IBM</Select.Option>
                      <Select.Option value="Oracle">Oracle</Select.Option>
                      <Select.Option value="SAP">SAP</Select.Option>
                      <Select.Option value="Salesforce">Salesforce</Select.Option>
                      <Select.Option value="Adobe">Adobe</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              )}

              {/* Competitor Analysis */}
              <Col xs={24}>
                <Form.Item 
                  name="competitorNotes" 
                  label="Competitor Analysis"
                  extra={hasCompetitors ? (
                    <Alert
                      message="SWOT Analysis Framework"
                      description={
                        <div style={{ marginTop: '8px' }}>
                          <div style={{ marginBottom: '8px' }}>
                            <strong>Use this framework to analyze your competitors:</strong>
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="green">Strengths</Tag> What advantages do they have? (e.g., market share, technology, brand recognition)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="red">Weaknesses</Tag> What are their limitations? (e.g., pricing, customer service, outdated technology)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="blue">Opportunities</Tag> What market gaps can you exploit? (e.g., unserved segments, emerging technologies)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="orange">Threats</Tag> What competitive risks exist? (e.g., new entrants, price wars, market consolidation)
                          </div>
                          <div style={{ marginTop: '8px', fontStyle: 'italic', color: '#666' }}>
                            <strong>Example:</strong> "Competitor A has strong brand recognition (Strength) but higher pricing (Weakness). 
                            They're expanding into mobile solutions (Threat) but lack AI capabilities (Opportunity for us)."
                          </div>
                        </div>
                      }
                      type="info"
                      showIcon
                      style={{ marginBottom: '12px' }}
                    />
                  ) : null}
                >
                  <TextArea
                    rows={hasCompetitors ? 8 : 4}
                    placeholder={
                      hasCompetitors 
                        ? "Analyze the competitive landscape using the SWOT framework above. Include key competitors, their strengths, weaknesses, market positioning, and competitive threats..."
                        : "Since there are no direct competitors, describe the unique market position and any potential indirect competition or substitutes..."
                    }
                    style={{ borderRadius: '6px' }}
                    disabled={!hasCompetitors}
                  />
                </Form.Item>
              </Col>

              {/* No Competitors Information */}
              {!hasCompetitors && (
                <Col xs={24}>
                  <Alert
                    message="No Direct Competitors"
                    description={
                      <div>
                        <p>You've indicated that there are no direct competitors. Consider analyzing:</p>
                        <ul style={{ marginLeft: '20px', marginBottom: '8px' }}>
                          <li>Indirect competitors or substitute products/services</li>
                          <li>Potential future competitors entering the market</li>
                          <li>Your unique value proposition and market positioning</li>
                          <li>Barriers to entry that protect your market position</li>
                        </ul>
                      </div>
                    }
                    type="success"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                </Col>
              )}
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Competitor Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Other Information Module */}
      {activeModule === 'other' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>📋 Other Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form 
            form={otherForm} 
            layout="vertical" 
            onFinish={handleSubmit}
            onFieldsChange={handleFieldChange}
          >
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="source" label="Lead Source">
                  <Select placeholder="Select lead source" style={{ borderRadius: '6px' }}>
                    <Option value="website">Website</Option>
                    <Option value="referral">Referral</Option>
                    <Option value="cold_call">Cold Call</Option>
                    <Option value="trade_show">Trade Show</Option>
                    <Option value="social_media">Social Media</Option>
                    <Option value="other">Other</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="tags" label="Tags">
                  <Input placeholder="e.g. high-priority, enterprise, strategic" style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item name="clientAnalysis" label="Client Analysis">
                  <TextArea
                    rows={6}
                    placeholder="Additional analysis about the client, market position, decision-making process..."
                    style={{ borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Other Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Save & Proceed 按钮 */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>
    </div>
  );
};

export default BasicInfo; 