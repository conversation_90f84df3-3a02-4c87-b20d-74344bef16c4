import { Request, Response, NextFunction } from 'express';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

/**
 * 验证请求体中的数据
 * @param type 要验证的类
 * @param skipMissingProperties 是否跳过缺失的属性
 */
export function validateBody(type: any, skipMissingProperties = false) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 将请求体转换为指定的类
      const instance = plainToClass(type, req.body);
      
      // 验证实例
      const errors = await validate(instance, { 
        skipMissingProperties,
        whitelist: true, // 只允许验证类中定义的属性
        forbidNonWhitelisted: true // 禁止未在类中定义的属性
      });
      
      // 如果有错误，返回400错误
      if (errors.length > 0) {
        // 格式化错误信息
        const formattedErrors = errors.reduce((acc: Record<string, string[]>, error) => {
          const property = error.property;
          const constraints = error.constraints || {};
          
          acc[property] = Object.values(constraints);
          return acc;
        }, {});
        
        return res.status(400).json({
          message: '数据验证失败',
          errors: formattedErrors
        });
      }
      
      // 将验证后的实例添加到请求对象
      req.body = instance;
      
      next();
    } catch (error) {
      console.error('验证中间件错误:', error);
      return res.status(500).json({
        message: '验证过程中发生错误',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
}

/**
 * 验证请求参数
 * @param type 要验证的类
 */
export function validateParams(type: any) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 将请求参数转换为指定的类
      const instance = plainToClass(type, req.params);
      
      // 验证实例
      const errors = await validate(instance);
      
      // 如果有错误，返回400错误
      if (errors.length > 0) {
        // 格式化错误信息
        const formattedErrors = errors.reduce((acc: Record<string, string[]>, error) => {
          const property = error.property;
          const constraints = error.constraints || {};
          
          acc[property] = Object.values(constraints);
          return acc;
        }, {});
        
        return res.status(400).json({
          message: '参数验证失败',
          errors: formattedErrors
        });
      }
      
      next();
    } catch (error) {
      console.error('验证中间件错误:', error);
      return res.status(500).json({
        message: '验证过程中发生错误',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
}

/**
 * 验证查询参数
 * @param type 要验证的类
 */
export function validateQuery(type: any) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 将查询参数转换为指定的类
      const instance = plainToClass(type, req.query);
      
      // 验证实例
      const errors = await validate(instance, { skipMissingProperties: true });
      
      // 如果有错误，返回400错误
      if (errors.length > 0) {
        // 格式化错误信息
        const formattedErrors = errors.reduce((acc: Record<string, string[]>, error) => {
          const property = error.property;
          const constraints = error.constraints || {};
          
          acc[property] = Object.values(constraints);
          return acc;
        }, {});
        
        return res.status(400).json({
          message: '查询参数验证失败',
          errors: formattedErrors
        });
      }
      
      next();
    } catch (error) {
      console.error('验证中间件错误:', error);
      return res.status(500).json({
        message: '验证过程中发生错误',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  };
}
