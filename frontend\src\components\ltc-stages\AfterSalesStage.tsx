import React, { useState } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Table, 
  Tag, 
  Tabs, 
  Statistic, 
  Space,
  Form,
  Input,
  Select,
  DatePicker,
  Modal,
  Typography,
  Progress,
  List,
  Timeline,
  Avatar,
  message
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CustomerServiceOutlined,
  ToolOutlined,
  FileTextOutlined,
  Bar<PERSON>hartOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  RightOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface AfterSalesStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

// 服务工单接口
interface ServiceTicket {
  id: string;
  title: string;
  type: string;
  priority: string;
  status: string;
  createdAt: string;
  assignee: string;
  description: string;
  customer: string;
  sla: string;
}

// 维护活动接口
interface MaintenanceActivity {
  id: string;
  title: string;
  type: string;
  status: string;
  date: string;
  duration: string;
  notes: string;
  impact: string;
  affectedSystems: string[];
}

// 客户反馈接口
interface CustomerFeedback {
  id: string;
  customer: string;
  rating: number;
  comment: string;
  date: string;
  category: string;
}

const AfterSalesStage: React.FC<AfterSalesStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [ticketModalVisible, setTicketModalVisible] = useState(false);
  const [maintenanceModalVisible, setMaintenanceModalVisible] = useState(false);
  const [editTicketModalVisible, setEditTicketModalVisible] = useState(false);
  const [editingTicket, setEditingTicket] = useState<any>(null);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [editFeedbackModalVisible, setEditFeedbackModalVisible] = useState(false);
  const [editingFeedback, setEditingFeedback] = useState<any>(null);
  const [ticketForm] = Form.useForm();
  const [editTicketForm] = Form.useForm();
  const [feedbackForm] = Form.useForm();
  const [editFeedbackForm] = Form.useForm();

  // 服务请求数据 - 新项目从空白开始
  const [tickets, setTickets] = useState<ServiceTicket[]>([]);

  // 维护活动数据 - 新项目从空白开始
  const [maintenanceActivities, setMaintenanceActivities] = useState<MaintenanceActivity[]>([]);

  // 客户反馈数据 - 新项目从空白开始
  const [customerFeedback, setCustomerFeedback] = useState<CustomerFeedback[]>([]);

  const handleSave = () => {
    if (onSave) {
      onSave({
        tickets,
        maintenanceActivities,
        customerFeedback,
        type: 'after_sales',
        status: 'active',
        updatedAt: new Date().toISOString()
      });
    }
  };

  const handleProceed = () => {
    if (onProceed) {
      onProceed('finance');
    }
  };

  // Ticket编辑和删除处理函数
  const handleEditTicket = (ticket: any) => {
    setEditingTicket(ticket);
    editTicketForm.setFieldsValue({
      title: ticket.title,
      customer: ticket.customer,
      type: ticket.type,
      priority: ticket.priority,
      description: ticket.description,
      assignee: ticket.assignee,
      status: ticket.status
    });
    setEditTicketModalVisible(true);
  };

  const handleSaveEditTicket = async () => {
    try {
      const values = await editTicketForm.validateFields();
      const updatedTickets = tickets.map(ticket => 
        ticket.id === editingTicket.id 
          ? { ...ticket, ...values, updatedAt: new Date().toISOString() }
          : ticket
      );
      setTickets(updatedTickets);
      setEditTicketModalVisible(false);
      setEditingTicket(null);
      message.success('Ticket updated successfully!');
    } catch (error) {
      console.error('Error updating ticket:', error);
      message.error('Failed to update ticket');
    }
  };

  const handleDeleteTicket = (ticket: any) => {
    Modal.confirm({
      title: 'Delete Ticket',
      content: `Are you sure you want to delete ticket #${ticket.id}: "${ticket.title}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        const updatedTickets = tickets.filter(t => t.id !== ticket.id);
        setTickets(updatedTickets);
        message.success('Ticket deleted successfully!');
      }
    });
  };

  // Customer Feedback编辑和删除处理函数
  const handleEditFeedback = (feedback: any) => {
    setEditingFeedback(feedback);
    editFeedbackForm.setFieldsValue({
      customer: feedback.customer,
      rating: feedback.rating,
      comment: feedback.comment,
      category: feedback.category
    });
    setEditFeedbackModalVisible(true);
  };

  const handleSaveEditFeedback = async () => {
    try {
      const values = await editFeedbackForm.validateFields();
      const updatedFeedback = customerFeedback.map(feedback => 
        feedback.id === editingFeedback.id 
          ? { ...feedback, ...values, updatedAt: new Date().toISOString() }
          : feedback
      );
      setCustomerFeedback(updatedFeedback);
      setEditFeedbackModalVisible(false);
      setEditingFeedback(null);
      message.success('Feedback updated successfully!');
    } catch (error) {
      console.error('Error updating feedback:', error);
      message.error('Failed to update feedback');
    }
  };

  const handleDeleteFeedback = (feedback: any) => {
    Modal.confirm({
      title: 'Delete Feedback',
      content: `Are you sure you want to delete feedback from "${feedback.customer}"?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        const updatedFeedback = customerFeedback.filter(f => f.id !== feedback.id);
        setCustomerFeedback(updatedFeedback);
        message.success('Feedback deleted successfully!');
      }
    });
  };

  const handleCreateFeedback = async () => {
    try {
      const values = await feedbackForm.validateFields();
      const newFeedback = {
        id: (customerFeedback.length + 1).toString(),
        ...values,
        date: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
      };
      setCustomerFeedback([...customerFeedback, newFeedback]);
      setFeedbackModalVisible(false);
      feedbackForm.resetFields();
      message.success('Feedback added successfully!');
    } catch (error) {
      console.error('Error creating feedback:', error);
      message.error('Failed to add feedback');
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'open':
        return <Tag icon={<ExclamationCircleOutlined />} color="warning">Open</Tag>;
      case 'in_progress':
        return <Tag icon={<ClockCircleOutlined />} color="processing">In Progress</Tag>;
      case 'resolved':
        return <Tag icon={<CheckCircleOutlined />} color="success">Resolved</Tag>;
      case 'closed':
        return <Tag color="default">Closed</Tag>;
      case 'upcoming':
        return <Tag color="blue">Upcoming</Tag>;
      case 'completed':
        return <Tag color="success">Completed</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const getPriorityTag = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Tag color="red">High</Tag>;
      case 'medium':
        return <Tag color="orange">Medium</Tag>;
      case 'low':
        return <Tag color="green">Low</Tag>;
      default:
        return <Tag color="default">{priority}</Tag>;
    }
  };

  const ticketColumns = [
    {
      title: 'Ticket ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 200,
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      width: 150,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeMap: Record<string, { color: string, text: string }> = {
          'issue': { color: 'error', text: 'Issue' },
          'support': { color: 'processing', text: 'Support' },
          'enhancement': { color: 'success', text: 'Enhancement' },
        };
        const { color, text } = typeMap[type] || { color: 'default', text: type };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => getPriorityTag(priority),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'SLA',
      dataIndex: 'sla',
      key: 'sla',
      width: 100,
    },
    {
      title: 'Assignee',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
    },
    {
      title: 'Actions',
      key: 'action',
      width: 100,
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditTicket(record)}
          />
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteTicket(record)}
          />
        </Space>
      ),
    },
  ];

  const maintenanceColumns = [
    {
      title: 'Activity Name',
      dataIndex: 'title',
      key: 'title',
      width: 280,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 140,
      render: (type: string) => {
        return type === 'scheduled' ?
          <Tag color="blue">Scheduled</Tag> :
          <Tag color="red">Emergency</Tag>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 140,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 140,
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      width: 120,
    },
    {
      title: 'Impact',
      dataIndex: 'impact',
      key: 'impact',
      width: 120,
      render: (impact: string) => {
        const color = impact === 'High' ? 'red' : impact === 'Medium' ? 'orange' : 'green';
        return <Tag color={color}>{impact}</Tag>;
      },
    },
    {
      title: 'Actions',
      key: 'action',
      width: 100,
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
          />
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />} 
            size="small"
          />
        </Space>
      ),
    },
  ];

  const feedbackColumns = [
    {
      title: 'Feedback ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: 'Customer',
      dataIndex: 'customer',
      key: 'customer',
      width: 150,
      render: (customer: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Avatar 
            size={24} 
            style={{ 
              backgroundColor: '#1890ff',
              fontSize: '12px',
              fontWeight: '500'
            }}
          >
            {customer.charAt(0)}
          </Avatar>
          <span>{customer}</span>
        </div>
      ),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '14px', fontWeight: '600', color: '#1890ff' }}>
            {rating}
          </span>
          <Progress 
            percent={rating * 20} 
            size="small"
            showInfo={false}
            strokeColor="#1890ff"
            style={{ width: '50px' }}
          />
        </div>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 130,
      render: (category: string) => {
        const categoryColors: Record<string, string> = {
          'Support Quality': 'blue',
          'Maintenance': 'green',
          'Response Time': 'orange',
          'Product Quality': 'purple',
          'Service': 'cyan'
        };
        return <Tag color={categoryColors[category] || 'blue'}>{category}</Tag>;
      },
    },
    {
      title: 'Comment',
      dataIndex: 'comment',
      key: 'comment',
      width: 300,
      render: (comment: string) => (
        <Text 
          style={{ 
            fontSize: '13px', 
            lineHeight: '1.4',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
        >
          {comment}
        </Text>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {date}
        </Text>
      ),
    },
    {
      title: 'Actions',
      key: 'action',
      width: 100,
      render: (_: any, record: any) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditFeedback(record)}
          />
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteFeedback(record)}
          />
        </Space>
      ),
    },
  ];

  // 计算统计数据
  const openTickets = tickets.filter(t => t.status === 'open' || t.status === 'in_progress').length;
  const resolvedTickets = tickets.filter(t => t.status === 'resolved' || t.status === 'closed').length;
  const avgRating = customerFeedback.reduce((sum, f) => sum + f.rating, 0) / customerFeedback.length;
  const slaCompliance = 95; // Mock data

  // 生成Recent Activities数据
  const generateRecentActivities = () => {
    const activities: Array<{
      id: string;
      type: 'ticket' | 'maintenance' | 'feedback';
      title: string;
      description: string;
      date: string;
      color: string;
      priority?: string;
    }> = [];

    // 从tickets中提取活动
    tickets.forEach(ticket => {
      const ticketDate = new Date(ticket.createdAt);
      let activityTitle = '';
      let activityColor = 'blue';
      
      if (ticket.status === 'resolved' || ticket.status === 'closed') {
        activityTitle = `Ticket #${ticket.id} Resolved`;
        activityColor = 'green';
      } else if (ticket.status === 'in_progress') {
        activityTitle = `Ticket #${ticket.id} In Progress`;
        activityColor = 'blue';
      } else {
        activityTitle = `New Ticket #${ticket.id} Created`;
        activityColor = 'orange';
      }

      activities.push({
        id: `ticket-${ticket.id}`,
        type: 'ticket',
        title: activityTitle,
        description: `${ticket.title} - ${ticket.customer}`,
        date: ticket.createdAt,
        color: activityColor,
        priority: ticket.priority
      });
    });

    // 从maintenance activities中提取活动
    maintenanceActivities.forEach(maintenance => {
      let activityTitle = '';
      let activityColor = 'blue';
      
      if (maintenance.status === 'completed') {
        activityTitle = `Maintenance Completed`;
        activityColor = 'green';
      } else if (maintenance.status === 'upcoming') {
        activityTitle = `Maintenance Scheduled`;
        activityColor = 'blue';
      }

      if (maintenance.type === 'emergency') {
        activityTitle = `Emergency Maintenance`;
        activityColor = 'red';
      }

      activities.push({
        id: `maintenance-${maintenance.id}`,
        type: 'maintenance',
        title: activityTitle,
        description: `${maintenance.title} - ${maintenance.impact} impact`,
        date: maintenance.date,
        color: activityColor
      });
    });

    // 从customer feedback中提取活动
    customerFeedback.forEach(feedback => {
      activities.push({
        id: `feedback-${feedback.id}`,
        type: 'feedback',
        title: `Customer Feedback Received`,
        description: `${feedback.customer} - ${feedback.rating}/5.0 rating (${feedback.category})`,
        date: feedback.date,
        color: feedback.rating >= 4 ? 'green' : feedback.rating >= 3 ? 'blue' : 'orange'
      });
    });

    // 按日期排序，最新的在前
    return activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 8);
  };

  // 计算时间差
  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
  };

  const recentActivities = generateRecentActivities();

  return (
    <div className="ltc-stage-content">
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h2 className="ltc-stage-title">After-Sales Service</h2>
      </div>

      {/* Tab导航 */}
      <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
        {/* 概览标签页 */}
        <TabPane 
          tab={
            <span>
              <BarChartOutlined />
              Overview
            </span>
          } 
          key="overview"
        >
          {/* 关键指标 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Open Tickets"
                  value={openTickets}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Resolved Tickets"
                  value={resolvedTickets}
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="Customer Rating"
                  value={avgRating.toFixed(1)}
                  precision={1}
                  suffix="/ 5.0"
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card className="ltc-stage-card">
                <Statistic
                  title="SLA Compliance"
                  value={slaCompliance}
                  suffix="%"
                />
              </Card>
            </Col>
          </Row>

          {/* 活动时间轴 */}
          <Card className="ltc-stage-card" title="Recent Activities">
            <Timeline>
              {recentActivities.map((activity) => (
                <Timeline.Item key={activity.id} color={activity.color}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <Text strong>{activity.title}</Text>
                      {activity.priority && (
                        <Tag 
                          color={activity.priority === 'high' ? 'red' : activity.priority === 'medium' ? 'orange' : 'green'}
                          style={{ marginLeft: '8px', fontSize: '11px' }}
                        >
                          {activity.priority}
                        </Tag>
                      )}
                      <br />
                      <Text type="secondary" style={{ fontSize: '13px' }}>
                        {activity.description}
                      </Text>
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px', whiteSpace: 'nowrap', marginLeft: '12px' }}>
                      {getTimeAgo(activity.date)}
                    </Text>
                  </div>
                </Timeline.Item>
              ))}
              {recentActivities.length === 0 && (
                <Timeline.Item color="gray">
                  <Text type="secondary">No recent activities</Text>
                </Timeline.Item>
              )}
            </Timeline>
          </Card>
        </TabPane>

        {/* 服务票证标签页 */}
        <TabPane 
          tab={
            <span>
              <CustomerServiceOutlined />
              Service Tickets
            </span>
          } 
          key="tickets"
        >
          <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setTicketModalVisible(true)}>
              Create Ticket
            </Button>
          </div>
          
          <Card className="ltc-stage-card">
            <Table
              columns={ticketColumns}
              dataSource={tickets}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        {/* 维护管理标签页 */}
        <TabPane 
          tab={
            <span>
              <ToolOutlined />
              Maintenance
            </span>
          } 
          key="maintenance"
        >
          <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setMaintenanceModalVisible(true)}>
              Schedule Maintenance
            </Button>
          </div>
          
          <Card className="ltc-stage-card">
            <Table
              columns={maintenanceColumns}
              dataSource={maintenanceActivities}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>

        {/* 客户反馈标签页 */}
        <TabPane 
          tab={
            <span>
              <FileTextOutlined />
              Customer Feedback
            </span>
          } 
          key="feedback"
        >
          <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setFeedbackModalVisible(true)}>
                Add Feedback
              </Button>
          </div>

          <Card className="ltc-stage-card">
            <Table
              columns={feedbackColumns}
              dataSource={customerFeedback}
              rowKey="id"
              pagination={{ pageSize: 10 }}
                  size="small" 
            />
          </Card>
        </TabPane>
      </Tabs>



      {/* 创建票证模态框 */}
      <Modal
        title="Create Service Ticket"
        visible={ticketModalVisible}
        onCancel={() => setTicketModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form layout="vertical">
          <Form.Item name="title" label="Title" rules={[{ required: true }]}>
            <Input placeholder="Enter ticket title" />
          </Form.Item>
          <Form.Item name="customer" label="Customer" rules={[{ required: true }]}>
            <Select placeholder="Select customer">
              <Option value="Acme Corp">Acme Corp</Option>
              <Option value="TechStart Inc">TechStart Inc</Option>
              <Option value="Global Systems">Global Systems</Option>
              <Option value="DataFlow Ltd">DataFlow Ltd</Option>
            </Select>
          </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true }]}>
            <Select placeholder="Select type">
              <Option value="issue">Issue</Option>
              <Option value="support">Support</Option>
              <Option value="enhancement">Enhancement</Option>
            </Select>
          </Form.Item>
          <Form.Item name="priority" label="Priority" rules={[{ required: true }]}>
            <Select placeholder="Select priority">
              <Option value="high">High</Option>
              <Option value="medium">Medium</Option>
              <Option value="low">Low</Option>
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description">
            <TextArea rows={4} placeholder="Describe the issue or request" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary">Create Ticket</Button>
              <Button onClick={() => setTicketModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 计划维护模态框 */}
      <Modal
        title="Schedule Maintenance"
        visible={maintenanceModalVisible}
        onCancel={() => setMaintenanceModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form layout="vertical">
          <Form.Item name="title" label="Activity Name" rules={[{ required: true }]}>
            <Input placeholder="Enter maintenance activity name" />
          </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true }]}>
            <Select placeholder="Select type">
              <Option value="scheduled">Scheduled</Option>
              <Option value="emergency">Emergency</Option>
            </Select>
          </Form.Item>
          <Form.Item name="date" label="Scheduled Date" rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="duration" label="Estimated Duration">
            <Input placeholder="e.g. 2 hours" />
          </Form.Item>
          <Form.Item name="impact" label="Expected Impact">
            <Select placeholder="Select impact level">
              <Option value="Low">Low</Option>
              <Option value="Medium">Medium</Option>
              <Option value="High">High</Option>
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description">
            <TextArea rows={4} placeholder="Describe the maintenance activity" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary">Schedule Maintenance</Button>
              <Button onClick={() => setMaintenanceModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑票证模态框 */}
      <Modal
        title="Edit Service Ticket"
        visible={editTicketModalVisible}
        onCancel={() => setEditTicketModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form form={editTicketForm} layout="vertical">
          <Form.Item name="title" label="Title" rules={[{ required: true }]}>
            <Input placeholder="Enter ticket title" />
          </Form.Item>
          <Form.Item name="customer" label="Customer" rules={[{ required: true }]}>
            <Select placeholder="Select customer">
              <Option value="Acme Corp">Acme Corp</Option>
              <Option value="TechStart Inc">TechStart Inc</Option>
              <Option value="Global Systems">Global Systems</Option>
              <Option value="DataFlow Ltd">DataFlow Ltd</Option>
            </Select>
          </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true }]}>
            <Select placeholder="Select type">
              <Option value="issue">Issue</Option>
              <Option value="support">Support</Option>
              <Option value="enhancement">Enhancement</Option>
            </Select>
          </Form.Item>
          <Form.Item name="priority" label="Priority" rules={[{ required: true }]}>
            <Select placeholder="Select priority">
              <Option value="high">High</Option>
              <Option value="medium">Medium</Option>
              <Option value="low">Low</Option>
            </Select>
          </Form.Item>
          <Form.Item name="status" label="Status" rules={[{ required: true }]}>
            <Select placeholder="Select status">
              <Option value="open">Open</Option>
              <Option value="in_progress">In Progress</Option>
              <Option value="resolved">Resolved</Option>
              <Option value="closed">Closed</Option>
            </Select>
          </Form.Item>
          <Form.Item name="assignee" label="Assignee">
            <Select placeholder="Select assignee">
              <Option value="John Smith">John Smith</Option>
              <Option value="Jane Doe">Jane Doe</Option>
              <Option value="Mike Johnson">Mike Johnson</Option>
              <Option value="Unassigned">Unassigned</Option>
            </Select>
          </Form.Item>
          <Form.Item name="description" label="Description">
            <TextArea rows={4} placeholder="Describe the issue or request" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSaveEditTicket}>Save Changes</Button>
              <Button onClick={() => setEditTicketModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加反馈模态框 */}
      <Modal
        title="Add Customer Feedback"
        visible={feedbackModalVisible}
        onCancel={() => setFeedbackModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form form={feedbackForm} layout="vertical">
          <Form.Item name="customer" label="Customer" rules={[{ required: true }]}>
            <Select placeholder="Select customer">
              <Option value="Acme Corp">Acme Corp</Option>
              <Option value="TechStart Inc">TechStart Inc</Option>
              <Option value="Global Systems">Global Systems</Option>
              <Option value="DataFlow Ltd">DataFlow Ltd</Option>
              <Option value="InnovateTech">InnovateTech</Option>
              <Option value="SmartSolutions Ltd">SmartSolutions Ltd</Option>
            </Select>
          </Form.Item>
          <Form.Item name="rating" label="Rating" rules={[{ required: true }]}>
            <Select placeholder="Select rating">
              <Option value={5.0}>5.0 - Excellent</Option>
              <Option value={4.5}>4.5 - Very Good</Option>
              <Option value={4.0}>4.0 - Good</Option>
              <Option value={3.5}>3.5 - Average</Option>
              <Option value={3.0}>3.0 - Fair</Option>
              <Option value={2.5}>2.5 - Below Average</Option>
              <Option value={2.0}>2.0 - Poor</Option>
              <Option value={1.5}>1.5 - Very Poor</Option>
              <Option value={1.0}>1.0 - Terrible</Option>
            </Select>
          </Form.Item>
          <Form.Item name="category" label="Category" rules={[{ required: true }]}>
            <Select placeholder="Select feedback category">
              <Option value="Support Quality">Support Quality</Option>
              <Option value="Maintenance">Maintenance</Option>
              <Option value="Response Time">Response Time</Option>
              <Option value="Product Quality">Product Quality</Option>
              <Option value="Service">Service</Option>
              <Option value="Communication">Communication</Option>
              <Option value="Overall Experience">Overall Experience</Option>
            </Select>
          </Form.Item>
          <Form.Item name="comment" label="Comment" rules={[{ required: true }]}>
            <TextArea rows={4} placeholder="Enter customer feedback comment" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleCreateFeedback}>Add Feedback</Button>
              <Button onClick={() => setFeedbackModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑反馈模态框 */}
      <Modal
        title="Edit Customer Feedback"
        visible={editFeedbackModalVisible}
        onCancel={() => setEditFeedbackModalVisible(false)}
        footer={null}
        className="ltc-modal"
      >
        <Form form={editFeedbackForm} layout="vertical">
          <Form.Item name="customer" label="Customer" rules={[{ required: true }]}>
            <Select placeholder="Select customer">
              <Option value="Acme Corp">Acme Corp</Option>
              <Option value="TechStart Inc">TechStart Inc</Option>
              <Option value="Global Systems">Global Systems</Option>
              <Option value="DataFlow Ltd">DataFlow Ltd</Option>
              <Option value="InnovateTech">InnovateTech</Option>
              <Option value="SmartSolutions Ltd">SmartSolutions Ltd</Option>
            </Select>
          </Form.Item>
          <Form.Item name="rating" label="Rating" rules={[{ required: true }]}>
            <Select placeholder="Select rating">
              <Option value={5.0}>5.0 - Excellent</Option>
              <Option value={4.5}>4.5 - Very Good</Option>
              <Option value={4.0}>4.0 - Good</Option>
              <Option value={3.5}>3.5 - Average</Option>
              <Option value={3.0}>3.0 - Fair</Option>
              <Option value={2.5}>2.5 - Below Average</Option>
              <Option value={2.0}>2.0 - Poor</Option>
              <Option value={1.5}>1.5 - Very Poor</Option>
              <Option value={1.0}>1.0 - Terrible</Option>
            </Select>
          </Form.Item>
          <Form.Item name="category" label="Category" rules={[{ required: true }]}>
            <Select placeholder="Select feedback category">
              <Option value="Support Quality">Support Quality</Option>
              <Option value="Maintenance">Maintenance</Option>
              <Option value="Response Time">Response Time</Option>
              <Option value="Product Quality">Product Quality</Option>
              <Option value="Service">Service</Option>
              <Option value="Communication">Communication</Option>
              <Option value="Overall Experience">Overall Experience</Option>
            </Select>
          </Form.Item>
          <Form.Item name="comment" label="Comment" rules={[{ required: true }]}>
            <TextArea rows={4} placeholder="Enter customer feedback comment" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSaveEditFeedback}>Save Changes</Button>
              <Button onClick={() => setEditFeedbackModalVisible(false)}>Cancel</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AfterSalesStage;
