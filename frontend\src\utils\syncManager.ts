import { ErrorHandler, ErrorType } from './errorHandler';

// 同步状态枚举
export enum SyncStatus {
  SYNCED = 'synced',         // 已同步
  PENDING = 'pending',       // 待同步
  CONFLICT = 'conflict',     // 冲突
  FAILED = 'failed',         // 同步失败
  OFFLINE = 'offline'        // 离线
}

// 同步项接口
export interface SyncItem<T> {
  id: string;                // 唯一标识符
  data: T;                   // 数据
  timestamp: number;         // 时间戳
  status: SyncStatus;        // 同步状态
  retryCount: number;        // 重试次数
  lastError?: string;        // 最后一次错误信息
}

// 同步队列接口
export interface SyncQueue<T> {
  items: SyncItem<T>[];      // 同步项列表
  entityType: string;        // 实体类型
}

// 同步管理器配置
export interface SyncManagerConfig {
  maxRetries: number;        // 最大重试次数
  retryDelay: number;        // 重试延迟（毫秒）
  syncInterval: number;      // 同步间隔（毫秒）
  conflictStrategy: 'client-wins' | 'server-wins' | 'manual'; // 冲突解决策略
  persistenceKey: string;    // 本地存储键
}

// 默认配置
const DEFAULT_CONFIG: SyncManagerConfig = {
  maxRetries: 3,
  retryDelay: 5000,
  syncInterval: 30000,
  conflictStrategy: 'client-wins',
  persistenceKey: 'sync_queue'
};

/**
 * 数据同步管理器
 * 处理离线/在线状态切换和数据同步
 */
export class SyncManager<T extends { id: string }> {
  private queue: SyncQueue<T>;
  private config: SyncManagerConfig;
  private syncTimer: number | null = null;
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;
  private onSyncCallbacks: ((status: SyncStatus, items?: SyncItem<T>[]) => void)[] = [];

  // API操作函数
  private apiCreate: (item: T) => Promise<T>;
  private apiUpdate: (id: string, item: T) => Promise<T>;
  private apiDelete: (id: string) => Promise<void>;
  private apiGet: (id: string) => Promise<T>;

  /**
   * 构造函数
   * @param entityType 实体类型
   * @param apiCreate 创建API函数
   * @param apiUpdate 更新API函数
   * @param apiDelete 删除API函数
   * @param apiGet 获取API函数
   * @param config 配置
   */
  constructor(
    entityType: string,
    apiCreate: (item: T) => Promise<T>,
    apiUpdate: (id: string, item: T) => Promise<T>,
    apiDelete: (id: string) => Promise<void>,
    apiGet: (id: string) => Promise<T>,
    config: Partial<SyncManagerConfig> = {}
  ) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.apiCreate = apiCreate;
    this.apiUpdate = apiUpdate;
    this.apiDelete = apiDelete;
    this.apiGet = apiGet;

    // 初始化同步队列
    this.queue = this.loadQueue(entityType) || {
      items: [],
      entityType
    };

    // 监听在线/离线状态变化
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // 如果在线，启动同步定时器
    if (this.isOnline) {
      this.startSyncTimer();
    }
  }

  /**
   * 添加同步回调
   * @param callback 回调函数
   */
  public addSyncCallback(callback: (status: SyncStatus, items?: SyncItem<T>[]) => void): void {
    this.onSyncCallbacks.push(callback);
  }

  /**
   * 移除同步回调
   * @param callback 回调函数
   */
  public removeSyncCallback(callback: (status: SyncStatus, items?: SyncItem<T>[]) => void): void {
    this.onSyncCallbacks = this.onSyncCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 触发同步回调
   * @param status 同步状态
   * @param items 同步项
   */
  private triggerSyncCallbacks(status: SyncStatus, items?: SyncItem<T>[]): void {
    this.onSyncCallbacks.forEach(callback => callback(status, items));
  }

  /**
   * 处理在线事件
   */
  private handleOnline = (): void => {
    console.log('网络连接已恢复');
    this.isOnline = true;
    this.triggerSyncCallbacks(SyncStatus.SYNCED);
    this.startSyncTimer();
    this.syncNow(); // 立即尝试同步
  };

  /**
   * 处理离线事件
   */
  private handleOffline = (): void => {
    console.log('网络连接已断开');
    this.isOnline = false;
    this.stopSyncTimer();
    this.triggerSyncCallbacks(SyncStatus.OFFLINE);
  };

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimer === null) {
      this.syncTimer = window.setInterval(() => this.syncNow(), this.config.syncInterval);
    }
  }

  /**
   * 停止同步定时器
   */
  private stopSyncTimer(): void {
    if (this.syncTimer !== null) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * 从本地存储加载同步队列
   * @param entityType 实体类型
   * @returns 同步队列
   */
  private loadQueue(entityType: string): SyncQueue<T> | null {
    try {
      const key = `${this.config.persistenceKey}_${entityType}`;
      const data = localStorage.getItem(key);
      if (data) {
        return JSON.parse(data);
      }
    } catch (error) {
      console.error('加载同步队列失败:', error);
    }
    return null;
  }

  /**
   * 保存同步队列到本地存储
   */
  private saveQueue(): void {
    try {
      const key = `${this.config.persistenceKey}_${this.queue.entityType}`;
      localStorage.setItem(key, JSON.stringify(this.queue));
    } catch (error) {
      console.error('保存同步队列失败:', error);
    }
  }

  /**
   * 添加项到同步队列
   * @param item 数据项
   * @param operation 操作类型
   * @returns 同步项
   */
  private addToQueue(item: T, operation: 'create' | 'update' | 'delete'): SyncItem<T> {
    // 检查是否已存在
    const existingIndex = this.queue.items.findIndex(i => i.id === item.id);

    // 创建新的同步项
    const syncItem: SyncItem<T> = {
      id: item.id,
      data: { ...item },
      timestamp: Date.now(),
      status: this.isOnline ? SyncStatus.PENDING : SyncStatus.OFFLINE,
      retryCount: 0
    };

    // 如果已存在，则更新
    if (existingIndex >= 0) {
      this.queue.items[existingIndex] = syncItem;
    } else {
      this.queue.items.push(syncItem);
    }

    // 保存队列
    this.saveQueue();

    // 如果在线，立即尝试同步
    if (this.isOnline) {
      this.syncNow();
    }

    return syncItem;
  }

  /**
   * 创建项
   * @param item 数据项
   * @returns 同步项
   */
  public create(item: T): SyncItem<T> {
    return this.addToQueue(item, 'create');
  }

  /**
   * 更新项
   * @param item 数据项
   * @returns 同步项
   */
  public update(item: T): SyncItem<T> {
    return this.addToQueue(item, 'update');
  }

  /**
   * 删除项
   * @param id 项ID
   * @returns 是否成功
   */
  public delete(id: string): boolean {
    const item = this.queue.items.find(i => i.id === id);
    if (!item) return false;

    item.status = this.isOnline ? SyncStatus.PENDING : SyncStatus.OFFLINE;
    this.saveQueue();

    if (this.isOnline) {
      this.syncNow();
    }

    return true;
  }

  /**
   * 立即同步
   */
  public syncNow(): void {
    if (!this.isOnline || this.syncInProgress) return;

    this.processSyncQueue();
  }

  /**
   * 处理同步队列
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || !this.isOnline || this.queue.items.length === 0) return;

    this.syncInProgress = true;
    this.triggerSyncCallbacks(SyncStatus.PENDING, this.queue.items);

    try {
      // 获取待同步的项
      const pendingItems = this.queue.items.filter(
        item => item.status === SyncStatus.PENDING || item.status === SyncStatus.OFFLINE
      );

      if (pendingItems.length === 0) {
        this.syncInProgress = false;
        this.triggerSyncCallbacks(SyncStatus.SYNCED);
        return;
      }

      // 处理每个待同步项
      for (const item of pendingItems) {
        await this.processSyncItem(item);
      }

      // 保存队列
      this.saveQueue();

      // 检查是否还有待同步项
      const remainingItems = this.queue.items.filter(
        item => item.status === SyncStatus.PENDING || item.status === SyncStatus.OFFLINE
      );

      if (remainingItems.length === 0) {
        this.triggerSyncCallbacks(SyncStatus.SYNCED);
      } else {
        this.triggerSyncCallbacks(SyncStatus.PENDING, remainingItems);
      }
    } catch (error) {
      console.error('同步过程中发生错误:', error);
      this.triggerSyncCallbacks(SyncStatus.FAILED);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * 处理单个同步项
   * @param item 同步项
   */
  private async processSyncItem(item: SyncItem<T>): Promise<void> {
    if (!this.isOnline) {
      item.status = SyncStatus.OFFLINE;
      return;
    }

    try {
      // 根据操作类型执行不同的API调用
      if (item.status === SyncStatus.PENDING || item.status === SyncStatus.OFFLINE) {
        // 尝试获取服务器上的最新数据
        let serverItem: T | null = null;
        try {
          serverItem = await this.apiGet(item.id);
        } catch (error) {
          // 如果是404错误，说明服务器上不存在该项，需要创建
          if (ErrorHandler.handle(error, undefined, false).type === ErrorType.NOT_FOUND) {
            serverItem = null;
          } else {
            throw error;
          }
        }

        // 如果服务器上存在该项，检查是否有冲突
        if (serverItem) {
          // 更新操作
          await this.apiUpdate(item.id, item.data);
        } else {
          // 创建操作
          await this.apiCreate(item.data);
        }

        // 同步成功
        item.status = SyncStatus.SYNCED;
        item.lastError = undefined;
      }
    } catch (error) {
      // 同步失败
      item.retryCount++;
      item.lastError = ErrorHandler.handle(error, undefined, false).message;

      // 如果超过最大重试次数，标记为失败
      if (item.retryCount >= this.config.maxRetries) {
        item.status = SyncStatus.FAILED;
      }
    }
  }

  /**
   * 解决冲突
   * @param itemId 项ID
   * @param resolution 解决方案
   */
  public resolveConflict(itemId: string, resolution: 'client' | 'server'): void {
    const item = this.queue.items.find(i => i.id === itemId && i.status === SyncStatus.CONFLICT);
    if (!item) return;

    if (resolution === 'client') {
      item.status = SyncStatus.PENDING;
    } else {
      // 从队列中移除
      this.queue.items = this.queue.items.filter(i => i.id !== itemId);
    }

    this.saveQueue();

    if (this.isOnline) {
      this.syncNow();
    }
  }

  /**
   * 清理已同步的项
   */
  public cleanup(): void {
    this.queue.items = this.queue.items.filter(
      item => item.status !== SyncStatus.SYNCED
    );
    this.saveQueue();
  }

  /**
   * 销毁同步管理器
   */
  public destroy(): void {
    this.stopSyncTimer();
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }
}
