/* Contract Component Styles */
.contract-container {
  width: 100%;
}

/* 合同文档卡片 */
.contract-card {
  margin-bottom: 24px;
}

/* 🔧 新增：合同签证信息样式 */
.signing-info-item {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  height: 100%;
  transition: all 0.2s ease;
}

.signing-info-item:hover {
  background-color: #f5f5f5;
  border-color: #FF7A00;
  box-shadow: 0 2px 4px rgba(255, 122, 0, 0.1);
}

.signing-info-item .ant-typography {
  margin-bottom: 0;
}

/* 签证状态图标样式 */
.signing-status-icon {
  font-size: 16px;
  margin-right: 8px;
}

.signing-status-icon.signed {
  color: #52c41a;
}

.signing-status-icon.pending {
  color: #faad14;
}

.signing-status-icon.not-signed {
  color: #d9d9d9;
}

/* 编辑按钮样式 */
.signing-edit-btn {
  color: #FF7A00 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  transition: all 0.2s ease;
}

.signing-edit-btn:hover {
  color: #e66a00 !important;
  background-color: rgba(255, 122, 0, 0.1) !important;
}

/* 表单保存按钮样式 */
.signing-save-btn {
  background-color: #FF7A00 !important;
  border-color: #FF7A00 !important;
  transition: all 0.2s ease;
}

.signing-save-btn:hover {
  background-color: #e66a00 !important;
  border-color: #e66a00 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .signing-info-item {
    margin-bottom: 16px;
    padding: 12px;
  }
  
  .signing-info-item .ant-typography {
    font-size: 14px;
  }
}

.contract-upload-area {
  margin-bottom: 24px;
}

/* 表格标题 */
.table-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
}

/* 版本链接样式 */
.version-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.version-link:hover {
  text-decoration: underline;
}

.version-link::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%230C7BEC' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'%3E%3C/path%3E%3Cpolyline points='14 2 14 8 20 8'%3E%3C/polyline%3E%3Cline x1='16' y1='13' x2='8' y2='13'%3E%3C/line%3E%3Cline x1='16' y1='17' x2='8' y2='17'%3E%3C/line%3E%3Cpolyline points='10 9 9 9 8 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
}

/* 合同类型样式 */
.contract-type {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.contract-type.blue {
  background-color: #e6f7ff;
  color: #1890ff;
}

.contract-type.green {
  background-color: #f6ffed;
  color: #52c41a;
}

.contract-type.purple {
  background-color: #f9f0ff;
  color: #722ed1;
}

.contract-type.default {
  background-color: #f5f5f5;
  color: #595959;
}

/* 状态标签样式 */
.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-submitted {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-internal {
  background-color: #f5f5f5;
  color: #595959;
}

/* 操作图标样式 */
.action-icons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 16px !important;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background-color: #f5f5f5;
  transform: scale(1.1);
}

/* 查看按钮 */
.view-icon:hover {
  color: #1890ff;
  background-color: #e6f7ff;
}

/* 编辑按钮 */
.edit-icon:hover {
  color: #fa8c16;
  background-color: #fff7e6;
}

/* 删除按钮 */
.delete-icon:hover {
  color: #ff4d4f;
  background-color: #fff2f0;
}

/* 空状态 */
.empty-state {
  padding: 48px 24px;
  text-align: center;
}

/* 上传对话框 */
.upload-dialog .ant-modal-body {
  padding: 24px;
}

.upload-dialog .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.upload-dialog .ant-modal-title {
  font-size: 14px !important;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.upload-dialog .ant-modal-close {
  color: rgba(0, 0, 0, 0.45);
}

.upload-dialog .ant-modal-close:hover {
  color: rgba(0, 0, 0, 0.75);
}

/* 上传区域 */
.upload-dropzone {
  margin-bottom: 16px;
}

/* 上传进度条 */
.upload-progress {
  margin-bottom: 16px;
}

/* 对话框底部按钮 */
.upload-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

/* 流转按钮区域 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.btn-workflow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px !important;
  font-weight: 500;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-workflow:hover {
  background-color: #e6e6e6;
  border-color: #d9d9d9;
}

.btn-workflow .anticon {
  transition: transform 0.3s ease;
}

.btn-workflow:hover .anticon {
  transform: translateX(3px);
}

/* 成功提示 */
.toast {
  position: fixed;
  bottom: 24px;
  right: 24px;
  background-color: #52c41a;
  color: #fff;
  padding: 12px 16px;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.toast.show {
  opacity: 1;
  transform: translateY(0);
}

.toast-icon {
  font-size: 12px !important;
}

.toast-text {
  font-size: 12px !important;
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .upload-dialog-footer {
    flex-direction: column;
  }

  .upload-dialog-footer button {
    width: 100%;
  }
}

/* 模态窗口样式 */
.contract-detail {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  min-width: 100px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-right: 16px;
  flex-shrink: 0;
}

.detail-value {
  color: rgba(0, 0, 0, 0.65);
  flex: 1;
  word-break: break-all;
}
