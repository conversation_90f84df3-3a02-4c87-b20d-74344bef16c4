import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToMany } from 'typeorm';
import { ProjectStage } from './ProjectStage';
import { TeamMember } from './TeamMember';
import { Document } from './Document';
import { ProjectCollaborator } from './ProjectCollaborator';

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  projectId: string;

  @Column()
  name: string;

  @Column()
  client: string;

  @Column()
  category: string;

  @Column('decimal')
  revenue: number;

  @Column({
    type: 'varchar',
    default: 'planning'
  })
  status: 'planning' | 'in_progress' | 'completed' | 'on_hold';

  @Column('decimal')
  probability: number;

  @Column()
  owner: string;

  @Column()
  country: string;

  @Column('text')
  description: string;

  @OneToMany(() => ProjectStage, stage => stage.project, { cascade: true })
  stages: ProjectStage[];

  @ManyToMany(() => TeamMember, teamMember => teamMember.projects)
  teamMembers: TeamMember[];

  @OneToMany(() => Document, document => document.project)
  documents: Document[];

  @OneToMany(() => ProjectCollaborator, collaborator => collaborator.project, { cascade: true })
  collaborators: ProjectCollaborator[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}