import { Notification, NotificationStats } from '../types';

class NotificationService {
  private baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5002/api';
  private storageKey = 'smartltc_notifications';
  
  // 开发模式标志 - 在开发模式下完全使用localStorage存储，不进行API调用
  private isDevelopmentMode = process.env.NODE_ENV === 'development' || !process.env.REACT_APP_API_BASE_URL;
  
  // 从localStorage获取通知数据
  private getStoredNotifications(): Notification[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading notifications from localStorage:', error);
      return [];
    }
  }

  // 保存通知数据到localStorage
  private saveNotifications(notifications: Notification[]): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(notifications));
    } catch (error) {
      console.error('Error saving notifications to localStorage:', error);
    }
  }

  // 生成新的通知ID
  private generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 创建新通知
  async createNotification(notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead' | 'isStarred'>): Promise<Notification> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(notificationData)
        });
        
        if (response.ok) {
          const data = await response.json();
          return data;
        } else {
          throw new Error('Failed to create notification');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const newNotification: Notification = {
      id: this.generateNotificationId(),
      ...notificationData,
      isRead: false,
      isStarred: false,
      createdAt: new Date().toISOString()
    };
    
    const notifications = this.getStoredNotifications();
    notifications.unshift(newNotification); // 新通知放在最前面
    this.saveNotifications(notifications);
    
    console.log('NotificationService: Created new notification:', newNotification);
    return newNotification;
  }

  // 获取通知列表
  async getNotifications(page = 1, limit = 20): Promise<{ notifications: Notification[], stats: NotificationStats }> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications?page=${page}&limit=${limit}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          return data;
        } else {
          throw new Error('Failed to fetch notifications');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储的数据
    const allNotifications = this.getStoredNotifications();
    const unreadCount = allNotifications.filter(n => !n.isRead).length;
    
    // 按创建时间倒序排序
    allNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    const result = {
      notifications: allNotifications.slice((page - 1) * limit, page * limit),
      stats: {
        unreadCount,
        totalCount: allNotifications.length,
        lastReadAt: new Date().toISOString()
      }
    };
    
    return Promise.resolve(result);
  }

  // 标记通知为已读
  async markAsRead(notificationId: string): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/${notificationId}/read`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to mark notification as read');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const notifications = this.getStoredNotifications();
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
      this.saveNotifications(notifications);
      console.log(`NotificationService: Marked notification ${notificationId} as read`);
    }
  }

  // 标记通知为未读
  async markAsUnread(notificationId: string): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/${notificationId}/unread`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to mark notification as unread');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const notifications = this.getStoredNotifications();
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = false;
      this.saveNotifications(notifications);
      console.log(`NotificationService: Marked notification ${notificationId} as unread`);
    }
  }

  // 标记所有通知为已读
  async markAllAsRead(): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/read-all`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to mark all notifications as read');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const notifications = this.getStoredNotifications();
    notifications.forEach(notification => {
      notification.isRead = true;
    });
    this.saveNotifications(notifications);
    console.log('NotificationService: Marked all notifications as read');
  }

  // 切换收藏状态
  async toggleStar(notificationId: string): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/${notificationId}/star`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to toggle star');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const notifications = this.getStoredNotifications();
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isStarred = !notification.isStarred;
      this.saveNotifications(notifications);
      console.log(`NotificationService: Toggled star for notification ${notificationId}`);
    }
  }

  // 删除通知
  async deleteNotification(notificationId: string): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/${notificationId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to delete notification');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    const notifications = this.getStoredNotifications();
    const filteredNotifications = notifications.filter(n => n.id !== notificationId);
    this.saveNotifications(filteredNotifications);
    console.log(`NotificationService: Deleted notification ${notificationId}`);
  }

  // 获取通知统计
  async getNotificationStats(): Promise<NotificationStats> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/stats`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          return data;
        } else {
          throw new Error('Failed to fetch notification stats');
        }
      } catch (error) {
        // 静默处理错误，避免频繁日志输出
      }
    }
    
    // 使用localStorage存储的数据计算统计
    const notifications = this.getStoredNotifications();
    const unreadCount = notifications.filter(n => !n.isRead).length;
    
    return {
      unreadCount,
      totalCount: notifications.length,
      lastReadAt: new Date().toISOString()
    };
  }

  // 清空所有通知
  async clearAllNotifications(): Promise<void> {
    if (!this.isDevelopmentMode) {
      try {
        const response = await fetch(`${this.baseUrl}/notifications/clear`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          return;
        } else {
          throw new Error('Failed to clear all notifications');
        }
      } catch (error) {
        console.warn('API call failed, using localStorage:', error);
      }
    }
    
    // 使用localStorage存储
    this.saveNotifications([]);
    console.log('NotificationService: Cleared all notifications');
  }

  // 项目相关的通知创建方法
  async notifyProjectCreated(projectName: string, projectId: string, createdBy: string): Promise<void> {
    await this.createNotification({
      title: 'New project created',
      message: `Project "${projectName}" has been created by ${createdBy}.`,
      type: 'success',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'medium'
    });
  }

  async notifyProjectUpdated(projectName: string, projectId: string, updatedBy: string, changes: string): Promise<void> {
    await this.createNotification({
      title: 'Project updated',
      message: `Project "${projectName}" has been updated by ${updatedBy}. Changes: ${changes}`,
      type: 'info',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'low'
    });
  }

  async notifyProjectStageChanged(projectName: string, projectId: string, newStage: string, changedBy: string): Promise<void> {
    await this.createNotification({
      title: 'Project stage changed',
      message: `Project "${projectName}" has moved to ${newStage} stage by ${changedBy}.`,
      type: 'info',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'medium'
    });
  }

  async notifyClientCreated(clientName: string, createdBy: string): Promise<void> {
    await this.createNotification({
      title: 'New client added',
      message: `Client "${clientName}" has been added to the system by ${createdBy}.`,
      type: 'success',
      actionUrl: '/client',
      priority: 'medium'
    });
  }

  async notifyFinanceUpdated(transactionType: string, amount: number, updatedBy: string): Promise<void> {
    await this.createNotification({
      title: 'Finance record updated',
      message: `A ${transactionType} transaction of €${amount.toLocaleString()} has been recorded by ${updatedBy}.`,
      type: 'info',
      actionUrl: '/finance',
      priority: 'medium'
    });
  }

  async notifySystemMaintenance(message: string, scheduledTime: string): Promise<void> {
    await this.createNotification({
      title: 'System maintenance scheduled',
      message: `${message} Scheduled for ${scheduledTime}.`,
      type: 'system',
      priority: 'low'
    });
  }

  async notifyDocumentReview(documentName: string, projectName: string, projectId: string): Promise<void> {
    await this.createNotification({
      title: 'Document requires review',
      message: `The document "${documentName}" in project "${projectName}" requires your review.`,
      type: 'warning',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'high'
    });
  }

  async notifyTeamMemberAdded(memberName: string, projectName: string, projectId: string): Promise<void> {
    await this.createNotification({
      title: 'New team member added',
      message: `${memberName} has been added to the "${projectName}" project team.`,
      type: 'team',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'medium'
    });
  }

  async notifyMention(mentionedBy: string, projectName: string, projectId: string, context: string): Promise<void> {
    await this.createNotification({
      title: 'You were mentioned',
      message: `${mentionedBy} mentioned you in "${projectName}": ${context}`,
      type: 'mention',
      projectId,
      projectName,
      actionUrl: `/opportunities`,
      priority: 'high'
    });
  }
}

export default new NotificationService(); 