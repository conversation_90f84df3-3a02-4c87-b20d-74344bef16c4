import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Select, Button, message, Alert } from 'antd';

const { Option } = Select;

const TestBasicInfo: React.FC = () => {
  const [form] = Form.useForm();
  const [debug, setDebug] = useState('');

  useEffect(() => {
    console.log('🧪 TEST BASIC INFO LOADED');
    
    // 强制启用样式
    const style = document.createElement('style');
    style.textContent = `
      .force-edit {
        background: white !important;
        color: black !important;
        pointer-events: auto !important;
        cursor: text !important;
      }
    `;
    document.head.appendChild(style);
  }, []);

  const forceEnable = () => {
    document.querySelectorAll('input, select, textarea').forEach((el: any) => {
      el.disabled = false;
      el.readOnly = false;
      el.style.backgroundColor = 'white';
      el.style.color = 'black';
      el.style.pointerEvents = 'auto';
      el.style.cursor = 'text';
    });
    message.success('Force enabled all fields!');
  };

  return (
    <div style={{ padding: '20px' }}>
      <Alert message="🧪 Test Component - Isolated Form Testing" type="info" style={{ marginBottom: '16px' }} />
      
      <Card title="Controls" style={{ marginBottom: '16px' }}>
        <Button onClick={forceEnable} type="primary">Force Enable All</Button>
        <div style={{ marginTop: '8px', background: '#f5f5f5', padding: '8px' }}>
          Debug: {debug}
        </div>
      </Card>

      <Card title="Test Form">
        <Form form={form} layout="vertical">
          <Form.Item label="Client Name" name="clientName">
            <Input
              className="force-edit"
              placeholder="Enter name..."
              onChange={(e) => {
                console.log('Name changed:', e.target.value);
                setDebug(`Name: ${e.target.value}`);
              }}
            />
          </Form.Item>

          <Form.Item label="Address (Problem Field)" name="address">
            <Input
              className="force-edit"
              placeholder="Enter address..."
              onChange={(e) => {
                console.log('Address changed:', e.target.value);
                setDebug(`Address: ${e.target.value}`);
              }}
            />
          </Form.Item>

          <Form.Item label="Key Stakeholder (Problem Field)" name="stakeholder">
            <Input
              className="force-edit"
              placeholder="Enter stakeholder..."
              onChange={(e) => {
                console.log('Stakeholder changed:', e.target.value);
                setDebug(`Stakeholder: ${e.target.value}`);
              }}
            />
          </Form.Item>

          <Form.Item label="Tier" name="tier">
            <Select
              placeholder="Select tier..."
              onChange={(value) => {
                console.log('Tier changed:', value);
                setDebug(`Tier: ${value}`);
              }}
            >
              <Option value="A">A</Option>
              <Option value="B">B</Option>
              <Option value="C">C</Option>
            </Select>
          </Form.Item>

          <Button type="primary" onClick={() => console.log('Form values:', form.getFieldsValue())}>
            Get Values
          </Button>
        </Form>
      </Card>

      <Card title="Native HTML Test" style={{ marginTop: '16px' }}>
        <input
          type="text"
          placeholder="Native input test..."
          style={{ width: '100%', padding: '8px' }}
          onChange={(e) => {
            console.log('Native changed:', e.target.value);
            setDebug(`Native: ${e.target.value}`);
          }}
        />
      </Card>
    </div>
  );
};

export default TestBasicInfo; 