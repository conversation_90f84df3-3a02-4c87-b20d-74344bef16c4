import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Row,
  Col,
  Statistic,
  Card,
  message,
  Spin,
  Button,
  Input,
  Select,
  Space,
  Avatar,
  Typography,
  Divider,
  Tag,
  notification
} from 'antd';
import { 
  TeamOutlined, 
  SettingOutlined, 
  MailOutlined, 
  LinkOutlined,
  GlobalOutlined,
  CopyOutlined,
  UserOutlined,
  CrownOutlined,
  SafetyOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { Project, TeamMember, TeamRole, TeamStats } from '../../types';
import teamService from '../../services/team.service';
import MemberList from './MemberList';
import TeamSettings from './TeamSettings';

interface TeamManagementModalProps {
  visible: boolean;
  onCancel: () => void;
  project: Project;
}

const { Text } = Typography;
const { Option } = Select;

const TeamManagementModal: React.FC<TeamManagementModalProps> = ({
  visible,
  onCancel,
  project
}) => {
  const [loading, setLoading] = useState(false);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [stats, setStats] = useState<TeamStats | null>(null);
  const [currentUserRole, setCurrentUserRole] = useState<TeamRole | null>(null);
  const [inviteEmails, setInviteEmails] = useState('');
  const [inviteRole, setInviteRole] = useState<TeamRole>(TeamRole.MEMBER);
  const [inviteLoading, setInviteLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('share');

  const loadTeamData = useCallback(async () => {
    setLoading(true);
    try {
      const [membersData, statsData, userRole] = await Promise.all([
        teamService.getTeamMembers(project.id),
        teamService.getTeamStats(project.id),
        teamService.getCurrentUserRole(project.id)
      ]);
      
      setMembers(membersData);
      setStats(statsData);
      setCurrentUserRole(userRole);
    } catch (error) {
      console.error('Failed to load team data:', error);
      message.error('Failed to load team data');
    } finally {
      setLoading(false);
    }
  }, [project.id]);

  useEffect(() => {
    if (visible && project) {
      loadTeamData();
    }
  }, [visible, project, loadTeamData]);

  const handleMemberRemoved = () => {
    loadTeamData();
    message.success('Member removed successfully');
  };

  const handleMemberUpdated = () => {
    loadTeamData();
    message.success('Member updated successfully');
  };

  // 复制分享链接
  const handleCopyLink = async () => {
    try {
      const shareUrl = `${window.location.origin}/opportunity/${project.id}`;
      await navigator.clipboard.writeText(shareUrl);
      notification.success({
        message: 'Link copied',
        description: 'Share link has been copied to clipboard',
        placement: 'topRight',
        duration: 2,
      });
    } catch (error) {
      message.error('Failed to copy link');
    }
  };

  // 批量邀请成员
  const handleBatchInvite = async () => {
    if (!inviteEmails.trim()) {
      message.warning('Please enter at least one email address');
      return;
    }

    setInviteLoading(true);
    try {
      const emails = inviteEmails.split(',').map(email => email.trim()).filter(email => email);
      const invitePromises = emails.map(email => 
        teamService.inviteMember(project.id, email, inviteRole, '')
      );
      
      await Promise.all(invitePromises);
      setInviteEmails('');
      loadTeamData();
      
      notification.success({
        message: 'Invitations sent',
        description: `Successfully sent ${emails.length} invitation${emails.length > 1 ? 's' : ''}`,
        placement: 'topRight',
      });
    } catch (error) {
      message.error('Failed to send invitations');
    } finally {
      setInviteLoading(false);
    }
  };

  const getRoleIcon = (role: TeamRole) => {
    switch (role) {
      case TeamRole.OWNER:
        return <CrownOutlined style={{ color: '#faad14' }} />;
      case TeamRole.ADMIN:
        return <SafetyOutlined style={{ color: '#1890ff' }} />;
      case TeamRole.MEMBER:
        return <UserOutlined style={{ color: '#52c41a' }} />;
      case TeamRole.VIEWER:
        return <EyeOutlined style={{ color: '#8c8c8c' }} />;
      default:
        return <UserOutlined />;
    }
  };

  const getRoleColor = (role: TeamRole) => {
    switch (role) {
      case TeamRole.OWNER: return 'gold';
      case TeamRole.ADMIN: return 'blue';
      case TeamRole.MEMBER: return 'green';
      case TeamRole.VIEWER: return 'default';
      default: return 'default';
    }
  };

  const renderShareTab = () => (
    <div style={{ padding: '8px 0' }}>
      {/* Copy Link Section */}
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        padding: '16px 0',
        borderBottom: '1px solid #f0f0f0',
        marginBottom: '24px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <LinkOutlined style={{ fontSize: 16, color: '#1890ff' }} />
          <Text strong>Copy link</Text>
        </div>
        <Button 
          type="primary" 
          icon={<CopyOutlined />}
          onClick={handleCopyLink}
          size="small"
        >
          Copy
        </Button>
      </div>

      {/* Invite Section */}
      <div style={{ marginBottom: 24 }}>
        <Row gutter={8}>
          <Col flex={1}>
            <Input
              placeholder="Emails, comma separated"
              value={inviteEmails}
              onChange={(e) => setInviteEmails(e.target.value)}
              onPressEnter={handleBatchInvite}
              style={{ height: 40 }}
            />
          </Col>
          <Col>
            <Select
              value={inviteRole}
              onChange={setInviteRole}
              style={{ width: 120, height: 40 }}
            >
              <Option value={TeamRole.VIEWER}>Viewer</Option>
              <Option value={TeamRole.MEMBER}>Member</Option>
              <Option value={TeamRole.ADMIN}>Admin</Option>
            </Select>
          </Col>
          <Col>
            <Button
              type="primary"
              onClick={handleBatchInvite}
              loading={inviteLoading}
              style={{ height: 40 }}
            >
              Invite
            </Button>
          </Col>
        </Row>
      </div>

      {/* Who has access */}
      <div>
        <Text strong style={{ fontSize: 16, marginBottom: 16, display: 'block' }}>
          Who has access
        </Text>

        {/* Public Access */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          padding: '12px 0',
          borderBottom: '1px solid #f5f5f5'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            <Avatar 
              icon={<GlobalOutlined />} 
              style={{ backgroundColor: '#f0f0f0', color: '#8c8c8c' }}
              size={32}
            />
            <Text>Anyone with the link</Text>
          </div>
          <Select
            defaultValue="view"
            style={{ width: 120 }}
            size="small"
            disabled
          >
            <Option value="view">can view</Option>
          </Select>
        </div>

        {/* Team Members */}
        {members.map((member) => (
          <div 
            key={member.id}
            style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              padding: '12px 0',
              borderBottom: members.indexOf(member) === members.length - 1 ? 'none' : '1px solid #f5f5f5'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
              <Avatar 
                src={member.avatar}
                icon={<UserOutlined />}
                size={32}
              />
              <div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Text strong>{member.name}</Text>
                  {member.role === TeamRole.OWNER && <Text type="secondary">(you)</Text>}
                </div>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  {member.email}
                </Text>
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Tag 
                color={getRoleColor(member.role)} 
                icon={getRoleIcon(member.role)}
                style={{ margin: 0 }}
              >
                {member.role}
              </Tag>
              {currentUserRole === TeamRole.OWNER && member.role !== TeamRole.OWNER && (
                <Select
                  value={member.role}
                  onChange={(newRole) => {
                    teamService.updateMemberRole(project.id, member.id, newRole);
                    loadTeamData();
                  }}
                  style={{ width: 100 }}
                  size="small"
                >
                  <Option value={TeamRole.VIEWER}>Viewer</Option>
                  <Option value={TeamRole.MEMBER}>Member</Option>
                  <Option value={TeamRole.ADMIN}>Admin</Option>
                </Select>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <TeamOutlined />
          <span>Share "{project.name}"</span>
        </div>
      }
      visible={visible}
      onCancel={onCancel}
      width={800}
      footer={null}
      destroyOnClose
    >
      <Spin spinning={loading}>
        {/* Navigation Tabs */}
        <div style={{ marginTop: 24, marginBottom: 12 }}>
          <Space size={0}>
            <Button 
              type={activeTab === 'share' ? 'primary' : 'text'}
              onClick={() => setActiveTab('share')}
              icon={<TeamOutlined />}
            >
              Share & Permissions
            </Button>
            <Button 
              type={activeTab === 'advanced' ? 'primary' : 'text'}
              onClick={() => setActiveTab('advanced')}
              icon={<SettingOutlined />}
            >
              Advanced
            </Button>
          </Space>
        </div>

        {/* Content */}
        {activeTab === 'share' && renderShareTab()}
        
        {activeTab === 'advanced' && (
          <div>
            {/* Team Statistics */}
            {stats && (
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Total Members"
                      value={stats.totalMembers}
                      prefix={<TeamOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="Pending Invitations"
                      value={stats.pendingInvitations}
                      valueStyle={{ color: '#cf1322' }}
                      prefix={<MailOutlined />}
                    />
                  </Card>
                </Col>
              </Row>
            )}

            {/* Advanced Member Management */}
            <MemberList
              members={members}
              projectId={project.id}
              currentUserRole={currentUserRole}
              onMemberRemoved={handleMemberRemoved}
              onMemberUpdated={handleMemberUpdated}
            />

            {/* Team Settings */}
            {currentUserRole === TeamRole.OWNER && (
              <div style={{ marginTop: 24 }}>
                <Divider />
                <TeamSettings
                  project={project}
                  onSettingsUpdated={loadTeamData}
                />
              </div>
            )}
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default TeamManagementModal; 