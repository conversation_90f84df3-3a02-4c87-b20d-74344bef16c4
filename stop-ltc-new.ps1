# LTC项目管理系统停止脚本 (PowerShell版本)
# 设置编码
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "===================================" -ForegroundColor Red
Write-Host "    LTC项目管理系统停止脚本" -ForegroundColor Red
Write-Host "===================================" -ForegroundColor Red
Write-Host ""

# 显示当前运行的Node.js进程
Write-Host "当前运行的Node.js进程:" -ForegroundColor Yellow
try {
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        $nodeProcesses | Format-Table -Property Id, ProcessName, CPU, WorkingSet -AutoSize
    } else {
        Write-Host "没有找到运行中的Node.js进程" -ForegroundColor Green
    }
} catch {
    Write-Host "检查Node.js进程时出错: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "正在停止LTC项目服务..." -ForegroundColor Yellow

# 停止占用3000端口的进程（前端）
Write-Host "停止前端服务 (端口 3000)..." -ForegroundColor Cyan
try {
    $port3000Connections = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    if ($port3000Connections) {
        foreach ($conn in $port3000Connections) {
            Write-Host "停止进程 $($conn.OwningProcess)" -ForegroundColor Yellow
            try {
                Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
            } catch {
                Write-Host "无法停止进程 $($conn.OwningProcess): $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "端口 3000 未被占用" -ForegroundColor Green
    }
} catch {
    Write-Host "检查端口 3000 时出错: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 停止占用5002端口的进程（后端）
Write-Host "停止后端服务 (端口 5002)..." -ForegroundColor Cyan
try {
    $port5002Connections = Get-NetTCPConnection -LocalPort 5002 -ErrorAction SilentlyContinue
    if ($port5002Connections) {
        foreach ($conn in $port5002Connections) {
            Write-Host "停止进程 $($conn.OwningProcess)" -ForegroundColor Yellow
            try {
                Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
            } catch {
                Write-Host "无法停止进程 $($conn.OwningProcess): $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "端口 5002 未被占用" -ForegroundColor Green
    }
} catch {
    Write-Host "检查端口 5002 时出错: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 询问是否停止所有Node.js进程
Write-Host ""
$choice = Read-Host "是否要停止所有Node.js进程? 这会影响其他Node.js应用 (Y/N)"
if ($choice -match "^[Yy]") {
    Write-Host "正在停止所有Node.js进程..." -ForegroundColor Yellow
    try {
        Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "✓ 已停止所有Node.js进程" -ForegroundColor Green
    } catch {
        Write-Host "停止Node.js进程时出错: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "跳过停止所有Node.js进程" -ForegroundColor White
}

Write-Host ""
Start-Sleep -Seconds 2

# 验证端口是否已释放
Write-Host "验证端口状态..." -ForegroundColor Yellow

# 检查端口3000
try {
    $port3000Check = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    if (-not $port3000Check) {
        Write-Host "✓ 端口 3000 已释放" -ForegroundColor Green
    } else {
        Write-Host "⚠ 端口 3000 仍被占用" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✓ 端口 3000 已释放" -ForegroundColor Green
}

# 检查端口5002
try {
    $port5002Check = Get-NetTCPConnection -LocalPort 5002 -ErrorAction SilentlyContinue
    if (-not $port5002Check) {
        Write-Host "✓ 端口 5002 已释放" -ForegroundColor Green
    } else {
        Write-Host "⚠ 端口 5002 仍被占用" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✓ 端口 5002 已释放" -ForegroundColor Green
}

Write-Host ""
Write-Host "===================================" -ForegroundColor Green
Write-Host "    停止完成!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""
Write-Host "LTC项目服务已停止" -ForegroundColor Green
Write-Host "如需重新启动，请运行 start-ltc-new.ps1" -ForegroundColor White
Write-Host ""
Read-Host "按任意键继续" 