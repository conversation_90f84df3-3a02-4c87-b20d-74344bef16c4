/**
 * 🛡️ 健壮数据管理系统 (Robust Data Manager)
 * 解决数据架构的稳定性和一致性问题
 * 
 * 设计原则：
 * 1. 单一数据源 (Single Source of Truth)
 * 2. 原子性操作 (Atomic Operations)
 * 3. 自动恢复机制 (Auto Recovery)
 * 4. 版本控制和迁移 (Versioning & Migration)
 * 5. 强类型约束 (Strong Typing)
 */

import { Project, ProjectStage, Client } from '../types';

// 数据版本和迁移接口
interface DataVersion {
  version: string;
  timestamp: string;
  description: string;
}

interface DataBackup {
  version: DataVersion;
  data: any;
  checksum: string;
}

interface ValidationRule<T> {
  field: keyof T;
  validator: (value: any) => boolean;
  message: string;
  severity: 'error' | 'warning';
}

interface TransactionLog {
  id: string;
  operation: string;
  entityType: string;
  entityId: string;
  timestamp: string;
  oldValue?: any;
  newValue?: any;
  success: boolean;
  error?: string;
}

class RobustDataManager {
  private static instance: RobustDataManager;
  private readonly CURRENT_VERSION = '3.0.0';
  private readonly STORAGE_PREFIX = 'rdm_'; // Robust Data Manager前缀
  
  // 统一存储键
  private readonly STORAGE_KEYS = {
    PROJECTS: `${this.STORAGE_PREFIX}projects`,
    STAGES: `${this.STORAGE_PREFIX}stages`,
    CLIENTS: `${this.STORAGE_PREFIX}clients`,
    METADATA: `${this.STORAGE_PREFIX}metadata`,
    TRANSACTION_LOG: `${this.STORAGE_PREFIX}transactions`,
    BACKUP: `${this.STORAGE_PREFIX}backup`
  } as const;

  // 缓存系统
  private cache = new Map<string, any>();
  private cacheTimestamp = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  // 事务系统
  private transactionLog: TransactionLog[] = [];
  private readonly MAX_LOG_SIZE = 1000;

  // 数据验证规则
  private projectValidationRules: ValidationRule<Project>[] = [
    { field: 'id', validator: (v) => !!v && typeof v === 'string', message: 'Project ID is required', severity: 'error' },
    { field: 'name', validator: (v) => !!v && v.length > 0, message: 'Project name is required', severity: 'error' },
    { field: 'client', validator: (v) => !!v && v.length > 0, message: 'Client name is required', severity: 'error' },
    { field: 'revenue', validator: (v) => typeof v === 'number' && v >= 0, message: 'Revenue must be a positive number', severity: 'warning' }
  ];

  private constructor() {
    this.initializeSystem();
  }

  public static getInstance(): RobustDataManager {
    if (!RobustDataManager.instance) {
      RobustDataManager.instance = new RobustDataManager();
    }
    return RobustDataManager.instance;
  }

  /**
   * 🚀 系统初始化
   */
  private async initializeSystem(): Promise<void> {
    try {
      console.log('🔄 Initializing Robust Data Manager...');
      
      // 1. 检查和迁移数据
      await this.migrateData();
      
      // 2. 验证数据完整性
      await this.validateDataIntegrity();
      
      // 3. 设置监听器
      this.setupEventListeners();
      
      // 4. 清理旧数据
      this.cleanupOldData();
      
      console.log('✅ Robust Data Manager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Robust Data Manager:', error);
      // 尝试从备份恢复
      await this.recoverFromBackup();
    }
  }

  /**
   * 📦 数据迁移系统
   */
  private async migrateData(): Promise<void> {
    const metadata = this.getMetadata();
    const currentVersion = metadata.version || '1.0.0';
    
    if (currentVersion === this.CURRENT_VERSION) {
      return; // 无需迁移
    }
    
    console.log(`🔄 Migrating data from ${currentVersion} to ${this.CURRENT_VERSION}`);
    
    // 创建备份
    await this.createBackup();
    
    try {
      // 迁移项目数据
      await this.migrateProjects();
      
      // 迁移阶段数据
      await this.migrateStages();
      
      // 迁移客户数据
      await this.migrateClients();
      
      // 更新版本信息
      this.updateMetadata({
        version: this.CURRENT_VERSION,
        migratedFrom: currentVersion,
        migrationDate: new Date().toISOString()
      });
      
      console.log('✅ Data migration completed successfully');
    } catch (error) {
      console.error('❌ Data migration failed:', error);
      throw error;
    }
  }

  /**
   * 🔄 项目数据迁移
   */
  private async migrateProjects(): Promise<void> {
    const sources = ['projects', 'ltc_projects', 'dal_projects'];
    const allProjects: Project[] = [];
    
    // 从所有可能的源收集项目数据
    for (const source of sources) {
      try {
        const data = this.getFromStorage(source, []);
        if (Array.isArray(data) && data.length > 0) {
          allProjects.push(...data);
        }
      } catch (error) {
        console.warn(`Failed to migrate from ${source}:`, error);
      }
    }
    
    // 去重和规范化
    const uniqueProjects = this.deduplicateProjects(allProjects);
    const normalizedProjects = uniqueProjects.map(this.normalizeProject);
    
    // 保存到新存储位置
    this.setToStorage(this.STORAGE_KEYS.PROJECTS, normalizedProjects);
    
    console.log(`📦 Migrated ${normalizedProjects.length} projects`);
  }

  /**
   * 🔄 阶段数据迁移
   */
  private async migrateStages(): Promise<void> {
    const stageKeys = Object.keys(localStorage).filter(key => key.startsWith('project_stages_'));
    const allStages: Record<string, ProjectStage[]> = {};
    
    for (const key of stageKeys) {
      try {
        const projectId = key.replace('project_stages_', '');
        const stages = this.getFromStorage(key, []);
        
        if (Array.isArray(stages) && stages.length > 0) {
          allStages[projectId] = stages.map(this.normalizeStage);
        }
      } catch (error) {
        console.warn(`Failed to migrate stages from ${key}:`, error);
      }
    }
    
    this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
    console.log(`📋 Migrated stages for ${Object.keys(allStages).length} projects`);
  }

  /**
   * 🔄 客户数据迁移
   */
  private async migrateClients(): Promise<void> {
    const sources = ['ltc_clients', 'dal_clients'];
    const allClients: Client[] = [];
    
    for (const source of sources) {
      try {
        const data = this.getFromStorage(source, []);
        if (Array.isArray(data) && data.length > 0) {
          allClients.push(...data);
        }
      } catch (error) {
        console.warn(`Failed to migrate clients from ${source}:`, error);
      }
    }
    
    const uniqueClients = this.deduplicateClients(allClients);
    this.setToStorage(this.STORAGE_KEYS.CLIENTS, uniqueClients);
    
    console.log(`👥 Migrated ${uniqueClients.length} clients`);
  }

  /**
   * ✅ 数据完整性验证
   */
  private async validateDataIntegrity(): Promise<void> {
    const projects = this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []);
    const stages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    
    let issuesFound = 0;
    
    // 验证项目数据
    for (const project of projects) {
      const validation = this.validateProject(project);
      if (!validation.isValid) {
        console.warn(`Project ${project.id} validation failed:`, validation.errors);
        issuesFound++;
      }
    }
    
    // 验证阶段数据
    for (const [projectId, projectStages] of Object.entries(stages)) {
      if (!projects.find((p: Project) => p.id === projectId)) {
        console.warn(`Orphaned stages found for project ${projectId}`);
        issuesFound++;
      }
    }
    
    if (issuesFound > 0) {
      console.warn(`⚠️ Found ${issuesFound} data integrity issues`);
    } else {
      console.log('✅ Data integrity validation passed');
    }
  }

  /**
   * 💾 备份系统
   */
  private async createBackup(): Promise<void> {
    try {
      const backup: DataBackup = {
        version: {
          version: this.CURRENT_VERSION,
          timestamp: new Date().toISOString(),
          description: 'Auto backup before migration'
        },
        data: {
          projects: this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []),
          stages: this.getFromStorage(this.STORAGE_KEYS.STAGES, {}),
          clients: this.getFromStorage(this.STORAGE_KEYS.CLIENTS, [])
        },
        checksum: this.generateChecksum()
      };
      
      this.setToStorage(this.STORAGE_KEYS.BACKUP, backup);
      console.log('💾 Backup created successfully');
    } catch (error) {
      console.error('❌ Failed to create backup:', error);
    }
  }

  /**
   * 🔧 从备份恢复
   */
  private async recoverFromBackup(): Promise<void> {
    try {
      const backup = this.getFromStorage(this.STORAGE_KEYS.BACKUP, null);
      
      if (!backup || !this.validateBackup(backup)) {
        throw new Error('No valid backup found');
      }
      
      // 恢复数据
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, backup.data.projects);
      this.setToStorage(this.STORAGE_KEYS.STAGES, backup.data.stages);
      this.setToStorage(this.STORAGE_KEYS.CLIENTS, backup.data.clients);
      
      console.log('🔧 Successfully recovered from backup');
    } catch (error) {
      console.error('❌ Failed to recover from backup:', error);
    }
  }

  /**
   * 🏗️ 项目数据操作 API
   */
  
  // 获取所有项目（带缓存）
  public getAllProjects(): Project[] {
    const cacheKey = 'projects_all';
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const projects = this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []);
    this.setCache(cacheKey, projects);
    
    return projects;
  }

  // 根据ID获取项目
  public getProjectById(id: string): Project | null {
    if (!id) return null;
    
    const cacheKey = `project_${id}`;
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const projects = this.getAllProjects();
    const project = projects.find(p => p.id === id);
    
    if (project) {
      this.setCache(cacheKey, project);
    }
    
    return project || null;
  }

  // 保存项目（原子性操作）
  public async saveProject(project: Project): Promise<void> {
    return this.executeTransaction('save_project', project.id, async () => {
      // 验证数据
      const validation = this.validateProject(project);
      if (!validation.isValid) {
        throw new Error(`Project validation failed: ${validation.errors.join(', ')}`);
      }
      
      // 规范化数据
      const normalizedProject = this.normalizeProject(project);
      
      // 获取现有项目
      const projects = this.getAllProjects();
      const existingIndex = projects.findIndex(p => p.id === project.id);
      
      // 更新或添加
      if (existingIndex >= 0) {
        projects[existingIndex] = normalizedProject;
      } else {
        projects.unshift(normalizedProject);
      }
      
      // 保存到存储
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, projects);
      
      // 清理相关缓存
      this.invalidateCache(['projects_all', `project_${project.id}`]);
    });
  }

  // 删除项目
  public async deleteProject(id: string): Promise<void> {
    return this.executeTransaction('delete_project', id, async () => {
      const projects = this.getAllProjects();
      const filteredProjects = projects.filter(p => p.id !== id);
      
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, filteredProjects);
      
      // 同时删除相关的阶段数据
      const stages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      if (stages[id]) {
        delete stages[id];
        this.setToStorage(this.STORAGE_KEYS.STAGES, stages);
      }
      
      // 清理缓存
      this.invalidateCache(['projects_all', `project_${id}`, `stages_${id}`]);
    });
  }

  /**
   * 📋 项目阶段数据操作 API
   */
  
  // 获取项目阶段
  public getProjectStages(projectId: string): ProjectStage[] {
    if (!projectId) return [];
    
    const cacheKey = `stages_${projectId}`;
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    const stages = allStages[projectId] || [];
    
    this.setCache(cacheKey, stages);
    return stages;
  }

  // 保存项目阶段
  public async saveProjectStages(projectId: string, stages: ProjectStage[]): Promise<void> {
    return this.executeTransaction('save_stages', projectId, async () => {
      const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      
      // 规范化阶段数据
      const normalizedStages = stages.map(this.normalizeStage);
      
      allStages[projectId] = normalizedStages;
      this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
      
      // 清理缓存
      this.invalidateCache([`stages_${projectId}`]);
    });
  }

  /**
   * 🔧 工具方法
   */
  
  // 数据去重
  private deduplicateProjects(projects: Project[]): Project[] {
    const seen = new Set<string>();
    return projects.filter(project => {
      if (seen.has(project.id)) {
        return false;
      }
      seen.add(project.id);
      return true;
    });
  }

  private deduplicateClients(clients: Client[]): Client[] {
    const seen = new Set<string>();
    return clients.filter(client => {
      const key = client.id || client.name;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  // 数据规范化
  private normalizeProject(project: Project): Project {
    return {
      ...project,
      id: project.id || `proj_${Date.now()}`,
      name: project.name?.trim() || 'Untitled Project',
      client: project.client?.trim() || 'Unknown Client',
      revenue: typeof project.revenue === 'number' ? project.revenue : 0,
      updatedAt: new Date().toISOString()
    };
  }

  private normalizeStage(stage: ProjectStage): ProjectStage {
    return {
      ...stage,
      id: stage.id || `stage_${Date.now()}`,
      projectId: stage.projectId,
      type: stage.type,
      status: stage.status || 'not_started',
      data: stage.data || '{}',
      updatedAt: new Date().toISOString()
    };
  }

  // 数据验证
  private validateProject(project: Project): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    for (const rule of this.projectValidationRules) {
      if (!rule.validator(project[rule.field])) {
        if (rule.severity === 'error') {
          errors.push(rule.message);
        }
      }
    }
    
    return { isValid: errors.length === 0, errors };
  }

  // 事务系统
  private async executeTransaction<T>(
    operation: string,
    entityId: string,
    action: () => Promise<T> | T
  ): Promise<T> {
    const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const result = await action();
      
      this.logTransaction({
        id: transactionId,
        operation,
        entityType: 'project',
        entityId,
        timestamp: new Date().toISOString(),
        success: true
      });
      
      return result;
    } catch (error) {
      this.logTransaction({
        id: transactionId,
        operation,
        entityType: 'project',
        entityId,
        timestamp: new Date().toISOString(),
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });
      
      throw error;
    }
  }

  // 事务日志
  private logTransaction(log: TransactionLog): void {
    this.transactionLog.unshift(log);
    
    // 限制日志大小
    if (this.transactionLog.length > this.MAX_LOG_SIZE) {
      this.transactionLog = this.transactionLog.slice(0, this.MAX_LOG_SIZE);
    }
    
    // 保存到存储
    this.setToStorage(this.STORAGE_KEYS.TRANSACTION_LOG, this.transactionLog);
  }

  // 缓存管理
  private isCacheValid(key: string): boolean {
    if (!this.cache.has(key)) return false;
    
    const timestamp = this.cacheTimestamp.get(key);
    if (!timestamp) return false;
    
    return Date.now() - timestamp < this.CACHE_TTL;
  }

  private setCache(key: string, value: any): void {
    this.cache.set(key, value);
    this.cacheTimestamp.set(key, Date.now());
  }

  private invalidateCache(keys: string[]): void {
    keys.forEach(key => {
      this.cache.delete(key);
      this.cacheTimestamp.delete(key);
    });
  }

  // 存储操作
  private getFromStorage(key: string, defaultValue: any = null): any {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.warn(`Failed to read from storage key ${key}:`, error);
      return defaultValue;
    }
  }

  private setToStorage(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Failed to write to storage key ${key}:`, error);
      throw error;
    }
  }

  // 元数据管理
  private getMetadata(): any {
    return this.getFromStorage(this.STORAGE_KEYS.METADATA, {});
  }

  private updateMetadata(updates: any): void {
    const metadata = this.getMetadata();
    this.setToStorage(this.STORAGE_KEYS.METADATA, { ...metadata, ...updates });
  }

  // 清理旧数据
  private cleanupOldData(): void {
    const oldKeys = [
      'projects', 'ltc_projects', 'dal_projects',
      'ltc_clients', 'dal_clients'
    ];
    
    oldKeys.forEach(key => {
      if (localStorage.getItem(key)) {
        console.log(`🧹 Cleaning up old storage key: ${key}`);
        localStorage.removeItem(key);
      }
    });
    
    // 清理旧的project_stages_*键
    const stageKeys = Object.keys(localStorage).filter(key => key.startsWith('project_stages_'));
    stageKeys.forEach(key => {
      console.log(`🧹 Cleaning up old stage key: ${key}`);
      localStorage.removeItem(key);
    });
  }

  // 工具方法
  private generateChecksum(): string {
    return `checksum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private validateBackup(backup: DataBackup): boolean {
    return !!(backup.version && backup.data && backup.checksum);
  }

  private setupEventListeners(): void {
    // 监听页面卸载，保存缓存
    window.addEventListener('beforeunload', () => {
      this.cache.clear();
      this.cacheTimestamp.clear();
    });
  }

  /**
   * 🎯 公共 API
   */
  
  // 获取系统状态
  public getSystemStatus(): any {
    const projects = this.getAllProjects();
    const stages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    const transactions = this.getFromStorage(this.STORAGE_KEYS.TRANSACTION_LOG, []);
    
    return {
      version: this.CURRENT_VERSION,
      projectsCount: projects.length,
      stagesCount: Object.keys(stages).length,
      transactionsCount: transactions.length,
      cacheSize: this.cache.size,
      lastUpdate: new Date().toISOString()
    };
  }

  // 导出所有数据
  public exportAllData(): any {
    return {
      version: this.CURRENT_VERSION,
      timestamp: new Date().toISOString(),
      projects: this.getAllProjects(),
      stages: this.getFromStorage(this.STORAGE_KEYS.STAGES, {}),
      clients: this.getFromStorage(this.STORAGE_KEYS.CLIENTS, []),
      metadata: this.getMetadata()
    };
  }

  // 强制重新初始化
  public async forceReinitialize(): Promise<void> {
    this.cache.clear();
    this.cacheTimestamp.clear();
    await this.initializeSystem();
  }
}

// 导出单例实例
export const robustDataManager = RobustDataManager.getInstance();
export default robustDataManager; 