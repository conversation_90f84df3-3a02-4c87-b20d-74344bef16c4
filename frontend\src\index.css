body, #root, .ant-app {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'WenQuanYi Micro Hei',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 简约项目卡片样式 */
.project-card {
  position: relative;
  transition: all 0.2s ease;
}

.project-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-color: #d9d9d9 !important;
}

.project-card .ant-card-body {
  position: relative;
}

/* 状态标签简约样式 */
.ant-tag {
  border-radius: 4px !important;
  font-weight: 500 !important;
  padding: 2px 8px !important;
  font-size: 12px !important;
  border: none !important;
}

/* 按钮样式优化 */
.project-card .ant-btn-primary {
  box-shadow: none !important;
}

.project-card .ant-btn-primary:hover {
  opacity: 0.9;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .project-card {
    min-width: 260px !important;
    max-width: 100% !important;
  }
}
