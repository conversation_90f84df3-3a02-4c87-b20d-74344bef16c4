import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  Button,
  Row,
  Col,
  Card,
  message,
  Space,
  Tag,
  AutoComplete
} from 'antd';
import {
  SaveOutlined,
  QuestionCircleOutlined,
  ArrowLeftOutlined,
  UserOutlined,
  BankOutlined,
  ProjectOutlined,
  TrophyOutlined
} from '@ant-design/icons';

import projectService from '../services/new-project.service';
import clientService from '../services/client.service';
import { Project, Client } from '../types';
import { useCurrency } from '../contexts/CurrencyContext';
import { dataSyncEngine } from '../services/dataSyncEngine';
import '../styles/form-styles.css';
import '../styles/create-project.css';
import authService from '../services/auth.service';
import notificationService from '../services/notification.service';

const { Option } = Select;

// 国家列表数据
const COUNTRIES = [
  "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", "Argentina", "Armenia", "Australia", "Austria",
  "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bhutan",
  "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia",
  "Cameroon", "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica",
  "Croatia", "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "Ecuador", "Egypt",
  "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Eswatini", "Ethiopia", "Fiji", "Finland", "France", "Gabon",
  "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea", "Guinea-Bissau", "Guyana",
  "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", "Israel",
  "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Korea, North", "Korea, South", "Kosovo",
  "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", "Lithuania",
  "Luxembourg", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", "Mauritius",
  "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", "Namibia",
  "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia", "Norway", "Oman",
  "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland", "Portugal",
  "Qatar", "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines", "Samoa", "San Marino", "Sao Tome and Principe",
  "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", "Somalia",
  "South Africa", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", "Taiwan",
  "Tajikistan", "Tanzania", "Thailand", "Timor-Leste", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan",
  "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City",
  "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
];

const CreateProject: React.FC = () => {
  console.log('🚨🚨🚨 CREATE-PROJECT PAGE LOADED!!! 🚨🚨🚨');
  
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [saving, setSaving] = React.useState(false);
  const [existingClients, setExistingClients] = React.useState<Client[]>([]);
  const [clientOptions, setClientOptions] = React.useState<{ value: string; label: string; data: Client }[]>([]);
  const { selectedCurrency, getCurrencySymbol } = useCurrency();



  // 自动生成项目ID
  const generateProjectId = () => {
    const year = new Date().getFullYear();
    const timestamp = String(Date.now()).slice(-3);
    return `OPP-${year}-${timestamp}`;
  };

  // 重新生成ID的处理函数
  const handleRegenerateId = () => {
    const newId = generateProjectId();
    form.setFieldsValue({
      projectId: newId
    });
    message.success('New Opportunity ID generated successfully!');
  };

  // 组件初始化
  useEffect(() => {
    loadExistingClients();
    // 设置默认的Opportunity ID
    form.setFieldsValue({
      projectId: generateProjectId(),
      currency: 'EUR',
      vatRate: 19
    });
  }, []);

  // 加载现有客户列表
  const loadExistingClients = async () => {
    try {
      const clients = await clientService.getAllClients();
      setExistingClients(clients);
      // 初始化客户选项
      const options = clients.map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
      setClientOptions(options);
    } catch (error) {
      console.error('Failed to load existing clients:', error);
    }
  };

  // 处理客户搜索
  const handleClientSearch = (value: string) => {
    if (!value) {
      const options = existingClients.map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
      setClientOptions(options);
      return;
    }

    const filteredOptions = existingClients
            .filter(client =>
        client.name.toLowerCase().includes(value.toLowerCase())
      )
      .map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
    
    setClientOptions(filteredOptions);
  };

  // 处理客户选择
  const handleClientSelect = (value: string, option: any) => {
    const selectedClient = option.data as Client;
    if (selectedClient) {
      // 自动填充客户相关信息
      form.setFieldsValue({
        client: selectedClient.name,
        tier: mapClientTierToProjectTier(selectedClient.tier),
        country: selectedClient.country,
        owner: selectedClient.contactPerson || ''
      });
      
      message.success(`Selected existing client: ${selectedClient.name}`);
    }
  };

  // 客户等级映射：从客户的tier映射到项目的tier
  const mapClientTierToProjectTier = (clientTier: string): string => {
    switch (clientTier) {
      case 'SVIP': return 'S';
      case 'VIP': return 'V';
      case 'BA': return 'B';
      case 'A': return 'A';
      case 'B': return 'B';
      case 'C': return 'A'; // 默认映射到A
      default: return 'A';
    }
  };

  // 生成客户ID
  const generateClientId = () => {
    const year = new Date().getFullYear();
    const timestamp = Date.now().toString().slice(-3);
    return `CLI-${year}-${timestamp}`;
  };

  // 客户等级映射：从项目的tier映射到客户的tier
  const mapProjectTierToClientTier = (projectTier: string): 'S' | 'V' | 'B' | 'A' => {
    switch (projectTier) {
      case 'S': return 'S';
      case 'V': return 'V';
      case 'B': return 'B';
      case 'A': return 'A';
      default: return 'A';
    }
  };

  // 根据行业推断客户行业
  const inferIndustryFromCategory = (category: string): string => {
    switch (category) {
      case 'Software': return 'Technology';
      case 'Service': return 'Consulting';
      case 'Product': return 'Manufacturing';
      default: return 'Other';
    }
  };

  // 检查并创建客户（如果不存在）
  const ensureClientExists = async (clientName: string, projectData: any): Promise<void> => {
    try {
      console.log('🚀 ensureClientExists called with:', { clientName, projectData });
      
      // 搜索现有客户
      console.log('🔍 Searching for existing clients with name:', clientName);
      const existingClients = await clientService.searchClients(clientName);
      console.log('🔍 Search results:', existingClients);
      
      const exactMatch = existingClients.find(client => 
        client.name.toLowerCase() === clientName.toLowerCase()
      );

      if (exactMatch) {
        console.log('✅ Client already exists:', exactMatch.name);
        return;
      }

      console.log('🆕 Client does not exist, creating new client...');
      
      // 客户不存在，创建新客户
      const newClientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'> = {
        clientId: generateClientId(),
        name: clientName,
        email: `contact@${clientName.toLowerCase().replace(/\s+/g, '')}.com`, // 临时生成邮箱
        industry: inferIndustryFromCategory(projectData.category || 'Other'),
        country: projectData.country || 'Germany',
        tier: mapProjectTierToClientTier(projectData.tier || 'A'),
        status: 'potential', // 新创建的客户默认为潜在客户
        contactPerson: projectData.owner || 'Unknown',
        totalRevenue: 0,
        totalProjects: 0,
        notes: `Automatically created from opportunity: ${projectData.projectId || 'Unknown'}`
      };

      console.log('🆕 Creating client with data:', newClientData);
      const createdClient = await clientService.createClient(newClientData);
      console.log('✅ New client created successfully:', createdClient);
      
      message.info(`New client "${clientName}" has been automatically added to the client list.`);
    } catch (error) {
      console.error('❌ Failed to create client:', error);
      // 不阻断项目创建流程，只是记录错误
      console.warn('⚠️ Client creation failed, but project creation will continue');
    }
  };

  // 表单提交处理
  const handleSubmit = async (values: any) => {
    console.log('🚨🚨🚨 HANDLE-SUBMIT CALLED!!! 🚨🚨🚨');
    console.log('🚀 Form submitted with values:', values);
    try {
      setSaving(true);

      // Set default values for optional fields
      const formData: Partial<Project> = {
        ...values,
        projectId: values.projectId || generateProjectId(),
        tier: values.tier || 'A',
        level: values.level || 'A',
        category: values.category || 'Software',
        revenue: values.revenue || 0,
        stage: values.stage || 'Lead',
        probability: values.probability || 'A',
        owner: values.owner || '-',
        country: values.country || 'Germany',
        status: 'planning'
      };

      console.log('📝 Sending data to API:', formData);

      // 检查并创建客户（如果需要）
      console.log('🔍 Checking if client needs to be created. Client name:', formData.client);
      if (formData.client) {
        console.log('✅ Client name provided, calling ensureClientExists...');
        await ensureClientExists(formData.client, formData);
        console.log('✅ ensureClientExists completed');
      } else {
        console.log('⚠️ No client name provided, skipping client creation');
      }

      // Create new opportunity
      console.log('🔄 Creating project...');
      console.log('🔄 Project service type:', typeof projectService);
      console.log('🔄 Project service createProject type:', typeof projectService.createProject);
      console.log('🔄 FormData being sent:', JSON.stringify(formData, null, 2));
      
      const newProject = await projectService.createProject(formData);
      console.log('✅ New opportunity created:', newProject);
      console.log('✅ New project ID:', newProject.id);
      console.log('✅ New project name:', newProject.name);

      // 创建通知
      const currentUser = authService.getCurrentUser();
      await notificationService.notifyProjectCreated(
        newProject.name || formData.name || 'New Project',
        newProject.id || formData.projectId || 'unknown',
        currentUser?.username || 'System User'
      );

      // 验证项目是否立即可以找到
      console.log('🔍 Verifying project creation...');
      const verifyProject = await projectService.getProjectById(newProject.id);
      console.log('🔍 Project verification result:', verifyProject);

      // 立即获取所有项目，检查新项目是否在列表中
      console.log('📋 Getting all projects to verify...');
      const allProjectsAfterCreate = await projectService.getAllProjects(true);
      console.log('📋 All projects after creation:', allProjectsAfterCreate.length);
      const foundNewProject = allProjectsAfterCreate.find(p => p.id === newProject.id);
      console.log('🔍 New project found in list:', !!foundNewProject);

      // 触发项目创建事件
      const createEvent = dataSyncEngine.createEvent(
        'PROJECT_CREATED',
        newProject.id,
        'project',
        {
          client: formData.client,
          revenue: formData.revenue,
          tier: formData.tier
        },
        'CreateProject',
        undefined,
        newProject
      );
      
      await dataSyncEngine.emitEvent(createEvent);

      // 显示成功消息
      message.success({
        content: 'Opportunity created successfully! Client information has been synchronized.',
        duration: 3,
      });

      // 延迟1秒后导航回项目列表页，让用户有时间看到成功消息
      setTimeout(() => {
        console.log('🔄 Navigating to opportunity list with forceRefresh...');
        // 使用 replace 并传递 state，强制重新加载列表页
        navigate('/opportunity', {
          replace: true,
          state: {
            forceRefresh: true,
            timestamp: Date.now(),
            lastCreatedProjectId: newProject.id
          }
        });
      }, 1000);
    } catch (error) {
      console.error('❌ Failed to create opportunity:', error);
      message.error('Failed to create opportunity. Please check the form data.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/opportunity')}
          style={{ marginBottom: '16px' }}
        >
          Back to Opportunity List
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          Create New Opportunity
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          console.log('🚨🚨🚨 FORM.onFinish TRIGGERED!!! 🚨🚨🚨');
          console.log('🚨 Form values:', values);
          handleSubmit(values);
        }}
        onFinishFailed={(errorInfo) => {
          console.log('🚨🚨🚨 FORM.onFinishFailed TRIGGERED!!! 🚨🚨🚨');
          console.log('🚨 Form validation errors:', errorInfo);
        }}
        scrollToFirstError
        initialValues={{
          tier: 'A',
          level: 'A',
          category: 'Software',
          stage: 'Lead',
          probability: 'A',
          country: 'Germany',
          revenue: 0,
          vatRate: 19
        }}
      >
        <Row gutter={24}>
          {/* 左侧列 - 客户信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  <span>Client Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={
                  <span>
                    Client Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="client"
                rules={[
                  { required: true, message: 'Please enter client name' },
                  { min: 2, message: 'Client name must be at least 2 characters' }
                ]}
                tooltip="The name of the client company - start typing to see existing clients"
              >
                <AutoComplete
                  options={clientOptions}
                  onSearch={handleClientSearch}
                  onSelect={handleClientSelect}
                  placeholder="Enter client name or search existing clients"
                  filterOption={false}
                  allowClear
                  style={{ fontSize: '12px' }}
                >
                  <Input prefix={<BankOutlined />} style={{ fontSize: '12px' }} />
                </AutoComplete>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span>
                        Client Tier <span style={{ color: 'red' }}>*</span>
                      </span>
                    }
                    name="tier"
                    rules={[{ required: true, message: 'Please select client tier' }]}
                    tooltip="Client tier based on business value and potential"
                  >
                    <Select placeholder="Select tier" style={{ fontSize: '12px' }}>
                      <Option value="S">
                        <Tag color="purple">S</Tag> - Strategic Client
                      </Option>
                      <Option value="V">
                        <Tag color="gold">V</Tag> - Valued Client
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Business Client
                      </Option>
                      <Option value="A">
                        <Tag color="green">A</Tag> - Average Client
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span>
                        Country / Region <span style={{ color: 'red' }}>*</span>
                      </span>
                    }
                    name="country"
                    rules={[{ required: true, message: 'Please select country' }]}
                    tooltip="Client's primary operating country"
                  >
                    <Select
                      placeholder="Select country"
                      showSearch
                      style={{ fontSize: '12px' }}
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      {COUNTRIES.map(country => (
                        <Option key={country} value={country}>
                          {country}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Client Contact"
                name="owner"
                tooltip="Client contact person responsible for this opportunity"
              >
                <Input 
                  placeholder="Enter client contact person"
                  prefix={<UserOutlined />}
                  style={{ fontSize: '12px' }}
                />
              </Form.Item>
            </Card>

            {/* 项目详情 */}
            <Card
              title={
                <Space>
                  <TrophyOutlined />
                  <span>Project Details</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Level"
                    name="level"
                    tooltip="Project level classification"
                  >
                    <Select placeholder="Select project level" style={{ fontSize: '12px' }}>
                      <Option value="A">
                        <Tag color="green">A</Tag> - High Priority
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Medium Priority
                      </Option>
                      <Option value="C">
                        <Tag color="orange">C</Tag> - Low Priority
                      </Option>
                      <Option value="D">
                        <Tag color="red">D</Tag> - Monitor Only
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Category"
                    name="category"
                    tooltip="Project category type"
                  >
                    <Select placeholder="Select project category" style={{ fontSize: '12px' }}>
                      <Option value="Software">
                        <Tag color="cyan">Software</Tag>
                      </Option>
                      <Option value="Service">
                        <Tag color="purple">Service</Tag>
                      </Option>
                      <Option value="Product">
                        <Tag color="geekblue">Product</Tag>
                      </Option>
                      <Option value="Other">
                        <Tag color="default">Other</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Stage"
                    name="stage"
                    tooltip="Current project stage"
                  >
                    <Select placeholder="Select project stage" style={{ fontSize: '12px' }}>
                      <Option value="Lead">
                        <Tag color="orange">Lead</Tag>
                      </Option>
                      <Option value="Proposal">
                        <Tag color="blue">Proposal</Tag>
                      </Option>
                      <Option value="Contract">
                        <Tag color="purple">Contract</Tag>
                      </Option>
                      <Option value="Execution">
                        <Tag color="cyan">Execution</Tag>
                      </Option>
                      <Option value="Acceptance">
                        <Tag color="green">Acceptance</Tag>
                      </Option>
                      <Option value="Close">
                        <Tag color="red">Close</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Probability"
                    name="probability"
                    tooltip="Project success probability"
                  >
                    <Select placeholder="Select probability range" style={{ fontSize: '12px' }}>
                      <Option value="A">
                        <Tag color="green">A</Tag> - 80-100%
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - 60-80%
                      </Option>
                      <Option value="C">
                        <Tag color="orange">C</Tag> - 40-60%
                      </Option>
                      <Option value="D">
                        <Tag color="red">D</Tag> - &lt;40%
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Status"
                    name="status"
                    tooltip="Current project status"
                    initialValue="planning"
                  >
                    <Select placeholder="Select status" style={{ fontSize: '12px' }}>
                      <Option value="planning">
                        <Tag color="blue">Planning</Tag>
                      </Option>
                      <Option value="in_progress">
                        <Tag color="processing">In Progress</Tag>
                      </Option>
                      <Option value="completed">
                        <Tag color="success">Completed</Tag>
                      </Option>
                      <Option value="on_hold">
                        <Tag color="warning">On Hold</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {/* 保留空列以保持布局平衡 */}
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 右侧列 - 机会信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <ProjectOutlined />
                  <span>Opportunity Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={
                  <span>
                    Opportunity Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="name"
                rules={[
                  { required: true, message: 'Please enter opportunity name' },
                  { min: 3, message: 'Name must be at least 3 characters' },
                  { max: 100, message: 'Name cannot exceed 100 characters' }
                ]}
                tooltip="Complete opportunity name"
              >
                <Input 
                  placeholder="Enter opportunity name"
                  prefix={<ProjectOutlined />}
                  style={{ fontSize: '12px' }}
                />
              </Form.Item>

              <Form.Item
                label="Opportunity ID"
                name="projectId"
                rules={[
                  { required: true, message: 'Please enter opportunity ID' },
                  { pattern: /^OPP-\d{4}-\d{3}$/, message: 'Opportunity ID format should be OPP-YYYY-XXX' }
                ]}
                tooltip="Unique identifier for the opportunity. Format: OPP-YEAR-XXX"
              >
                <Input 
                  placeholder="OPP-2025-001"
                  addonAfter={
                    <Button 
                      type="text" 
                      size="small"
                      onClick={handleRegenerateId}
                      title="Generate new ID"
                      style={{ padding: '0 8px' }}
                    >
                      🔄
                    </Button>
                  }
                  style={{ fontSize: '12px' }}
                />
              </Form.Item>

              {/* Currency, Estimated Revenue & VAT Rate 三个字段 */}
              <Row gutter={12}>
                <Col span={8}>
                  <Form.Item
                    label="Currency"
                    name="currency"
                    rules={[{ required: true, message: 'Please select currency!' }]}
                    tooltip="Project currency"
                    initialValue="EUR"
                  >
                    <Select style={{ fontSize: '12px' }}>
                      <Option value="EUR">EUR (€)</Option>
                      <Option value="USD">USD ($)</Option>
                      <Option value="GBP">GBP (£)</Option>
                      <Option value="CNY">CNY (¥)</Option>
                      <Option value="JPY">JPY (¥)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="Estimated Revenue"
                    name="revenue"
                    rules={[{ required: true, message: 'Please input the estimated revenue!' }]}
                  >
                    <InputNumber
                      className="full-width-input"
                      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => (value ? value.replace(/\$\s?|(,*)/g, '') : '')}
                      style={{ fontSize: '14px', width: '100%' }}
                      placeholder="0"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="VAT Rate"
                    name="vatRate"
                    tooltip="Value Added Tax rate percentage"
                    rules={[
                      { type: 'number', min: 0, max: 100, message: 'VAT rate must be between 0% and 100%' }
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%', fontSize: '12px' }}
                      min={0}
                      max={100}
                      precision={1}
                      formatter={value => value ? `${value}%` : ''}
                      parser={(value: any) => value ? parseFloat(value.replace('%', '')) : ''}
                      placeholder="Enter VAT rate (e.g. 19)"
                      addonAfter="%"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Estimated Start Date"
                name="closeDate"
                tooltip="Expected start date for this opportunity"
              >
                <DatePicker 
                  style={{ width: '100%', fontSize: '12px' }}
                  placeholder="Select start date"
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Card>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button 
                size="large"
                onClick={() => navigate('/opportunity')}
              >
                Cancel
              </Button>
              <Button 
                type="primary" 
                size="large"
                htmlType="submit"
                loading={saving}
                icon={<SaveOutlined />}
                onClick={() => {
                  console.log('🚨🚨🚨 SUBMIT BUTTON CLICKED!!! 🚨🚨🚨');
                  console.log('🚨 Form instance:', form);
                  console.log('🚨 Current form values:', form.getFieldsValue());
                }}
              >
                Create Opportunity
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default CreateProject;
