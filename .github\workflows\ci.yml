name: CI

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: '14.x'
    - name: Install dependencies
      run: |
        cd backend
        npm ci
        cd ../frontend
        npm ci
    - name: Run tests
      run: |
        cd backend
        npm test
        cd ../frontend
        npm test
    - name: Build
      run: |
        cd frontend
        npm run build