/* 整体容器 */
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9f6f2;
  position: relative;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 顶部导航 */
.forgot-password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  width: 100%;
  z-index: 10;
  box-sizing: border-box;
  max-width: 100%;
}

.logo a {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: #000;
  text-decoration: none;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.sign-in-link, .sign-up-link {
  color: #000;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  margin-right: 15px;
  white-space: nowrap;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sign-in-link {
  color: #666;
}

.sign-in-link:hover {
  color: #000;
  background-color: rgba(0, 0, 0, 0.05);
}

.sign-up-link {
  color: #FF7A00;
}

.sign-up-link:hover {
  color: #FF7A00;
  background-color: rgba(255, 122, 0, 0.1);
}

/* 忘记密码卡片 */
.forgot-password-card-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  z-index: 5;
}

.forgot-password-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.forgot-password-title {
  margin-bottom: 10px !important;
  font-size: 16px !important;
  font-weight: var(--font-weight-semibold) !important;
  line-height: var(--line-height-tight) !important;
}

/* 强制覆盖Ant Design的Title样式 */
.forgot-password-card .ant-typography h2.forgot-password-title {
  font-size: 16px !important;
}

.ant-typography.forgot-password-title {
  font-size: 16px !important;
}

.forgot-password-subtitle {
  display: block;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: #666;
  margin-bottom: 30px;
}

.forgot-password-form {
  text-align: left;
}

.forgot-password-input {
  height: 50px;
  border-radius: 8px;
}

.forgot-password-button {
  width: 100%;
  height: 50px;
  border-radius: 8px;
  font-weight: var(--font-weight-medium);
  font-size: 14px !important;
  background-color: #ffcb8c;
  border: none;
  color: #000;
  margin-bottom: 20px;
}

/* 强制覆盖Ant Design Button样式 */
.ant-btn.forgot-password-button {
  font-size: 14px !important;
}

.forgot-password-card .ant-btn.forgot-password-button {
  font-size: 14px !important;
}

.forgot-password-button:hover {
  background-color: #ffc27b !important;
  color: #000 !important;
}

.back-to-login {
  text-align: center;
  margin-top: 20px;
}

.back-to-login a {
  color: #666;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.back-to-login a:hover {
  color: #000;
}

.email-sent-actions {
  margin-top: 30px;
}

/* 页脚 */
.forgot-password-footer {
  padding: 20px;
  text-align: center;
  z-index: 10;
  font-size: var(--font-size-sm);
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.footer-copyright {
  color: #666 !important;
  font-size: 14px !important;
  line-height: var(--line-height-normal);
  margin-top: -5px;
}

.footer-copyright span {
  color: #666 !important;
  font-size: 14px !important;
}

.footer-buttons {
  display: flex;
  gap: 16px;
}

.footer-button {
  background-color: #718096;
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.footer-button:hover {
  background-color: #2d3748 !important;
  color: white !important;
  transform: translateY(-1px);
}

/* 装饰元素 */
.decoration-line {
  position: absolute;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  z-index: 1;
}

.line-1 {
  width: 200px;
  height: 100px;
  top: 100px;
  left: 200px;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.line-2 {
  width: 300px;
  height: 150px;
  top: 200px;
  right: 100px;
  border-top: none;
  border-right: none;
  border-bottom: none;
}

.line-3 {
  width: 200px;
  height: 100px;
  bottom: 150px;
  right: 300px;
  border-top: none;
  border-left: none;
  border-right: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .forgot-password-header {
    padding: 15px 20px;
  }

  .forgot-password-card {
    padding: 30px 20px;
  }

  .decoration-line {
    display: none;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .footer-button {
    font-size: var(--font-size-base);
    padding: 0 10px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .forgot-password-header {
    padding: 10px 15px;
  }

  .nav-links {
    gap: 5px;
  }

  .sign-up-link {
    font-size: var(--font-size-base);
  }

  .logo a {
    font-size: var(--font-size-xl);
  }
} 