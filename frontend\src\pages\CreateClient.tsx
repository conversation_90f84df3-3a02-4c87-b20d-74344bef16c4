import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  DatePicker,
  InputNumber,
  Row,
  Col,
  Space,
  Typography,
  Divider,
  message,
  Tag
} from 'antd';
import dayjs from 'dayjs';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  BankOutlined,
  PlusOutlined,
  DeleteOutlined,
  HistoryOutlined,
  DollarOutlined,
  PoundCircleOutlined,
  EuroOutlined,
  PoundOutlined
} from '@ant-design/icons';
import clientService from '../services/client.service';
import projectService from '../services/new-project.service';
import authService from '../services/auth.service';
import notificationService from '../services/notification.service';
import { useCurrency } from '../contexts/CurrencyContext';

const { Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CreateClient: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [contactRecords, setContactRecords] = useState<any[]>([]);
  const [calculatedRevenue, setCalculatedRevenue] = useState<number>(0);
  
  const { selectedCurrency } = useCurrency();

  // 监听客户名称变化并计算实际收入
  const calculateClientRevenue = async (clientName: string) => {
    if (!clientName || clientName.trim() === '') {
      setCalculatedRevenue(0);
      form.setFieldsValue({ totalRevenue: 0 });
      return;
    }

    try {
      const projects = await projectService.getAllProjects();
      const clientProjects = projects.filter(
        project => project.client.toLowerCase() === clientName.toLowerCase()
      );
      
      const totalRevenue = clientProjects.reduce((sum, project) => {
        return sum + (project.revenue || 0);
      }, 0);

      setCalculatedRevenue(totalRevenue);
      form.setFieldsValue({ totalRevenue: totalRevenue });
    } catch (error) {
      console.error('Failed to calculate client revenue:', error);
    }
  };

  // 获取货币图标
  const getCurrencyIcon = () => {
    switch (selectedCurrency) {
      case 'USD':
        return <DollarOutlined />;
      case 'EUR':
        return <EuroOutlined />;
      case 'GBP':
        return <PoundOutlined />;
      default:
        return <DollarOutlined />;
    }
  };

  // 获取国家码配置
  const getCountryCode = (country: string) => {
    const countryConfigs: Record<string, { code: string; flag: string }> = {
      'United States': { code: '+1', flag: '🇺🇸' },
      'Germany': { code: '+49', flag: '🇩🇪' },
      'United Kingdom': { code: '+44', flag: '🇬🇧' },
      'France': { code: '+33', flag: '🇫🇷' },
      'Canada': { code: '+1', flag: '🇨🇦' },
      'Australia': { code: '+61', flag: '🇦🇺' },
      'Japan': { code: '+81', flag: '🇯🇵' },
      'China': { code: '+86', flag: '🇨🇳' },
      'India': { code: '+91', flag: '🇮🇳' },
      'Brazil': { code: '+55', flag: '🇧🇷' },
      'Netherlands': { code: '+31', flag: '🇳🇱' },
      'Sweden': { code: '+46', flag: '🇸🇪' },
      'Switzerland': { code: '+41', flag: '🇨🇭' },
      'Spain': { code: '+34', flag: '🇪🇸' },
      'Italy': { code: '+39', flag: '🇮🇹' },
      'South Korea': { code: '+82', flag: '🇰🇷' },
      'Singapore': { code: '+65', flag: '🇸🇬' },
      'Belgium': { code: '+32', flag: '🇧🇪' },
      'Denmark': { code: '+45', flag: '🇩🇰' },
      'Norway': { code: '+47', flag: '🇳🇴' },
      'Finland': { code: '+358', flag: '🇫🇮' },
      'Austria': { code: '+43', flag: '🇦🇹' },
      'Ireland': { code: '+353', flag: '🇮🇪' }
    };
    return countryConfigs[country] || { code: '+1', flag: '🌍' };
  };

  // 监听国家变化来更新电话号码前缀
const handleCountryChange = (country: string) => {
  const { code } = getCountryCode(country);
  const currentPhone = form.getFieldValue('phone');
  const currentContactPhone = form.getFieldValue('contactPhone');
  
  // 如果电话号码为空或只有旧的国家码，设置新的国家码
  if (!currentPhone || currentPhone.startsWith('+')) {
    form.setFieldsValue({ phone: `${code} ` });
  }
  if (!currentContactPhone || currentContactPhone.startsWith('+')) {
    form.setFieldsValue({ contactPhone: `${code} ` });
  }
};

  // 添加新的接触记录
  const addContactRecord = () => {
    const newRecord = {
      id: Date.now().toString(),
      date: new Date().toISOString().split('T')[0],
      clientContactPerson: '',
      internalContactPerson: '',
      notes: ''
    };
    setContactRecords([...contactRecords, newRecord]);
  };

  // 删除接触记录
  const removeContactRecord = (id: string) => {
    setContactRecords(contactRecords.filter(record => record.id !== id));
  };

  // 更新接触记录
  const updateContactRecord = (id: string, field: 'date' | 'clientContactPerson' | 'internalContactPerson' | 'notes', value: string) => {
    setContactRecords(contactRecords.map(record => 
      record.id === id ? { ...record, [field]: value } : record
    ));
  };

  // 新增顺序ID生成函数
  const getNextClientId = () => {
    const year = new Date().getFullYear();
    const key = `client_id_seq_${year}`;
    let seq = 1;
    try {
      const stored = localStorage.getItem(key);
      if (stored) seq = parseInt(stored, 10) + 1;
    } catch {}
    // 生成ID
    const id = `CLI-${year}-${String(seq).padStart(3, '0')}`;
    // 记忆到localStorage
    localStorage.setItem(key, String(seq));
    return id;
  };

  // useEffect初始化时自动填充顺序ID
  useEffect(() => {
    form.setFieldsValue({
      clientId: getNextClientId()
    });
  }, [form]);

  // 重新生成ID按钮也用顺序ID
  const handleRegenerateId = () => {
    const newId = getNextClientId();
    form.setFieldsValue({
      clientId: newId
    });
    message.success('New Client ID generated successfully!');
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setSaving(true);
    try {
      // 字段名映射，确保与Client类型一致
      const clientData = {
        ...values,
        name: values.clientName, // 映射
        clientId: values.clientId || getNextClientId(),
        totalRevenue: values.totalRevenue || 0,
        totalProjects: 0,
        contactRecords: contactRecords.filter(record => record.notes.trim() !== ''),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      delete clientData.clientName; // 移除冗余字段

      await clientService.createClient(clientData);
      
      // 创建通知
      const currentUser = authService.getCurrentUser();
      await notificationService.notifyClientCreated(
        clientData.name,
        currentUser?.username || 'System User'
      );
      
      message.success('Client created successfully!');
      navigate('/client');
    } catch (error) {
      console.error('Failed to create client:', error);
      message.error('Failed to create client. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // 自动修复历史脏数据
  useEffect(() => {
    try {
      const raw = localStorage.getItem('clients');
      if (raw) {
        const arr = JSON.parse(raw);
        let changed = false;
        arr.forEach((c: any) => {
          if (!c.name && c.clientName) {
            c.name = c.clientName;
            changed = true;
          }
        });
        if (changed) {
          localStorage.setItem('clients', JSON.stringify(arr));
        }
      }
    } catch {}
  }, []);

  // 行业选项
  const industries = [
    'Technology', 'Financial Services', 'Healthcare', 'Manufacturing', 'Retail',
    'Education', 'Food & Beverage', 'Hospitality', 'Real Estate', 'Construction',
    'Transportation', 'Energy', 'Telecommunications', 'Media & Entertainment',
    'Government', 'Non-profit', 'Agriculture', 'Automotive', 'Aerospace',
    'Consulting', 'Legal', 'Insurance', 'Pharmaceuticals', 'Chemicals',
    'Textiles', 'Mining', 'Sports & Fitness', 'Beauty & Personal Care',
    'Gaming', 'E-commerce', 'Other'
  ];

  // 国家选项
  const countries = [
    'United States', 'Germany', 'United Kingdom', 'France', 'Canada',
    'Australia', 'Japan', 'China', 'India', 'Brazil', 'Netherlands',
    'Sweden', 'Switzerland', 'Spain', 'Italy', 'South Korea', 'Singapore',
    'Belgium', 'Denmark', 'Norway', 'Finland', 'Austria', 'Ireland', 'Other'
  ];

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/client')}
          style={{ marginBottom: '16px' }}
        >
          Back to Client List
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          Create New Client
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
        initialValues={{
          status: 'potential',
          tier: 'A'
        }}
      >
        <Row gutter={24}>
          {/* 左侧列 - 基本信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  <span>Basic Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label="Client ID"
                name="clientId"
                rules={[
                  { required: true, message: 'Please enter client ID' },
                  { pattern: /^CLI-\d{4}-\d{3}$/, message: 'Client ID format should be CLI-YYYY-XXX' }
                ]}
                tooltip="Unique identifier for the client. Format: CLI-YEAR-XXX"
              >
                <Input 
                  placeholder="CLI-2025-001"
                  addonAfter={
                    <Button 
                      type="text" 
                      size="small"
                      onClick={handleRegenerateId}
                      title="Generate new ID"
                      style={{ padding: '0 8px' }}
                    >
                      🔄
                    </Button>
                  }
                />
              </Form.Item>

              <Form.Item
                name="clientName"
                label={<span>Client Name <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please enter client name' }]}
              >
                <Input 
                  placeholder="Enter client name"
                  prefix={<UserOutlined />}
                  onChange={(e) => calculateClientRevenue(e.target.value)}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Client Tier"
                    name="tier"
                    rules={[{ required: true, message: 'Please select client tier' }]}
                    tooltip="Client tier based on business value and potential"
                  >
                    <Select placeholder="Select tier">
                      <Option value="S">
                        <Tag color="purple">S</Tag> - Strategic Client
                      </Option>
                      <Option value="V">
                        <Tag color="gold">V</Tag> - Valued Client
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Business Client
                      </Option>
                      <Option value="A">
                        <Tag color="green">A</Tag> - Average Client
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Status"
                    name="status"
                    rules={[{ required: true, message: 'Please select status' }]}
                    tooltip="Current relationship status with the client"
                  >
                    <Select placeholder="Select status">
                      <Option value="active">
                        <Tag color="green">Active</Tag>
                      </Option>
                      <Option value="potential">
                        <Tag color="blue">Potential</Tag>
                      </Option>
                      <Option value="inactive">
                        <Tag color="red">Inactive</Tag>
                      </Option>
                      <Option value="churned">
                        <Tag color="volcano">Churned</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Industry"
                    name="industry"
                    rules={[{ required: true, message: 'Please select industry' }]}
                    tooltip="Primary industry or business sector"
                  >
                    <Select
                      placeholder="Type to search industry..."
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      <Option value="Technology">💻 Technology</Option>
                      <Option value="Financial Services">🏦 Financial Services</Option>
                      <Option value="Healthcare">🏥 Healthcare</Option>
                      <Option value="Manufacturing">🏭 Manufacturing</Option>
                      <Option value="Retail">🛍️ Retail</Option>
                      <Option value="Education">🎓 Education</Option>
                      <Option value="Food & Beverage">🍽️ Food & Beverage</Option>
                      <Option value="Hospitality">🏨 Hospitality</Option>
                      <Option value="Real Estate">🏢 Real Estate</Option>
                      <Option value="Construction">🏗️ Construction</Option>
                      <Option value="Transportation">🚛 Transportation</Option>
                      <Option value="Energy">⚡ Energy</Option>
                      <Option value="Telecommunications">📡 Telecommunications</Option>
                      <Option value="Media & Entertainment">🎬 Media & Entertainment</Option>
                      <Option value="Government">🏛️ Government</Option>
                      <Option value="Non-profit">❤️ Non-profit</Option>
                      <Option value="Agriculture">🌾 Agriculture</Option>
                      <Option value="Automotive">🚗 Automotive</Option>
                      <Option value="Aerospace">✈️ Aerospace</Option>
                      <Option value="Consulting">💼 Consulting</Option>
                      <Option value="Legal">⚖️ Legal</Option>
                      <Option value="Insurance">🛡️ Insurance</Option>
                      <Option value="Pharmaceuticals">💊 Pharmaceuticals</Option>
                      <Option value="Chemicals">🧪 Chemicals</Option>
                      <Option value="Textiles">👕 Textiles</Option>
                      <Option value="Mining">⛏️ Mining</Option>
                      <Option value="Sports & Fitness">🏃 Sports & Fitness</Option>
                      <Option value="Beauty & Personal Care">💄 Beauty & Personal Care</Option>
                      <Option value="Gaming">🎮 Gaming</Option>
                      <Option value="E-commerce">🛒 E-commerce</Option>
                      <Option value="Other">📋 Other</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Country"
                    name="country"
                    tooltip="Primary operating country (optional)"
                  >
                    <Select
                      placeholder="Select country"
                      showSearch
                      onChange={handleCountryChange}
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      {countries.map(country => (
                        <Option key={country} value={country}>
                          {country}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Client Tags"
                name="tags"
                tooltip="Keywords or labels to categorize this client"
              >
                <Select
                  mode="tags"
                  placeholder="Add tags (select from presets or type custom tags)"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  options={[
                    { label: '🎯 Strategic', value: 'Strategic' },
                    { label: '💎 High-Value', value: 'High-Value' },
                    { label: '⏰ Long-term', value: 'Long-term' },
                    { label: '👑 Premium', value: 'Premium' },
                    { label: '⭐ VIP', value: 'VIP' },
                    { label: '🏢 Enterprise', value: 'Enterprise' },
                    { label: '🏪 SMB', value: 'SMB' },
                    { label: '🚀 Startup', value: 'Startup' },
                    { label: '🏛️ Government', value: 'Government' },
                    { label: '❤️ Non-profit', value: 'Non-profit' },
                    { label: '🌍 International', value: 'International' },
                    { label: '🏠 Domestic', value: 'Domestic' },
                    { label: '💻 Technology', value: 'Technology' },
                    { label: '💰 Finance', value: 'Finance' },
                    { label: '⚕️ Healthcare', value: 'Healthcare' },
                    { label: '🏭 Manufacturing', value: 'Manufacturing' },
                    { label: '🛍️ Retail', value: 'Retail' },
                    { label: '🎓 Education', value: 'Education' },
                    { label: '🔄 Recurring', value: 'Recurring' },
                    { label: '⚡ Quick-Response', value: 'Quick-Response' }
                  ]}
                />
              </Form.Item>
            </Card>

            {/* 财务信息 */}
            <Card
              title={
                <Space>
                  <BankOutlined />
                  <span>Business Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label="Website"
                name="website"
                tooltip="Company website URL"
                rules={[
                  { type: 'url', message: 'Please enter a valid URL' }
                ]}
              >
                <Input 
                  placeholder="https://example.com"
                  prefix={<GlobalOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Business Address"
                name="address"
                tooltip="Primary business address"
              >
                <TextArea 
                  placeholder="Enter business address"
                  rows={3}
                />
              </Form.Item>

              <Form.Item
                label={`Total Revenue (${selectedCurrency})`}
                name="totalRevenue"
                tooltip={`Calculated automatically from all projects for this client in ${selectedCurrency}`}
                extra={calculatedRevenue > 0 ? `Auto-calculated from ${calculatedRevenue > 0 ? 'existing' : 'no'} projects` : undefined}
              >
                <InputNumber
                  placeholder="Auto-calculated from projects"
                  style={{ width: '100%' }}
                  min={0}
                  precision={0}
                  addonBefore={getCurrencyIcon()}
                  readOnly
                  disabled
                  formatter={value => {
                    if (!value) return '';
                    return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  }}
                  parser={(value: any) => {
                    if (!value) return 0;
                    const cleanValue = value.replace(/[€$£¥\s,]/g, '');
                    const parsed = parseFloat(cleanValue);
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Card>
          </Col>

          {/* 右侧列 - 联系信息 */}
          <Col span={12}>
            <Card
              title={
                <Space>
                  <PhoneOutlined />
                  <span>Contact Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={<span>Primary Contact Person <span style={{ color: 'red' }}>*</span></span>}
                name="contactPerson"
                rules={[
                  { required: true, message: 'Please enter contact person name' },
                  { min: 2, message: 'Contact person name must be at least 2 characters' }
                ]}
                tooltip="Main point of contact at the client organization"
              >
                <Input 
                  placeholder="Enter contact person name"
                  prefix={<UserOutlined />}
                />
              </Form.Item>

              <Form.Item
                name="primaryEmail"
                label={<span>Primary Email</span>}
                rules={[{ type: 'email', message: 'Please enter a valid email address' }]}
              >
                <Input 
                  placeholder="Enter email address"
                  prefix={<MailOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Primary Phone"
                name="phone"
                tooltip="Primary phone number for business communications"
              >
                <Input 
                  placeholder="(*************"
                  prefix={
                    <span style={{ marginRight: '4px' }}>
                      {getCountryCode(form.getFieldValue('country') || '').flag} {getCountryCode(form.getFieldValue('country') || '').code}
                    </span>
                  }
                />
              </Form.Item>

              <Divider orientation="left">
                <Text strong>Alternative Contact</Text>
              </Divider>

              <Form.Item
                label="Contact Person Email"
                name="contactEmail"
                rules={[
                  { type: 'email', message: 'Please enter a valid email address' }
                ]}
                tooltip="Alternative email for the contact person"
              >
                <Input 
                  placeholder="<EMAIL>"
                  prefix={<MailOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Contact Person Phone"
                name="contactPhone"
                tooltip="Direct phone number for the contact person"
              >
                <Input 
                  placeholder="(*************"
                  prefix={
                    <span style={{ marginRight: '4px' }}>
                      {getCountryCode(form.getFieldValue('country') || '').flag} {getCountryCode(form.getFieldValue('country') || '').code}
                    </span>
                  }
                />
              </Form.Item>
            </Card>

            {/* Contact Log */}
            <Card
              title={
                <Space>
                  <HistoryOutlined />
                  <span>Contact Log</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
              extra={
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={addContactRecord}
                >
                  Add Record
                </Button>
              }
            >
              {contactRecords.length === 0 ? (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '40px 20px',
                  background: '#fafafa',
                  borderRadius: '8px',
                  border: '1px dashed #d9d9d9'
                }}>
                  <HistoryOutlined style={{ fontSize: '24px', color: '#d9d9d9', marginBottom: '8px' }} />
                  <div style={{ color: '#999', marginBottom: '16px' }}>No contact records yet</div>
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={addContactRecord}
                  >
                    Add First Record
                  </Button>
                </div>
              ) : (
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {/* Header Row */}
                  <div style={{
                    display: 'flex',
                    backgroundColor: '#fafafa',
                    padding: '12px 16px',
                    borderRadius: '6px',
                    marginBottom: '12px',
                    border: '1px solid #e8e8e8',
                    fontWeight: 'bold',
                    fontSize: '11px',
                    color: '#666'
                  }}>
                    <div style={{ width: '120px', flexShrink: 0 }}>Date</div>
                    <div style={{ flex: '1', marginLeft: '12px' }}>Client Contact</div>
                    <div style={{ flex: '1', marginLeft: '12px' }}>Internal Contact</div>
                    <div style={{ flex: '1.5', marginLeft: '12px' }}>Notes</div>
                    <div style={{ width: '40px', textAlign: 'center', marginLeft: '12px' }}>Action</div>
                  </div>

                  {contactRecords.map((record, index) => (
                    <div
                      key={record.id}
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        padding: '12px 16px',
                        border: '1px solid #e8e8e8',
                        borderRadius: '6px',
                        marginBottom: '8px',
                        backgroundColor: '#ffffff',
                        transition: 'box-shadow 0.2s',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      <div style={{ width: '120px', flexShrink: 0 }}>
                        <DatePicker
                          style={{ width: '100%' }}
                          value={record.date ? dayjs(record.date) : null}
                          onChange={(date) => {
                            const dateStr = date ? date.format('YYYY-MM-DD') : '';
                            updateContactRecord(record.id, 'date', dateStr);
                          }}
                          placeholder="Select date"
                          size="small"
                          format="DD-MM-YYYY"
                        />
                      </div>
                      
                      <div style={{ flex: '1', marginLeft: '12px' }}>
                        <Input
                          value={record.clientContactPerson}
                          onChange={(e) => updateContactRecord(record.id, 'clientContactPerson', e.target.value)}
                          placeholder="Client contact"
                          size="small"
                        />
                      </div>
                      
                      <div style={{ flex: '1', marginLeft: '12px' }}>
                        <Input
                          value={record.internalContactPerson}
                          onChange={(e) => updateContactRecord(record.id, 'internalContactPerson', e.target.value)}
                          placeholder="Internal contact"
                          size="small"
                        />
                      </div>
                      
                      <div style={{ flex: '1.5', marginLeft: '12px' }}>
                        <Input
                          value={record.notes}
                          onChange={(e) => updateContactRecord(record.id, 'notes', e.target.value)}
                          placeholder="Notes and details..."
                          size="small"
                        />
                      </div>
                      
                      <div style={{ width: '40px', textAlign: 'center', marginLeft: '12px' }}>
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeContactRecord(record.id)}
                          style={{ 
                            minWidth: 'auto',
                            padding: '4px',
                            height: '24px',
                            width: '24px'
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Card>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button 
                size="large"
                onClick={() => navigate('/client')}
              >
                Cancel
              </Button>
              <Button 
                type="primary" 
                size="large"
                htmlType="submit"
                loading={saving}
                icon={<SaveOutlined />}
              >
                Create Client
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default CreateClient; 