/**
 * Safe localStorage utility functions with proper error handling
 */

export class SafeLocalStorage {
  /**
   * Safely get and parse JSON data from localStorage
   */
  static getJSON<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      if (!item || item === 'undefined' || item === 'null') {
        return defaultValue;
      }
      return JSON.parse(item);
    } catch (error) {
      console.error(`Error parsing localStorage item '${key}':`, error);
      // Clear corrupted data
      localStorage.removeItem(key);
      return defaultValue;
    }
  }

  /**
   * Safely set JSON data to localStorage
   */
  static setJSON<T>(key: string, value: T): boolean {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Error setting localStorage item '${key}':`, error);
      return false;
    }
  }

  /**
   * Safely get string data from localStorage
   */
  static getString(key: string, defaultValue: string = ''): string {
    try {
      const item = localStorage.getItem(key);
      return item && item !== 'undefined' && item !== 'null' ? item : defaultValue;
    } catch (error) {
      console.error(`Error getting localStorage item '${key}':`, error);
      return defaultValue;
    }
  }

  /**
   * Safely remove item from localStorage
   */
  static remove(key: string): boolean {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing localStorage item '${key}':`, error);
      return false;
    }
  }

  /**
   * Clear all localStorage data (use with caution)
   */
  static clear(): boolean {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing localStorage:', error);
      return false;
    }
  }

  /**
   * Check if a key exists and has valid data
   */
  static hasValidData(key: string): boolean {
    try {
      const item = localStorage.getItem(key);
      return !!(item && item !== 'undefined' && item !== 'null');
    } catch (error) {
      console.error(`Error checking localStorage item '${key}':`, error);
      return false;
    }
  }

  /**
   * Get all keys from localStorage with optional prefix filter
   */
  static getKeys(prefix?: string): string[] {
    try {
      const keys = Object.keys(localStorage);
      return prefix ? keys.filter(key => key.startsWith(prefix)) : keys;
    } catch (error) {
      console.error('Error getting localStorage keys:', error);
      return [];
    }
  }

  /**
   * Clean up corrupted or invalid data from localStorage
   */
  static cleanup(keysToCheck?: string[]): number {
    let cleanedCount = 0;
    try {
      const keys = keysToCheck || this.getKeys();
      
      for (const key of keys) {
        const item = localStorage.getItem(key);
        if (item === 'undefined' || item === 'null' || item === '') {
          localStorage.removeItem(key);
          cleanedCount++;
          console.log(`Cleaned up corrupted localStorage item: ${key}`);
        }
      }
    } catch (error) {
      console.error('Error during localStorage cleanup:', error);
    }
    
    return cleanedCount;
  }
} 