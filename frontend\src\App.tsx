import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout, App as AntApp } from 'antd';
import { GoogleOAuthProvider } from '@react-oauth/google';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import NewProjectList from './pages/NewProjectList';
import ProjectDetail from './pages/ProjectDetail';
import CreateProject from './pages/CreateProject';
import EditProject from './pages/EditProject';
import ClientList from './pages/ClientList';
import CreateClient from './pages/CreateClient';
import EditClient from './pages/EditClient';
import VendorManagement from './pages/VendorManagement';
import CreateVendor from './pages/CreateVendor';
import EditVendor from './pages/EditVendor';

import FinanceManagement from './pages/FinanceManagement';
import CreateTransaction from './pages/CreateTransaction';
import EditTransaction from './pages/EditTransaction';
import ViewTransaction from './pages/ViewTransaction';
import AIAssistant from './pages/AIAssistant';
import AIAnalysis from './pages/AIAnalysis';
import AIChat from './pages/AIChat';
import MessageCenter from './pages/MessageCenter';
import Settings from './pages/Settings';
import Profile from './pages/Profile';
import DataSyncTest from './components/DataSyncTest';
import DataArchitectureTest from './components/DataArchitectureTest';
import OpportunityDataTest from './components/OpportunityDataTest';
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';

import Home from './pages/Home';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import { ThemeProvider } from './contexts/ThemeContext';
import { CurrencyProvider } from './contexts/CurrencyContext';

// 导入存储工具 - 防止JSON解析错误
import './utils/storageUtils';

// 🚨 紧急数据修复 - 自动加载
import './utils/emergencyDataFix';

// 导入样式
import './App.css';
import './styles/unified-components.css';
import './styles/components.css';
import './styles/project-list.css';
import './styles/project-profit-dashboard.css';

const { Content } = Layout;

const App: React.FC = () => {
  // Google OAuth 客户端 ID - 您需要替换为您从 Google Cloud Console 获取的客户端 ID
  // 这里使用一个临时 ID
  const googleClientId = '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com';

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <CurrencyProvider>
          <AntApp>
            <GoogleOAuthProvider clientId={googleClientId}>
            <Router>
            <Routes>
              {/* 根路径显示首页 */}
              <Route path="/" element={<Home />} />

              {/* 登录和注册路由 */}
              <Route path="/login" element={<Login />} />
              {/* 保留旧路径，但重定向到新登录页面 */}
              <Route path="/old-login" element={<Navigate to="/login" replace />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              

              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <Layout style={{ minHeight: '100vh' }}>
                      <Sidebar />
                      <Layout>
                        <Header />
                        <Content style={{
                          margin: 'var(--spacing-md)',
                          padding: 0,
                          background: 'transparent',
                          borderRadius: 'var(--radius-md)',
                          overflow: 'hidden'
                        }}>
                          <Routes>
                            <Route path="/dashboard" element={<Dashboard />} />
                            <Route path="/opportunity" element={<NewProjectList />} />
                            <Route path="/opportunity/create" element={<CreateProject />} />
                            <Route path="/opportunity/edit/:id" element={<EditProject />} />
                            <Route path="/opportunity/:id" element={<ProjectDetail />} />
                            <Route path="/client" element={<ClientList />} />
                            <Route path="/client/create" element={<CreateClient />} />
                            <Route path="/client/edit/:id" element={<EditClient />} />
                            <Route path="/vendor" element={<VendorManagement />} />
                            <Route path="/vendor/create" element={<CreateVendor />} />
                            <Route path="/vendor/edit/:id" element={<EditVendor />} />

                            <Route path="/finance" element={<FinanceManagement />} />
                            <Route path="/finance/create" element={<CreateTransaction />} />
                            <Route path="/finance/edit/:id" element={<EditTransaction />} />
                            <Route path="/finance/:id" element={<ViewTransaction />} />
                            <Route path="/ai" element={<AIAssistant />} />
                            <Route path="/ai/analysis" element={<AIAnalysis />} />
                            <Route path="/ai/chat" element={<AIChat />} />
                            <Route path="/messages" element={<MessageCenter />} />
                            <Route path="/profile" element={<Profile />} />
                            <Route path="/settings" element={<Settings />} />
                            <Route path="/system" element={<Settings />} />
                            <Route path="/data-sync-test" element={<DataSyncTest />} />
          <Route path="/data-architecture-test" element={<DataArchitectureTest />} />
                            <Route path="/opportunity-data-test" element={<OpportunityDataTest />} />
                            <Route path="*" element={<Navigate to="/dashboard" replace />} />
                          </Routes>
                        </Content>
                      </Layout>
                    </Layout>
                  </ProtectedRoute>
                }
              />
            </Routes>
                      </Router>
          </GoogleOAuthProvider>
        </AntApp>
        </CurrencyProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
