"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyAppleTokenProduction = exports.verifyAppleToken = void 0;
const axios_1 = __importDefault(require("axios"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
async function verifyAppleToken(idToken) {
    try {
        const decoded = jsonwebtoken_1.default.decode(idToken);
        if (!decoded) {
            throw new Error('Invalid Apple ID token');
        }
        return {
            appleId: decoded.sub,
            email: decoded.email,
            username: decoded.email ? decoded.email.split('@')[0] : `apple_user_${Date.now()}`,
            verified: true
        };
    }
    catch (error) {
        console.error('Error verifying Apple token:', error);
        throw new Error('Invalid Apple ID token');
    }
}
exports.verifyAppleToken = verifyAppleToken;
async function verifyAppleTokenProduction(idToken) {
    try {
        const response = await axios_1.default.get('https://appleid.apple.com/auth/keys');
        const keys = response.data.keys;
        const decoded = jsonwebtoken_1.default.decode(idToken, { complete: true });
        if (!decoded) {
            throw new Error('Invalid Apple ID token');
        }
        if (decoded.payload.iss !== 'https://appleid.apple.com') {
            throw new Error('Invalid token issuer');
        }
        if (decoded.payload.aud !== process.env.APPLE_CLIENT_ID) {
            throw new Error('Invalid token audience');
        }
        const currentTime = Math.floor(Date.now() / 1000);
        if (decoded.payload.exp < currentTime) {
            throw new Error('Token has expired');
        }
        return {
            appleId: decoded.payload.sub,
            email: decoded.payload.email,
            username: decoded.payload.email ? decoded.payload.email.split('@')[0] : `apple_user_${Date.now()}`,
            verified: true
        };
    }
    catch (error) {
        console.error('Error verifying Apple token:', error);
        throw new Error('Invalid Apple ID token');
    }
}
exports.verifyAppleTokenProduction = verifyAppleTokenProduction;
//# sourceMappingURL=apple-auth.service.js.map