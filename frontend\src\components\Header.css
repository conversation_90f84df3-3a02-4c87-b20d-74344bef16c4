/* 头部样式 */
.site-header {
  background-color: var(--card-background);
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: var(--header-height, 64px);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid var(--border);
}

/* 搜索框 */
.header-search {
  max-width: 400px;
  width: 100%;
}

.header-search .ant-input-affix-wrapper {
  border-radius: var(--radius-sm);
  background-color: var(--gray-light);
  border: 1px solid transparent;
  transition: all var(--transition-fast);
}

.header-search .ant-input-affix-wrapper:hover,
.header-search .ant-input-affix-wrapper:focus,
.header-search .ant-input-affix-wrapper-focused {
  background-color: var(--card-background);
  border-color: var(--border);
  box-shadow: none;
}

.header-search .ant-input {
  background-color: transparent;
}

/* 右侧工具栏 */
.header-toolbar {
  display: flex;
  align-items: center;
  margin-left: auto;
  padding-left: 40px;
}

.toolbar-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
  margin-left: 18px;
}

.toolbar-item:first-child {
  margin-left: 0;
}

/* 通知徽章 */
.notification-badge .ant-badge-count {
  background-color: var(--primary);
  box-shadow: none;
}

/* 用户菜单 */
.user-menu {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.user-menu:hover {
  background-color: var(--hover);
}

.user-avatar {
  background-color: var(--primary) !important;
  color: #fff !important;
  width: 28px !important;
  height: 28px !important;
  font-size: 12px !important;
}

.user-name {
  margin-left: 8px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 13px;
}

/* 用户下拉菜单样式 */
.ant-dropdown-menu .ant-dropdown-menu-item {
  font-size: 13px !important;
  padding: 8px 12px !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item-icon {
  font-size: 14px !important;
  margin-right: 8px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .site-header {
    padding: 0 16px;
  }
  
  .header-search {
    max-width: 200px;
  }
  
  .user-name {
    display: none;
  }
}
