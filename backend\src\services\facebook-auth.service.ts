import axios from 'axios';

/**
 * 验证 Facebook 访问令牌并获取用户信息
 * @param accessToken Facebook 提供的访问令牌
 * @returns 用户信息对象
 */
export async function verifyFacebookToken(accessToken: string) {
  try {
    // 验证令牌并获取用户信息
    const response = await axios.get(
      `https://graph.facebook.com/me?fields=id,name,email&access_token=${accessToken}`
    );
    
    const userData = response.data;
    
    if (!userData || !userData.id) {
      throw new Error('Invalid Facebook token');
    }
    
    return {
      facebookId: userData.id,
      email: userData.email,
      username: userData.name || (userData.email ? userData.email.split('@')[0] : `fb_user_${userData.id}`),
      verified: true // Facebook 已经验证了用户的身份
    };
  } catch (error) {
    console.error('Error verifying Facebook token:', error);
    throw new Error('Invalid Facebook token');
  }
}

/**
 * 验证 Facebook 令牌的详细信息
 * @param accessToken Facebook 提供的访问令牌
 * @returns 令牌详细信息
 */
export async function verifyFacebookTokenDetails(accessToken: string) {
  try {
    // 验证令牌
    const appId = process.env.FACEBOOK_APP_ID;
    const appSecret = process.env.FACEBOOK_APP_SECRET;
    
    // 获取令牌详细信息
    const response = await axios.get(
      `https://graph.facebook.com/debug_token?input_token=${accessToken}&access_token=${appId}|${appSecret}`
    );
    
    const data = response.data.data;
    
    // 验证令牌是否有效
    if (!data.is_valid) {
      throw new Error('Invalid Facebook token');
    }
    
    // 验证令牌的应用 ID
    if (data.app_id !== appId) {
      throw new Error('Token was not issued for this app');
    }
    
    // 获取用户信息
    return await verifyFacebookToken(accessToken);
  } catch (error) {
    console.error('Error verifying Facebook token details:', error);
    throw new Error('Invalid Facebook token');
  }
}
