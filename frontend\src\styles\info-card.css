/* 信息卡片样式 */
.info-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 24px;
}

.info-card .ant-card-body {
  padding: 0;
}

.info-sections {
  display: flex;
  flex-direction: column;
}

.info-section {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.info-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #1f2023;
  margin-bottom: 16px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  border-radius: 2px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

@media (min-width: 768px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 992px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 13px;
  color: #8c8c8c;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #262626;
}

.info-value.highlight {
  font-weight: 600;
  font-size: 15px;
  color: #1f2023;
}

/* 标签样式 */
.info-value .ant-tag {
  margin-right: 0;
  font-weight: 500;
}

/* 空值样式 */
.info-value .ant-btn-link {
  height: auto;
  line-height: 1.5;
}

/* 财务数字样式 */
.info-value .financial-number {
  font-family: 'SF Mono', 'Consolas', monospace;
  font-weight: 500;
}

/* 紧凑型信息卡片样式 */
.compact-info-card .ant-card-head {
  min-height: auto;
  padding: 8px 16px;
}

.compact-info-card .ant-card-head-title {
  padding: 8px 0;
}

.compact-info-container {
  width: 100%;
}

.compact-info-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.compact-info-section {
  flex: 1;
  min-width: 250px;
  padding: 0 12px;
  margin-bottom: 8px;
  border-right: 1px solid #f0f0f0;
}

.compact-info-section:last-child {
  border-right: none;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.section-icon {
  color: #1890ff;
  margin-right: 6px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.section-title-small {
  font-size: 14px;
  font-weight: 600;
  color: #1f2023;
}

.compact-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
}

.compact-info-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
  position: relative;
}

.compact-info-item::after {
  content: '';
  position: absolute;
  left: 100px;
  right: 0;
  top: 50%;
  border-bottom: 1px dotted #f0f0f0;
  z-index: 0;
}

.info-label-small {
  color: #8c8c8c;
  margin-right: 8px;
  min-width: 100px;
  width: 100px;
  display: inline-block;
  text-align: right;
  position: relative;
  z-index: 1;
  background-color: white;
  padding-left: 4px;
}

.info-value-small {
  color: #262626;
  flex: 1;
  padding-left: 8px;
  position: relative;
  z-index: 1;
  background-color: white;
  padding-right: 4px;
}

.info-value-small.highlight {
  font-weight: 600;
  color: #1f2023;
}

.empty-value {
  color: #d9d9d9;
  font-style: italic;
  font-size: 12px;
}
