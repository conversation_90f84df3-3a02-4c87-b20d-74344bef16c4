import React from 'react';
import { Switch, Tooltip, Button, Dropdown } from 'antd';
import { MoonOutlined, SunOutlined, SettingOutlined } from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import type { MenuProps, ButtonProps, SwitchProps } from 'antd';
import './ThemeSwitcher.css';

interface ThemeSwitcherProps {
  mode?: 'switch' | 'button' | 'dropdown';
  size?: ButtonProps['size'] | SwitchProps['size'];
  showTooltip?: boolean;
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 主题切换组件
 * 支持开关、按钮和下拉菜单三种模式
 */
const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({
  mode = 'switch',
  size = 'default',
  showTooltip = true,
  tooltipPlacement = 'bottom',
  className = '',
  style = {}
}) => {
  const { isDarkMode, toggleTheme } = useTheme();

  // 开关模式
  const renderSwitch = () => {
    const switchComponent = (
      <Switch
        checked={isDarkMode}
        onChange={toggleTheme}
        checkedChildren={<MoonOutlined />}
        unCheckedChildren={<SunOutlined />}
        className={`theme-switcher-switch ${className}`}
        size={size === 'large' ? undefined : (size === 'middle' ? 'default' : size) as SwitchProps['size']}
        style={style}
      />
    );

    if (showTooltip) {
      return (
        <Tooltip
          title={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
          placement={tooltipPlacement}
        >
          {switchComponent}
        </Tooltip>
      );
    }

    return switchComponent;
  };

  // 按钮模式
  const renderButton = () => {
    const buttonComponent = (
      <Button
        type="text"
        icon={isDarkMode ? <MoonOutlined /> : <SunOutlined />}
        onClick={toggleTheme}
        className={`theme-switcher-button ${className}`}
        size={size as ButtonProps['size']}
        style={style}
      />
    );

    if (showTooltip) {
      return (
        <Tooltip
          title={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
          placement={tooltipPlacement}
        >
          {buttonComponent}
        </Tooltip>
      );
    }

    return buttonComponent;
  };

  // 下拉菜单模式
  const renderDropdown = () => {
    const items: MenuProps['items'] = [
      {
        key: 'light',
        label: '浅色模式',
        icon: <SunOutlined />,
        onClick: () => isDarkMode && toggleTheme()
      },
      {
        key: 'dark',
        label: '深色模式',
        icon: <MoonOutlined />,
        onClick: () => !isDarkMode && toggleTheme()
      }
    ];

    const dropdownComponent = (
      <Dropdown menu={{ items }} placement="bottomRight" trigger={['click']}>
        <Button
          type="text"
          icon={<SettingOutlined />}
          className={`theme-switcher-dropdown ${className}`}
          size={size as ButtonProps['size']}
          style={style}
        />
      </Dropdown>
    );

    if (showTooltip) {
      return (
        <Tooltip title="主题设置" placement={tooltipPlacement}>
          {dropdownComponent}
        </Tooltip>
      );
    }

    return dropdownComponent;
  };

  // 根据模式渲染不同的组件
  switch (mode) {
    case 'button':
      return renderButton();
    case 'dropdown':
      return renderDropdown();
    case 'switch':
    default:
      return renderSwitch();
  }
};

export default ThemeSwitcher;
