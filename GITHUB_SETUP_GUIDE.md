# GitHub 配置指南

本指南将帮助你将 LTC 项目管理系统上传到 GitHub，并进行正确的配置。

## 1. 创建 GitHub 账号

如果你还没有 GitHub 账号，请先在 [GitHub](https://github.com/) 注册一个账号。

## 2. 安装 Git

如果你的电脑上还没有安装 Git，请从 [Git 官网](https://git-scm.com/downloads) 下载并安装。

## 3. 配置 Git 用户信息

打开命令行终端，运行以下命令配置你的 Git 用户信息：

```bash
git config --global user.name "你的名字"
git config --global user.email "你的邮箱"
```

## 4. 在 GitHub 上创建新仓库

1. 登录 GitHub
2. 点击右上角的 "+" 图标，选择 "New repository"
3. 填写仓库名称，例如 "ltc-project"
4. 添加仓库描述（可选）
5. 选择仓库可见性（公开或私有）
6. 不要勾选 "Initialize this repository with a README"
7. 点击 "Create repository"

## 5. 初始化本地 Git 仓库

在项目根目录下（e:\AI\LTC\v0.53）打开命令行终端，运行以下命令：

```bash
# 初始化 Git 仓库
git init

# 将所有文件添加到暂存区
git add .

# 提交第一次更改
git commit -m "初始化项目"
```

## 6. 连接本地仓库与 GitHub 仓库

```bash
# 添加远程仓库地址（将 YOUR_USERNAME 替换为你的 GitHub 用户名）
git remote add origin https://github.com/YOUR_USERNAME/ltc-project.git

# 推送代码到 GitHub
git push -u origin master
```
## 7. 分支管理策略

建议采用以下分支管理策略：

- `master`：主分支，用于生产环境
- `develop`：开发分支，用于开发环境
- `feature/*`：特性分支，用于开发新功能
- `bugfix/*`：修复分支，用于修复 bug

创建开发分支：

```bash
git checkout -b develop
git push -u origin develop
```

## 8. 配置 GitHub Actions（可选）

你可以在项目根目录下创建 `.github/workflows` 目录，并添加 CI/CD 配置文件，例如：

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js
      uses: actions/setup-node@v1
      with:
        node-version: '14.x'
    - name: Install dependencies
      run: |
        cd backend
        npm ci
        cd ../frontend
        npm ci
    - name: Run tests
      run: |
        cd backend
        npm test
        cd ../frontend
        npm test
```

## 9. 配置 GitHub Pages（可选）

如果你想展示项目的文档或前端页面，可以配置 GitHub Pages：

1. 在 GitHub 仓库页面，点击 "Settings"
2. 滚动到 "GitHub Pages" 部分
3. 在 "Source" 下拉菜单中选择 "master branch" 或 "gh-pages branch"
4. 点击 "Save"

## 10. 日常工作流程

```bash
# 拉取最新代码
git pull

# 创建新特性分支
git checkout -b feature/new-feature

# 开发完成后，提交更改
git add .
git commit -m "添加新特性"

# 推送到 GitHub
git push -u origin feature/new-feature

# 在 GitHub 上创建 Pull Request，将特性分支合并到开发分支
```

## 11. 团队协作最佳实践

- 经常提交代码，保持提交信息清晰明了
- 在开始新工作前先拉取最新代码
- 使用 Pull Request 进行代码审查
- 使用 Issues 跟踪任务和 bug
- 使用 Projects 进行项目管理
- 使用 Wiki 记录项目文档

## 12. 其他有用的 Git 命令

```bash
# 查看状态
git status

# 查看提交历史
git log

# 查看分支
git branch

# 切换分支
git checkout branch-name

# 合并分支
git merge branch-name

# 解决冲突
# 当出现冲突时，编辑冲突文件，然后：
git add .
git commit -m "解决冲突"
```