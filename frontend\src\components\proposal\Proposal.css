/* Proposal Component Styles */
.proposal-container {
  width: 100%;
  padding: 24px;
}

/* 提案版本卡片 */
.proposal-version-card {
  margin-bottom: 24px;
}

.proposal-upload-area {
  margin-bottom: 24px;
}

/* 版本表格样式 */
.version-table {
  margin-top: 16px;
}

/* Actions列对齐设置 - 更强的样式覆盖 */
.version-table .ant-table-thead > tr > th:last-child,
.version-table .ant-table-thead > tr > th[data-column-key="actions"] {
  text-align: left !important;
  padding-left: 16px !important;
}

.version-table .ant-table-tbody > tr > td:last-child,
.version-table .ant-table-tbody > tr > td[data-column-key="actions"] {
  text-align: left !important;
  padding-left: 16px !important;
}

/* 确保Actions列内容左对齐 - 加强版本 */
.version-table .ant-table-cell:last-child,
.version-table .ant-table-cell[data-column-key="actions"] {
  text-align: left !important;
  justify-content: flex-start !important;
}

/* 强制Actions列标题和内容左对齐 */
.version-table th:last-child,
.version-table td:last-child {
  text-align: left !important;
}

/* Actions列的具体对齐 */
.version-table .ant-table-thead th:last-child .ant-table-column-title,
.version-table .ant-table-tbody td:last-child {
  text-align: left !important;
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
}

/* 版本链接样式 */
.version-link {
  color: #1890ff;
  cursor: pointer;
}

.version-link:hover {
  text-decoration: underline;
}

/* 状态标签样式 */
.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-submitted {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-internal {
  background-color: #f5f5f5;
  color: #595959;
}

/* 操作图标样式 */
.action-icons {
  display: flex !important;
  gap: 8px;
  align-items: center;
  justify-content: flex-start !important;
  margin: 0;
  padding: 0;
  width: 100%;
  text-align: left !important;
}

.action-icon {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background-color: #f5f5f5;
  transform: scale(1.1);
}

/* 查看按钮 */
.view-icon:hover {
  color: #1890ff;
  background-color: #e6f7ff;
}

/* 编辑按钮 */
.edit-icon:hover {
  color: #fa8c16;
  background-color: #fff7e6;
}

/* 删除按钮 */
.delete-icon:hover {
  color: #ff4d4f;
  background-color: #fff2f0;
}

/* 提案细分部分 */
.proposal-breakdown {
  margin-top: 48px; /* 增加margin-top让标题与Save & Proceed按钮水平对齐 */
}

/* 标题水平居中布局 */
.section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.section-title {
  font-size: var(--font-size-lg, 18px);
  font-weight: 600;
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
  margin: 0 !important;
  padding: 0 !important;
  text-align: center;
  width: 100%;
}

/* Switch样式 - 右上角定位 */
.breakdown-switch {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制覆盖Ant Design Switch的所有默认样式 */
.breakdown-switch.ant-switch {
  margin: 0 !important;
  padding: 0 !important;
}

/* 提案卡片网格 - 标准卡片布局 */
.proposal-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

@media (max-width: 1200px) {
  .proposal-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .proposal-cards {
    grid-template-columns: 1fr;
  }
}

/* 提案卡片样式 - 符合系统卡片规范 */
.proposal-card {
  background-color: var(--card-background, #ffffff);
  border-radius: var(--radius-md, 8px);
  border: 1px solid var(--border, #e8e8e8);
  height: 100%;
  transition: box-shadow var(--transition-fast, 0.2s);
}

.proposal-card:hover {
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.1));
}

.proposal-card .ant-card-head {
  border-bottom: 1px solid var(--border, #f0f0f0);
  padding: 12px 16px;
}

.proposal-card .ant-card-body {
  padding: 16px;
}

.proposal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.proposal-card-title {
  margin: 0;
  font-size: var(--font-size-md, 14px);
  font-weight: 500;
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
}

/* 提案信息 */
.proposal-info {
  margin-bottom: 16px;
}

.proposal-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.proposal-info-label {
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
  font-size: var(--font-size-sm, 12px);
}

.proposal-info-value {
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
  font-size: var(--font-size-sm, 12px);
}

/* 逾期警告 */
.due-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--danger, #ff4d4f);
  font-size: var(--font-size-xs, 11px);
}

/* 文件上传区域样式 - 符合系统上传组件规范 */
.file-upload-area {
  margin-bottom: 16px;
  border: 1px dashed var(--border, #d9d9d9);
  border-radius: var(--radius-sm, 4px);
  background: var(--background-light, #fafafa);
}

.file-upload-area .ant-upload-text {
  font-size: var(--font-size-xs, 11px);
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
}

.file-upload-area .ant-upload-drag-icon {
  margin-bottom: 8px;
}

/* 子项卡片上传图标设置为25px */
.proposal-cards .file-upload-area .ant-upload-drag-icon svg {
  font-size: 25px !important;
  width: 25px !important;
  height: 25px !important;
}

/* 确保CloudUploadOutlined图标大小为25px */
.proposal-cards .ant-upload-drag-icon .anticon-cloud-upload {
  font-size: 25px !important;
}

.proposal-cards .ant-upload-drag-icon .anticon {
  font-size: 25px !important;
}

/* 已附加文件 */
.attached-files {
  margin-top: 16px;
}

.attached-file {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: var(--background-light, #f5f5f5);
  border-radius: var(--radius-sm, 4px);
  margin-bottom: 8px;
}

.file-icon {
  color: var(--primary, #1890ff);
}

.file-name {
  flex: 1;
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
  font-size: var(--font-size-sm, 12px);
}

.file-size {
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
  font-size: var(--font-size-xs, 11px);
}

.file-remove {
  padding: 0;
  color: var(--text-secondary, rgba(0, 0, 0, 0.45));
}

.file-remove:hover {
  color: var(--danger, #ff4d4f);
}

/* 流转按钮区域 - LTC标准样式 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.btn-workflow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px !important;
  font-weight: 500;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-workflow:hover {
  background-color: #e6e6e6;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.85);
}

.btn-workflow .anticon {
  transition: transform 0.3s ease;
}

.btn-workflow:hover .anticon {
  transform: translateX(3px);
}

/* 版本详情 */
.version-detail {
  padding: 16px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
  gap: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  color: rgba(0, 0, 0, 0.65);
  flex: 1;
}

/* Action buttons styles */
.ant-space-item {
  display: inline-flex;
  align-items: center;
}

.ant-space-item .anticon {
  font-size: 16px;
  transition: all 0.3s;
  padding: 4px;
  border-radius: 4px;
}

.ant-space-item .anticon:hover {
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.04);
}

.ant-space-item .anticon-edit {
  color: #1890ff;
}

.ant-space-item .anticon-edit:hover {
  color: #40a9ff !important;
  background-color: rgba(24, 144, 255, 0.1);
}

.ant-space-item .anticon-delete {
  color: #ff4d4f;
}

.ant-space-item .anticon-delete:hover {
  color: #ff7875 !important;
  background-color: rgba(255, 77, 79, 0.1);
}

.ant-space-item .anticon-download {
  color: #52c41a;
}

.ant-space-item .anticon-download:hover {
  color: #73d13d !important;
  background-color: rgba(82, 196, 26, 0.1);
}

/* Table Actions column */
.version-table .ant-table-tbody > tr > td:last-child {
  text-align: center;
}

.version-table .ant-space {
  justify-content: center;
}

/* Ensure icons are visible */
.version-table .anticon {
  display: inline-block !important;
  visibility: visible !important;
}
