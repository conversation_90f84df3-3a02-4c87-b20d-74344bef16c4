"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateShareLink = exports.verifyInvitationToken = exports.generateInvitationToken = void 0;
const jwt = __importStar(require("jsonwebtoken"));
const SECRET_KEY = process.env.JWT_SECRET || 'your_secret_key';
function generateInvitationToken(collaboratorId) {
    const payload = {
        collaboratorId,
        type: 'invitation',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60)
    };
    return jwt.sign(payload, SECRET_KEY);
}
exports.generateInvitationToken = generateInvitationToken;
function verifyInvitationToken(token) {
    try {
        const decoded = jwt.verify(token, SECRET_KEY);
        if (decoded.type !== 'invitation') {
            throw new Error('Invalid token type');
        }
        return { collaboratorId: decoded.collaboratorId };
    }
    catch (error) {
        throw new Error('Invalid invitation token');
    }
}
exports.verifyInvitationToken = verifyInvitationToken;
function generateShareLink(projectId, permissions = ['view']) {
    const payload = {
        projectId,
        permissions,
        type: 'share_link',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60)
    };
    const token = jwt.sign(payload, SECRET_KEY);
    return `${process.env.FRONTEND_URL || 'http://localhost:3000'}/shared/${token}`;
}
exports.generateShareLink = generateShareLink;
//# sourceMappingURL=tokenUtils.js.map