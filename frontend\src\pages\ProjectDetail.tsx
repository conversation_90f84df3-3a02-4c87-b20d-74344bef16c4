import React, { useEffect, useState, useCallback, lazy, Suspense, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Form, Input, Select, InputNumber, Button, Row, Col, Tag, message, Spin, Skeleton } from 'antd';
import { SaveOutlined, ArrowLeftOutlined, EuroOutlined, PercentageOutlined, EditOutlined, CheckOutlined } from '@ant-design/icons';
import projectService from '../services/new-project.service';
import projectStageService from '../services/project-stage.service';
import emergencyDataManager from '../services/emergencyDataManager';
import { Project, ProjectStage } from '../types';
import moment from 'moment';
import { LTC_STAGES } from '../components/ltc-stages';
import '../styles/ltc-stages.css';
import '../styles/form-styles.css';
import '../styles/info-card.css';
import '../styles/finance-module.css';

// 使用懒加载导入FinanceModule
const FinanceModule = lazy(() => import('../components/finance/FinanceModule'));

const { Option } = Select;
const { TextArea } = Input;

const ProjectDetail: React.FC = () => {
  console.log('🚀 ProjectDetail: Component rendering for ID:', useParams().id);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();

  // 使用useMemo避免在每次渲染时重新计算
  const isNewProject = React.useMemo(() => !id || id === 'create', [id]);

  // 状态定义
  const [project, setProject] = useState<Project | null>(null);
  const [stages, setStages] = useState<ProjectStage[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  // 🔧 修复：根据URL和localStorage判断初始activeModule
  const [activeModule, setActiveModule] = useState<'main' | 'finance'>(() => {
    // 检查URL是否包含finance相关路径
    const currentPath = window.location.pathname;
    const isFinancePage = currentPath.includes('/finance') || 
                         currentPath.includes('finance') ||
                         window.location.hash.includes('finance');
    
    // 检查localStorage中是否有Finance模块的活跃状态
    const savedActiveModule = localStorage.getItem(`activeModule_${id}`);
    
    if (isFinancePage || savedActiveModule === 'finance') {
      return 'finance';
    }
    
    return 'main';
  });

  const fetchProjectData = useCallback(async () => {
    // 检查是否应该获取数据
    const currentPath = window.location.pathname;

    if (!currentPath.startsWith('/opportunity/') || !id || currentPath === '/opportunity') {
      return;
    }

    try {
      console.log(`🔍 ProjectDetail: Starting data fetch for project ${id}`);
      
      // 简化加载状态：只在真正需要网络请求时显示
      const useServerAPI = window.location.search.includes('api=server') || 
                          window.location.search.includes('force-server');

      if (useServerAPI) {
        setLoading(true);
        message.loading({
          content: 'Loading project data from server...',
          key: 'projectLoading',
          duration: 0
        });
      }

      // 🚀 优化：直接获取项目数据，不设置复杂的进度条
      console.log(`📥 ProjectDetail: Fetching project data for ${id}`);
      const projectData = await projectService.getProjectById(id!);

      // 🚀 优化：并行获取阶段数据，不等待项目数据
      const stagesPromise = projectStageService.getStagesByProjectId(id!).catch(error => {
        console.warn(`⚠️ ProjectDetail: Failed to load stages from API:`, error);
        return [];
      });

      // 立即检查项目数据
      if (!projectData) {
        console.warn(`❌ ProjectDetail: No project data found for ${id}`);
        message.error('Project not found');
        navigate('/opportunity');
        return;
      }

      console.log(`✅ ProjectDetail: Project data loaded successfully:`, projectData);

      // 🔧 数据清理：确保关键字段有默认值
      const cleanedProjectData = {
        ...projectData,
        client: projectData.client || projectData.name || 'Unknown Client',
        tier: projectData.tier || 'A',
        level: projectData.level || '-',
        country: projectData.country || '-',
        category: projectData.category || '-',
        stage: projectData.stage || 'Planning',
        probability: projectData.probability || '-',
        owner: projectData.owner || 'Unassigned',
        revenue: projectData.revenue || 0,
        createdAt: projectData.createdAt || new Date().toISOString(),
        updatedAt: projectData.updatedAt || new Date().toISOString()
      };

      // 立即设置项目数据，不等待stages
      setProject(cleanedProjectData);
      setLoading(false);

      // 设置表单数据
      form.setFieldsValue({
        ...cleanedProjectData,
      });

      // 并行处理stages数据
      const stagesData = await stagesPromise;
      
      // 优先使用emergencyDataManager的数据
      let finalStagesData = stagesData || [];
      
      try {
        const localStagesData = emergencyDataManager.getProjectStages(id);
        if (localStagesData.length > 0) {
          finalStagesData = localStagesData;
          console.log(`✅ ProjectDetail: Using local stages data (${localStagesData.length} stages)`);
        }
      } catch (storageError) {
        console.error('❌ ProjectDetail: Failed to load stages from emergencyDataManager:', storageError);
      }
      
      setStages(finalStagesData);

      if (useServerAPI) {
        message.success({
          content: 'Project data loaded successfully',
          key: 'projectLoading',
          duration: 1.5
        });
      } else {
        message.destroy('projectLoading');
      }

      console.log(`🎉 ProjectDetail: Data loading completed for project ${id}`);

    } catch (error) {
      console.error(`❌ ProjectDetail: Error in fetchProjectData for ${id}:`, error);
      
      setLoading(false);
      
      // 检查是否是服务器API请求
      const isServerRequest = window.location.search.includes('api=server') || 
                             window.location.search.includes('force-server');
      
      if (isServerRequest) {
        message.error({
          content: 'Failed to load project data, please retry',
          key: 'projectLoading',
          duration: 3
        });
      } else {
        // 尝试从localStorage恢复基本数据
        try {
          const storedProjects = localStorage.getItem('edm_projects');
          if (storedProjects) {
            const projects = JSON.parse(storedProjects);
            const project = projects.find((p: any) => p.id === id || p.projectId === id);
            if (project) {
              console.log(`🔄 ProjectDetail: Recovered project from localStorage:`, project);
              
              // 🔧 数据清理：确保恢复的数据也有默认值
              const cleanedProject = {
                ...project,
                client: project.client || project.name || 'Unknown Client',
                tier: project.tier || 'A',
                level: project.level || '-',
                country: project.country || '-',
                category: project.category || '-',
                stage: project.stage || 'Planning',
                probability: project.probability || '-',
                owner: project.owner || 'Unassigned',
                revenue: project.revenue || 0,
                createdAt: project.createdAt || new Date().toISOString(),
                updatedAt: project.updatedAt || new Date().toISOString()
              };
              
              setProject(cleanedProject);
              form.setFieldsValue({ ...cleanedProject });
              message.info('Project data loaded from local storage');
              return;
            }
          }
        } catch (localError) {
          console.error('❌ ProjectDetail: Failed to recover from localStorage:', localError);
        }
        
        message.error('Failed to load project data');
      }
    }
  }, [id, navigate, form]); // 移除isNetworkRequest依赖，避免无限循环

  // 使用useRef控制数据获取，避免重复调用
  const hasInitialized = useRef(false);
  
  useEffect(() => {
    // 防止重复初始化
    if (hasInitialized.current) {
      return;
    }

    if (!isNewProject && id) {
      // 只在有有效ID且路径确实是opportunity路径时才获取数据
      const currentPath = window.location.pathname;
      if (currentPath.startsWith('/opportunity/') && !currentPath.includes('/client/')) {
        console.log(`🚀 ProjectDetail: Initializing data fetch for project ${id}`);
        fetchProjectData();
        hasInitialized.current = true;
      }
    }
  }, [id, isNewProject]); // 移除fetchProjectData依赖，避免无限循环

  // 终极只补全一次修复
  const hasPatchedStages = useRef(false);
  const stagesInitialized = useRef(false);

  useEffect(() => {
    const handleStagesCompletion = async () => {
      // 彻底阻断条件：已补全过 OR 无ID OR 已初始化
      if (hasPatchedStages.current || !id || stagesInitialized.current) {
        return;
      }

      // 🔧 关键修复：使用emergencyDataManager加载现有数据，避免覆盖用户数据
      let existingStages: any[] = [];
      try {
        const localStagesData = emergencyDataManager.getProjectStages(id);
        if (Array.isArray(localStagesData) && localStagesData.length > 0) {
          existingStages = localStagesData;
          console.log(`🔄 ProjectDetail: Found existing stages in emergencyDataManager`, {
            count: localStagesData.length,
            types: localStagesData.map(s => s.type)
          });
        }
      } catch (e) {
        console.warn(`⚠️ ProjectDetail: Failed to load existing stages from emergencyDataManager`, e);
      }

      // 基于现有数据检查缺失的stages
      const requiredTypes = LTC_STAGES.map(s => s.key);
      const missingTypes = requiredTypes.filter(type => !existingStages.some(s => s.type === type));
      
      if (missingTypes.length > 0) {
        const now = Date.now();
        const newStages = [
          ...existingStages, // 🔧 修复：保留现有数据
          ...missingTypes.map((type, idx) => ({
            id: `local_${type}_${now + idx}`,
            projectId: id ?? '',
            name: LTC_STAGES.find(s => s.key === type)?.title || type,
            type: type as ProjectStage['type'],
            status: 'not_started' as const,
            data: '{}', // 只有新创建的stages才使用空对象
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }))
        ];
        
        setStages(newStages as ProjectStage[]);
        
        // 🔧 使用emergencyDataManager原子性保存
        const success = await emergencyDataManager.saveProjectStages(id, newStages);
        if (success) {
          console.log(`✅ ProjectDetail: Updated emergencyDataManager with merged stages`, {
            total: newStages.length,
            existing: existingStages.length,
            new: missingTypes.length,
            missingTypes
          });
        } else {
          console.error(`❌ ProjectDetail: Failed to save merged stages to emergencyDataManager`);
        }
      } else {
        // 如果没有缺失的stages，但localStorage中有数据，则使用localStorage数据
        if (existingStages.length > 0) {
          setStages(existingStages as ProjectStage[]);
          console.log(`✅ ProjectDetail: Restored stages from emergencyDataManager`, {
            count: existingStages.length
          });
        }
      }
      
      // 无论是否补全，都标记为已处理
      hasPatchedStages.current = true;
      stagesInitialized.current = true;
    };

    handleStagesCompletion();
  }, [id]); // 移除stages依赖，避免无限循环

  // 移除可能导致无限循环的stages监听
  // useEffect(() => {
  //   if (stages.length > 0 && !stagesInitialized.current) {
  //     stagesInitialized.current = true;
  //   }
  // }, [stages.length]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      // 设置默认值
      const formData = {
        ...values,
        // 确保ID存在，并使用正确的格式
        projectId: values.projectId || "Oppty-2025-001",
        // 设置其他字段的默认值
        tier: values.tier || '-',
        category: values.category || '-',
        revenue: values.revenue || 0,
        stage: values.stage || '-',
        probability: values.probability || '-',
        owner: values.owner || '-',
        country: values.country || '-'
      };

      if (isNewProject) {
        // 创建新机会
        console.log('🚨🚨🚨 PROJECT-DETAIL PAGE: Creating new project!!! 🚨🚨🚨');
        console.log('🚨 ProjectDetail formData:', formData);
        const newProject = await projectService.createProject(formData);
        console.log('🚨 ProjectDetail new project created:', newProject);
        message.success('Opportunity created successfully');
        navigate(`/opportunity/${newProject.id}`);
      } else {
        // 更新现有机会
        await projectService.updateProject(id!, formData);
        message.success('Opportunity updated successfully');
        fetchProjectData();
      }
    } catch (error) {

      message.error('Failed to save opportunity');
    } finally {
      setSaving(false);
    }
  };

  // 生成项目ID
  const generateProjectId = () => {
    const year = new Date().getFullYear();
    const timestamp = String(Date.now()).slice(-3);
    return `OPP-${year}-${timestamp}`;
  };

  // 重新生成ID的处理函数
  const handleRegenerateId = () => {
    const newId = generateProjectId();
    form.setFieldsValue({
      projectId: newId
    });
    message.success('New Opportunity ID generated successfully!');
  };

  // 基本信息表单 - 定义为普通函数组件
  function BasicInfoFormComponent() {
    // 在组件挂载时生成ID
    React.useEffect(() => {
      if (isNewProject) {
        form.setFieldsValue({
          projectId: generateProjectId()
        });
      }
    }, []);

    return (
      <Form layout="vertical" form={form} className="enhanced-form-container">
        {/* 基本信息分组 */}
        <div className="form-section-card">
          <div className="form-section-header">
            <h3 className="form-section-title">Basic Information</h3>
          </div>
          <div className="form-section-content">
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="ID"
                  name="projectId"
                  rules={[
                    { required: true, message: 'Please enter opportunity ID' },
                    { pattern: /^OPP-\d{4}-\d{3}$/, message: 'Opportunity ID format should be OPP-YYYY-XXX' }
                  ]}
                  tooltip="Unique identifier for the opportunity. Format: OPP-YEAR-XXX"
                >
                  <div className="enhanced-input">
                    <Input 
                      placeholder="OPP-2025-001"
                      addonAfter={
                        isNewProject ? (
                          <Button 
                            type="text" 
                            size="small"
                            onClick={handleRegenerateId}
                            title="Generate new ID"
                            style={{ padding: '0 8px' }}
                          >
                            🔄
                          </Button>
                        ) : null
                      }
                    />
                  </div>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={<span><span style={{ color: '#ff4d4f' }}>*</span> Name</span>}
                  name="name"
                  rules={[{ required: true, message: 'Please enter project name' }]}
                  tooltip="Complete name of the project"
                  required={false}
                >
                  <div className="enhanced-input">
                    <Input placeholder="Enter project name" />
                  </div>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={<span><span style={{ color: '#ff4d4f' }}>*</span> Client</span>}
                  name="client"
                  rules={[{ required: true, message: 'Please enter client name' }]}
                  tooltip="Client company name"
                  required={false}
                >
                  <div className="enhanced-input">
                    <Input placeholder="Enter client name" />
                  </div>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Tier"
                  name="tier"
                  tooltip="Client tier classification"
                >
                  <div className="enhanced-input">
                    <Select placeholder="Select client tier">
                      <Option value="T1">T1</Option>
                      <Option value="T2">T2</Option>
                      <Option value="T3">T3</Option>
                    </Select>
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>

        {/* 商业信息分组 */}
        <div className="form-section-card">
          <div className="form-section-header">
            <h3 className="form-section-title">Business Information</h3>
          </div>
          <div className="form-section-content">
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label={<span><span style={{ color: '#ff4d4f' }}>*</span> Country</span>}
                  name="country"
                  rules={[{ required: true, message: 'Please enter country/region' }]}
                  tooltip="Client's country or region"
                  required={false}
                >
                  <div className="enhanced-input">
                    <Input placeholder="e.g. Germany" />
                  </div>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Category"
                  name="category"
                  tooltip="Project category"
                >
                  <div className="enhanced-input">
                    <Select placeholder="Select project category">
                      <Option value="service">Service</Option>
                      <Option value="product">Product</Option>
                      <Option value="solution">Solution</Option>
                    </Select>
                  </div>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Revenue"
                  name="revenue"
                  tooltip="Expected project revenue"
                >
                  <div className="enhanced-input">
                    <InputNumber
                      style={{ width: '100%' }}
                      formatter={(value) => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => {
                        const parsed = value ? Number(value.replace(/€\s?|(,*)/g, '')) : 0;
                        return parsed;
                      }}
                      placeholder="Enter expected revenue"
                      prefix={<EuroOutlined />}
                    />
                  </div>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Stage"
                  name="stage"
                  tooltip="Current project stage"
                >
                  <div className="enhanced-input">
                    <Select placeholder="Select project stage">
                      <Option value="lead">Lead</Option>
                      <Option value="opportunity">Opportunity</Option>
                      <Option value="proposal">Proposal</Option>
                      <Option value="negotiation">Negotiation</Option>
                      <Option value="closed">Closed</Option>
                    </Select>
                  </div>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Probability"
                  name="probability"
                  tooltip="Project success probability"
                >
                  <div className="enhanced-input">
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={100}
                      formatter={(value) => `${value}%`}
                      parser={(value) => {
                        const parsed = value ? Number(value.replace('%', '')) : 0;
                        return parsed as 0 | 100;
                      }}
                      placeholder="Enter success probability"
                      suffix={<PercentageOutlined />}
                    />
                  </div>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12}>
                <Form.Item
                  label="Owner"
                  name="owner"
                  tooltip="Project owner"
                >
                  <div className="enhanced-input">
                    <Input placeholder="Enter owner name" />
                  </div>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>

        {/* 其他信息分组 */}
        <div className="form-section-card">
          <div className="form-section-header">
            <h3 className="form-section-title">Other Information</h3>
          </div>
          <div className="form-section-content">
            <Form.Item
              label="Description"
              name="description"
              tooltip="Detailed description of the project"
            >
              <div className="enhanced-input">
                <TextArea rows={4} placeholder="Enter project description (optional)" />
              </div>
            </Form.Item>
          </div>
        </div>

        {/* 表单操作按钮 */}
        <div className="form-actions">
          <Button onClick={() => navigate('/opportunity')}>Cancel</Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={saving}>
            {isNewProject ? 'Save Basic Information' : 'Update Basic Information'}
          </Button>
        </div>
      </Form>
    );
  }

  // 基本信息表单组件
  const BasicInfoForm = BasicInfoFormComponent;

  
  // 处理阶段数据保存 - 简化版避免死循环
  const handleStageDataSave = useCallback(async (stageType: string, data: any) => {
    try {
      setSaving(true);

      // 简化的本地存储逻辑
      const stageStorageKey = `project_stages_${id}`;
      const existingStages = JSON.parse(localStorage.getItem(stageStorageKey) || '[]');
      
      // 查找或创建阶段
      let updatedStages = [...existingStages];
      const stageIndex = updatedStages.findIndex(stage => stage.type === stageType);
      
      // 🔧 关键修复：不要对data字段进行JSON.stringify，直接保存对象
      const stageData = {
        id: stageIndex >= 0 ? updatedStages[stageIndex].id : `local_${stageType}_${Date.now()}`,
        projectId: id!,
        name: stageType,
        type: stageType,
        status: 'completed',
        data: data, // 直接保存对象，不序列化
        createdAt: stageIndex >= 0 ? updatedStages[stageIndex].createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 🔍 调试：验证数据结构
      console.log(`🔧 ProjectDetail: Saving stage data for ${stageType}`, {
        dataType: typeof data,
        dataKeys: Object.keys(data || {}),
        dataContent: data,
        stageDataType: typeof stageData.data,
        willStringifyCorrectly: true
      });
      
      if (stageIndex >= 0) {
        updatedStages[stageIndex] = stageData;
      } else {
        updatedStages.push(stageData);
      }
      
      // 保存到localStorage
      localStorage.setItem(stageStorageKey, JSON.stringify(updatedStages));
      
      // 只在必要时更新stages状态
      setStages(updatedStages);
      
      // 🔧 修复：移除重复的成功消息，由子组件处理消息显示
    } catch (error) {
      message.error('Failed to save information');
    } finally {
      setSaving(false);
    }
  }, [id]);


  // 简化版组件创建，避免复杂缓存
  const createLtcStageComponent = useCallback((stageKey: string) => {
    const stageConfig = LTC_STAGES.find(s => s.key === stageKey);
    if (!stageConfig) {
      return <div>Stage configuration not found</div>;
    }

    const StageComponent = stageConfig.component;
    // 移除实时stages查找，避免无限循环
    let stageData = null;
    
    // 尝试从emergencyDataManager获取stage数据
    try {
      const localStagesData = emergencyDataManager.getProjectStages(id || '');
      stageData = localStagesData.find(s => s.type === stageKey);
    } catch (e) {
      // 忽略错误
    }
    
    // 🔧 修复：如果找不到stageData，创建一个默认的stage数据
    if (!stageData) {
      stageData = {
        id: `temp_${stageKey}_${Date.now()}`,
        projectId: id || '',
        name: stageConfig.title,
        type: stageKey as ProjectStage['type'],
        status: 'not_started' as const,
        data: '{}',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // 移除可能导致无限循环的日志
      console.log(`📝 createLtcStageComponent: Using temporary stage data for ${stageKey}`);
    }

    return (
      <StageComponent
        project={project || undefined}
        stage={stageData}
        onSave={(data: any) => handleStageDataSave(stageKey, data)}
        onProceed={(nextStage: string) => {
          const stageNames = ['basic_info', 'proposal', 'contract', 'execution', 'acceptance', 'closeout', 'after_sales'];
          const nextIndex = stageNames.indexOf(nextStage);
          if (nextIndex !== -1) {
            setCurrentStep(nextIndex);
          }
        }}
        id={id}
      />
    );
  }, [project, id, handleStageDataSave]); // 移除stages依赖，避免无限重新渲染


  // 使用useMemo优化steps定义，避免无限重新渲染
  const steps = React.useMemo(() => [
    {
      title: 'Basic Info',
      content: isNewProject ? <BasicInfoForm /> : createLtcStageComponent('basic_info'),
    },
    {
      title: 'Proposal',
      content: createLtcStageComponent('proposal'),
    },
    {
      title: 'Contract',
      content: createLtcStageComponent('contract'),
    },
    {
      title: 'Execution',
      content: createLtcStageComponent('execution'),
    },
    {
      title: 'Acceptance',
      content: createLtcStageComponent('acceptance'),
    },
    {
      title: 'Closeout',
      content: createLtcStageComponent('closeout'),
    },
    {
      title: 'After Sales',
      content: createLtcStageComponent('after_sales'),
    },
  ], [isNewProject, createLtcStageComponent]);

  // 智能加载状态：只在网络请求时显示加载界面
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" tip="Loading project data from server..." />
      </div>
    );
  }

  return (
    <div>
      <Button
        icon={<ArrowLeftOutlined />}
        style={{ marginBottom: 16 }}
        onClick={() => navigate('/opportunity')}
      >
        Back to Opportunity List
      </Button>

      {(project || id) && (
        <Card
          style={{ marginBottom: 16 }}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h3 className="process-title">Opportunity Information</h3>
              {id && (
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => navigate(`/opportunity/edit/${id}`)}
                  size="small"
                >
                  Edit
                </Button>
              )}
            </div>
          }
          className="info-card compact-info-card"
          styles={{ body: { padding: '10px 8px' } }}
        >
          <div className="compact-info-container">
            <div className="compact-info-row">
              {/* First Column: Oppty Name, ID, Client Name, Tier */}
              <div className="compact-info-section">
                <div className="compact-info-grid">
                  <div className="compact-info-item">
                    <span className="info-label-small">Oppty Name:</span>
                    <span className="info-value-small highlight">{project?.name || (id ? `Project ${id}` : 'Loading...')}</span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">ID:</span>
                    <span className="info-value-small">{project?.projectId || id || 'N/A'}</span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Client Name:</span>
                    <span className="info-value-small highlight">{project?.client || 'Loading...'}</span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Tier:</span>
                    <span className="info-value-small">
                      {project?.tier ?
                        <Tag color="blue" style={{ margin: 0, fontSize: '12px', padding: '0 4px', lineHeight: '18px' }}>{project.tier}</Tag> :
                        <span className="empty-value">Not Set</span>
                      }
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Level:</span>
                    <span className="info-value-small">
                      {project?.level && project.level !== '-' ?
                        <Tag color="orange" style={{ margin: 0, fontSize: '12px', padding: '0 4px', lineHeight: '18px' }}>{project.level}</Tag> :
                        <span className="empty-value">Not Set</span>
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Second Column: Country, Category, Revenue, Stage, Probability */}
              <div className="compact-info-section">
                <div className="compact-info-grid">
                  <div className="compact-info-item">
                    <span className="info-label-small">Country:</span>
                    <span className="info-value-small">
                      {project?.country && project.country !== '-' ? project.country : <span className="empty-value">Not Set</span>}
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Category:</span>
                    <span className="info-value-small">
                      {project?.category && project.category !== '-' ?
                        <Tag color="green" style={{ margin: 0, fontSize: '12px', padding: '0 4px', lineHeight: '18px' }}>{project.category}</Tag> :
                        <span className="empty-value">Not Set</span>
                      }
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Revenue:</span>
                    <span className="info-value-small">
                      {project?.revenue ?
                        <span style={{ color: '#52c41a' }}>€{project.revenue.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span> :
                        <span className="empty-value">€0.00</span>
                      }
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Stage:</span>
                    <span className="info-value-small">
                      {project?.stage && project.stage !== '-' ?
                        <Tag color="purple" style={{ margin: 0, fontSize: '12px', padding: '0 4px', lineHeight: '18px' }}>{project.stage}</Tag> :
                        <span className="empty-value">Not Set</span>
                      }
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Probability:</span>
                    <span className="info-value-small">
                      {project?.probability && project.probability !== '-' ?
                        <Tag color={
                          project.probability === 'A' ? 'green' :
                          project.probability === 'B' ? 'blue' :
                          project.probability === 'C' ? 'orange' :
                          'red'
                        } style={{ margin: 0, fontSize: '12px', padding: '0 4px', lineHeight: '18px' }}>
                          {project.probability}
                        </Tag> :
                        <span className="empty-value">Not Set</span>
                      }
                    </span>
                  </div>
                </div>
              </div>

              {/* Third Column: Create Date, Creator, Oppty Owner */}
              <div className="compact-info-section">
                <div className="compact-info-grid">
                  <div className="compact-info-item">
                    <span className="info-label-small">Create Date:</span>
                    <span className="info-value-small">
                      {project?.createdAt ? moment(project.createdAt).format('YYYY-MM-DD') : 'N/A'}
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Creator:</span>
                    <span className="info-value-small">
                      <span className="empty-value">Not Set</span>
                    </span>
                  </div>
                  <div className="compact-info-item">
                    <span className="info-label-small">Oppty Owner:</span>
                    <span className="info-value-small">
                      {project?.owner || <span className="empty-value">Not Set</span>}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {!isNewProject && (
        <Card
          style={{ marginBottom: 16 }}
          title={<h3 className="process-title">LTC Process</h3>}
        >
          
          {/* 集成的LTC流程 - 主流程和Finance支线 */}
          <div className="process-section">

            {/* 主流程步骤指示器 */}
            <div className="ltc-steps main-process">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`ltc-step ${currentStep === index && activeModule === 'main' ? 'active' : ''} ${index < currentStep ? 'completed' : ''}`}
                  onClick={() => {
                    setCurrentStep(index);
                    setActiveModule('main');
                    // 🔧 保存activeModule状态到localStorage
                    localStorage.setItem(`activeModule_${id}`, 'main');
                  }}
                >
                  <div className="ltc-step-circle">
                    {index < currentStep ? (
                      <CheckOutlined style={{ fontSize: '12px' }} />
                    ) : index === currentStep && activeModule === 'main' ? (
                      index + 1
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="ltc-step-label">{step.title}</div>
                  {index < steps.length - 1 && (
                    <div className="ltc-step-connector"></div>
                  )}
                </div>
              ))}
            </div>

            {/* Finance支线流程 */}
            <div className="finance-process-container">
              <div className="finance-process-connector"></div>
              <div className="finance-process-item">
                <div
                  className={`finance-process-circle ${activeModule === 'finance' ? 'active' : ''}`}
                  onClick={() => {
                    const newActiveModule = activeModule === 'finance' ? 'main' : 'finance';
                    setActiveModule(newActiveModule);
                    // 🔧 保存activeModule状态到localStorage
                    localStorage.setItem(`activeModule_${id}`, newActiveModule);
                  }}
                >
                  <span style={{
                    fontSize: '15px',
                    fontWeight: '600',
                    color: activeModule === 'finance' ? '#FFFFFF' : '#722ed1'
                  }}>€</span>
                </div>
                <div className={`finance-process-label ${activeModule === 'finance' ? 'active' : ''}`}>Finance</div>
              </div>
            </div>

            {/* 内容区域 - 根据activeModule显示不同内容 */}
            <div style={{ marginTop: 40 }}>
              {/* 主流程内容 */}
              {activeModule === 'main' && steps[currentStep].content}

              {/* Finance模块内容 - 使用Suspense懒加载 */}
              {activeModule === 'finance' && (
                <div id="finance-module" className="finance-content" style={{ animation: 'fadeIn 0.3s ease-in-out' }}>
                  <Suspense fallback={
                    <div className="loading-container">
                      <Skeleton active paragraph={{ rows: 10 }} />
                      <div style={{ textAlign: 'center', marginTop: '20px' }}>
                        <Spin size="large" tip="加载财务模块..." />
                      </div>
                    </div>
                  }>
                    <FinanceModule
                      project={project || undefined}
                      stages={stages}
                      onSave={(data) => handleStageDataSave('finance', data)}
                    />
                  </Suspense>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {isNewProject && (
        <Card>
          {steps[0].content}
        </Card>
      )}
    </div>
  );
};

export default ProjectDetail;
