import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ProjectFilters from '../ProjectFilters';
import { ProjectFilters as ProjectFiltersType } from '../../types';

const mockFilters: ProjectFiltersType = {
  searchText: '',
  status: 'all',
  country: 'all',
  tier: 'all',
  category: 'all',
  revenue: 'all',
  probability: 'all',
  level: 'all',
  createDate: 'all'
};

const mockOnFilterChange = jest.fn();
const mockOnReset = jest.fn();

describe('ProjectFilters', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染所有过滤器', () => {
    render(
      <ProjectFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    expect(screen.getByPlaceholderText('搜索项目...')).toBeInTheDocument();
    expect(screen.getByText('所有状态')).toBeInTheDocument();
    expect(screen.getByText('所有国家')).toBeInTheDocument();
    expect(screen.getByText('所有级别')).toBeInTheDocument();
    expect(screen.getByText('所有类别')).toBeInTheDocument();
    expect(screen.getByText('所有收入')).toBeInTheDocument();
    expect(screen.getByText('所有概率')).toBeInTheDocument();
    expect(screen.getByText('所有等级')).toBeInTheDocument();
    expect(screen.getByText('所有时间')).toBeInTheDocument();
  });

  it('搜索框输入应该触发 onFilterChange', () => {
    render(
      <ProjectFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    const searchInput = screen.getByPlaceholderText('搜索项目...');
    fireEvent.change(searchInput, { target: { value: '测试' } });

    expect(mockOnFilterChange).toHaveBeenCalledWith({ searchText: '测试' });
  });

  it('状态选择应该触发 onFilterChange', () => {
    render(
      <ProjectFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    const statusSelect = screen.getByText('所有状态').closest('.ant-select-selector');
    if (statusSelect) {
      fireEvent.mouseDown(statusSelect);
      const option = screen.getByText('进行中');
      fireEvent.click(option);
    }

    expect(mockOnFilterChange).toHaveBeenCalledWith({ status: 'in_progress' });
  });

  it('重置按钮应该触发 onReset', () => {
    render(
      <ProjectFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    const resetButton = screen.getByText('重置');
    fireEvent.click(resetButton);

    expect(mockOnReset).toHaveBeenCalled();
  });

  it('应该正确显示所有选项', () => {
    render(
      <ProjectFilters
        filters={mockFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    // 检查状态选项
    const statusSelect = screen.getByText('所有状态').closest('.ant-select-selector');
    if (statusSelect) {
      fireEvent.mouseDown(statusSelect);
      expect(screen.getByText('规划中')).toBeInTheDocument();
      expect(screen.getByText('进行中')).toBeInTheDocument();
      expect(screen.getByText('已完成')).toBeInTheDocument();
      expect(screen.getByText('已暂停')).toBeInTheDocument();
    }

    // 检查国家选项
    const countrySelect = screen.getByText('所有国家').closest('.ant-select-selector');
    if (countrySelect) {
      fireEvent.mouseDown(countrySelect);
      expect(screen.getByText('中国')).toBeInTheDocument();
      expect(screen.getByText('美国')).toBeInTheDocument();
      expect(screen.getByText('英国')).toBeInTheDocument();
      expect(screen.getByText('德国')).toBeInTheDocument();
    }
  });

  it('应该正确处理过滤器的初始值', () => {
    const initialFilters = {
      ...mockFilters,
      searchText: '测试',
      status: 'in_progress',
      country: 'CN'
    };

    render(
      <ProjectFilters
        filters={initialFilters}
        onFilterChange={mockOnFilterChange}
        onReset={mockOnReset}
      />
    );

    const searchInput = screen.getByPlaceholderText('搜索项目...') as HTMLInputElement;
    expect(searchInput.value).toBe('测试');
    expect(screen.getByText('进行中')).toBeInTheDocument();
    expect(screen.getByText('中国')).toBeInTheDocument();
  });
}); 