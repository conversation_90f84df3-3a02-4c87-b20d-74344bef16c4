import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Avatar,
  Button,
  Typography,
  Space,
  Empty,
  Spin,
  Tag,
  message,
  Select,
  Input,
  Checkbox,
  Tooltip,
  Modal,
  Divider
} from 'antd';
import {
  UserAddOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  SettingOutlined,
  SearchOutlined,
  StarOutlined,
  StarFilled,
  DeleteOutlined,
  CheckOutlined,
  EyeOutlined,
  ReloadOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Notification, NotificationStats } from '../types';
import notificationService from '../services/notification.service';
import '../styles/message-center.css';

const { Title, Text } = Typography;
const { Search } = Input;

const MessageCenter: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({ unreadCount: 0, totalCount: 0 });
  const [loading, setLoading] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'starred' | 'read'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'priority'>('newest');
  const navigate = useNavigate();

  const loadNotifications = async () => {
    setLoading(true);
    try {
      const data = await notificationService.getNotifications();
      setNotifications(data.notifications);
      setStats(data.stats);
    } catch (error) {
      message.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadNotifications();
  }, []);

  useEffect(() => {
    let filtered = [...notifications];

    // Search filter
    if (searchText) {
      filtered = filtered.filter(n => 
        n.title.toLowerCase().includes(searchText.toLowerCase()) ||
        n.message.toLowerCase().includes(searchText.toLowerCase()) ||
        n.projectName?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // Type filter
    switch (filterType) {
      case 'unread':
        filtered = filtered.filter(n => !n.isRead);
        break;
      case 'starred':
        filtered = filtered.filter(n => n.isStarred);
        break;
      case 'read':
        filtered = filtered.filter(n => n.isRead);
        break;
    }

    // Sort
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        filtered.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
        break;
    }

    setFilteredNotifications(filtered);
  }, [notifications, searchText, filterType, sortBy]);

  const getNotificationIcon = (type: Notification['type']) => {
    const iconStyle = { fontSize: 16 };
    const iconMap = {
      team: <UserAddOutlined style={{ ...iconStyle, color: '#1890ff' }} />,
      success: <CheckCircleOutlined style={{ ...iconStyle, color: '#52c41a' }} />,
      warning: <ExclamationCircleOutlined style={{ ...iconStyle, color: '#faad14' }} />,
      error: <ExclamationCircleOutlined style={{ ...iconStyle, color: '#ff4d4f' }} />,
      mention: <MessageOutlined style={{ ...iconStyle, color: '#722ed1' }} />,
      system: <SettingOutlined style={{ ...iconStyle, color: '#8c8c8c' }} />,
      info: <InfoCircleOutlined style={{ ...iconStyle, color: '#1890ff' }} />
    };
    return iconMap[type] || iconMap.info;
  };

  const getTypeTag = (type: Notification['type']) => {
    const colorMap = {
      team: 'blue',
      success: 'green',
      warning: 'orange',
      error: 'red',
      mention: 'purple',
      system: 'default',
      info: 'blue'
    };
    const labelMap = {
      team: 'Team',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      mention: 'Mention',
      system: 'System',
      info: 'Info'
    };
    return <Tag color={colorMap[type]} style={{ fontSize: '11px' }}>{labelMap[type]}</Tag>;
  };

  const getPriorityTag = (priority: Notification['priority']) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'blue'
    };
    const labelMap = {
      high: 'High',
      medium: 'Medium',
      low: 'Low'
    };
    return <Tag color={colorMap[priority]} style={{ fontSize: '11px' }}>{labelMap[priority]}</Tag>;
  };

  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    } else {
      setSelectedNotifications([]);
    }
  };

  const handleSelectNotification = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedNotifications(prev => [...prev, id]);
    } else {
      setSelectedNotifications(prev => prev.filter(nId => nId !== id));
    }
  };

  const handleBatchMarkAsRead = async () => {
    try {
      await Promise.all(selectedNotifications.map(id => notificationService.markAsRead(id)));
      setNotifications(prev => 
        prev.map(n => selectedNotifications.includes(n.id) ? { ...n, isRead: true } : n)
      );
      setStats(prev => ({ 
        ...prev, 
        unreadCount: prev.unreadCount - selectedNotifications.filter(id => 
          notifications.find(n => n.id === id && !n.isRead)
        ).length 
      }));
      setSelectedNotifications([]);
      message.success('Batch mark as read successful');
    } catch (error) {
      message.error('Batch operation failed');
    }
  };

  const handleBatchDelete = async () => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete ${selectedNotifications.length} selected notifications?`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await Promise.all(selectedNotifications.map(id => notificationService.deleteNotification(id)));
          setNotifications(prev => prev.filter(n => !selectedNotifications.includes(n.id)));
          setStats(prev => ({
            ...prev,
            totalCount: prev.totalCount - selectedNotifications.length,
            unreadCount: prev.unreadCount - selectedNotifications.filter(id => 
              notifications.find(n => n.id === id && !n.isRead)
            ).length
          }));
          setSelectedNotifications([]);
          message.success('Batch delete successful');
        } catch (error) {
          message.error('Batch delete failed');
        }
      }
    });
  };

  const handleMarkAsRead = async (notification: Notification) => {
    if (notification.isRead) return;
    
    try {
      await notificationService.markAsRead(notification.id);
      setNotifications(prev => 
        prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
      );
      setStats(prev => ({ ...prev, unreadCount: prev.unreadCount - 1 }));
    } catch (error) {
      message.error('Failed to mark as read');
    }
  };

  const handleToggleStar = async (notification: Notification) => {
    try {
      await notificationService.toggleStar(notification.id);
      setNotifications(prev => 
        prev.map(n => n.id === notification.id ? { ...n, isStarred: !n.isStarred } : n)
      );
      message.success(notification.isStarred ? 'Unstarred' : 'Starred');
    } catch (error) {
      message.error('Operation failed');
    }
  };

  const handleDelete = async (notification: Notification) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: 'Are you sure you want to delete this notification?',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await notificationService.deleteNotification(notification.id);
          setNotifications(prev => prev.filter(n => n.id !== notification.id));
          setStats(prev => ({
            ...prev,
            totalCount: prev.totalCount - 1,
            unreadCount: !notification.isRead ? prev.unreadCount - 1 : prev.unreadCount
          }));
          message.success('Delete successful');
        } catch (error) {
          message.error('Delete failed');
        }
      }
    });
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      handleMarkAsRead(notification);
    }
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  const clearAllFilters = () => {
    setSearchText('');
    setFilterType('all');
    setSortBy('newest');
    setSelectedNotifications([]);
  };

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Page Title */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, fontSize: '24px', fontWeight: 600 }}>
            Message Center
          </Title>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            Manage all system notifications and messages
          </Text>
        </div>

        {/* Statistics Cards */}
        <div style={{ 
          display: 'flex', 
          gap: '12px', 
          marginBottom: '32px',
          flexWrap: 'wrap'
        }}>
          <div style={{ 
            flex: '1 1 calc(25% - 9px)',
            minWidth: '200px'
          }}>
            <Card 
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB',
                height: '120px',
                width: '100%'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <MessageOutlined style={{ fontSize: '20px', color: '#3B82F6' }} />
                <Text type="secondary" style={{ marginLeft: '8px', fontSize: '14px' }}>
                  Total Messages
                </Text>
              </div>
              <span className="message-center-stat-number" style={{ color: '#3B82F6' }}>{stats.totalCount}</span>
            </Card>
          </div>

          <div style={{ 
            flex: '1 1 calc(25% - 9px)',
            minWidth: '200px'
          }}>
            <Card 
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB',
                height: '120px',
                width: '100%'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <ExclamationCircleOutlined style={{ fontSize: '20px', color: '#EF4444' }} />
                <Text type="secondary" style={{ marginLeft: '8px', fontSize: '14px' }}>
                  Unread Messages
                </Text>
              </div>
              <span className="message-center-stat-number" style={{ 
                color: stats.unreadCount > 0 ? '#EF4444' : '#6B7280'
              }}>
                {stats.unreadCount}
              </span>
            </Card>
          </div>

          <div style={{ 
            flex: '1 1 calc(25% - 9px)',
            minWidth: '200px'
          }}>
            <Card 
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB',
                height: '120px',
                width: '100%'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <StarFilled style={{ fontSize: '20px', color: '#F59E0B' }} />
                <Text type="secondary" style={{ marginLeft: '8px', fontSize: '14px' }}>
                  Starred
                </Text>
              </div>
              <span className="message-center-stat-number" style={{ 
                color: '#F59E0B'
              }}>
                {notifications.filter(n => n.isStarred).length}
              </span>
            </Card>
          </div>

          <div style={{ 
            flex: '1 1 calc(25% - 9px)',
            minWidth: '200px'
          }}>
            <Card 
              style={{ 
                borderRadius: '8px',
                boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
                border: '1px solid #E5E7EB',
                height: '120px',
                width: '100%'
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                <ExclamationCircleOutlined style={{ fontSize: '20px', color: '#EF4444' }} />
                <Text type="secondary" style={{ marginLeft: '8px', fontSize: '14px' }}>
                  High Priority
                </Text>
              </div>
              <span className="message-center-stat-number" style={{ 
                color: '#EF4444'
              }}>
                {notifications.filter(n => n.priority === 'high').length}
              </span>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <Card>
          {/* Toolbar */}
          <div style={{ 
            marginBottom: '16px', 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: '12px',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <Space wrap>
              <Search
                placeholder="Search notifications..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 250 }}
                prefix={<SearchOutlined />}
              />
              
              <Select
                value={filterType}
                onChange={setFilterType}
                style={{ width: 120 }}
                options={[
                  { value: 'all', label: `All (${notifications.length})` },
                  { value: 'unread', label: `Unread (${notifications.filter(n => !n.isRead).length})` },
                  { value: 'starred', label: `Starred (${notifications.filter(n => n.isStarred).length})` },
                  { value: 'read', label: `Read (${notifications.filter(n => n.isRead).length})` }
                ]}
              />

              <Select
                value={sortBy}
                onChange={setSortBy}
                style={{ width: 120 }}
                options={[
                  { value: 'newest', label: 'Newest First' },
                  { value: 'oldest', label: 'Oldest First' },
                  { value: 'priority', label: 'Priority' }
                ]}
              />

              <Button
                icon={<ClearOutlined />}
                onClick={clearAllFilters}
                title="Clear all filters"
              >
                Clear Filters
              </Button>
            </Space>

            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadNotifications}
                loading={loading}
              >
                Refresh
              </Button>
            </Space>
          </div>

          {/* Batch Operations Toolbar */}
          {selectedNotifications.length > 0 && (
            <div style={{ 
              marginBottom: '16px', 
              padding: '12px', 
              backgroundColor: '#f0f9ff', 
              borderRadius: '6px',
              border: '1px solid #bae7ff'
            }}>
              <Space>
                <Text>Selected {selectedNotifications.length} items</Text>
                <Divider type="vertical" />
                <Button
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={handleBatchMarkAsRead}
                >
                  Mark as Read
                </Button>
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                >
                  Batch Delete
                </Button>
                <Button
                  size="small"
                  onClick={() => setSelectedNotifications([])}
                >
                  Cancel Selection
                </Button>
              </Space>
            </div>
          )}

          {/* Notification List */}
          <Spin spinning={loading} tip="Loading...">
            {!loading && filteredNotifications.length === 0 ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <Empty description={<span style={{ color: '#bfbfbf', fontSize: '15px' }}>暂无消息</span>} />
              </div>
            ) : (
              <>
                {/* Select All Option */}
                <div style={{ marginBottom: '12px', paddingBottom: '12px', borderBottom: '1px solid #f0f0f0' }}>
                  <Checkbox
                    indeterminate={selectedNotifications.length > 0 && selectedNotifications.length < filteredNotifications.length}
                    checked={selectedNotifications.length === filteredNotifications.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    Select all on current page ({filteredNotifications.length} items)
                  </Checkbox>
                </div>

                <List
                  dataSource={filteredNotifications}
                  renderItem={(notification) => (
                    <List.Item
                      style={{
                        padding: '16px',
                        backgroundColor: !notification.isRead ? '#fff7e6' : '#fff',
                        borderRadius: '8px',
                        marginBottom: '8px',
                        border: '1px solid #f0f0f0',
                        cursor: 'pointer'
                      }}
                      onClick={() => handleNotificationClick(notification)}
                      actions={[
                        <Space key="actions">
                          <Tooltip title={notification.isStarred ? "Unstar" : "Star"}>
                            <Button
                              type="text"
                              size="small"
                              icon={notification.isStarred ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleToggleStar(notification);
                              }}
                            />
                          </Tooltip>
                          {!notification.isRead && (
                            <Tooltip title="Mark as read">
                              <Button
                                type="text"
                                size="small"
                                icon={<EyeOutlined />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleMarkAsRead(notification);
                                }}
                              />
                            </Tooltip>
                          )}
                          <Tooltip title="Delete">
                            <Button
                              type="text"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(notification);
                              }}
                            />
                          </Tooltip>
                        </Space>
                      ]}
                    >
                      <div style={{ display: 'flex', alignItems: 'flex-start', width: '100%' }}>
                        <Checkbox
                          checked={selectedNotifications.includes(notification.id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            handleSelectNotification(notification.id, e.target.checked);
                          }}
                          onClick={(e) => e.stopPropagation()}
                          style={{ marginRight: '12px', marginTop: '4px' }}
                        />
                        
                        <List.Item.Meta
                          avatar={
                            <Avatar 
                              icon={getNotificationIcon(notification.type)}
                              style={{ backgroundColor: 'transparent' }}
                            />
                          }
                          title={
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Space>
                                <Text strong={!notification.isRead} style={{ fontSize: '14px' }}>
                                  {notification.title}
                                </Text>
                                {notification.isStarred && (
                                  <StarFilled style={{ color: '#faad14', fontSize: 12 }} />
                                )}
                              </Space>
                              <Space>
                                {getTypeTag(notification.type)}
                                {getPriorityTag(notification.priority)}
                              </Space>
                            </div>
                          }
                          description={
                            <div>
                              <Text style={{ fontSize: '13px', color: '#595959', lineHeight: '1.5' }}>
                                {notification.message}
                              </Text>
                              <div style={{ marginTop: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  {formatRelativeTime(notification.createdAt)}
                                </Text>
                                {notification.projectName && (
                                  <Tag color="blue" style={{ fontSize: '11px' }}>
                                    {notification.projectName}
                                  </Tag>
                                )}
                              </div>
                            </div>
                          }
                        />
                      </div>
                    </List.Item>
                  )}
                />
              </>
            )}
          </Spin>
        </Card>
      </div>
    </div>
  );
};

export default MessageCenter;
