import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Table, Tag, message, Typography, Alert, Statistic, Row, Col } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, SyncOutlined, DatabaseOutlined } from '@ant-design/icons';
import { dal } from '../services/dataAccessLayer';
import TierMappingService from '../services/tierMappingService';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
}

const DataArchitectureTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [storageMetrics, setStorageMetrics] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);

  useEffect(() => {
    loadMetrics();
  }, []);

  const loadMetrics = () => {
    const metrics = dal.getStorageMetrics();
    const cache = dal.getCacheStats();
    setStorageMetrics(metrics);
    setCacheStats(cache);
  };

  const runDataArchitectureTests = async () => {
    setLoading(true);
    const results: TestResult[] = [];

    try {
      // 测试1：Tier映射服务
      console.log('🧪 Testing Tier Mapping Service...');
      try {
        const testTiers = ['SVIP', 'VIP', 'BA', 'A', 'B', 'C', 'S', 'V'];
        const mappedTiers = testTiers.map(tier => ({
          original: tier,
          mapped: TierMappingService.normalizeToStandard(tier),
          info: TierMappingService.getTierInfo(tier)
        }));
        
        const allValid = mappedTiers.every(item => 
          TierMappingService.isValidTier(item.mapped)
        );

        results.push({
          name: 'Tier Mapping Service',
          status: allValid ? 'success' : 'error',
          message: allValid ? 'All tier mappings are valid' : 'Some tier mappings failed',
          details: mappedTiers
        });
      } catch (error) {
        results.push({
          name: 'Tier Mapping Service',
          status: 'error',
          message: `Tier mapping failed: ${error}`,
        });
      }

      // 测试2：数据访问层初始化
      console.log('🧪 Testing Data Access Layer...');
      try {
        const projects = dal.getAllProjects();
        const clients = dal.getAllClients();
        
        results.push({
          name: 'Data Access Layer',
          status: 'success',
          message: `DAL initialized successfully. Found ${projects.length} projects and ${clients.length} clients`,
          details: { projectsCount: projects.length, clientsCount: clients.length }
        });
      } catch (error) {
        results.push({
          name: 'Data Access Layer',
          status: 'error',
          message: `DAL initialization failed: ${error}`,
        });
      }

      // 测试3：数据一致性检查
      console.log('🧪 Testing Data Consistency...');
      try {
        const projects = dal.getAllProjects();
        const clients = dal.getAllClients();
        
        // 检查项目tier的一致性
        const projectTierIssues = projects.filter(p => !TierMappingService.isValidTier(p.tier));
        
        // 检查客户tier的一致性  
        const clientTierIssues = clients.filter(c => !TierMappingService.isValidTier(c.tier));
        
        const totalIssues = projectTierIssues.length + clientTierIssues.length;
        
        results.push({
          name: 'Data Consistency Check',
          status: totalIssues === 0 ? 'success' : 'warning',
          message: totalIssues === 0 ? 'All data is consistent' : `Found ${totalIssues} tier inconsistencies`,
          details: {
            projectTierIssues: projectTierIssues.map(p => ({ id: p.id, name: p.name, tier: p.tier })),
            clientTierIssues: clientTierIssues.map(c => ({ id: c.id, name: c.name, tier: c.tier }))
          }
        });
      } catch (error) {
        results.push({
          name: 'Data Consistency Check',
          status: 'error',
          message: `Consistency check failed: ${error}`,
        });
      }

    } catch (error) {
      results.push({
        name: 'General Test Failure',
        status: 'error',
        message: `Test suite failed: ${error}`,
      });
    }

    setTestResults(results);
    setLoading(false);
    loadMetrics(); // 刷新指标

    // 显示总结消息
    const successCount = results.filter(r => r.status === 'success').length;
    const totalCount = results.length;
    
    if (successCount === totalCount) {
      message.success(`✅ All ${totalCount} tests passed! Data architecture is healthy.`);
    } else {
      message.warning(`⚠️ ${successCount}/${totalCount} tests passed. Check failed tests for details.`);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <SyncOutlined />;
    }
  };

  const testColumns = [
    {
      title: 'Test Name',
      dataIndex: 'name',
      key: 'name',
      width: '30%',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (status: string) => (
        <Tag color={status === 'success' ? 'green' : status === 'warning' ? 'orange' : 'red'}>
          {getStatusIcon(status)} {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      width: '55%',
    },
  ];

  const tierMappingData = TierMappingService.getTierOptions().map((option, index) => ({
    key: index,
    tier: option.value,
    label: option.label,
    color: option.color,
    priority: TierMappingService.getTierInfo(option.value).priority
  }));

  const tierColumns = [
    {
      title: 'Tier Code',
      dataIndex: 'tier',
      key: 'tier',
      render: (tier: string, record: any) => (
        <Tag color={record.color}>{tier}</Tag>
      ),
    },
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <DatabaseOutlined /> Data Architecture Test Suite
      </Title>
      
      <Paragraph>
        This page tests the unified data architecture implementation, including the Data Access Layer (DAL), 
        Tier Mapping Service, and data consistency checks.
      </Paragraph>

      <Alert
        message="Data Architecture Optimization"
        description="The system has been upgraded with unified tier types ('S', 'V', 'B', 'A'), project-centric storage architecture, and improved caching mechanisms."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Space direction="vertical" style={{ width: '100%' }} size="large">
        
        {/* Storage Metrics */}
        <Card title="📊 Storage Metrics" size="small">
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="Projects"
                value={storageMetrics?.projectsCount || 0}
                prefix={<DatabaseOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Clients"
                value={storageMetrics?.clientsCount || 0}
                prefix={<DatabaseOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Storage Size"
                value={storageMetrics?.storageSize || 0}
                suffix="bytes"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="Cache Hit Rate"
                value={Math.round((storageMetrics?.cacheHitRate || 0) * 100)}
                suffix="%"
              />
            </Col>
          </Row>
        </Card>

        {/* Tier Mapping Reference */}
        <Card title="🏷️ Tier Mapping Reference" size="small">
          <Text type="secondary">Standardized tier mapping used across the system:</Text>
          <Table
            dataSource={tierMappingData}
            columns={tierColumns}
            pagination={false}
            size="small"
            style={{ marginTop: 12 }}
          />
        </Card>

        {/* Test Controls */}
        <Card title="🧪 Test Controls" size="small">
          <Space>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              loading={loading}
              onClick={runDataArchitectureTests}
            >
              Run All Tests
            </Button>
            <Button onClick={loadMetrics}>
              Refresh Metrics
            </Button>
          </Space>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card title="📋 Test Results" size="small">
            <Table
              dataSource={testResults.map((result, index) => ({ key: index, ...result }))}
              columns={testColumns}
              pagination={false}
              expandable={{
                expandedRowRender: (record) => (
                  <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
                    {JSON.stringify(record.details, null, 2)}
                  </pre>
                ),
                rowExpandable: (record) => !!record.details,
              }}
            />
          </Card>
        )}

        {/* Cache Statistics */}
        {cacheStats && (
          <Card title="⚡ Cache Statistics" size="small">
            <Row gutter={16}>
              <Col span={6}>
                <Statistic title="Cache Hits" value={cacheStats.hits} />
              </Col>
              <Col span={6}>
                <Statistic title="Cache Misses" value={cacheStats.misses} />
              </Col>
              <Col span={6}>
                <Statistic title="Hit Rate" value={cacheStats.hitRate} />
              </Col>
              <Col span={6}>
                <Statistic title="Cache Size" value={cacheStats.cacheSize} />
              </Col>
            </Row>
          </Card>
        )}

      </Space>
    </div>
  );
};

export default DataArchitectureTest; 