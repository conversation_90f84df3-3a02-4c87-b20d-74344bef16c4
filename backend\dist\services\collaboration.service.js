"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationService = void 0;
const typeorm_1 = require("typeorm");
const ProjectCollaborator_1 = require("../entities/ProjectCollaborator");
const Project_1 = require("../entities/Project");
const User_1 = require("../entities/User");
const ProjectActivity_1 = require("../entities/ProjectActivity");
const errors_1 = require("../utils/errors");
const emailService_1 = require("../utils/emailService");
const tokenUtils_1 = require("../utils/tokenUtils");
class CollaborationService {
    constructor() {
        this.collaboratorRepository = (0, typeorm_1.getRepository)(ProjectCollaborator_1.ProjectCollaborator);
        this.projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        this.userRepository = (0, typeorm_1.getRepository)(User_1.User);
        this.activityRepository = (0, typeorm_1.getRepository)(ProjectActivity_1.ProjectActivity);
    }
    async inviteCollaborator(projectId, inviterUserId, email, role, message, expiresInDays = 7) {
        await this.validateInviterPermission(projectId, inviterUserId);
        const project = await this.projectRepository.findOne(projectId);
        if (!project) {
            throw new errors_1.NotFoundError('项目不存在');
        }
        const existingCollaborator = await this.collaboratorRepository.findOne({
            where: [
                { project: { id: projectId }, user: { email } },
                { project: { id: projectId }, invitedEmail: email }
            ]
        });
        if (existingCollaborator) {
            throw new errors_1.BadRequestError('用户已经是项目协作者');
        }
        const existingUser = await this.userRepository.findOne({ where: { email } });
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + expiresInDays);
        const collaborator = this.collaboratorRepository.create({
            project,
            user: existingUser,
            invitedEmail: email,
            role,
            status: ProjectCollaborator_1.InvitationStatus.PENDING,
            expiresAt,
            invitedBy: inviterUserId,
            invitationMessage: message,
            permissions: this.getDefaultPermissions(role)
        });
        const savedCollaborator = await this.collaboratorRepository.save(collaborator);
        const invitationToken = (0, tokenUtils_1.generateInvitationToken)(savedCollaborator.id);
        await (0, emailService_1.sendInvitationEmail)(email, project.name, invitationToken, message);
        await this.recordActivity(projectId, inviterUserId, ProjectActivity_1.ActivityType.COLLABORATOR_ADDED, `邀请 ${email} 加入项目`, { targetUser: email, role });
        return savedCollaborator;
    }
    async acceptInvitation(token, userId) {
        const collaborator = await this.getCollaboratorByToken(token);
        if (collaborator.status !== ProjectCollaborator_1.InvitationStatus.PENDING) {
            throw new errors_1.BadRequestError('邀请已经被处理');
        }
        if (collaborator.expiresAt && collaborator.expiresAt < new Date()) {
            collaborator.status = ProjectCollaborator_1.InvitationStatus.EXPIRED;
            await this.collaboratorRepository.save(collaborator);
            throw new errors_1.BadRequestError('邀请已过期');
        }
        if (userId) {
            const user = await this.userRepository.findOne(userId);
            if (user && user.email === collaborator.invitedEmail) {
                collaborator.user = user;
            }
        }
        collaborator.status = ProjectCollaborator_1.InvitationStatus.ACCEPTED;
        const savedCollaborator = await this.collaboratorRepository.save(collaborator);
        await this.recordActivity(collaborator.project.id, userId || null, ProjectActivity_1.ActivityType.COLLABORATOR_ADDED, `${collaborator.invitedEmail} 接受了项目邀请`);
        return savedCollaborator;
    }
    async declineInvitation(token) {
        const collaborator = await this.getCollaboratorByToken(token);
        collaborator.status = ProjectCollaborator_1.InvitationStatus.DECLINED;
        await this.collaboratorRepository.save(collaborator);
        await this.recordActivity(collaborator.project.id, null, ProjectActivity_1.ActivityType.COLLABORATOR_REMOVED, `${collaborator.invitedEmail} 拒绝了项目邀请`);
    }
    async updateCollaboratorPermissions(projectId, collaboratorId, updaterUserId, newRole, customPermissions) {
        await this.validateManagePermission(projectId, updaterUserId);
        const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
            relations: ['project', 'user']
        });
        if (!collaborator || collaborator.project.id !== projectId) {
            throw new errors_1.NotFoundError('协作者不存在');
        }
        const oldRole = collaborator.role;
        collaborator.role = newRole;
        collaborator.permissions = customPermissions || this.getDefaultPermissions(newRole);
        const savedCollaborator = await this.collaboratorRepository.save(collaborator);
        await this.recordActivity(projectId, updaterUserId, ProjectActivity_1.ActivityType.PERMISSION_CHANGED, `更新了 ${collaborator.invitedEmail} 的权限`, { oldValue: oldRole, newValue: newRole });
        return savedCollaborator;
    }
    async removeCollaborator(projectId, collaboratorId, removerUserId) {
        await this.validateManagePermission(projectId, removerUserId);
        const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
            relations: ['project', 'user']
        });
        if (!collaborator || collaborator.project.id !== projectId) {
            throw new errors_1.NotFoundError('协作者不存在');
        }
        const targetEmail = collaborator.invitedEmail;
        await this.collaboratorRepository.remove(collaborator);
        await this.recordActivity(projectId, removerUserId, ProjectActivity_1.ActivityType.COLLABORATOR_REMOVED, `移除了协作者 ${targetEmail}`);
    }
    async getProjectCollaborators(projectId) {
        return await this.collaboratorRepository.find({
            where: { project: { id: projectId } },
            relations: ['user', 'project'],
            order: { createdAt: 'DESC' }
        });
    }
    async checkPermission(projectId, userId, permission) {
        const collaborator = await this.collaboratorRepository.findOne({
            where: { project: { id: projectId }, user: { id: userId } },
            relations: ['user']
        });
        if (!collaborator || collaborator.status !== ProjectCollaborator_1.InvitationStatus.ACCEPTED) {
            return false;
        }
        const permissions = collaborator.permissions;
        switch (permission) {
            case 'edit':
                return permissions.canEdit;
            case 'delete':
                return permissions.canDelete;
            case 'invite':
                return permissions.canInvite;
            case 'manageStages':
                return permissions.canManageStages;
            case 'uploadDocuments':
                return permissions.canUploadDocuments;
            case 'viewFinancials':
                return permissions.canViewFinancials;
            default:
                return false;
        }
    }
    async getUserProjects(userId) {
        const collaborators = await this.collaboratorRepository.find({
            where: { user: { id: userId }, status: ProjectCollaborator_1.InvitationStatus.ACCEPTED },
            relations: ['project']
        });
        return collaborators.map(c => c.project);
    }
    async validateInviterPermission(projectId, userId) {
        const hasPermission = await this.checkPermission(projectId, userId, 'invite');
        if (!hasPermission) {
            throw new errors_1.ForbiddenError('没有邀请权限');
        }
    }
    async validateManagePermission(projectId, userId) {
        const collaborator = await this.collaboratorRepository.findOne({
            where: { project: { id: projectId }, user: { id: userId } }
        });
        if (!collaborator || ![ProjectCollaborator_1.CollaboratorRole.OWNER, ProjectCollaborator_1.CollaboratorRole.ADMIN].includes(collaborator.role)) {
            throw new errors_1.ForbiddenError('没有管理权限');
        }
    }
    async getCollaboratorByToken(token) {
        throw new Error('Token validation not implemented');
    }
    getDefaultPermissions(role) {
        const permissionMap = {
            [ProjectCollaborator_1.CollaboratorRole.OWNER]: {
                canEdit: true,
                canDelete: true,
                canInvite: true,
                canManageStages: true,
                canUploadDocuments: true,
                canViewFinancials: true,
                moduleAccess: ['all']
            },
            [ProjectCollaborator_1.CollaboratorRole.ADMIN]: {
                canEdit: true,
                canDelete: false,
                canInvite: true,
                canManageStages: true,
                canUploadDocuments: true,
                canViewFinancials: true,
                moduleAccess: ['all']
            },
            [ProjectCollaborator_1.CollaboratorRole.EDITOR]: {
                canEdit: true,
                canDelete: false,
                canInvite: false,
                canManageStages: true,
                canUploadDocuments: true,
                canViewFinancials: false,
                moduleAccess: ['basic', 'documents', 'stages']
            },
            [ProjectCollaborator_1.CollaboratorRole.VIEWER]: {
                canEdit: false,
                canDelete: false,
                canInvite: false,
                canManageStages: false,
                canUploadDocuments: false,
                canViewFinancials: false,
                moduleAccess: ['basic']
            },
            [ProjectCollaborator_1.CollaboratorRole.GUEST_EDITOR]: {
                canEdit: true,
                canDelete: false,
                canInvite: false,
                canManageStages: false,
                canUploadDocuments: true,
                canViewFinancials: false,
                moduleAccess: ['basic', 'documents']
            },
            [ProjectCollaborator_1.CollaboratorRole.GUEST_VIEWER]: {
                canEdit: false,
                canDelete: false,
                canInvite: false,
                canManageStages: false,
                canUploadDocuments: false,
                canViewFinancials: false,
                moduleAccess: ['basic']
            }
        };
        return permissionMap[role];
    }
    async recordActivity(projectId, userId, type, title, metadata) {
        if (!userId)
            return;
        const activity = this.activityRepository.create({
            project: { id: projectId },
            user: { id: userId },
            type,
            title,
            metadata
        });
        await this.activityRepository.save(activity);
    }
}
exports.CollaborationService = CollaborationService;
//# sourceMappingURL=collaboration.service.js.map