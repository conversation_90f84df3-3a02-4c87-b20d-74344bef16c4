import { Project } from '../types';
import * as XLSX from 'xlsx';

export const exportToExcel = (projects: Project[]) => {
  // 准备导出数据
  const exportData = projects.map(project => ({
    '项目ID': project.projectId,
    '项目名称': project.name,
    '客户': project.client,
    '国家': project.country,
    '级别': project.tier,
    '类别': project.category,
    '收入': `€${project.revenue.toLocaleString()}`,
    '状态': project.status,
    '概率': project.probability,
    '等级': project.level,
    '负责人': project.owner,
    '创建日期': new Date(project.createdAt).toLocaleDateString(),
    '更新日期': new Date(project.updatedAt).toLocaleDateString()
  }));

  // 创建工作簿
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.json_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 项目ID
    { wch: 30 }, // 项目名称
    { wch: 20 }, // 客户
    { wch: 10 }, // 国家
    { wch: 8 },  // 级别
    { wch: 12 }, // 类别
    { wch: 15 }, // 收入
    { wch: 10 }, // 状态
    { wch: 8 },  // 概率
    { wch: 8 },  // 等级
    { wch: 15 }, // 负责人
    { wch: 12 }, // 创建日期
    { wch: 12 }  // 更新日期
  ];
  ws['!cols'] = colWidths;

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, '项目列表');

  // 导出文件
  XLSX.writeFile(wb, `项目列表_${new Date().toLocaleDateString()}.xlsx`);
};

export const exportToCSV = (projects: Project[]) => {
  // 准备CSV头部
  const headers = [
    '项目ID',
    '项目名称',
    '客户',
    '国家',
    '级别',
    '类别',
    '收入',
    '状态',
    '概率',
    '等级',
    '负责人',
    '创建日期',
    '更新日期'
  ].join(',');

  // 准备数据行
  const rows = projects.map(project => [
    project.projectId,
    project.name,
    project.client,
    project.country,
    project.tier,
    project.category,
    `€${project.revenue.toLocaleString()}`,
    project.status,
    project.probability,
    project.level,
    project.owner,
    new Date(project.createdAt).toLocaleDateString(),
    new Date(project.updatedAt).toLocaleDateString()
  ].join(','));

  // 组合CSV内容
  const csvContent = [headers, ...rows].join('\n');

  // 创建Blob对象
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);

  // 下载文件
  link.setAttribute('href', url);
  link.setAttribute('download', `项目列表_${new Date().toLocaleDateString()}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}; 