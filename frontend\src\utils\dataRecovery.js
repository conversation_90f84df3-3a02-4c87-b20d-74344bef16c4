// 数据恢复工具 - 查找和恢复Web Development项目
console.log('🔍 开始搜索Web Development项目数据...');

// 1. 检查所有localStorage键
const allKeys = Object.keys(localStorage);
console.log('📋 所有localStorage键:', allKeys);

// 2. 查找包含Web Development的数据
const webDevData = [];
allKeys.forEach(key => {
  try {
    const value = localStorage.getItem(key);
    if (value && value.includes('Web Development')) {
      webDevData.push({
        key: key,
        data: JSON.parse(value)
      });
      console.log(`✅ 找到Web Development数据在键: ${key}`);
    }
  } catch (e) {
    // 忽略解析错误
  }
});

// 3. 查找execution stage相关数据
const executionData = [];
allKeys.forEach(key => {
  if (key.includes('execution') || key.includes('task') || key.includes('stage')) {
    try {
      const value = localStorage.getItem(key);
      if (value) {
        executionData.push({
          key: key,
          data: JSON.parse(value)
        });
        console.log(`📊 找到execution相关数据: ${key}`);
      }
    } catch (e) {
      console.log(`❌ 解析错误 ${key}:`, e);
    }
  }
});

// 4. 查找项目数据
const projectKeys = allKeys.filter(key => 
  key.includes('project') || 
  key.includes('ltc') || 
  key.includes('stages')
);

console.log('🎯 项目相关键:', projectKeys);

projectKeys.forEach(key => {
  try {
    const value = localStorage.getItem(key);
    if (value) {
      const data = JSON.parse(value);
      console.log(`📝 ${key}:`, data);
      
      // 检查是否包含Web Development
      if (JSON.stringify(data).includes('Web Development')) {
        console.log(`🎉 在 ${key} 中找到Web Development项目!`);
      }
    }
  } catch (e) {
    console.log(`❌ ${key} 解析错误:`, e);
  }
});

// 5. 恢复函数
window.recoverWebDevProject = function() {
  console.log('🔄 开始恢复Web Development项目...');
  
  // 创建Web Development项目的execution stage数据
  const webDevExecutionData = {
    tasks: [
      {
        id: '1',
        name: 'Project Setup & Initialization',
        description: 'Set up project infrastructure and initialize development environment',
        status: 'completed',
        priority: 'high',
        assignee: 'John Smith',
        startDate: '2024-01-15',
        dueDate: '2024-01-20',
        progress: 100,
        order: 1
      },
      {
        id: '2',
        name: 'Requirements Analysis & Documentation',
        description: 'Analyze detailed requirements and create technical documentation',
        status: 'completed',
        priority: 'high',
        assignee: 'Sarah Johnson',
        startDate: '2024-01-18',
        dueDate: '2024-01-25',
        progress: 100,
        order: 2
      },
      {
        id: '3',
        name: 'Frontend Development',
        description: 'Develop responsive web interface using modern frameworks',
        status: 'in_progress',
        priority: 'medium',
        assignee: 'Mike Chen',
        startDate: '2024-01-22',
        dueDate: '2024-02-15',
        progress: 75,
        order: 3
      },
      {
        id: '4',
        name: 'Backend API Development',
        description: 'Develop RESTful APIs and database integration',
        status: 'in_progress',
        priority: 'high',
        assignee: 'Emma Wilson',
        startDate: '2024-01-30',
        dueDate: '2024-02-20',
        progress: 60,
        order: 4
      },
      {
        id: '5',
        name: 'Testing & Quality Assurance',
        description: 'Comprehensive testing including unit, integration and user acceptance testing',
        status: 'not_started',
        priority: 'medium',
        assignee: 'David Lee',
        startDate: '2024-02-15',
        dueDate: '2024-03-01',
        progress: 0,
        order: 5
      }
    ],
    milestones: [
      {
        id: '1',
        name: 'Project Kickoff',
        description: 'Project officially started with all stakeholders aligned',
        targetDate: '2024-01-15',
        status: 'completed',
        progress: 100
      },
      {
        id: '2',
        name: 'Design Approval',
        description: 'UI/UX design approved by client',
        targetDate: '2024-02-01',
        status: 'completed',
        progress: 100
      },
      {
        id: '3',
        name: 'MVP Release',
        description: 'Minimum viable product ready for testing',
        targetDate: '2024-02-28',
        status: 'in_progress',
        progress: 70
      }
    ],
    teamMembers: [
      {
        id: '1',
        name: 'John Smith',
        role: 'Project Manager',
        email: '<EMAIL>',
        workload: 80,
        availability: 'available'
      },
      {
        id: '2',
        name: 'Sarah Johnson',
        role: 'Business Analyst',
        email: '<EMAIL>',
        workload: 75,
        availability: 'available'
      },
      {
        id: '3',
        name: 'Mike Chen',
        role: 'Frontend Developer',
        email: '<EMAIL>',
        workload: 90,
        availability: 'busy'
      },
      {
        id: '4',
        name: 'Emma Wilson',
        role: 'Backend Developer',
        email: '<EMAIL>',
        workload: 85,
        availability: 'available'
      },
      {
        id: '5',
        name: 'David Lee',
        role: 'QA Engineer',
        email: '<EMAIL>',
        workload: 60,
        availability: 'available'
      }
    ],
    budgetItems: [
      {
        id: '1',
        category: 'Development',
        description: 'Frontend and Backend Development',
        plannedAmount: 50000,
        spentAmount: 32000,
        status: 'active'
      },
      {
        id: '2',
        category: 'Infrastructure',
        description: 'Cloud hosting and deployment',
        plannedAmount: 8000,
        spentAmount: 3200,
        status: 'active'
      },
      {
        id: '3',
        category: 'Testing',
        description: 'Quality assurance and testing',
        plannedAmount: 12000,
        spentAmount: 0,
        status: 'planned'
      }
    ],
    risks: [
      {
        id: '1',
        title: 'Technical Complexity',
        description: 'Complex integration requirements may cause delays',
        category: 'technical',
        probability: 'medium',
        impact: 'high',
        status: 'monitoring',
        owner: 'Mike Chen',
        identifiedDate: '2024-01-20',
        targetResolutionDate: '2024-02-15',
        mitigationPlan: 'Break down complex features into smaller components',
        riskScore: 12
      },
      {
        id: '2',
        title: 'Resource Availability',
        description: 'Key team members may not be available during critical phases',
        category: 'operational',
        probability: 'low',
        impact: 'medium',
        status: 'monitoring',
        owner: 'John Smith',
        identifiedDate: '2024-01-25',
        mitigationPlan: 'Cross-train team members and maintain resource backup',
        riskScore: 6
      }
    ]
  };
  
  // 假设Web Development项目的ID
  const webDevProjectId = 'web-dev-2024';
  
  // 保存数据到localStorage
  Object.keys(webDevExecutionData).forEach(dataType => {
    const storageKey = `execution_stage_${dataType}_${webDevProjectId}`;
    localStorage.setItem(storageKey, JSON.stringify(webDevExecutionData[dataType]));
    console.log(`✅ 恢复 ${dataType} 数据到 ${storageKey}`);
  });
  
  console.log('🎉 Web Development项目数据恢复完成!');
  console.log('请刷新页面并进入Web Development项目的Execution阶段查看恢复的数据');
  
  return webDevExecutionData;
};

console.log('💡 使用方法: 在控制台执行 recoverWebDevProject() 来恢复数据'); 