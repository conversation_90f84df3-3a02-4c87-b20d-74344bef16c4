import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  message,
  Tabs,
  Row,
  Col,
  Divider,
  Typography,
  Space,
  Switch,
  Select,
  TimePicker,
  Tag,
  Alert,
  Modal,
  List
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  LockOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  BellOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  TeamOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd';
import dayjs from 'dayjs';
import { formatTimeWithTimezone, getBrowserTimezone } from '../utils/timezoneUtils';
import userService from '../services/user.service';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface UserProfile {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  department: string;
  position: string;
  avatar: string;
  bio: string;
  location: string;
  timezone: string;
  language: string;
  joinDate: string;
  lastLogin: string;
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  projectUpdates: boolean;
  teamMentions: boolean;
  systemMaintenance: boolean;
  weeklyDigest: boolean;
  marketingEmails: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  passwordLastChanged: string;
  loginSessions: Array<{
    id: string;
    device: string;
    location: string;
    lastAccess: string;
    current: boolean;
  }>;
}

const Profile: React.FC = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState('');

  // User data
  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: '',
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    department: '',
    position: '',
    avatar: '',
    bio: '',
    location: '',
    timezone: '',
    language: '',
    joinDate: '',
    lastLogin: ''
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: false,
    pushNotifications: false,
    smsNotifications: false,
    projectUpdates: false,
    teamMentions: false,
    systemMaintenance: false,
    weeklyDigest: false,
    marketingEmails: false,
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00'
  });

  // Security settings
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    passwordLastChanged: '',
    loginSessions: []
  });

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        
        // Load user profile
        const profile = await userService.getUserProfile();
        setUserProfile(profile);
        profileForm.setFieldsValue(profile);
        
        // Load notification settings
        const notifications = await userService.getNotificationSettings();
        setNotificationSettings(notifications);
        
        // Load security settings
        const security = await userService.getSecuritySettings();
        setSecuritySettings(security);
        
      } catch (error) {
        console.error('Error loading user data:', error);
        message.error('Failed to load user data');
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [profileForm]);

  // Update current time every second
  useEffect(() => {
    const updateTime = () => {
      if (userProfile.timezone) {
        setCurrentTime(formatTimeWithTimezone(userProfile.timezone));
      }
    };
    
    updateTime(); // Initial update
    const interval = setInterval(updateTime, 1000);
    
    return () => clearInterval(interval);
  }, [userProfile.timezone]);

  // Avatar upload handler
  const handleAvatarUpload: UploadProps['customRequest'] = async (options) => {
    const { file } = options;
    
    try {
      setAvatarLoading(true);
      
      // Upload avatar using service
      const avatarUrl = await userService.uploadAvatar(file as File);
      
      // Update profile with new avatar
      const updatedProfile = await userService.updateProfile({ avatar: avatarUrl });
      setUserProfile(updatedProfile);
      
      message.success('Avatar uploaded successfully!');
      
      if (options.onSuccess) {
        options.onSuccess(avatarUrl);
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      message.error('Failed to upload avatar');
      
      if (options.onError) {
        options.onError(error as any);
      }
    } finally {
      setAvatarLoading(false);
    }
  };

  // Save profile
  const handleProfileSave = async () => {
    try {
      setLoading(true);
      const values = await profileForm.validateFields();
      
      // Save profile using service
      const updatedProfile = await userService.updateProfile(values);
      setUserProfile(updatedProfile);
      
      message.success('Profile updated successfully!');
    } catch (error) {
      console.error('Profile save error:', error);
      message.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  // Change password
  const handlePasswordChange = async () => {
    try {
      setLoading(true);
      const values = await passwordForm.validateFields();
      
      if (values.newPassword !== values.confirmPassword) {
        message.error('Passwords do not match');
        return;
      }
      
      // Change password using service
      await userService.changePassword(values);
      
      passwordForm.resetFields();
      message.success('Password changed successfully!');
      
      // Reload security settings to update last changed date
      const security = await userService.getSecuritySettings();
      setSecuritySettings(security);
      
    } catch (error: any) {
      console.error('Password change error:', error);
      message.error(error.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  // Save notification settings
  const handleNotificationSave = async () => {
    try {
      setLoading(true);
      
      // Save notification settings using service
      await userService.saveNotificationSettings(notificationSettings);
      
      message.success('Notification settings saved successfully!');
    } catch (error) {
      console.error('Notification save error:', error);
      message.error('Failed to save notification settings');
    } finally {
      setLoading(false);
    }
  };

  // Toggle two-factor authentication
  const handleTwoFactorToggle = async (enabled: boolean) => {
    if (enabled) {
      Modal.confirm({
        title: 'Enable Two-Factor Authentication',
        content: (
          <div>
            <p>Are you sure you want to enable two-factor authentication?</p>
            <p>You will need to set up an authenticator app to complete this process.</p>
          </div>
        ),
        onOk: async () => {
          try {
            setLoading(true);
            await userService.toggleTwoFactor(enabled);
            const security = await userService.getSecuritySettings();
            setSecuritySettings(security);
            message.success('Two-factor authentication enabled successfully!');
          } catch (error) {
            console.error('2FA toggle error:', error);
            message.error('Failed to enable two-factor authentication');
          } finally {
            setLoading(false);
          }
        }
      });
    } else {
      Modal.confirm({
        title: 'Disable Two-Factor Authentication',
        content: 'Are you sure you want to disable two-factor authentication? This will reduce your account security.',
        onOk: async () => {
          try {
            setLoading(true);
            await userService.toggleTwoFactor(enabled);
            const security = await userService.getSecuritySettings();
            setSecuritySettings(security);
            message.success('Two-factor authentication disabled');
          } catch (error) {
            console.error('2FA toggle error:', error);
            message.error('Failed to disable two-factor authentication');
          } finally {
            setLoading(false);
          }
        }
      });
    }
  };

  // Logout session
  const handleLogoutSession = async (sessionId: string) => {
    Modal.confirm({
      title: 'Logout Session',
      content: 'Are you sure you want to logout this session?',
      onOk: async () => {
        try {
          await userService.logoutSession(sessionId);
          const security = await userService.getSecuritySettings();
          setSecuritySettings(security);
          message.success('Session logged out successfully');
        } catch (error) {
          console.error('Logout session error:', error);
          message.error('Failed to logout session');
        }
      }
    });
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2}>
          <UserOutlined /> User Profile
        </Title>
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* Profile */}
          <TabPane
            tab={
              <span>
                <UserOutlined />
                Profile
              </span>
            }
            key="profile"
          >
            <Row gutter={24}>
              <Col span={8}>
                <Card title="Avatar Settings">
                  <div style={{ textAlign: 'center' }}>
                    <Avatar
                      size={120}
                      src={userProfile.avatar}
                      icon={<UserOutlined />}
                      style={{ marginBottom: '16px' }}
                    />
                    <br />
                    <Upload
                      showUploadList={false}
                      accept="image/*"
                      customRequest={handleAvatarUpload}
                    >
                      <Button 
                        icon={<CameraOutlined />} 
                        loading={avatarLoading}
                      >
                        Change Avatar
                      </Button>
                    </Upload>
                    <Paragraph type="secondary" style={{ marginTop: '8px', fontSize: '12px' }}>
                      Supports JPG, PNG formats, file size not exceeding 2MB
                    </Paragraph>
                  </div>
                </Card>

                <Card title="Account Information" style={{ marginTop: '16px' }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text type="secondary">Username: </Text>
                      <Text strong>{userProfile.username}</Text>
                    </div>
                    <div>
                      <Text type="secondary">Join Date: </Text>
                      <Text>{userProfile.joinDate}</Text>
                    </div>
                    <div>
                      <Text type="secondary">Last Login: </Text>
                      <Text>{userProfile.lastLogin}</Text>
                    </div>
                  </Space>
                </Card>
              </Col>

              <Col span={16}>
                <Card title="Basic Information" extra={
                  <Button 
                    type="primary" 
                    icon={<SaveOutlined />}
                    loading={loading}
                    onClick={handleProfileSave}
                  >
                    Save
                  </Button>
                }>
                  <Form
                    form={profileForm}
                    layout="vertical"
                    initialValues={userProfile}
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="firstName"
                          label="First Name"
                          rules={[{ required: true, message: 'Please enter your first name' }]}
                        >
                          <Input prefix={<UserOutlined />} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="lastName"
                          label="Last Name"
                          rules={[{ required: true, message: 'Please enter your last name' }]}
                        >
                          <Input prefix={<UserOutlined />} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="email"
                          label="Email"
                          rules={[
                            { required: true, message: 'Please enter email' },
                            { type: 'email', message: 'Please enter a valid email address' }
                          ]}
                        >
                          <Input prefix={<MailOutlined />} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="phone"
                          label="Phone"
                        >
                          <Input prefix={<PhoneOutlined />} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="department"
                          label="Department"
                        >
                          <Select
                            placeholder="Select or type department"
                            mode="tags"
                            maxTagCount={1}
                            style={{ width: '100%' }}
                            showSearch={true}
                          >
                            {/* Executive & Leadership */}
                            <Option value="Executive Office">Executive Office</Option>
                            <Option value="Board of Directors">Board of Directors</Option>
                            <Option value="Strategic Planning">Strategic Planning</Option>
                            
                            {/* Technology & Engineering */}
                            <Option value="Information Technology">Information Technology</Option>
                            <Option value="Software Development">Software Development</Option>
                            <Option value="Data & Analytics">Data & Analytics</Option>
                            <Option value="Cybersecurity">Cybersecurity</Option>
                            <Option value="DevOps & Infrastructure">DevOps & Infrastructure</Option>
                            <Option value="Product Engineering">Product Engineering</Option>
                            <Option value="Research & Development">Research & Development</Option>
                            <Option value="Quality Assurance">Quality Assurance</Option>
                            
                            {/* Sales & Marketing */}
                            <Option value="Sales">Sales</Option>
                            <Option value="Business Development">Business Development</Option>
                            <Option value="Marketing">Marketing</Option>
                            <Option value="Customer Success">Customer Success</Option>
                            <Option value="Account Management">Account Management</Option>
                            <Option value="Digital Marketing">Digital Marketing</Option>
                            <Option value="Channel Partners">Channel Partners</Option>
                            
                            {/* Operations & Management */}
                            <Option value="Operations">Operations</Option>
                            <Option value="Project Management">Project Management</Option>
                            <Option value="Supply Chain">Supply Chain</Option>
                            <Option value="Manufacturing">Manufacturing</Option>
                            <Option value="Logistics">Logistics</Option>
                            <Option value="Procurement">Procurement</Option>
                            <Option value="Facilities Management">Facilities Management</Option>
                            
                            {/* Finance & Administration */}
                            <Option value="Finance">Finance</Option>
                            <Option value="Accounting">Accounting</Option>
                            <Option value="Treasury">Treasury</Option>
                            <Option value="Financial Planning & Analysis">Financial Planning & Analysis</Option>
                            <Option value="Internal Audit">Internal Audit</Option>
                            <Option value="Risk Management">Risk Management</Option>
                            <Option value="Investor Relations">Investor Relations</Option>
                            
                            {/* Human Resources */}
                            <Option value="Human Resources">Human Resources</Option>
                            <Option value="Talent Acquisition">Talent Acquisition</Option>
                            <Option value="Learning & Development">Learning & Development</Option>
                            <Option value="Compensation & Benefits">Compensation & Benefits</Option>
                            <Option value="Employee Relations">Employee Relations</Option>
                            
                            {/* Legal & Compliance */}
                            <Option value="Legal">Legal</Option>
                            <Option value="Compliance">Compliance</Option>
                            <Option value="Corporate Affairs">Corporate Affairs</Option>
                            <Option value="Intellectual Property">Intellectual Property</Option>
                            <Option value="Regulatory Affairs">Regulatory Affairs</Option>
                            
                            {/* Customer-facing */}
                            <Option value="Customer Service">Customer Service</Option>
                            <Option value="Technical Support">Technical Support</Option>
                            <Option value="Customer Experience">Customer Experience</Option>
                            <Option value="Training & Education">Training & Education</Option>
                            
                            {/* Specialized Functions */}
                            <Option value="Business Intelligence">Business Intelligence</Option>
                            <Option value="Corporate Strategy">Corporate Strategy</Option>
                            <Option value="Mergers & Acquisitions">Mergers & Acquisitions</Option>
                            <Option value="Environmental Health & Safety">Environmental Health & Safety</Option>
                            <Option value="Public Relations">Public Relations</Option>
                            <Option value="Government Relations">Government Relations</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="position"
                          label="Position"
                        >
                          <Input prefix={<TeamOutlined />} />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="location"
                      label="Location"
                    >
                      <Input prefix={<EnvironmentOutlined />} />
                    </Form.Item>

                    <Row gutter={16}>
                      <Col span={24}>
                        <Form.Item
                          name="timezone"
                          label="Timezone"
                        >
                          <Select
                            showSearch={true}
                            placeholder="Select your timezone"
                            style={{ width: '100%' }}
                            filterOption={(input, option) => {
                              if (!option || !input) return false;
                              
                              const searchText = input.toLowerCase().trim();
                              const optionText = String(option.children || '').toLowerCase();
                              const optionValue = String(option.value || '').toLowerCase();
                              
                              // Check for direct matches in text or value
                              if (optionText.includes(searchText) || optionValue.includes(searchText)) {
                                return true;
                              }
                              
                              // City/Country mapping for better search
                              const cityMappings: { [key: string]: string[] } = {
                                'beijing': ['beijing', 'china', 'cst'],
                                'shanghai': ['shanghai', 'china', 'cst'],
                                'china': ['beijing', 'shanghai', 'cst'],
                                'tokyo': ['tokyo', 'japan', 'jst'],
                                'japan': ['tokyo', 'osaka', 'jst'],
                                'london': ['london', 'uk', 'britain', 'gmt'],
                                'uk': ['london', 'dublin', 'gmt'],
                                'britain': ['london', 'dublin', 'gmt'],
                                'new york': ['new york', 'toronto', 'eastern'],
                                'ny': ['new york', 'toronto', 'eastern'],
                                'nyc': ['new york', 'toronto', 'eastern'],
                                'los angeles': ['los angeles', 'vancouver', 'pacific'],
                                'la': ['los angeles', 'vancouver', 'pacific'],
                                'singapore': ['singapore', 'sgt'],
                                'hong kong': ['hong kong', 'hkt'],
                                'hongkong': ['hong kong', 'hkt'],
                                'hk': ['hong kong', 'hkt'],
                                'sydney': ['sydney', 'melbourne', 'australia'],
                                'melbourne': ['sydney', 'melbourne', 'australia'],
                                'australia': ['sydney', 'melbourne', 'perth', 'adelaide'],
                                'paris': ['paris', 'berlin', 'rome', 'cet'],
                                'berlin': ['paris', 'berlin', 'rome', 'cet'],
                                'rome': ['paris', 'berlin', 'rome', 'cet'],
                                'moscow': ['moscow', 'msk'],
                                'india': ['mumbai', 'delhi', 'kolkata', 'ist'],
                                'mumbai': ['mumbai', 'delhi', 'india', 'ist'],
                                'delhi': ['mumbai', 'delhi', 'india', 'ist'],
                                'dubai': ['dubai', 'abu dhabi', 'gst'],
                                'bangkok': ['bangkok', 'hanoi', 'ict'],
                                'seoul': ['seoul', 'korea', 'kst'],
                                'korea': ['seoul', 'kst']
                              };
                              
                              // Check if search term has mappings
                              const mappings = cityMappings[searchText];
                              if (mappings) {
                                return mappings.some(term => optionText.includes(term));
                              }
                              
                              return false;
                            }}
                            onChange={(value) => {
                              // Set browser timezone if user changes it
                              if (value) {
                                setUserProfile(prev => ({ ...prev, timezone: value }));
                                // Here you could add logic to apply timezone changes
                                console.log('Timezone changed to:', value);
                              }
                            }}
                          >
                            {/* Americas */}
                            <Option value="America/New_York">Eastern Time (ET) - New York, Toronto</Option>
                            <Option value="America/Chicago">Central Time (CT) - Chicago, Mexico City</Option>
                            <Option value="America/Denver">Mountain Time (MT) - Denver, Phoenix</Option>
                            <Option value="America/Los_Angeles">Pacific Time (PT) - Los Angeles, Vancouver</Option>
                            <Option value="America/Anchorage">Alaska Time (AKST) - Anchorage</Option>
                            <Option value="Pacific/Honolulu">Hawaii Time (HST) - Honolulu</Option>
                            <Option value="America/Sao_Paulo">Brasilia Time (BRT) - São Paulo, Rio de Janeiro</Option>
                            <Option value="America/Argentina/Buenos_Aires">Argentina Time (ART) - Buenos Aires</Option>
                            <Option value="America/Lima">Peru Time (PET) - Lima</Option>
                            <Option value="America/Bogota">Colombia Time (COT) - Bogotá</Option>
                            
                            {/* Europe */}
                            <Option value="Europe/London">Greenwich Mean Time (GMT) - London, Dublin</Option>
                            <Option value="Europe/Paris">Central European Time (CET) - Paris, Berlin, Rome</Option>
                            <Option value="Europe/Athens">Eastern European Time (EET) - Athens, Helsinki</Option>
                            <Option value="Europe/Moscow">Moscow Time (MSK) - Moscow</Option>
                            <Option value="Europe/Istanbul">Turkey Time (TRT) - Istanbul</Option>
                            <Option value="Europe/Zurich">Central European Time (CET) - Zurich, Vienna</Option>
                            <Option value="Europe/Amsterdam">Central European Time (CET) - Amsterdam</Option>
                            <Option value="Europe/Stockholm">Central European Time (CET) - Stockholm</Option>
                            
                            {/* Asia */}
                            <Option value="Asia/Shanghai">China Standard Time (CST) - Beijing, Shanghai</Option>
                            <Option value="Asia/Tokyo">Japan Standard Time (JST) - Tokyo, Osaka</Option>
                            <Option value="Asia/Seoul">Korea Standard Time (KST) - Seoul</Option>
                            <Option value="Asia/Hong_Kong">Hong Kong Time (HKT) - Hong Kong</Option>
                            <Option value="Asia/Singapore">Singapore Time (SGT) - Singapore</Option>
                            <Option value="Asia/Bangkok">Indochina Time (ICT) - Bangkok, Hanoi</Option>
                            <Option value="Asia/Jakarta">Western Indonesia Time (WIB) - Jakarta</Option>
                            <Option value="Asia/Manila">Philippine Time (PHT) - Manila</Option>
                            <Option value="Asia/Kuala_Lumpur">Malaysia Time (MYT) - Kuala Lumpur</Option>
                            <Option value="Asia/Kolkata">India Standard Time (IST) - Mumbai, Delhi</Option>
                            <Option value="Asia/Dubai">Gulf Standard Time (GST) - Dubai, Abu Dhabi</Option>
                            <Option value="Asia/Riyadh">Arabia Standard Time (AST) - Riyadh</Option>
                            <Option value="Asia/Tehran">Iran Standard Time (IRST) - Tehran</Option>
                            
                            {/* Oceania */}
                            <Option value="Australia/Sydney">Australian Eastern Time (AET) - Sydney, Melbourne</Option>
                            <Option value="Australia/Perth">Australian Western Time (AWT) - Perth</Option>
                            <Option value="Australia/Adelaide">Australian Central Time (ACT) - Adelaide</Option>
                            <Option value="Pacific/Auckland">New Zealand Time (NZST) - Auckland</Option>
                            <Option value="Pacific/Fiji">Fiji Time (FJT) - Suva</Option>
                            
                            {/* Africa */}
                            <Option value="Africa/Cairo">Eastern European Time (EET) - Cairo</Option>
                            <Option value="Africa/Johannesburg">South Africa Time (SAST) - Johannesburg</Option>
                            <Option value="Africa/Lagos">West Africa Time (WAT) - Lagos</Option>
                            <Option value="Africa/Nairobi">East Africa Time (EAT) - Nairobi</Option>
                            <Option value="Africa/Casablanca">Western European Time (WET) - Casablanca</Option>
                          </Select>
                          {currentTime && (
                            <div style={{ 
                              marginTop: '8px', 
                              padding: '8px 12px', 
                              backgroundColor: '#f6ffed', 
                              border: '1px solid #b7eb8f', 
                              borderRadius: '6px',
                              fontSize: '13px',
                              color: '#389e0d'
                            }}>
                              📍 Current time in your timezone: <strong>{currentTime}</strong>
                            </div>
                          )}
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="bio"
                      label="Bio"
                    >
                      <TextArea rows={4} placeholder="Tell us about yourself..." />
                    </Form.Item>
                  </Form>
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* Security Settings */}
          <TabPane
            tab={
              <span>
                <LockOutlined />
                Security
              </span>
            }
            key="security"
          >
            <Row gutter={24}>
              <Col span={12}>
                <Card title="Change Password">
                  <Form
                    form={passwordForm}
                    layout="vertical"
                    onFinish={handlePasswordChange}
                  >
                    <Form.Item
                      name="currentPassword"
                      label="Current Password"
                      rules={[{ required: true, message: 'Please enter current password' }]}
                    >
                      <Input.Password
                        prefix={<LockOutlined />}
                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                      />
                    </Form.Item>

                    <Form.Item
                      name="newPassword"
                      label="New Password"
                      rules={[
                        { required: true, message: 'Please enter new password' },
                        { min: 8, message: 'Password must be at least 8 characters' }
                      ]}
                    >
                      <Input.Password
                        prefix={<LockOutlined />}
                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                      />
                    </Form.Item>

                    <Form.Item
                      name="confirmPassword"
                      label="Confirm New Password"
                      rules={[{ required: true, message: 'Please confirm new password' }]}
                    >
                      <Input.Password
                        prefix={<LockOutlined />}
                        iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                      />
                    </Form.Item>

                    <Button 
                      type="primary" 
                      htmlType="submit" 
                      loading={loading}
                      block
                    >
                      Change Password
                    </Button>
                  </Form>

                  <div style={{ marginTop: '16px' }}>
                    <Text type="secondary">
                      Password last changed: {securitySettings.passwordLastChanged}
                    </Text>
                  </div>
                </Card>

                <Card title="Two-Factor Authentication" style={{ marginTop: '16px' }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Two-Factor Authentication</Text>
                        <br />
                        <Text type="secondary">Add an extra layer of security to your account</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={securitySettings.twoFactorEnabled}
                        onChange={handleTwoFactorToggle}
                      />
                    </div>
                    
                    {securitySettings.twoFactorEnabled && (
                      <Alert
                        message="Two-factor authentication enabled"
                        description="Your account is protected by two-factor authentication."
                        type="success"
                        showIcon
                      />
                    )}
                  </Space>
                </Card>
              </Col>

              <Col span={12}>
                <Card title="Login Sessions">
                  <List
                    dataSource={securitySettings.loginSessions}
                    renderItem={(session) => (
                      <List.Item
                        actions={[
                          session.current ? (
                            <Tag color="green">Current Session</Tag>
                          ) : (
                            <Button 
                              type="link" 
                              danger 
                              size="small"
                              onClick={() => handleLogoutSession(session.id)}
                            >
                              Logout
                            </Button>
                          )
                        ]}
                      >
                        <List.Item.Meta
                          avatar={<Avatar icon={<EnvironmentOutlined />} />}
                          title={session.device}
                          description={
                            <Space direction="vertical" size="small">
                              <Text type="secondary">{session.location}</Text>
                              <Text type="secondary">Last access: {session.lastAccess}</Text>
                            </Space>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* Notification Settings */}
          <TabPane
            tab={
              <span>
                <BellOutlined />
                Notifications
              </span>
            }
            key="notifications"
          >
            <Card 
              title="Notification Preferences"
              extra={
                <Button 
                  type="primary" 
                  icon={<SaveOutlined />}
                  loading={loading}
                  onClick={handleNotificationSave}
                >
                  Save Settings
                </Button>
              }
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Title level={5}>Notification Methods</Title>
                  <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Email Notifications</Text>
                        <br />
                        <Text type="secondary">Receive notifications via email</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.emailNotifications}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, emailNotifications: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Push Notifications</Text>
                        <br />
                        <Text type="secondary">Browser push notifications</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.pushNotifications}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, pushNotifications: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>SMS Notifications</Text>
                        <br />
                        <Text type="secondary">Important event SMS alerts</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.smsNotifications}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, smsNotifications: checked }))
                        }
                      />
                    </div>
                  </Space>
                </Col>

                <Col span={12}>
                  <Title level={5}>Notification Content</Title>
                  <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Project Updates</Text>
                        <br />
                        <Text type="secondary">Project status change notifications</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.projectUpdates}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, projectUpdates: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Team Mentions</Text>
                        <br />
                        <Text type="secondary">Notifications when mentioned</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.teamMentions}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, teamMentions: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>System Maintenance</Text>
                        <br />
                        <Text type="secondary">System maintenance and update notifications</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.systemMaintenance}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, systemMaintenance: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Weekly Digest</Text>
                        <br />
                        <Text type="secondary">Weekly project summary</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.weeklyDigest}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, weeklyDigest: checked }))
                        }
                      />
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                      <div style={{ flex: 1, marginRight: '20px' }}>
                        <Text strong>Marketing Emails</Text>
                        <br />
                        <Text type="secondary">Product updates and event information</Text>
                      </div>
                      <Switch
                        size="small"
                        checked={notificationSettings.marketingEmails}
                        onChange={(checked) => 
                          setNotificationSettings(prev => ({ ...prev, marketingEmails: checked }))
                        }
                      />
                    </div>
                  </Space>
                </Col>
              </Row>

              <Divider />

              <Title level={5}>Quiet Hours</Title>
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingRight: '16px' }}>
                    <div style={{ flex: 1, marginRight: '20px' }}>
                      <Text strong>Enable Quiet Hours</Text>
                    </div>
                    <Switch
                      size="small"
                      checked={notificationSettings.quietHoursEnabled}
                      onChange={(checked) => 
                        setNotificationSettings(prev => ({ ...prev, quietHoursEnabled: checked }))
                      }
                    />
                  </div>
                </Col>
                {notificationSettings.quietHoursEnabled && (
                  <>
                    <Col span={8}>
                      <Space>
                        <Text>Start Time: </Text>
                        <TimePicker
                          value={dayjs(notificationSettings.quietHoursStart, 'HH:mm')}
                          format="HH:mm"
                          onChange={(time) =>
                            setNotificationSettings(prev => ({
                              ...prev,
                              quietHoursStart: time?.format('HH:mm') || '22:00'
                            }))
                          }
                        />
                      </Space>
                    </Col>
                    <Col span={8}>
                      <Space>
                        <Text>End Time: </Text>
                        <TimePicker
                          value={dayjs(notificationSettings.quietHoursEnd, 'HH:mm')}
                          format="HH:mm"
                          onChange={(time) =>
                            setNotificationSettings(prev => ({
                              ...prev,
                              quietHoursEnd: time?.format('HH:mm') || '08:00'
                            }))
                          }
                        />
                      </Space>
                    </Col>
                  </>
                )}
              </Row>
            </Card>
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default Profile; 