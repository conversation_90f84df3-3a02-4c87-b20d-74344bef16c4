@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

.lazy-image-container {
  position: relative;
  overflow: hidden;
}

.lazy-image {
  transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
}

.lazy-image-loading {
  filter: blur(10px);
  opacity: 0.6;
}

.lazy-image-loaded {
  filter: blur(0);
  opacity: 1;
}

.lazy-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-image-pulse {
  width: 30%;
  height: 30%;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  animation: pulse 1.5s infinite ease-in-out;
}
