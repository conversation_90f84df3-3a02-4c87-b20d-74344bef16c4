/* 统一组件样式 */

/* ===== 基础样式 ===== */
:root {
  /* 基础变量在ThemeContext中设置 */
  
  /* 字体大小缩小30% (原1.1倍改为0.77倍) */
  --font-size-xs: calc(12px * 0.77);
  --font-size-sm: calc(14px * 0.77);
  --font-size-md: calc(16px * 0.77);
  --font-size-lg: calc(18px * 0.77);
  --font-size-xl: calc(20px * 0.77);
  --font-size-xxl: calc(24px * 0.77);
  
  /* 行高减少到70% */
  --line-height: 0.7;
}

body {
  font-family: var(--font-family, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Microsoft YaHei', sans-serif);
  background-color: var(--background);
  color: var(--text-primary);
  line-height: var(--line-height, 1.5);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== 布局组件 ===== */

/* 页面容器 */
.page-container {
  padding: var(--spacing-md, 16px);
  max-width: var(--layout-max-width, 1200px);
  margin: 0 auto;
}

/* 页面标题 */
.page-title {
  font-size: var(--font-size-xxl, 26.4px);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md, 16px);
}

/* 页面描述 */
.page-description {
  font-size: var(--font-size-md, 17.6px);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg, 24px);
}

/* 内容区块 */
.content-section {
  margin-bottom: var(--spacing-lg, 24px);
}

/* 内容区块标题 */
.section-title {
  font-size: var(--font-size-lg, 19.8px);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm, 8px);
}

/* 内容区块描述 */
.section-description {
  font-size: var(--font-size-sm, 15.4px);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md, 16px);
}

/* ===== 卡片组件 ===== */

/* 基础卡片 */
.card {
  background-color: var(--card-background);
  border-radius: var(--radius-md, 8px);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 卡片头部 */
.card-header {
  padding: var(--spacing-md, 16px);
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 卡片标题 */
.card-title {
  font-size: var(--font-size-lg, 19.8px);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 卡片内容 */
.card-content {
  padding: var(--spacing-md, 16px);
}

/* 卡片底部 */
.card-footer {
  padding: var(--spacing-md, 16px);
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm, 8px);
}

/* 统计卡片 */
.stat-card {
  padding: var(--spacing-md, 16px);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.stat-card__title {
  font-size: var(--font-size-sm, 15.4px);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs, 4px);
}

.stat-card__value {
  font-size: var(--font-size-xxl, 26.4px);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs, 4px);
}

.stat-card__trend {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs, 13.2px);
  margin-top: auto;
}

.stat-card__trend--up {
  color: var(--success);
}

.stat-card__trend--down {
  color: var(--danger);
}

/* ===== 表单组件 ===== */

/* 表单容器 */
.form-container {
  margin-bottom: var(--spacing-lg, 24px);
}

/* 表单分组 */
.form-section {
  margin-bottom: var(--spacing-lg, 24px);
}

/* 表单分组标题 */
.form-section-title {
  font-size: var(--font-size-lg, 19.8px);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm, 8px);
}

/* 表单分组描述 */
.form-section-description {
  font-size: var(--font-size-sm, 15.4px);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md, 16px);
}

/* 表单项 */
.form-item {
  margin-bottom: var(--spacing-md, 16px);
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: var(--font-size-sm, 15.4px);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs, 4px);
}

/* 必填标记 */
.form-label-required::after {
  content: '*';
  color: var(--danger);
  margin-left: 4px;
}

/* 表单输入框 */
.form-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--font-size-sm, 15.4px);
  border: 1px solid var(--border);
  border-radius: var(--radius-sm, 4px);
  background-color: var(--card-background);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 表单帮助文本 */
.form-help-text {
  font-size: var(--font-size-xs, 13.2px);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs, 4px);
}

/* 表单错误文本 */
.form-error-text {
  font-size: var(--font-size-xs, 13.2px);
  color: var(--danger);
  margin-top: var(--spacing-xs, 4px);
}

/* 表单操作区 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm, 8px);
  margin-top: var(--spacing-lg, 24px);
}

/* ===== 按钮组件 ===== */

/* 基础按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--font-size-sm, 15.4px);
  font-weight: 500;
  border-radius: var(--radius-sm, 4px);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

/* 主要按钮 */
.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  filter: brightness(1.1);
}

/* 次要按钮 */
.btn-secondary {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.btn-secondary:hover {
  background-color: var(--gray-light);
}

/* 危险按钮 */
.btn-danger {
  background-color: var(--danger);
  color: white;
}

.btn-danger:hover {
  filter: brightness(1.1);
}

/* 成功按钮 */
.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-success:hover {
  filter: brightness(1.1);
}

/* 警告按钮 */
.btn-warning {
  background-color: var(--warning);
  color: white;
}

.btn-warning:hover {
  filter: brightness(1.1);
}

/* 链接按钮 */
.btn-link {
  background-color: transparent;
  color: var(--primary);
  padding: 0;
}

.btn-link:hover {
  text-decoration: underline;
}

/* 图标按钮 */
.btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: var(--radius-sm, 4px);
}

/* ===== 表格组件 ===== */

/* 表格容器 */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-md, 8px);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg, 24px);
}

/* 表格 */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: var(--card-background);
}

/* 表头 */
.table thead th {
  background-color: var(--gray-light);
  color: var(--text-secondary);
  font-weight: 600;
  text-align: left;
  padding: 12px 16px;
  font-size: var(--font-size-sm, 15.4px);
  border-bottom: 1px solid var(--border);
}

/* 表格单元格 */
.table tbody td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border);
  color: var(--text-primary);
  font-size: var(--font-size-sm, 15.4px);
}

/* 表格行悬停效果 */
.table tbody tr:hover td {
  background-color: var(--hover);
}

/* 表格行选中效果 */
.table tbody tr.selected td {
  background-color: var(--primary-light);
}

/* 表格数字列居中 */
.table .numeric {
  text-align: center;
}

/* 表格空状态 */
.table-empty {
  padding: var(--spacing-lg, 24px);
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-md, 17.6px);
}

/* ===== 标签组件 ===== */

/* 基础标签 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--radius-sm, 4px);
  font-size: var(--font-size-xs, 13.2px);
  font-weight: 500;
}

/* 主要标签 */
.tag-primary {
  background-color: var(--primary-light);
  color: var(--primary);
}

/* 成功标签 */
.tag-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

/* 警告标签 */
.tag-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

/* 危险标签 */
.tag-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

/* 信息标签 */
.tag-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info);
}

/* ===== 响应式工具类 ===== */

/* 隐藏元素 */
.hidden {
  display: none !important;
}

/* 在小屏幕上隐藏 */
@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

/* 在中等屏幕上隐藏 */
@media (max-width: 992px) and (min-width: 769px) {
  .hidden-md {
    display: none !important;
  }
}

/* 在大屏幕上隐藏 */
@media (min-width: 993px) {
  .hidden-lg {
    display: none !important;
  }
}

/* 强制缩小AntD上传ICON为16px */
.ant-upload-drag-icon {
  font-size: 16px !important;
  line-height: 1 !important;
}
