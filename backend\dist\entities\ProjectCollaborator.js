"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectCollaborator = exports.InvitationStatus = exports.CollaboratorRole = void 0;
const typeorm_1 = require("typeorm");
const Project_1 = require("./Project");
const User_1 = require("./User");
var CollaboratorRole;
(function (CollaboratorRole) {
    CollaboratorRole["OWNER"] = "owner";
    CollaboratorRole["ADMIN"] = "admin";
    CollaboratorRole["EDITOR"] = "editor";
    CollaboratorRole["VIEWER"] = "viewer";
    CollaboratorRole["GUEST_EDITOR"] = "guest_editor";
    CollaboratorRole["GUEST_VIEWER"] = "guest_viewer";
})(CollaboratorRole = exports.CollaboratorRole || (exports.CollaboratorRole = {}));
var InvitationStatus;
(function (InvitationStatus) {
    InvitationStatus["PENDING"] = "pending";
    InvitationStatus["ACCEPTED"] = "accepted";
    InvitationStatus["DECLINED"] = "declined";
    InvitationStatus["EXPIRED"] = "expired";
})(InvitationStatus = exports.InvitationStatus || (exports.InvitationStatus = {}));
let ProjectCollaborator = class ProjectCollaborator {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Project_1.Project, project => project.collaborators, { onDelete: 'CASCADE' }),
    __metadata("design:type", Project_1.Project)
], ProjectCollaborator.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, { nullable: true, onDelete: 'CASCADE' }),
    __metadata("design:type", User_1.User)
], ProjectCollaborator.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "invitedEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: CollaboratorRole.VIEWER
    }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "role", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: InvitationStatus.PENDING
    }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], ProjectCollaborator.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "invitedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "invitationMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectCollaborator.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectCollaborator.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ProjectCollaborator.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], ProjectCollaborator.prototype, "lastAccessedAt", void 0);
ProjectCollaborator = __decorate([
    (0, typeorm_1.Entity)('project_collaborators'),
    (0, typeorm_1.Index)(['project', 'user'], { unique: true })
], ProjectCollaborator);
exports.ProjectCollaborator = ProjectCollaborator;
//# sourceMappingURL=ProjectCollaborator.js.map