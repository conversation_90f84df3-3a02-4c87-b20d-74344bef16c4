.project-card {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f6f7 100%);
  border-radius: var(--radius-lg);
  padding: 18px;
  height: 280px; /* Fixed moderate height */
  min-height: 280px;
  max-height: 320px;
  width: 100%;
  max-width: 280px; /* 进一步缩小宽度，支持一行4个卡片 */
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid #e1e5e9;
  /* 多层阴影效果，营造强烈悬浮感 */
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.04),
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 8px 20px rgba(0, 0, 0, 0.06);
  margin: 0 auto; /* 居中对齐 */
  position: relative;
  transform: translateY(0); /* 默认位置 */
  overflow: hidden; /* 防止内容溢出 */
  word-wrap: break-word; /* 强制换行长单词 */
  box-sizing: border-box; /* 确保padding包含在总尺寸内 */
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.3) 100%);
  border-radius: var(--radius-lg);
  pointer-events: none;
}

/* 添加边缘光晕效果 */
.project-card::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.05) 50%, rgba(24, 144, 255, 0.1) 100%);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.project-card:hover {
  transform: translateY(-6px); /* 悬停时更明显的上浮 */
  /* 悬停时更强的多层阴影 */
  box-shadow: 
    0 4px 8px rgba(0, 0, 0, 0.06),
    0 8px 20px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.10);
  border-color: var(--primary);
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
}

.project-card:hover::after {
  opacity: 1; /* 悬停时显示边缘光晕 */
}

.project-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  position: relative;
  z-index: 2;
}

.project-card__title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 2;
  word-wrap: break-word; /* 长单词换行 */
  hyphens: auto; /* 自动断字 */
  max-height: 2.8em; /* 限制最大高度为2行 */
}

.project-card__title a {
  color: inherit;
  text-decoration: none;
}

.project-card__title a:hover {
  color: var(--primary);
}

.project-card__id {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.project-card__client {
  font-size: 15px;
  color: var(--text-secondary);
  margin-bottom: 20px;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 2;
}

.project-card__details {
  flex: 1;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
  overflow: hidden; /* 防止内容溢出 */
  min-height: 0; /* 允许flex子项收缩 */
}

.project-card__detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
}

.project-card__detail-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.project-card__detail-item .value {
  color: var(--text-primary);
  font-weight: 600;
  text-align: right;
  flex-shrink: 0;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* 确保单行显示 */
  word-break: break-all; /* 强制长单词换行 */
}

.project-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e8eaed;
  position: relative;
  z-index: 2;
  min-height: 48px; /* 确保足够高度 */
  gap: 8px; /* 添加间距 */
  flex-shrink: 0; /* 防止收缩 */
}

.project-card__owner {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
  overflow: hidden; /* 防止内容溢出 */
  max-width: calc(100% - 140px); /* 为按钮组预留空间 */
}

.project-card__owner-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.project-card__owner-name {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
  flex-shrink: 0;
}

.status-tag.blue {
  background-color: #e6f3ff;
  color: #1890ff;
  border-color: #b3d9ff;
}

.status-tag.green {
  background-color: #f0f9e8;
  color: #52c41a;
  border-color: #b7e084;
}

.status-tag.purple {
  background-color: #f4f0ff;
  color: #722ed1;
  border-color: #d3c4f0;
}

.status-tag.orange {
  background-color: #fff7e6;
  color: #fa8c16;
  border-color: #ffd591;
}

.delete-button:hover {
  background-color: var(--red-1);
}

/* Action buttons styling */
.project-card__footer .ant-btn {
  border: none;
  box-shadow: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  flex-shrink: 0; /* 防止按钮收缩 */
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-width: 28px;
}

.project-card__footer .ant-btn:hover {
  background-color: var(--primary-light);
  color: var(--primary);
  transform: scale(1.05);
}

.project-card__footer .ant-btn.delete-button:hover {
  background-color: #ffebee;
  color: #f5222d;
}

/* 确保Space组件不会溢出 */
.project-card__footer .ant-space {
  flex-shrink: 0;
  gap: 4px !important;
}

.project-card__footer .ant-space-item {
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .project-card {
    max-width: 260px; /* 中等屏幕稍小一些 */
  }
}

@media (max-width: 768px) {
  .project-card {
    padding: 16px;
    height: 260px;
    min-height: 260px;
    max-height: 280px;
    max-width: 250px; /* 平板端适配 */
  }

  .project-card__title {
    font-size: 16px;
  }

  .project-card__client {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .project-card__detail-item {
    font-size: 13px;
    margin-bottom: 8px;
  }

  .project-card__owner-avatar {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .project-card__owner-name {
    font-size: 13px;
  }

  .project-card__footer {
    padding-top: 12px;
  }
}

@media (max-width: 480px) {
  .project-card {
    padding: 14px;
    height: 240px;
    min-height: 240px;
    max-height: 260px;
    max-width: 240px; /* 手机端进一步缩小 */
  }

  .project-card__title {
    font-size: 15px;
  }

  .project-card__detail-item .value {
    max-width: 50%;
  }

  .status-tag {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 为不同屏幕大小调整网格布局 */
@media (min-width: 1201px) {
  .grid-container {
    grid-template-columns: repeat(4, 280px) !important; /* 大屏幕固定4列 */
  }
}

@media (max-width: 1200px) and (min-width: 901px) {
  .grid-container {
    grid-template-columns: repeat(3, 260px) !important; /* 中等屏幕3列 */
  }
}

@media (max-width: 900px) and (min-width: 601px) {
  .grid-container {
    grid-template-columns: repeat(2, 250px) !important; /* 平板2列 */
    gap: 16px !important;
  }
}

@media (max-width: 600px) {
  .grid-container {
    grid-template-columns: repeat(1, 240px) !important; /* 手机1列 */
    gap: 16px !important;
    padding: 16px !important;
  }
}

