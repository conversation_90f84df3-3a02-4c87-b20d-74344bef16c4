import { Router } from 'express';
import { getRepository } from 'typeorm';
import { ProjectStage } from '../entities/ProjectStage';
import { Project } from '../entities/Project';
import { authMiddleware } from '../middlewares/auth.middleware';
import { validateBody, validateParams } from '../middlewares/validation.middleware';
import {
  CreateProjectStageDto,
  UpdateProjectStageDto,
  ProjectStageIdParamDto,
  ProjectIdParamDto
} from '../dtos/project-stage.dto';

const router = Router();

// 使用认证中间件保护所有路由
router.use(authMiddleware);

// 获取项目的所有阶段
router.get('/project/:projectId', validateParams(ProjectIdParamDto), async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne({ where: { id: projectId } });

    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }

    const stageRepository = getRepository(ProjectStage);
    const stages = await stageRepository.find({
      where: { project: project },
      order: { createdAt: 'ASC' }
    });

    res.json(stages);
  } catch (error) {
    res.status(500).json({ message: '获取项目阶段失败', error });
  }
});

// 获取单个阶段
router.get('/:id', validateParams(ProjectStageIdParamDto), async (req, res) => {
  try {
    const stageRepository = getRepository(ProjectStage);
    const stage = await stageRepository.findOne({
      where: { id: req.params.id },
      relations: ['project']
    });

    if (!stage) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }

    res.json(stage);
  } catch (error) {
    res.status(500).json({ message: '获取项目阶段失败', error });
  }
});

// 创建新阶段
router.post('/', validateBody(CreateProjectStageDto), async (req, res) => {
  try {
    const { projectId, ...stageData } = req.body;

    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne({ where: { id: projectId } });

    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }

    const stageRepository = getRepository(ProjectStage);
    const stage = stageRepository.create({
      ...stageData,
      project
    });

    const result = await stageRepository.save(stage);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({ message: '创建项目阶段失败', error });
  }
});

// 更新阶段
router.put('/:id', validateParams(ProjectStageIdParamDto), validateBody(UpdateProjectStageDto), async (req, res) => {
  try {
    const stageRepository = getRepository(ProjectStage);
    const stage = await stageRepository.findOne({ where: { id: req.params.id } });

    if (!stage) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }

    stageRepository.merge(stage, req.body);
    const result = await stageRepository.save(stage);
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: '更新项目阶段失败', error });
  }
});

// 删除阶段
router.delete('/:id', validateParams(ProjectStageIdParamDto), async (req, res) => {
  try {
    const stageRepository = getRepository(ProjectStage);
    const stage = await stageRepository.findOne({ where: { id: req.params.id } });

    if (!stage) {
      return res.status(404).json({ message: '项目阶段不存在' });
    }

    await stageRepository.remove(stage);
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: '删除项目阶段失败', error });
  }
});

export default router;
