import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tabs, Modal, Form, Input, DatePicker, Select, Button, InputNumber, message, Card, Row, Col, Typography, Space, Tooltip, Popconfirm, Tag, List, Table } from 'antd';
import { PlusOutlined, BarChartOutlined, CalendarOutlined, TrophyOutlined, ExclamationCircleOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useCurrency } from '../../contexts/CurrencyContext';
import financeService from '../../services/finance.service';
import '../../styles/ltc-stages.css';

const { TabPane } = Tabs;
const { Option } = Select;
const { Text } = Typography;

// 定义Revenue Item Line数据结构
interface RevenueItemLine {
  id: string;
  name: string;
  type: 'NRC' | 'MRC';
  unitPrice: number;
  quantity: number; // For NRC: percent, for MRC: months
  lineTotal: number;
  vatRate: number;
  billingPeriod?: string;
}

// 定义销售发票数据结构 - 支持多个Revenue Items
interface Invoice {
  id: string;
  number: string;
  revenueItem: string; // 显示摘要
  type: string;
  amount: number; // subtotal
  vatAmount: number;
  totalAmount: number;
  invoiceDate: string;
  dueDate: string;
  status: string;
}

// 定义费用发票数据结构
interface ExpenseInvoice {
  id: string;
  number: string;
  budgetCategoryId: string;
  category: string;
  description: string;
  expense: string;
  type: string;
  currency: string;
  amount: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  invoiceDate: string;
  dueDate: string;
  status: string;
  approvedBy: string;
  reference: string;
}

interface InvoiceManagementProps {
  // 可以根据需要添加属性
}

const InvoiceManagement: React.FC<InvoiceManagementProps> = () => {
  const [activeTab, setActiveTab] = useState('sales');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const { getCurrencySymbol, formatAmount } = useCurrency();

  // 从localStorage加载Invoice数据
  const [invoices, setInvoices] = useState<Invoice[]>(() => {
    try {
      const saved = localStorage.getItem('finance_invoices');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Failed to load invoices from localStorage:', error instanceof Error ? error.message : String(error));
      return [];
    }
  });

  // 🔧 使用useRef避免无限循环
  const syncInvoiceStatusWithTransactionsRef = useRef<() => void>();
  
  // 自动同步Invoice状态与Transaction状态
  const syncInvoiceStatusWithTransactions = useCallback(() => {
    try {
      const transactionsData = localStorage.getItem('finance_transactions');
      if (!transactionsData) return;

      // 🔧 从localStorage获取最新的发票数据，而不依赖状态
      const invoicesData = localStorage.getItem('finance_invoices');
      if (!invoicesData) return;
      
      const currentInvoices = JSON.parse(invoicesData);
      const transactions = JSON.parse(transactionsData);
      const incomeTransactions = transactions.filter((tx: any) => tx.type === 'income');

      console.log('🔄 Syncing invoice status with transactions:', {
        totalTransactions: transactions.length,
        incomeTransactions: incomeTransactions.length,
        totalInvoices: currentInvoices.length
      });

      const updatedInvoices = currentInvoices.map((invoice: Invoice) => {
        // 🎯 增强的发票匹配逻辑 - 支持多种匹配方式
        const matchingTransactions = incomeTransactions.filter((tx: any) => {
          // 方式1: 通过invoiceNumber字段直接匹配
          if (tx.invoiceNumber && tx.invoiceNumber === invoice.number) {
            return true;
          }
          
          // 方式2: 通过revenueItem匹配
          if (tx.revenueItem === invoice.revenueItem) {
            return true;
          }
          
          // 方式3: 通过description中包含invoice number
          if (tx.description && tx.description.includes(invoice.number)) {
            return true;
          }
          
          // 方式4: 通过tags中包含invoice number
          if (tx.tags && Array.isArray(tx.tags) && tx.tags.includes(invoice.number)) {
            return true;
          }
          
          return false;
        });

        if (matchingTransactions.length === 0) {
          console.log(`❌ No matching transactions for invoice: ${invoice.number}`);
          return invoice;
        }

        // 减少频繁日志输出
        if (matchingTransactions.length > 0) {
          console.log(`🔍 Found ${matchingTransactions.length} matching transactions for invoice ${invoice.number}`);
        }

        // 💰 计算已支付金额和更新状态
        let paidAmount = 0;
        let hasCompletedPayments = false;
        
        matchingTransactions.forEach((tx: any) => {
          if (['Completed', 'Paid', 'completed', 'paid'].includes(tx.status)) {
            paidAmount += tx.totalAmount || 0;
            hasCompletedPayments = true;
            // 减少频繁日志输出
          }
        });

        // 🎯 智能状态更新逻辑
        let newStatus = invoice.status;
        
        if (paidAmount >= invoice.totalAmount) {
          newStatus = 'Paid';
        } else if (paidAmount > 0) {
          newStatus = 'Partial'; // 部分支付状态
        } else if (hasCompletedPayments) {
          // 如果有completed状态的交易但金额为0，可能是数据问题
          console.warn(`⚠️ Invoice ${invoice.number} has completed transactions but 0 amount`);
        } else {
          // 检查是否有过期的发票
          const dueDate = dayjs(invoice.dueDate);
          const today = dayjs();
          if (dueDate.isBefore(today) && newStatus === 'Pending') {
            newStatus = 'Overdue';
          }
        }

        // 🔄 返回更新的发票（包含支付金额信息）
        const updatedInvoice = { 
          ...invoice, 
          status: newStatus,
          // 🔧 安全处理：确保所有金额字段都有有效数值
          amount: invoice.amount || 0,
          vatAmount: invoice.vatAmount || 0,
          totalAmount: invoice.totalAmount || 0,
          paidAmount: paidAmount, // 添加已付金额字段
          pendingAmount: Math.max(0, (invoice.totalAmount || 0) - paidAmount) // 添加待付金额字段
        };
        
        if (updatedInvoice.status !== invoice.status) {
          console.log(`🔄 Status changed for invoice ${invoice.number}: ${invoice.status} → ${newStatus}`);
        }
        
        return updatedInvoice;
      });

      // 💾 检查并保存状态变化 - 🔧 FIX: 改进变化检测逻辑
      let hasChanges = false;
      
      // 检查是否有任何发票状态变化
      for (let i = 0; i < updatedInvoices.length; i++) {
        const updatedInvoice = updatedInvoices[i];
        const originalInvoice = currentInvoices[i];
        
        if (!originalInvoice) {
          hasChanges = true;
          break;
        }
        
        if (updatedInvoice.status !== originalInvoice.status) {
          console.log(`🔄 Status change detected for ${updatedInvoice.number}: ${originalInvoice.status} → ${updatedInvoice.status}`);
          hasChanges = true;
        }
        
        if ((updatedInvoice as any).paidAmount !== (originalInvoice as any)?.paidAmount) {
          console.log(`💰 Paid amount change detected for ${updatedInvoice.number}: ${(originalInvoice as any)?.paidAmount || 0} → ${(updatedInvoice as any).paidAmount || 0}`);
          hasChanges = true;
        }
      }

      // 🔧 FIX: 强制更新UI状态，即使没有检测到变化（可能是初始化问题）
      setInvoices(updatedInvoices);
      localStorage.setItem('finance_invoices', JSON.stringify(updatedInvoices));
      
      // 🆕 触发Invoice数据更新事件
      window.dispatchEvent(new Event('invoiceDataUpdated'));
      
      if (hasChanges) {
        console.log('✅ Invoice statuses synchronized with transactions - changes saved');
        
        // 触发成功提示
        const paidCount = updatedInvoices.filter((inv: Invoice) => inv.status === 'Paid').length;
        if (paidCount > 0) {
          message.success(`${paidCount} invoice(s) updated to Paid status`);
        }
      } else {
        console.log('ℹ️ No changes detected in invoice statuses - but UI state updated anyway');
      }
    } catch (error) {
      console.error('❌ Failed to sync invoice status with transactions:', error instanceof Error ? error.message : String(error));
      message.error('Failed to sync invoice statuses');
    }
  }, []); // 🔧 移除invoices依赖避免无限循环
  
  // 🔧 设置ref引用
  syncInvoiceStatusWithTransactionsRef.current = syncInvoiceStatusWithTransactions;

  // 🆕 费用发票状态同步逻辑
  const syncExpenseInvoiceStatusWithTransactions = useCallback(() => {
    try {
      const transactionsData = localStorage.getItem('finance_transactions');
      if (!transactionsData) return;

      const expenseInvoicesData = localStorage.getItem('finance_expense_invoices');
      if (!expenseInvoicesData) return;

      const currentExpenseInvoices = JSON.parse(expenseInvoicesData);
      const transactions = JSON.parse(transactionsData);
      const expenseTransactions = transactions.filter((tx: any) => tx.type === 'expense');

      console.log('🔄 Syncing expense invoice status with transactions:', {
        totalTransactions: transactions.length,
        expenseTransactions: expenseTransactions.length,
        totalExpenseInvoices: currentExpenseInvoices.length
      });

      let hasChanges = false;
      const updatedExpenseInvoices = currentExpenseInvoices.map((invoice: ExpenseInvoice) => {
        // 查找匹配的费用交易
        const matchingTransactions = expenseTransactions.filter((tx: any) => {
          // 通过发票号匹配
          if (tx.invoiceNumber && tx.invoiceNumber === invoice.number) {
            return true;
          }

          // 通过描述中包含发票号匹配
          if (tx.description && tx.description.includes(invoice.number)) {
            return true;
          }

          // 通过预算项目匹配
          if (tx.category && invoice.expense && tx.category === invoice.expense) {
            return true;
          }

          return false;
        });

        if (matchingTransactions.length === 0) {
          return invoice;
        }

        // 计算已支付金额
        const paidAmount = matchingTransactions
          .filter((tx: any) => ['completed', 'Completed', 'paid', 'Paid'].includes(tx.status))
          .reduce((sum: number, tx: any) => sum + (tx.totalAmount || tx.amount || 0), 0);

        // 确定新状态
        let newStatus = invoice.status;
        if (paidAmount >= invoice.totalAmount) {
          newStatus = 'Paid';
        } else if (paidAmount > 0) {
          newStatus = 'Partial';
        } else {
          // 检查是否有过期的发票
          const dueDate = dayjs(invoice.dueDate);
          const today = dayjs();
          if (dueDate.isBefore(today) && newStatus === 'Pending') {
            newStatus = 'Overdue';
          }
        }

        if (newStatus !== invoice.status) {
          hasChanges = true;
          console.log(`📝 Updating expense invoice ${invoice.number}: ${invoice.status} → ${newStatus}`);
        }

        return {
          ...invoice,
          status: newStatus,
          paidAmount: paidAmount,
          pendingAmount: Math.max(0, invoice.totalAmount - paidAmount)
        };
      });

      // 更新状态和localStorage
      setExpenseInvoices(updatedExpenseInvoices);
      localStorage.setItem('finance_expense_invoices', JSON.stringify(updatedExpenseInvoices));

      // 触发更新事件
      window.dispatchEvent(new Event('invoiceDataUpdated'));

      if (hasChanges) {
        console.log('✅ Expense invoice statuses synchronized with transactions');
        const paidCount = updatedExpenseInvoices.filter((inv: ExpenseInvoice) => inv.status === 'Paid').length;
        if (paidCount > 0) {
          message.success(`${paidCount} expense invoice(s) updated to Paid status`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to sync expense invoice status with transactions:', error instanceof Error ? error.message : String(error));
    }
  }, []);
  
  const [expenseInvoices, setExpenseInvoices] = useState<ExpenseInvoice[]>(() => {
    try {
      const saved = localStorage.getItem('finance_expense_invoices');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Failed to load expense invoices from localStorage:', error instanceof Error ? error.message : String(error));
      return [];
    }
  });
  
  // Revenue Items状态 - 从Revenue Management获取
  const [revenueItems, setRevenueItems] = useState<Array<{id: string, name: string, type: 'NRC' | 'MRC', amount: number, currency?: string, vatRate?: number}>>([]);
  
  // 选中的Revenue Items状态 - 用于多选发票
  const [selectedRevenueItems, setSelectedRevenueItems] = useState<RevenueItemLine[]>([]);
  
  // 编辑状态
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null);
  const [editingExpenseInvoice, setEditingExpenseInvoice] = useState<ExpenseInvoice | null>(null);
  const [selectedBudgetItem, setSelectedBudgetItem] = useState<any>(null);

  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (changedValues.invoiceDate || changedValues.paymentTermDays || changedValues.paymentTermUnit) {
      const { invoiceDate, paymentTermDays, paymentTermUnit } = allValues;
  
      if (invoiceDate && paymentTermDays !== null && paymentTermDays !== undefined && paymentTermUnit) {
        let dueDate = dayjs(invoiceDate);
        if (paymentTermUnit === 'calendar') {
          dueDate = dueDate.add(paymentTermDays, 'day');
        } else { // working days
          let daysAdded = 0;
          let currentDate = dayjs(invoiceDate);
          while (daysAdded < paymentTermDays) {
            currentDate = currentDate.add(1, 'day');
            if (currentDate.day() !== 0 && currentDate.day() !== 6) {
              daysAdded++;
            }
          }
          dueDate = currentDate;
        }
        // 强制更新Due Date字段并触发重新渲染
        form.setFieldsValue({ dueDate: dueDate });
        console.log('Due Date calculated:', dueDate.format('DD/MM/YYYY'));
      }
    }
  };

  // 提取loadRevenueItems函数，可以在多处调用
  const loadRevenueItems = React.useCallback(async () => {
    try {
      console.log('🔄 Loading Revenue Items from localStorage...');
      let allItems: Array<{id: string, name: string, type: 'NRC' | 'MRC', amount: number, currency?: string, vatRate?: number}> = [];
      
      // 获取当前项目ID (从多种方式尝试获取)
      const urlParams = new URLSearchParams(window.location.search);
      let currentProjectId = urlParams.get('projectId') || urlParams.get('id');
      
      // 如果URL参数中没有，尝试从路径中获取
      if (!currentProjectId) {
        const pathSegments = window.location.pathname.split('/');
        const projectIndex = pathSegments.findIndex(segment => segment === 'project');
        if (projectIndex !== -1 && projectIndex + 1 < pathSegments.length) {
          currentProjectId = pathSegments[projectIndex + 1];
        } else {
          // 尝试最后一个路径段落
          currentProjectId = pathSegments[pathSegments.length - 1];
        }
      }
      
      // 如果还是没有，尝试从localStorage中获取最近的项目ID
      if (!currentProjectId || currentProjectId === 'finance') {
        const recentProjectId = localStorage.getItem('current_project_id') || localStorage.getItem('selected_project_id');
        if (recentProjectId) {
          currentProjectId = recentProjectId;
          console.log(`🔄 Using stored project ID: ${currentProjectId}`);
        }
      }
      
      if (currentProjectId) {
        console.log(`🔍 Loading Revenue Items for project: ${currentProjectId}`);
        
        // 🔧 FIX: 优先使用组合数据源，与Revenue Management保持一致
        const combinedKey = `finance_revenue_items_${currentProjectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        
        if (combinedData) {
          console.log(`📊 Found combined revenue data in ${combinedKey}`);
          const combinedItems = JSON.parse(combinedData);
          
          const formattedItems = combinedItems.map((item: any) => ({
            id: item.id || `${item.type}-${Date.now()}-${Math.random()}`,
            name: item.name,
            type: item.type === 'nrc' ? 'NRC' : 'MRC',
            amount: item.type === 'mrc' ? (item.unitPrice || item.amount || 0) : (item.amount || 0),
            currency: item.currency,
            vatRate: item.vatRate
          }));
          
          allItems = formattedItems;
          console.log('✅ Using combined revenue data:', formattedItems);
        } else {
          // 备用方案：从单独的NRC和MRC存储中加载
          console.log('⚠️ Combined revenue data not found, loading from separate storages...');
          
          const nrcKey = `nrc_items_${currentProjectId}`;
          const mrcKey = `mrc_items_${currentProjectId}`;
          
          try {
            // 加载NRC数据
            const nrcData = localStorage.getItem(nrcKey);
            if (nrcData) {
              const nrcItems = JSON.parse(nrcData);
              console.log(`📊 Found NRC data in ${nrcKey}:`, nrcItems);
              
              const nrcFormatted = nrcItems.map((item: any) => ({
                id: item.id || `nrc-${Date.now()}-${Math.random()}`,
                name: item.name,
                type: 'NRC' as const,
                amount: item.amount || item.totalAmount || 0,
                currency: item.currency,
                vatRate: item.vatRate
              }));
              
              allItems = [...allItems, ...nrcFormatted];
            }
            
            // 加载MRC数据
            const mrcData = localStorage.getItem(mrcKey);
            if (mrcData) {
              const mrcItems = JSON.parse(mrcData);
              console.log(`📊 Found MRC data in ${mrcKey}:`, mrcItems);
              
              const mrcFormatted = mrcItems.map((item: any) => ({
                id: item.id || `mrc-${Date.now()}-${Math.random()}`,
                name: item.name,
                type: 'MRC' as const,
                amount: item.unitPrice || item.amount || 0,
                currency: item.currency,
                vatRate: item.vatRate
              }));
              
              allItems = [...allItems, ...mrcFormatted];
            }
          } catch (error) {
            console.warn(`Failed to parse revenue data:`, error instanceof Error ? error.message : String(error));
          }
        }
      } else {
        console.warn('❌ No project ID found, scanning all localStorage keys...');
        
        // 备用方案：扫描所有localStorage键
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (!key) continue;
          
          try {
            // 优先查找组合数据
            if (key.startsWith('finance_revenue_items_')) {
              const data = JSON.parse(localStorage.getItem(key) || '[]');
              console.log(`📊 Found combined revenue data in ${key}:`, data);
              
              const formattedItems = data.map((item: any) => ({
                id: item.id || `${item.type}-${Date.now()}-${Math.random()}`,
                name: item.name,
                type: item.type === 'nrc' ? 'NRC' : 'MRC',
                amount: item.type === 'mrc' ? (item.unitPrice || item.amount || 0) : (item.amount || 0),
                currency: item.currency,
                vatRate: item.vatRate
              }));
              
              allItems = [...allItems, ...formattedItems];
              continue; // 如果找到组合数据，跳过单独数据
            }
            
            if (key.startsWith('nrc_items_')) {
              const data = JSON.parse(localStorage.getItem(key) || '[]');
              console.log(`📊 Found NRC data in ${key}:`, data);
              
              const nrcFormatted = data.map((item: any) => ({
                id: item.id || `nrc-${Date.now()}-${Math.random()}`,
                name: item.name,
                type: 'NRC' as const,
                amount: item.amount || item.totalAmount || 0,
                currency: item.currency,
                vatRate: item.vatRate
              }));
              
              allItems = [...allItems, ...nrcFormatted];
            }
            
            if (key.startsWith('mrc_items_')) {
              const data = JSON.parse(localStorage.getItem(key) || '[]');
              console.log(`📊 Found MRC data in ${key}:`, data);
              
              const mrcFormatted = data.map((item: any) => ({
                id: item.id || `mrc-${Date.now()}-${Math.random()}`,
                name: item.name,
                type: 'MRC' as const,
                amount: item.unitPrice || item.amount || 0,
                currency: item.currency,
                vatRate: item.vatRate
              }));
              
              allItems = [...allItems, ...mrcFormatted];
            }
          } catch (error) {
            console.warn(`Failed to parse data from ${key}:`, error instanceof Error ? error.message : String(error));
          }
        }
      }
      
      // 去重
      const uniqueItems = allItems.filter((item, index, self) => 
        index === self.findIndex(t => t.name === item.name && t.type === item.type)
      );
      
      // 新增排序逻辑
      const sortedItems = uniqueItems.sort((a, b) => {
        // 规则1: NRC排在MRC前面
        if (a.type === 'NRC' && b.type === 'MRC') return -1;
        if (a.type === 'MRC' && b.type === 'NRC') return 1;
        // 规则2: 同类型下按金额从高到低排序
        return b.amount - a.amount;
      });
      
      console.log('✅ Final Sorted Revenue Items loaded for Invoice dropdown:', sortedItems);
      setRevenueItems(sortedItems);
      
      // 不再提供默认选项，让用户自己在Revenue Management中创建
      if (sortedItems.length === 0) {
        console.log('ℹ️ No Revenue Items found in localStorage - user needs to create them in Revenue Management first');
        setRevenueItems([]);
      }
    } catch (error) {
      console.error('❌ Failed to load revenue items:', error instanceof Error ? error.message : String(error));
      // 出错时设置为空数组，不提供默认数据
      setRevenueItems([]);
    }
  }, []);

  // 加载Revenue Items数据
  useEffect(() => {
    loadRevenueItems();
  }, [loadRevenueItems]);

  // 🚨 EMERGENCY FIX: Create matching transaction for SINV-2025-001 (ONE-TIME ONLY)
  useEffect(() => {
    const EMERGENCY_FIX_KEY = 'emergency_fix_sinv_2025_001_completed';
    
    // Check if emergency fix already completed
    if (localStorage.getItem(EMERGENCY_FIX_KEY)) {
      return; // Skip if already completed
    }
    
    const emergencyFixInvoiceSync = () => {
      try {
        console.log('🚨 EMERGENCY FIX: Checking SINV-2025-001 transaction sync...');
        
        // Check if SINV-2025-001 exists and is Pending
        const targetInvoiceNumber = 'SINV-2025-001';
        const targetInvoice = invoices.find(inv => inv.number === targetInvoiceNumber);
        
        if (!targetInvoice) {
          console.log('📄 SINV-2025-001 not found, marking fix as completed');
          localStorage.setItem(EMERGENCY_FIX_KEY, 'true');
          return;
        }
        
        console.log(`📄 Found invoice ${targetInvoiceNumber} with status: ${targetInvoice.status}`);
        
        if (targetInvoice.status === 'Paid') {
          console.log('✅ Invoice already Paid, marking fix as completed');
          localStorage.setItem(EMERGENCY_FIX_KEY, 'true');
          return;
        }
        
        // Check if matching completed transaction exists
        const transactionsData = localStorage.getItem('finance_transactions');
        if (!transactionsData) {
          console.log('💳 No transactions found');
          return;
        }
        
        const transactions = JSON.parse(transactionsData);
        const matchingTx = transactions.find((tx: any) => 
          tx.invoiceNumber === targetInvoiceNumber &&
          ['completed', 'Completed', 'paid', 'Paid'].includes(tx.status)
        );
        
        if (matchingTx) {
          console.log(`✅ Found completed transaction: ${matchingTx.transactionId || matchingTx.id}, marking fix as completed`);
          localStorage.setItem(EMERGENCY_FIX_KEY, 'true');
          // Force sync to update invoice status
          setTimeout(() => syncInvoiceStatusWithTransactions(), 1000);
          return;
        }
        
        console.log('🚨 EMERGENCY FIX: Creating matching transaction for SINV-2025-001...');
        
        // Create emergency transaction
        const newTransaction = {
          id: `tx-emergency-${Date.now()}`,
          transactionId: `TXN-EMERGENCY-${Date.now()}`,
          type: 'income',
          category: 'project_revenue',
          description: `Payment for ${targetInvoiceNumber} (${targetInvoice.revenueItem})`,
          amount: targetInvoice.amount || 975.0,
          vatRate: 21.0,
          vatAmount: targetInvoice.vatAmount || 204.8,
          totalAmount: targetInvoice.totalAmount || 1179.8,
          date: new Date().toISOString().split('T')[0],
          paymentMethod: 'Bank Transfer',
          status: 'completed', // Key: completed status
          project: 'web-dev-2024',
          revenueItem: targetInvoice.revenueItem,
          invoiceNumber: targetInvoiceNumber, // Key: exact match
          tags: [targetInvoiceNumber, 'emergency-fix'],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        // Add transaction
        transactions.unshift(newTransaction);
        localStorage.setItem('finance_transactions', JSON.stringify(transactions));
        
        console.log(`✅ EMERGENCY FIX: Created transaction ${newTransaction.transactionId}`);
        console.log(`   InvoiceNumber: ${newTransaction.invoiceNumber}`);
        console.log(`   Status: ${newTransaction.status}`);
        console.log(`   Amount: €${newTransaction.totalAmount}`);
        
        // Trigger storage event
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'finance_transactions',
          newValue: JSON.stringify(transactions)
        }));
        
        // Force sync after 1 second and mark as completed
        setTimeout(() => {
          syncInvoiceStatusWithTransactions();
          localStorage.setItem(EMERGENCY_FIX_KEY, 'true');
          console.log('🔄 Emergency fix sync completed and marked as done');
        }, 1500);
        
      } catch (error) {
        console.error('❌ Emergency invoice sync fix failed:', error instanceof Error ? error.message : String(error));
        localStorage.setItem(EMERGENCY_FIX_KEY, 'true'); // Mark as completed even on error
      }
    };

    // Execute emergency fix only once, 3 seconds after component load
    setTimeout(emergencyFixInvoiceSync, 3000);
  }, []); // Empty dependency array - run only once

  // 自动同步Invoice状态（组件挂载和定期检查）
  useEffect(() => {
    // 立即执行一次同步 - 销售发票和费用发票
    if (syncInvoiceStatusWithTransactionsRef.current) {
      syncInvoiceStatusWithTransactionsRef.current();
    }
    syncExpenseInvoiceStatusWithTransactions();

    // 🔧 移除定期同步，避免无限循环
    // 只在有事件触发时同步

    // 监听localStorage变化（当Transaction状态更新时）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'finance_transactions') {
        console.log('🔄 Detected transaction changes, syncing invoice status...');
        setTimeout(() => {
          // 同步销售发票状态
          if (syncInvoiceStatusWithTransactionsRef.current) {
            syncInvoiceStatusWithTransactionsRef.current();
          }
          // 同步费用发票状态
          syncExpenseInvoiceStatusWithTransactions();
        }, 1000); // 延迟1秒执行，确保数据已保存
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // 监听交易数据更新事件
    const handleTransactionDataUpdate = () => {
      console.log('🔄 Detected transaction data update, syncing invoice status...');
      setTimeout(() => {
        // 同步销售发票状态
        if (syncInvoiceStatusWithTransactionsRef.current) {
          syncInvoiceStatusWithTransactionsRef.current();
        }
        // 同步费用发票状态
        syncExpenseInvoiceStatusWithTransactions();
      }, 500);
    };

    window.addEventListener('transactionDataUpdated', handleTransactionDataUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionDataUpdated', handleTransactionDataUpdate);
    };
  }, [syncExpenseInvoiceStatusWithTransactions]); // 添加费用发票同步函数依赖

  // 计算汇总数据
  const calculateSummary = () => {
    const totalInvoiced = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    const paidAmount = invoices.filter(invoice => invoice.status === 'Paid').reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    const pendingAmount = invoices.filter(invoice => invoice.status === 'Pending').reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    const overdueAmount = invoices.filter(invoice => invoice.status === 'Overdue').reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    
    return {
      totalInvoiced,
      paidAmount,
      pendingAmount,
      overdueAmount
    };
  };

  const { totalInvoiced, paidAmount, pendingAmount, overdueAmount } = calculateSummary();

  const showCreateInvoiceModal = () => {
    form.resetFields();
    setSelectedRevenueItems([]);
    setSelectedBudgetItem(null);
    setEditingInvoice(null);
    setEditingExpenseInvoice(null);
    
    const initialFormValues: any = {
        paymentTermDays: 14,
        paymentTermUnit: 'working',
        invoiceDate: dayjs(),
    };
    
    // 不自动添加Revenue Item，让用户手动选择
    form.setFieldsValue(initialFormValues);
    
    // 强制计算Due Date - 传入有效的changedValues来触发计算
    handleValuesChange({ invoiceDate: dayjs(), paymentTermDays: 14, paymentTermUnit: 'working' }, initialFormValues);
    
    // 重新加载Revenue Items数据
    loadRevenueItems();
    
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setSelectedRevenueItems([]);
    setSelectedBudgetItem(null);
    setEditingInvoice(null);
    setEditingExpenseInvoice(null);
  };

  // 🔧 Budget Item选择处理函数 - 自动填充Currency和VAT Rate
  const handleBudgetItemChange = (budgetItemId: string) => {
    try {
      // 获取项目ID和Budget Items数据
      let projectId = 'default';
      
      // 项目ID获取逻辑（复用现有逻辑）
      const pathSegments = window.location.pathname.split('/');
      const projectIndex = pathSegments.indexOf('project');
      if (projectIndex !== -1 && projectIndex + 1 < pathSegments.length) {
        projectId = pathSegments[projectIndex + 1];
      }
      
      const urlParams = new URLSearchParams(window.location.search);
      const urlProjectId = urlParams.get('projectId');
      if (urlProjectId) {
        projectId = urlProjectId;
      }
      
      const currentProject = localStorage.getItem('current_project');
      let projectData = null;
      try {
        projectData = currentProject ? JSON.parse(currentProject) : null;
      } catch (e) {
        console.warn('Failed to parse current_project from localStorage:', e);
      }
      if (projectData?.id) {
        projectId = projectData.id;
      }
      
      // 如果还是default，尝试web-dev-2024
      if (projectId === 'default') {
        projectId = 'web-dev-2024';
      }
      
      // 获取Budget Items数据
      const possibleKeys = [
        `execution_stage_budgetItems_${projectId}`,
        `budget_items_${projectId}`,
        'budget_items',
        `project_${projectId}_budgetItems`
      ];
      
      let budgetItems = [];
      for (const key of possibleKeys) {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const items = JSON.parse(data);
            if (Array.isArray(items) && items.length > 0) {
              budgetItems = items;
              break;
            }
          } catch (e) {
            console.warn(`Failed to parse data from key: ${key}`, e);
          }
        }
      }
      
      // 查找选中的Budget Item
      const selectedBudgetItem = budgetItems.find((item: any) => item.id === budgetItemId);
      
      if (selectedBudgetItem) {
        console.log('🔧 Budget Item selected:', {
          id: selectedBudgetItem.id,
          name: selectedBudgetItem.budgetItem || selectedBudgetItem.category,
          currency: selectedBudgetItem.currency,
          vatRate: selectedBudgetItem.vatRate
        });

        // 保存选中的预算项目信息
        setSelectedBudgetItem(selectedBudgetItem);

        // 自动填充Currency和VAT Rate
        const fieldsToUpdate: any = {};
        
        if (selectedBudgetItem.currency) {
          fieldsToUpdate.currency = selectedBudgetItem.currency;
        }
        
        if (selectedBudgetItem.vatRate !== undefined && selectedBudgetItem.vatRate !== null) {
          fieldsToUpdate.vatRate = selectedBudgetItem.vatRate;
        }
        
        // 更新表单字段
        if (Object.keys(fieldsToUpdate).length > 0) {
          form.setFieldsValue(fieldsToUpdate);
          console.log('✅ Auto-filled fields:', fieldsToUpdate);
          
          // 显示成功提示
          const fieldNames = [];
          if (fieldsToUpdate.currency) fieldNames.push('Currency');
          if (fieldsToUpdate.vatRate !== undefined) fieldNames.push('VAT Rate');
          
          if (fieldNames.length > 0) {
            message.success(`Auto-filled ${fieldNames.join(' and ')} from Expense Item`);
          }
        } else {
          console.log('ℹ️ No currency or VAT rate data found in selected Budget Item');
        }
      } else {
        console.warn('❌ Selected Budget Item not found:', budgetItemId);
      }
    } catch (error) {
      console.error('❌ Failed to handle Budget Item change:', error instanceof Error ? error.message : String(error));
      message.error('Failed to auto-fill fields from Expense Item');
    }
  };

  // 添加Revenue Item到选中列表，默认quantity=100% for NRC and 1 month for MRC
  const addRevenueItem = (revenueItemName: string) => {
    const selectedItem = revenueItems.find(item => item.name === revenueItemName);
    if (!selectedItem) return;
    const existingIndex = selectedRevenueItems.findIndex(item => item.name === selectedItem.name);
    if (existingIndex !== -1) {
      message.warning(`${selectedItem.name} already added to invoice`);
      return;
    }
    const newItem: RevenueItemLine = {
      id: selectedItem.id,
      name: selectedItem.name,
      type: selectedItem.type,
      unitPrice: selectedItem.amount,
      quantity: 0, // 初始数量设为0，用户需要手动输入
      lineTotal: 0, // 初始金额为0，需要用户输入数量后计算
      vatRate: selectedItem.vatRate || 21, // 使用Revenue Item的VAT Rate，如果没有则默认21%
      billingPeriod: selectedItem.type === 'MRC' ? 'Monthly' : undefined
    };
    setSelectedRevenueItems([...selectedRevenueItems, newItem]);
  };

  // 更新Revenue Item的数量或百分比
  const updateRevenueItemQuantity = (index: number, quantity: number) => {
    const updated = [...selectedRevenueItems];
    const item = updated[index];
    item.quantity = quantity;
    if (item.type === 'NRC') {
      item.lineTotal = item.unitPrice * (quantity / 100);
    } else { // MRC
      item.lineTotal = item.unitPrice * quantity;
    }
    setSelectedRevenueItems(updated);
  };

  // 更新单项VAT Rate
  const updateRevenueItemVatRate = (index: number, vatRate: number) => {
    const updated = [...selectedRevenueItems];
    updated[index].vatRate = vatRate;
    setSelectedRevenueItems(updated);
  };

  // 移除Revenue Item
  const removeRevenueItem = (index: number) => {
    const updated = selectedRevenueItems.filter((_, i) => i !== index);
    setSelectedRevenueItems(updated);
  };

  // 计算总金额（每项单独VAT）
  const calculateInvoiceTotal = () => {
    const subtotal = selectedRevenueItems.reduce((sum, item) => sum + item.lineTotal, 0);
    const totalVat = selectedRevenueItems.reduce((sum, item) => sum + (item.lineTotal * (item.vatRate || 0) / 100), 0);
    const totalAmount = subtotal + totalVat;
    return { subtotal, totalVat, totalAmount };
  };

  const handleSubmit = () => {
    // 验证是否选择了Revenue Items
    if (activeTab === 'sales' && selectedRevenueItems.length === 0) {
      message.error('Please add at least one revenue item to the invoice');
      return;
    }

    form.validateFields().then(values => {
      if (activeTab === 'sales') {
        // 计算发票总金额
        const { subtotal, totalVat, totalAmount } = calculateInvoiceTotal();
        
        // 生成混合发票号
        const invoiceNumber = `SINV-${new Date().getFullYear()}-${(invoices.length + 1).toString().padStart(3, '0')}`;
        
        // 创建发票描述
        const itemsSummary = selectedRevenueItems.map(item => {
          if (item.type === 'NRC') {
            return `${item.name} at ${item.quantity}%`;
          } else {
            return `${item.name} for ${item.quantity} months`;
          }
        }).join(', ');
        
        const newInvoice: Omit<Invoice, 'vatRate'> & { vatRate?: number } = {
          id: (invoices.length + 1).toString(),
          number: invoiceNumber,
          revenueItem: itemsSummary, // 使用摘要作为显示
          type: selectedRevenueItems.length === 1 ? selectedRevenueItems[0].type : 'Mixed', // 单项用原类型，多项用Mixed
          amount: subtotal,
          vatAmount: totalVat,
          totalAmount: totalAmount,
          invoiceDate: values.invoiceDate ? values.invoiceDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          dueDate: values.dueDate ? values.dueDate.format('YYYY-MM-DD') : dayjs().add(14, 'day').format('YYYY-MM-DD'),
          status: 'Pending'
        };

        const updatedInvoices = [...invoices, newInvoice as Invoice];
        setInvoices(updatedInvoices);
        
        // 保存到localStorage
        localStorage.setItem('finance_invoices', JSON.stringify(updatedInvoices));
        
        // 🔧 增强的事件触发系统 - 确保CreateTransaction接收到更新
        console.log('🔥 Triggering invoice data update events for:', newInvoice.number);
        
        // 1. 触发自定义事件
        window.dispatchEvent(new Event('invoiceDataUpdated'));
        
        // 2. 触发storage事件（跨标签页通信）
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'finance_invoices',
          newValue: JSON.stringify(updatedInvoices),
          oldValue: JSON.stringify(invoices)
        }));
        
        // 3. 延迟触发，确保某些延迟加载的组件也能收到
        setTimeout(() => {
          window.dispatchEvent(new Event('invoiceDataUpdated'));
          console.log('🔄 Delayed invoice update event dispatched');
        }, 500);
        
        message.success(`Mixed invoice created successfully with ${selectedRevenueItems.length} items`);
        
        // 保存详细的Revenue Items信息到localStorage（用于发票详情）
        const invoiceDetails = {
          invoiceId: newInvoice.id,
          revenueItems: selectedRevenueItems,
          subtotal,
          totalVat,
          totalAmount,
          createdAt: new Date().toISOString()
        };
        localStorage.setItem(`invoice_details_${newInvoice.id}`, JSON.stringify(invoiceDetails));
      } else {
        // Expense Invoice计算
        const vatAmount = (values.amount * values.vatRate) / 100;
        const totalAmount = values.amount + vatAmount;
        
        // 获取Budget Item信息
        let budgetItemName = 'Unknown Budget Item';

        // 优先使用已保存的选中预算项目信息
        if (selectedBudgetItem) {
          budgetItemName = selectedBudgetItem.budgetItem ||
                          selectedBudgetItem.category ||
                          selectedBudgetItem.name ||
                          'Unknown Budget Item';
          console.log('✅ 使用已保存的Budget Item信息:', budgetItemName);
        } else {
          // 如果没有保存的信息，尝试从localStorage重新获取
          try {
          // 🔧 修复1：增强的项目ID获取逻辑
          let projectId = 'default';
          
          // 方法1: 从URL路径获取项目ID
          const pathSegments = window.location.pathname.split('/');
          const projectIndex = pathSegments.indexOf('project');
          if (projectIndex !== -1 && projectIndex + 1 < pathSegments.length) {
            projectId = pathSegments[projectIndex + 1];
          }
          
          // 方法2: 从URL查询参数获取项目ID
          const urlParams = new URLSearchParams(window.location.search);
          const urlProjectId = urlParams.get('projectId');
          if (urlProjectId) {
            projectId = urlProjectId;
          }
          
          // 方法3: 从localStorage获取当前项目
          const currentProject = localStorage.getItem('current_project');
          let projectData = null;
          try {
            projectData = currentProject ? JSON.parse(currentProject) : null;
          } catch (e) {
            console.warn('Failed to parse current_project from localStorage:', e);
          }
          if (projectData?.id) {
            projectId = projectData.id;
          }
          
          // 方法4: 从edm_projects获取
          const edmProjectsData = localStorage.getItem('edm_projects');
          if (edmProjectsData && projectId === 'default') {
            try {
              const edmProjects = JSON.parse(edmProjectsData);
              if (Array.isArray(edmProjects) && edmProjects.length > 0) {
                const webDevProject = edmProjects.find((p: any) => p?.id === 'web-dev-2024' || p?.id?.includes('web'));
                if (webDevProject) {
                  projectId = webDevProject.id;
                } else {
                  projectId = edmProjects[0]?.id || 'web-dev-2024';
                }
              }
            } catch (e) {
              console.warn('Failed to parse edm_projects:', e);
            }
          }
          
          // 如果还是default，默认使用web-dev-2024
          if (projectId === 'default') {
            projectId = 'web-dev-2024';
          }
          
          console.log('🔍 Budget Item获取 - 项目ID:', projectId);
          
          // 🔧 修复2：尝试多个可能的localStorage键
          const possibleKeys = [
            `execution_stage_budgetItems_${projectId}`,
            `budget_items_${projectId}`,
            'budget_items',
            `project_${projectId}_budgetItems`,
            'execution_stage_budgetItems_web-dev-2024' // 备用键
          ];
          
          let budgetItems = [];
          let usedKey = '';
          for (const key of possibleKeys) {
            const data = localStorage.getItem(key);
            if (data) {
              try {
                const items = JSON.parse(data);
                if (Array.isArray(items) && items.length > 0) {
                  budgetItems = items;
                  usedKey = key;
                  console.log(`✅ 找到Budget Items数据，键: ${key}, 数量: ${items.length}`);
                  break;
                }
              } catch (e) {
                console.warn(`解析Budget Items失败，键: ${key}`, e);
              }
            }
          }
          
          console.log('🔍 Budget Items数据:', budgetItems);
          console.log('🔍 选择的budgetCategoryId:', values.budgetCategoryId);
          
          // 🔧 修复3：改进Budget Item查找逻辑
          const selectedBudgetItem = budgetItems.find((item: any) => {
            // 尝试多种ID匹配方式
            return item.id === values.budgetCategoryId || 
                   item.category === values.budgetCategoryId ||
                   item.budgetItem === values.budgetCategoryId ||
                   item.name === values.budgetCategoryId;
          });
          
          if (selectedBudgetItem) {
            // 🔧 修复4：优先使用最合适的字段作为Budget Item名称
            budgetItemName = selectedBudgetItem.budgetItem || 
                           selectedBudgetItem.category || 
                           selectedBudgetItem.name || 
                           selectedBudgetItem.description || 
                           'Unknown Budget Item';
            console.log('✅ 找到Budget Item:', budgetItemName, selectedBudgetItem);
          } else {
            console.warn('⚠️ 未找到匹配的Budget Item，ID:', values.budgetCategoryId);
            
            // 🔧 修复5：如果找不到，尝试根据values.budgetCategoryId直接作为名称
            if (values.budgetCategoryId && values.budgetCategoryId !== 'others') {
              budgetItemName = values.budgetCategoryId;
              console.log('🔄 使用budgetCategoryId作为Budget Item名称:', budgetItemName);
            }
          }
          } catch (error) {
            console.error('Failed to get budget item name:', error instanceof Error ? error.message : String(error));
            // 🔧 修复6：错误时尝试使用表单值
            if (values.budgetCategoryId && values.budgetCategoryId !== 'others') {
              budgetItemName = values.budgetCategoryId;
            }
          }
        }
        
        if (editingExpenseInvoice) {
          // 编辑现有Expense Invoice
          let expenseItemWithSequence = budgetItemName;

          // 如果预算项目改变了，重新计算序号
          if (editingExpenseInvoice.budgetCategoryId !== values.budgetCategoryId) {
            const sameItemCount = expenseInvoices.filter(invoice =>
              invoice.budgetCategoryId === values.budgetCategoryId &&
              invoice.id !== editingExpenseInvoice.id
            ).length;
            const itemSequence = String(sameItemCount + 1).padStart(3, '0');
            expenseItemWithSequence = `${budgetItemName} ${itemSequence}`;
          } else {
            // 保持原有的序号（从现有的expense字段中提取）
            expenseItemWithSequence = editingExpenseInvoice.expense || budgetItemName;
          }

          const updatedExpenseInvoice: ExpenseInvoice = {
            ...editingExpenseInvoice,
            budgetCategoryId: values.budgetCategoryId,
            category: budgetItemName, // 使用Budget Item名称
            description: values.description,
            expense: expenseItemWithSequence, // 使用带序号的名称
            type: budgetItemName, // 使用Budget Item名称作为type
            amount: values.amount,
            vatRate: values.vatRate,
            vatAmount: vatAmount,
            totalAmount: totalAmount,
            invoiceDate: values.invoiceDate ? values.invoiceDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
            dueDate: values.dueDate ? values.dueDate.format('YYYY-MM-DD') : dayjs().add(14, 'day').format('YYYY-MM-DD'),
            status: editingExpenseInvoice.status || 'Pending',
            approvedBy: values.approvedBy || editingExpenseInvoice.approvedBy,
            reference: values.reference || editingExpenseInvoice.reference
          };

          const updatedExpenseInvoices = expenseInvoices.map(invoice => 
            invoice.id === editingExpenseInvoice.id ? updatedExpenseInvoice : invoice
          );
          setExpenseInvoices(updatedExpenseInvoices);
          
          // 保存到localStorage
          localStorage.setItem('finance_expense_invoices', JSON.stringify(updatedExpenseInvoices));
          
          // 🆕 触发Invoice数据更新事件
          window.dispatchEvent(new Event('invoiceDataUpdated'));
          
          message.success('Expense invoice updated successfully');
          setEditingExpenseInvoice(null);
        } else {
          // 计算同一预算项目的使用次数
          const sameItemCount = expenseInvoices.filter(invoice =>
            invoice.budgetCategoryId === values.budgetCategoryId
          ).length;
          const itemSequence = String(sameItemCount + 1).padStart(3, '0');

          // 生成带序号的费用项目名称
          const expenseItemWithSequence = `${budgetItemName} ${itemSequence}`;

          // 创建新的Expense Invoice
          const newExpenseInvoice: ExpenseInvoice = {
            id: (expenseInvoices.length + 1).toString(),
            number: `EINV-${new Date().getFullYear()}-${(expenseInvoices.length + 1).toString().padStart(3, '0')}`,
            budgetCategoryId: values.budgetCategoryId,
            category: budgetItemName, // 使用Budget Item名称
            description: values.description,
            expense: expenseItemWithSequence, // 使用带序号的名称
            type: budgetItemName, // 使用Budget Item名称作为type
            currency: values.currency || 'EUR', // 添加货币字段
            amount: values.amount,
            vatRate: values.vatRate,
            vatAmount: vatAmount,
            totalAmount: totalAmount,
            invoiceDate: values.invoiceDate ? values.invoiceDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
            dueDate: values.dueDate ? values.dueDate.format('YYYY-MM-DD') : dayjs().add(14, 'day').format('YYYY-MM-DD'),
            status: 'Pending',
            approvedBy: values.approvedBy,
            reference: values.reference
          };

          const updatedExpenseInvoices = [...expenseInvoices, newExpenseInvoice];
          setExpenseInvoices(updatedExpenseInvoices);
          
          // 保存到localStorage
          localStorage.setItem('finance_expense_invoices', JSON.stringify(updatedExpenseInvoices));
          
          // 🆕 触发Invoice数据更新事件
          window.dispatchEvent(new Event('invoiceDataUpdated'));
          
          message.success('Expense invoice created successfully');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
      setSelectedRevenueItems([]);
      setSelectedBudgetItem(null);
    }).catch(error => {
      console.error('Form validation failed:', error instanceof Error ? error.message : String(error));
      message.error('Please check all required fields and try again');
    });
  };

  // 删除Sales Invoice
  const handleDeleteInvoice = (invoiceId: string) => {
    const updatedInvoices = invoices.filter(invoice => invoice.id !== invoiceId);
    setInvoices(updatedInvoices);
    localStorage.setItem('finance_invoices', JSON.stringify(updatedInvoices));
    
    // 🆕 触发Invoice数据更新事件
    window.dispatchEvent(new Event('invoiceDataUpdated'));
    
    // 同时删除相关的详细信息
    localStorage.removeItem(`invoice_details_${invoiceId}`);
    message.success('Invoice deleted successfully');
  };

  // 删除Expense Invoice
  const handleDeleteExpenseInvoice = (invoiceId: string) => {
    const updatedExpenseInvoices = expenseInvoices.filter(invoice => invoice.id !== invoiceId);
    setExpenseInvoices(updatedExpenseInvoices);
    localStorage.setItem('finance_expense_invoices', JSON.stringify(updatedExpenseInvoices));
    
    // 🆕 触发Invoice数据更新事件
    window.dispatchEvent(new Event('invoiceDataUpdated'));
    
    message.success('Expense invoice deleted successfully');
  };

  // 编辑Invoice
  const handleEditInvoice = (record: Invoice) => {
    // 首先加载所有可用的Revenue Items
    loadRevenueItems();
    
    // 设置表单数据
    const invoiceDetails = localStorage.getItem(`invoice_details_${record.id}`);
    if (invoiceDetails) {
      const details = JSON.parse(invoiceDetails);
      
      // 设置已选择的Revenue Items（这是关键修复）
      if (details.selectedRevenueItems && Array.isArray(details.selectedRevenueItems)) {
        setSelectedRevenueItems(details.selectedRevenueItems);
        console.log('✅ Loaded selectedRevenueItems for edit:', details.selectedRevenueItems);
      } else {
        // 如果没有详细的selectedRevenueItems，尝试从发票数据重构
        console.log('⚠️ No selectedRevenueItems found, attempting to reconstruct from invoice data');
        const reconstructedItems: RevenueItemLine[] = [];
        
        // 解析 revenueItem 字符串来重构选择的项目
        if (record.revenueItem) {
          const items = record.revenueItem.split(',').map(item => item.trim());
          items.forEach(itemStr => {
            // 尝试从itemStr中提取项目信息
            const match = itemStr.match(/^([^(]+)(?:\s*\(([^)]+)\))?/);
            if (match) {
              const itemName = match[1].trim();
              const percentageMatch = itemStr.match(/(\d+)%/);
              const monthsMatch = itemStr.match(/(\d+)\s*months?/);
              
              reconstructedItems.push({
                id: `reconstructed-${Date.now()}-${Math.random()}`,
                name: itemName,
                type: percentageMatch ? 'NRC' : 'MRC',
                unitPrice: record.amount / items.length, // 估算单价
                quantity: percentageMatch ? parseInt(percentageMatch[1]) : (monthsMatch ? parseInt(monthsMatch[1]) : 1),
                lineTotal: record.amount / items.length,
                vatRate: record.vatAmount > 0 ? Math.round((record.vatAmount / record.amount) * 100) : 21,
                billingPeriod: monthsMatch ? 'monthly' : undefined
              });
            }
          });
          
          if (reconstructedItems.length > 0) {
            setSelectedRevenueItems(reconstructedItems);
            console.log('✅ Reconstructed selectedRevenueItems:', reconstructedItems);
          }
        }
      }
      
      form.setFieldsValue({
        invoiceDate: dayjs(record.invoiceDate),
        paymentTermDays: 14,
        paymentTermUnit: 'working'
      });
    }
    
    setIsModalVisible(true);
    setEditingInvoice(record);
  };

  // 编辑Expense Invoice
  const handleEditExpenseInvoice = (record: ExpenseInvoice) => {
    form.setFieldsValue({
      budgetCategoryId: record.budgetCategoryId,
      description: record.description,
      currency: record.currency || 'EUR',
      amount: record.amount,
      vatRate: record.vatRate,
      invoiceDate: dayjs(record.invoiceDate),
      paymentTermDays: 14,
      paymentTermUnit: 'working'
    });
    setIsModalVisible(true);
    setEditingExpenseInvoice(record);
  };

  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .payment-term-select .ant-select-selector {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 表格列定义
  const columns: any[] = [
    { 
      title: 'Invoice No.', 
      dataIndex: 'number', 
      key: 'number', 
      width: 140,
      resizable: true,
      sorter: (a: Invoice, b: Invoice) => a.number.localeCompare(b.number) 
    },
    { 
      title: 'Revenue Items', 
      dataIndex: 'revenueItem', 
      key: 'revenueItem', 
      width: 200,
      resizable: true,
      ellipsis: true,
      sorter: (a: Invoice, b: Invoice) => a.revenueItem.localeCompare(b.revenueItem)
    },
    { 
      title: 'Type', 
      dataIndex: 'type', 
      key: 'type',
      width: 100,
      resizable: true,
      sorter: (a: Invoice, b: Invoice) => a.type.localeCompare(b.type)
    },
    { 
      title: 'Amount', 
      dataIndex: 'amount', 
      key: 'amount', 
      width: 120,
      resizable: true,
      render: (val: number) => formatAmount(val),
      sorter: (a: Invoice, b: Invoice) => a.amount - b.amount
    },
    { 
      title: 'VAT Amount', 
      dataIndex: 'vatAmount', 
      key: 'vatAmount', 
      width: 120,
      resizable: true,
      render: (val: number) => formatAmount(val),
      sorter: (a: Invoice, b: Invoice) => a.vatAmount - b.vatAmount
    },
    { 
      title: 'Total Amount', 
      dataIndex: 'totalAmount', 
      key: 'totalAmount', 
      width: 130,
      resizable: true,
      render: (val: number) => formatAmount(val),
      sorter: (a: Invoice, b: Invoice) => a.totalAmount - b.totalAmount
    },
    { 
      title: 'Invoice Date', 
      dataIndex: 'invoiceDate', 
      key: 'invoiceDate',
      width: 120,
      resizable: true,
      sorter: (a: Invoice, b: Invoice) => dayjs(a.invoiceDate).unix() - dayjs(b.invoiceDate).unix() 
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 120,
      resizable: true,
      sorter: (a: Invoice, b: Invoice) => dayjs(a.dueDate).unix() - dayjs(b.dueDate).unix(),
      render: (dueDate: string) => {
        const today = dayjs().startOf('day');
        const date = dayjs(dueDate);
        let color = 'inherit';
        let fontWeight: "normal" | "bold" = 'normal';

        if (date.isBefore(today)) {
          color = '#f5222d';
          fontWeight = 'bold';
        } else if (date.diff(today, 'day') <= 7) {
          color = '#faad14';
        } else {
          color = '#52c41a';
        }
        return <span style={{ color, fontWeight }}>{date && date.isValid() ? date.format('YYYY-MM-DD') : dueDate}</span>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      resizable: true,
      filters: [
        { text: 'Pending', value: 'Pending' },
        { text: 'Paid', value: 'Paid' },
        { text: 'Overdue', value: 'Overdue' },
        { text: 'Draft', value: 'Draft' },
      ],
      onFilter: (value: string | number | boolean, record: Invoice) => record.status === value,
      sorter: (a: Invoice, b: Invoice) => a.status.localeCompare(b.status),
      render: (status: string) => {
        let color = '';
        let text = '';
        switch (status) {
          case 'Paid':
            color = 'green';
            text = 'Paid';
            break;
          case 'Pending':
            color = 'orange';
            text = 'Pending';
            break;
          case 'Overdue':
            color = 'red';
            text = 'Overdue';
            break;
          case 'Draft':
            color = 'blue';
            text = 'Draft';
            break;
          default:
            color = 'default';
            text = status;
        }
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      resizable: true,
      render: (_: any, record: Invoice) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => handleEditInvoice(record)}
              size="small"
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Invoice"
            description="Are you sure you want to delete this invoice?"
            onConfirm={() => handleDeleteInvoice(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                danger
                size="small"
                style={{ fontSize: '12px' }}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const expenseColumns: any[] = [
    { 
      title: 'Invoice No.', 
      dataIndex: 'number', 
      key: 'number',
      width: 140,
      resizable: true,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.number.localeCompare(b.number)
    },
    {
      title: 'Expense Item',
      dataIndex: 'expense',
      key: 'expense',
      width: 200,
      resizable: true,
      ellipsis: true,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.expense.localeCompare(b.expense)
    },

    { 
      title: 'Amount', 
      dataIndex: 'amount', 
      key: 'amount',
      width: 120,
      resizable: true,
      render: (val: number) => formatAmount(val),
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.amount - b.amount
    },
    { 
      title: 'VAT', 
      dataIndex: 'vatRate', 
      key: 'vatRate',
      width: 80,
      resizable: true,
      render: (val: number) => `${val}%`,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.vatRate - b.vatRate
    },
    { 
      title: 'Total Amount', 
      dataIndex: 'totalAmount', 
      key: 'totalAmount',
      width: 130,
      resizable: true,
      render: (val: number) => formatAmount(val),
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.totalAmount - b.totalAmount
    },
    { 
      title: 'Invoice Date', 
      dataIndex: 'invoiceDate', 
      key: 'invoiceDate',
      width: 120,
      resizable: true,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => dayjs(a.invoiceDate).unix() - dayjs(b.invoiceDate).unix()
    },
    { 
      title: 'Due Date', 
      dataIndex: 'dueDate', 
      key: 'dueDate',
      width: 120,
      resizable: true,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => dayjs(a.dueDate).unix() - dayjs(b.dueDate).unix()
    },
    { 
      title: 'Status', 
      dataIndex: 'status', 
      key: 'status',
      width: 100,
      resizable: true,
      filters: [
        { text: 'Pending', value: 'Pending' },
        { text: 'Approved', value: 'Approved' },
        { text: 'Rejected', value: 'Rejected' },
        { text: 'Paid', value: 'Paid' },
        { text: 'Overdue', value: 'Overdue' },
      ],
      onFilter: (value: string | number | boolean, record: ExpenseInvoice) => record.status === value,
      sorter: (a: ExpenseInvoice, b: ExpenseInvoice) => a.status.localeCompare(b.status),
      render: (status: string) => {
        let color = '';
        let text = '';
        switch (status) {
          case 'Paid':
            color = 'green';
            text = 'Paid';
            break;
          case 'Approved':
            color = 'blue';
            text = 'Approved';
            break;
          case 'Pending':
            color = 'orange';
            text = 'Pending';
            break;
          case 'Rejected':
            color = 'red';
            text = 'Rejected';
            break;
          case 'Overdue':
            color = 'red';
            text = 'Overdue';
            break;
          default:
            color = 'default';
            text = status;
        }
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      resizable: true,
      render: (_: any, record: ExpenseInvoice) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => handleEditExpenseInvoice(record)}
              size="small"
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Expense Invoice"
            description="Are you sure you want to delete this expense invoice?"
            onConfirm={() => handleDeleteExpenseInvoice(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                danger
                size="small"
                style={{ fontSize: '12px' }}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const paymentTermsStyle = `
    .payment-terms-group .ant-select-selector,
    .payment-terms-group .ant-input-number-input,
    .payment-terms-group .ant-input-number {
        height: 39.6px !important;
        display: flex;
        align-items: center;
    }
`;

  return (
    <div className="invoice-management-container" style={{ padding: '24px' }}>
      <style>{paymentTermsStyle}</style>
      <Card
        bordered={false}
        bodyStyle={{ padding: '0 0 24px 0' }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Button type="primary" icon={<PlusOutlined />} onClick={showCreateInvoiceModal}>
              Create Invoice
            </Button>
          }
        >
          <TabPane tab="Sales Invoices" key="sales">
            <Table 
              dataSource={invoices} 
              columns={columns} 
              rowKey="id" 
              scroll={{ x: 'max-content' }}
              bordered
              size="middle"
            />
          </TabPane>
          <TabPane tab="Expense Invoices" key="expenses">
            <Table 
              dataSource={expenseInvoices} 
              columns={expenseColumns} 
              rowKey="id" 
              scroll={{ x: 'max-content' }}
              bordered
              size="middle"
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建发票模态框 */}
      <Modal
        title={
          editingInvoice ? "Edit Sales Invoice" :
          editingExpenseInvoice ? "Edit Expense Invoice" :
          activeTab === 'sales' ? "Create Sales Invoice" : "Create Expense Invoice"
        }
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button key="submit" type="primary" onClick={handleSubmit}>
            Create
          </Button>,
        ]}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          name="createInvoice"
          onValuesChange={(changedValues, allValues) => {
            handleValuesChange(changedValues, allValues);
          }}
          initialValues={{
            paymentTermDays: 14,
            paymentTermUnit: 'working',
            invoiceDate: dayjs(),
            vatRate: 21,
          }}
        >
          {activeTab === 'sales' ? (
            // Sales Invoice字段 - 多选Revenue Items
            <>
              {/* Revenue Item选择器 */}
              <Form.Item
                label={<span>Add Revenue Items <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please add at least one revenue item!' }]}
              >
                <Select 
                  defaultValue={revenueItems.length > 0 ? revenueItems[0].name : undefined}
                  placeholder="Select revenue items to add to invoice"
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  notFoundContent={revenueItems.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '8px' }}>
                      <div>No revenue items found</div>
                      <div style={{ fontSize: '11px', color: '#888', marginTop: '4px' }}>
                        Create revenue items in Revenue Management first
                      </div>
                      <Button 
                        size="small" 
                        onClick={loadRevenueItems} 
                        style={{ marginTop: '8px', fontSize: '11px' }}
                      >
                        Refresh
                      </Button>
                    </div>
                  ) : "No matching items"}
                  onSelect={(value) => {
                    if (value && typeof value === 'string') {
                      addRevenueItem(value);
                    }
                  }}
                >
                  {revenueItems.map(item => (
                    <Option key={item.id} value={item.name}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{item.name}</span>
                        <div style={{ fontSize: '11px', color: '#6B7280' }}>
                          <span style={{ 
                            backgroundColor: item.type === 'NRC' ? '#EDE9FE' : '#D1FAE5',
                            color: item.type === 'NRC' ? '#8B5CF6' : '#10B981',
                            padding: '1px 4px',
                            borderRadius: '8px',
                            marginRight: '6px',
                            fontSize: '9px'
                          }}>
                            {item.type}
                          </span>
                          {formatAmount(item.amount)}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
                <div style={{ color: '#888', fontSize: '12px', marginTop: '4px' }}>* Multiple revenue items can be selected.</div>
              </Form.Item>

              {/* 选中的Revenue Items列表 */}
              {selectedRevenueItems.length > 0 && (
                <Form.Item label="Selected Revenue Items">
                  <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px', backgroundColor: '#fafafa' }}>
                    {selectedRevenueItems.map((item, index) => (
                      <div key={index} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '8px', backgroundColor: '#fff', borderRadius: '4px', marginBottom: index < selectedRevenueItems.length - 1 ? '8px' : '0', border: '1px solid #f0f0f0' }}>
                        {/* 左侧：项目名称 */}
                        <div style={{ minWidth: '200px' }}>
                          <span style={{ 
                            backgroundColor: item.type === 'NRC' ? '#EDE9FE' : '#D1FAE5',
                            color: item.type === 'NRC' ? '#8B5CF6' : '#10B981',
                            padding: '2px 6px',
                            borderRadius: '8px',
                            fontSize: '10px',
                            fontWeight: '500',
                            marginRight: '8px'
                          }}>
                            {item.type}
                          </span>
                          <span style={{ fontWeight: '500', fontSize: '12px' }}>{item.name}</span>
                        </div>

                        {/* 右侧：操作控件组 */}
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <span style={{ fontSize: '11px', color: '#666' }}>
                            {item.type === 'NRC' ? 'Percent:' : 'Months:'}
                          </span>
                          <Input.Group compact style={{ display: 'flex', flex: 1 }}>
                            <Form.Item name={['lineItems', index, 'quantity']} noStyle>
                              <InputNumber
                                min={0}
                                formatter={(value) => `${value}${item.type === 'NRC' ? '%' : ''}`}
                                parser={(value) => (value?.replace('%', '') || '') as any}
                                onChange={(value) => updateRevenueItemQuantity(index, Number(value) || 0)}
                                style={{ width: '25%' }}
                              />
                            </Form.Item>
                            <Input
                              readOnly
                              value={`@ ${formatAmount(item.unitPrice)}`}
                              style={{ width: '35%', background: '#f5f5f5', color: '#888', textAlign: 'center' }}
                            />
                            <Form.Item
                              name={['lineItems', index, 'vatRate']}
                              noStyle
                            >
                              <InputNumber
                                min={0}
                                max={100}
                                defaultValue={21}
                                formatter={(value) => `VAT: ${value}%`}
                                parser={(value) => (value?.replace('VAT: ', '').replace('%', '') || '') as any}
                                onChange={(value) => updateRevenueItemVatRate(index, Number(value) || 21)}
                                style={{ width: '110px' }}
                                placeholder="VAT: 21%"
                              />
                            </Form.Item>
                          </Input.Group>
                          <div style={{ marginLeft: '8px', minWidth: '130px', textAlign: 'right', display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <Text strong style={{ fontSize: '14px', color: '#1890ff' }}>
                              {formatAmount(item.lineTotal * (1 + item.vatRate / 100))}
                            </Text>
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={() => removeRevenueItem(index)}
                              style={{ marginLeft: 8 }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                    
                    {/* 总计显示 */}
                    <div style={{ 
                      marginTop: '12px', 
                      paddingTop: '8px', 
                      borderTop: '1px solid #e8e8e8',
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: '12px',
                      fontSize: '12px'
                    }}>
                      <span style={{ color: '#666' }}>
                        Subtotal: <strong>{formatAmount(calculateInvoiceTotal().subtotal)}</strong>
                      </span>
                      <span style={{ color: '#666' }}>
                        VAT: <strong>{formatAmount(calculateInvoiceTotal().totalVat)}</strong>
                      </span>
                      <span style={{ color: '#10B981', fontWeight: '600' }}>
                        Total: <strong>{formatAmount(calculateInvoiceTotal().totalAmount)}</strong>
                      </span>
                    </div>
                  </div>
                </Form.Item>
              )}
            </>
          ) : (
            // Expense Invoice字段
            <>
              <div style={{ display: 'flex', gap: '16px' }}>
                <Form.Item
                  name="budgetCategoryId"
                  label={<span>Add Expense Item <span style={{ color: '#ff4d4f' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select expense item' }]}
                  style={{ flex: 1 }}
                >
                  <Select
                    placeholder="Select expense item"
                    onChange={handleBudgetItemChange}
                  >
                    {(() => {
                      try {
                        // 🔧 增强的项目ID获取逻辑
                        let projectId = 'default';
                        
                        // 方法1: 从URL路径获取项目ID (比如 /project/web-dev-2024/detail)
                        const pathSegments = window.location.pathname.split('/');
                        const projectIndex = pathSegments.indexOf('project');
                        if (projectIndex !== -1 && projectIndex + 1 < pathSegments.length) {
                          projectId = pathSegments[projectIndex + 1];
                        }
                        
                        // 方法2: 从URL查询参数获取项目ID
                        const urlParams = new URLSearchParams(window.location.search);
                        const urlProjectId = urlParams.get('projectId');
                        if (urlProjectId) {
                          projectId = urlProjectId;
                        }
                        
                        // 方法3: 从localStorage获取当前项目
                        const currentProject = localStorage.getItem('current_project');
                        let projectData = null;
                        try {
                          projectData = currentProject ? JSON.parse(currentProject) : null;
                        } catch (e) {
                          console.warn('Failed to parse current_project from localStorage:', e);
                        }
                        if (projectData?.id) {
                          projectId = projectData.id;
                        }
                        
                        // 方法4: 从projects数据中获取第一个项目ID（如果只有一个项目）
                        const allProjects = localStorage.getItem('projects');
                        let projectsList = [];
                        try {
                          projectsList = allProjects ? JSON.parse(allProjects) : [];
                        } catch (e) {
                          console.warn('Failed to parse projects from localStorage:', e);
                        }
                        if (projectsList.length === 1) {
                          projectId = projectsList[0]?.id;
                        }
                        
                        // 方法5: 从edm_projects中获取项目ID
                        const edmProjects = localStorage.getItem('edm_projects');
                        let edmProjectsList = [];
                        try {
                          edmProjectsList = edmProjects ? JSON.parse(edmProjects) : [];
                        } catch (e) {
                          console.warn('Failed to parse edm_projects from localStorage:', e);
                        }
                        if (edmProjectsList.length > 0) {
                          // 使用第一个项目或查找web-dev-2024
                          const webDevProject = edmProjectsList.find((p: any) => p?.id === 'web-dev-2024' || p?.id?.includes('web'));
                          if (webDevProject) {
                            projectId = webDevProject.id;
                          } else {
                            projectId = edmProjectsList[0]?.id;
                          }
                        }
                        
                        console.log('🔍 Budget Item Loading Debug:', {
                          urlPath: window.location.pathname,
                          pathSegments: pathSegments || [],
                          urlProjectId: urlProjectId || 'none',
                          currentProjectData: projectData ? 'found' : 'none',
                          projectsListLength: projectsList.length,
                          edmProjectsListLength: edmProjectsList.length,
                          finalProjectId: projectId
                        });
                        
                        // 尝试多个可能的localStorage键
                        const possibleKeys = [
                          `execution_stage_budgetItems_${projectId}`,
                          `budget_items_${projectId}`,
                          'budget_items',
                          `project_${projectId}_budgetItems`
                        ];
                        
                        // 如果项目ID仍是default，尝试已知的项目ID
                        if (projectId === 'default') {
                          possibleKeys.push('execution_stage_budgetItems_web-dev-2024');
                          possibleKeys.push('budget_items_web-dev-2024');
                        }
                        
                        let budgetItems = [];
                        let usedKey = '';
                        
                        for (const key of possibleKeys) {
                          const data = localStorage.getItem(key);
                          if (data) {
                            try {
                              const items = JSON.parse(data);
                              if (Array.isArray(items) && items.length > 0) {
                                budgetItems = items;
                                usedKey = key;
                                break;
                              }
                            } catch (e) {
                              console.warn(`Failed to parse data from key: ${key}`, e);
                            }
                          }
                        }
                        
                        console.log('💰 Budget Items Loading Result:', {
                          possibleKeys,
                          usedKey,
                          budgetItemsCount: budgetItems.length,
                          budgetItems: budgetItems.map(item => ({
                            id: item.id,
                            name: item.budgetItem || item.category,
                            currency: item.currency,
                            amount: item.plannedAmount
                          }))
                        });
                        
                        if (budgetItems.length === 0) {
                          return [
                            <Option key="no-data" value="" disabled>
                              No expense items found for project: {projectId}
                            </Option>
                          ];
                        }
                        
                        return budgetItems.map((item: any) => {
                          const name = String(item.budgetItem || item.category || 'Unknown');
                          const currency = String(item.currency || 'EUR');
                          const amount = Number(item.plannedAmount || 0);
                          return (
                            <Option key={item.id} value={item.id}>
                              {name} - {currency} {amount.toLocaleString()}
                            </Option>
                          );
                        });
                      } catch (error) {
                        console.error('❌ Failed to load expense items:', error instanceof Error ? error.message : String(error));
                        return [
                          <Option key="error" value="" disabled>
                            Error loading expense items
                          </Option>
                        ];
                      }
                    })()}
                  </Select>
                </Form.Item>

                <Form.Item
                  name="description"
                  label="Description"
                  style={{ flex: 1 }}
                >
                  <Input placeholder="Enter expense description" />
                </Form.Item>
              </div>
            </>
          )}

          {activeTab !== 'sales' ? (
          <div style={{ display: 'flex', gap: '16px' }}>
            <Form.Item
              name="currency"
              label="Currency"
              rules={[{ required: true, message: 'Please select currency' }]}
              initialValue="EUR"
              style={{ width: '120px' }}
            >
              <Select>
                <Option value="EUR">EUR</Option>
                <Option value="USD">USD</Option>
                <Option value="GBP">GBP</Option>
                <Option value="CNY">CNY</Option>
                <Option value="JPY">JPY</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="amount"
              label={<span>Amount (Excl. VAT) <span style={{ color: '#ff4d4f' }}>*</span></span>}
              rules={[{ required: true, message: 'Please enter the amount' }]}
              style={{ flex: 1 }}
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="0.00"
                formatter={value => value ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''}
                parser={value => value ? parseFloat(value.replace(/,/g, '')) || 0 : 0 as any}
              />
            </Form.Item>

            <Form.Item
              name="vatRate"
              label={<span>VAT Rate (%) <span style={{ color: '#ff4d4f' }}>*</span></span>}
              rules={[{ required: true, message: 'Please enter the VAT rate' }]}
              initialValue={21}
              style={{ flex: 1 }}
            >
              <InputNumber 
                min={0} 
                max={100} 
                defaultValue={21}
                style={{ width: '100%' }} 
                formatter={value => `${value}%`}
                parser={value => value ? parseFloat(value.replace('%', '')) : 21 as any}
                placeholder="21"
              />
            </Form.Item>

              <Form.Item
                label="Total Amount"
                style={{ flex: 1 }}
              >
                <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
                  prevValues.amount !== currentValues.amount || prevValues.vatRate !== currentValues.vatRate || prevValues.currency !== currentValues.currency
                }>
                  {({ getFieldValue }) => {
                    const amount = getFieldValue('amount') || 0;
                    const vatRate = getFieldValue('vatRate') || 0;
                    const currency = getFieldValue('currency') || 'EUR';
                    const vatAmount = (amount * vatRate) / 100;
                    const totalAmount = amount + vatAmount;
                    
                    // 简单的货币符号映射
                    const currencySymbols = {
                      'EUR': '€',
                      'USD': '$',
                      'GBP': '£',
                      'CNY': '¥',
                      'JPY': '¥'
                    };
                    
                    const symbol = currencySymbols[currency as keyof typeof currencySymbols] || '€';
                    const formattedAmount = `${symbol}${totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
                    
                    return (
                      <Input 
                        value={formattedAmount}
                        readOnly
                        style={{ 
                          backgroundColor: '#f5f5f5',
                          color: '#10B981',
                          fontWeight: '600'
                        }}
                      />
                    );
                  }}
                </Form.Item>
              </Form.Item>
          </div>
          ) : null}

          {/* 优化后的底部信息栏 */}
          <div style={{ 
            background: '#f8fafc', 
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            padding: '16px 20px',
            marginTop: '20px',
            marginBottom: '12px'
          }}>
            <Row gutter={[16, 12]}>
              <Col span={8}>
                <div style={{ marginBottom: '6px' }}>
                  <span style={{ 
                    fontSize: '12px', 
                    fontWeight: '500', 
                    color: '#64748b'
                  }}>
                    Invoice Date
                  </span>
                </div>
                <Form.Item name="invoiceDate" style={{ marginBottom: 0 }}>
                  <DatePicker 
                    style={{ 
                      width: '100%', 
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '13px'
                    }} 
                    format="DD/MM/YYYY"
                    placeholder="Select date"
                  />
                </Form.Item>
              </Col>
              
              <Col span={8}>
                <div style={{ marginBottom: '6px' }}>
                  <span style={{ 
                    fontSize: '12px', 
                    fontWeight: '500', 
                    color: '#64748b'
                  }}>
                    Payment Terms
                  </span>
                </div>
                <div style={{ display: 'flex', width: '100%' }}>
                  <Form.Item name="paymentTermDays" style={{ width: '30%', marginRight: '0', marginBottom: '0' }}>
                    <InputNumber 
                      min={1} 
                      style={{ 
                        width: '100%',
                        height: '32px',
                        borderRadius: '6px 0 0 6px'
                      }} 
                      placeholder="14"
                      controls={false}
                    />
                  </Form.Item>
                  <Form.Item name="paymentTermUnit" style={{ width: '70%', marginLeft: '0', marginBottom: '0' }} initialValue="working">
                    <Select 
                      style={{ 
                        width: '100%',
                        height: '32px',
                        borderRadius: '0 6px 6px 0'
                      }}
                      defaultValue="working"
                    >
                      <Option value="working">Working Days</Option>
                      <Option value="calendar">Calendar Days</Option>
                    </Select>
            </Form.Item>
                </div>
              </Col>
              
              <Col span={8}>
                <div style={{ marginBottom: '6px' }}>
                  <span style={{ 
                    fontSize: '12px', 
                    fontWeight: '500', 
                    color: '#64748b'
                  }}>
                    Due Date
                  </span>
                </div>
                <Form.Item name="dueDate" style={{ marginBottom: 0 }}>
                  <DatePicker 
                    style={{ 
                      width: '100%', 
                      height: '32px',
                      borderRadius: '6px',
                      fontSize: '13px'
                    }} 
                    format="DD/MM/YYYY"
                    placeholder="Auto calculated"
                  />
            </Form.Item>
              </Col>
            </Row>
          </div>

          {activeTab !== 'sales' && (
            <>
              <div style={{ display: 'flex', gap: '16px', marginTop: '24px' }}>
                <Form.Item
                  name="approvedBy"
                  label={<span>Approved By <span style={{ color: '#ff4d4f' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please enter approver name' }]}
                  style={{ flex: 1 }}
                >
                  <Input placeholder="Enter approver name" />
                </Form.Item>
              </div>

              <Form.Item
                name="reference"
                label="Reference"
              >
                <Input placeholder="Enter reference number or notes" />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </div>
  );
};
export default InvoiceManagement; 