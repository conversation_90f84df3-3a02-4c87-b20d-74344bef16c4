import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { IsEmail } from 'class-validator';
import bcrypt from 'bcryptjs';
import { Document } from './Document';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  username: string;

  @Column({ unique: true })
  @IsEmail()
  email: string;

  @Column()
  password: string;

  @Column({
    nullable: true
  })
  googleId: string;

  @Column({
    nullable: true
  })
  appleId: string;

  @Column({
    nullable: true
  })
  facebookId: string;

  @Column({
    type: 'varchar',
    default: 'user'
  })
  role: 'admin' | 'manager' | 'user';

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Document, document => document.uploadedBy)
  documents: Document[];

  hashPassword() {
    this.password = bcrypt.hashSync(this.password, 8);
  }

  checkPassword(unencryptedPassword: string) {
    return bcrypt.compareSync(unencryptedPassword, this.password);
  }
}