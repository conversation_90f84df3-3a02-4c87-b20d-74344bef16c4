# Opportunity数据自动关联到Basic Information功能测试说明

## 功能概述
当用户从Opportunity列表进入项目详情的Lead to Cash流程时，Basic Information阶段会自动关联并填充已有的Opportunity数据，避免用户重复输入。

## 核心功能测试

### 1. 数据自动映射测试

**测试步骤：**
1. 在Lead to Cash页面(/opportunity)，点击任意现有项目进入详情页
2. 点击Basic Info阶段，观察表单是否自动填充

**预期结果：**
- 显示"数据自动关联成功"提示卡片
- 客户信息自动填充：
  - Client Name: 从project.client映射
  - Client Tier: 智能转换（T1→SVIP, T2→VIP, T3→BA等）
  - Country: 从project.country映射
  - Primary Contact: 从project.owner映射
  - Industry: 根据project.category智能推断

- 机会信息自动填充：
  - Opportunity Name: 从project.name映射
  - Total Value: 从project.revenue映射
  - Win Probability: 智能转换（A→90%, B→75%, C→55%, D→30%）
  - Project Stage: 从project.stage映射
  - Priority: 根据tier智能设置

### 2. 数据优先级测试

**测试场景：**
- 如果该项目的Basic Information已经保存过，应优先显示阶段数据
- 如果是首次进入，显示从Opportunity映射的数据

**预期行为：**
数据优先级：阶段数据 > 项目数据 > 空值

### 3. 双向同步测试

**测试步骤：**
1. 在Basic Information中修改客户名称、收入等信息
2. 点击保存
3. 返回Opportunity列表查看是否同步更新

**预期结果：**
- 显示"数据保存成功！已更新：客户信息、机会详情"等具体字段提示
- 延迟显示"项目主信息已同步更新，修改内容已反映到机会列表中"
- Opportunity列表中的对应项目信息已更新

### 4. 智能字段转换测试

**客户层级转换：**
```javascript
// Opportunity → Basic Information
T1 → SVIP
T2 → VIP  
T3 → BA
其他 → 保持原值

// Basic Information → Opportunity (反向)
SVIP → T1
VIP → T2
BA → T3
其他 → 保持原值
```

**概率转换：**
```javascript
// 数字 → 等级
>= 90% → A
>= 75% → B  
>= 55% → C
< 55% → D

// 等级 → 数字
A → 90%
B → 75%
C → 55%
D → 30%
```

### 5. 用户体验测试

**视觉反馈：**
- 数据关联成功提示卡片（蓝色渐变背景）
- 详细的保存成功反馈
- 项目同步确认提示
- 完成度进度圈显示

**性能测试：**
- 页面加载时数据映射应该瞬时完成
- 保存操作应有适当的loading状态

## 开发环境测试

### 启动项目
```bash
# 终端1 - 启动后端
cd backend && npm run dev

# 终端2 - 启动前端  
cd frontend && npm start
```

### 测试路径
1. 访问 http://localhost:3000
2. 导航到 Lead to Cash (/opportunity)
3. 点击任意项目进入详情页
4. 测试Basic Information阶段的数据自动关联

### 控制台调试信息
在浏览器开发者工具中查看console，应该看到：
```
BasicInfoStage useEffect triggered
Project data: {...}
Auto-mapping project data to form fields...
Auto-mapped form data: {...}
✅ Successfully auto-filled form with project data
```

## 已知限制和注意事项

1. **仅在有project数据时生效**：新建项目时不会有自动映射
2. **依赖localStorage**：项目数据来源于本地存储的项目列表
3. **字段兼容性**：支持新旧数据格式的兼容处理

## 技术实现要点

- **自动映射触发**：BasicInfoStage组件的useEffect监听project变化
- **数据转换逻辑**：智能的字段映射和格式转换
- **双向同步**：ProjectDetail中的handleStageDataSave处理反向更新
- **用户反馈**：多层次的成功提示和状态显示

这个功能大大改善了用户体验，实现了Opportunity和Basic Information之间的无缝数据流转，避免了重复输入，提高了工作效率。 