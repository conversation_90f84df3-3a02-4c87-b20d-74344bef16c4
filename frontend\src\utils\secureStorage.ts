import encryptionService from './encryptionService';

/**
 * 存储项元数据
 */
interface StorageItemMetadata {
  encrypted: boolean;              // 是否加密
  timestamp: number;               // 存储时间
  expiresAt?: number;              // 过期时间
  version: string;                 // 版本
  checksum?: string;               // 校验和
}

/**
 * 存储项
 */
interface StorageItem<T> {
  data: T;                         // 数据
  metadata: StorageItemMetadata;   // 元数据
}

/**
 * 安全存储配置
 */
export interface SecureStorageConfig {
  namespace: string;               // 命名空间
  defaultTTL?: number;             // 默认过期时间（毫秒）
  encryptByDefault: boolean;       // 默认是否加密
  version: string;                 // 版本
  storageType: 'localStorage' | 'sessionStorage'; // 存储类型
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: SecureStorageConfig = {
  namespace: 'ltc_secure',
  defaultTTL: 24 * 60 * 60 * 1000, // 24小时
  encryptByDefault: true,
  version: '1.0',
  storageType: 'localStorage'
};

/**
 * 安全存储类
 */
export class SecureStorage {
  private config: SecureStorageConfig;
  private storage: Storage;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<SecureStorageConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.storage = this.config.storageType === 'localStorage' ? localStorage : sessionStorage;
  }

  /**
   * 获取带命名空间的键
   * @param key 键
   * @returns 带命名空间的键
   */
  private getNamespacedKey(key: string): string {
    return `${this.config.namespace}:${key}`;
  }

  /**
   * 设置项
   * @param key 键
   * @param value 值
   * @param options 选项
   * @returns 是否成功
   */
  public async set<T>(
    key: string,
    value: T,
    options: {
      encrypt?: boolean;
      ttl?: number;
    } = {}
  ): Promise<boolean> {
    try {
      const shouldEncrypt = options.encrypt ?? this.config.encryptByDefault;
      const ttl = options.ttl ?? this.config.defaultTTL;
      
      // 创建元数据
      const metadata: StorageItemMetadata = {
        encrypted: shouldEncrypt,
        timestamp: Date.now(),
        version: this.config.version
      };
      
      // 设置过期时间
      if (ttl) {
        metadata.expiresAt = Date.now() + ttl;
      }
      
      // 准备存储项
      let storageItem: StorageItem<any> = {
        data: value,
        metadata
      };
      
      // 如果需要加密
      if (shouldEncrypt) {
        // 将数据转换为字符串
        const dataStr = typeof value === 'string' ? value : JSON.stringify(value);
        
        // 加密数据
        const encryptedData = await encryptionService.encrypt(dataStr);
        
        // 更新存储项
        storageItem.data = encryptedData;
        
        // 计算校验和
        metadata.checksum = await encryptionService.hash(dataStr);
      }
      
      // 存储数据
      const namespacedKey = this.getNamespacedKey(key);
      this.storage.setItem(namespacedKey, JSON.stringify(storageItem));
      
      return true;
    } catch (error) {
      console.error('安全存储设置失败:', error);
      return false;
    }
  }

  /**
   * 获取项
   * @param key 键
   * @returns 值或null
   */
  public async get<T>(key: string): Promise<T | null> {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      const item = this.storage.getItem(namespacedKey);
      
      if (!item) {
        return null;
      }
      
      // 解析存储项
      const storageItem = JSON.parse(item) as StorageItem<any>;
      const { data, metadata } = storageItem;
      
      // 检查是否过期
      if (metadata.expiresAt && metadata.expiresAt < Date.now()) {
        // 自动删除过期项
        this.remove(key);
        return null;
      }
      
      // 如果数据已加密
      if (metadata.encrypted) {
        try {
          // 解密数据
          const decryptedData = await encryptionService.decrypt(data);
          
          // 尝试解析为JSON
          try {
            return JSON.parse(decryptedData);
          } catch {
            // 如果不是JSON，直接返回
            return decryptedData as unknown as T;
          }
        } catch (decryptError) {
          console.error('解密数据失败:', decryptError);
          return null;
        }
      }
      
      // 未加密数据直接返回
      return data;
    } catch (error) {
      console.error('安全存储获取失败:', error);
      return null;
    }
  }

  /**
   * 移除项
   * @param key 键
   * @returns 是否成功
   */
  public remove(key: string): boolean {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      this.storage.removeItem(namespacedKey);
      return true;
    } catch (error) {
      console.error('安全存储移除失败:', error);
      return false;
    }
  }

  /**
   * 清除所有项
   * @returns 是否成功
   */
  public clear(): boolean {
    try {
      // 只清除当前命名空间的项
      const keys = Object.keys(this.storage);
      const namespacedKeys = keys.filter(key => key.startsWith(`${this.config.namespace}:`));
      
      for (const key of namespacedKeys) {
        this.storage.removeItem(key);
      }
      
      return true;
    } catch (error) {
      console.error('安全存储清除失败:', error);
      return false;
    }
  }

  /**
   * 获取所有键
   * @returns 键列表
   */
  public keys(): string[] {
    try {
      const keys = Object.keys(this.storage);
      const namespacedKeys = keys.filter(key => key.startsWith(`${this.config.namespace}:`));
      
      // 移除命名空间前缀
      return namespacedKeys.map(key => key.substring(this.config.namespace.length + 1));
    } catch (error) {
      console.error('安全存储获取键失败:', error);
      return [];
    }
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns 是否存在
   */
  public has(key: string): boolean {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      return this.storage.getItem(namespacedKey) !== null;
    } catch (error) {
      console.error('安全存储检查键失败:', error);
      return false;
    }
  }

  /**
   * 获取项元数据
   * @param key 键
   * @returns 元数据或null
   */
  public getMetadata(key: string): StorageItemMetadata | null {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      const item = this.storage.getItem(namespacedKey);
      
      if (!item) {
        return null;
      }
      
      const storageItem = JSON.parse(item) as StorageItem<any>;
      return storageItem.metadata;
    } catch (error) {
      console.error('安全存储获取元数据失败:', error);
      return null;
    }
  }

  /**
   * 更新项过期时间
   * @param key 键
   * @param ttl 过期时间（毫秒）
   * @returns 是否成功
   */
  public async updateExpiry(key: string, ttl: number): Promise<boolean> {
    try {
      const namespacedKey = this.getNamespacedKey(key);
      const item = this.storage.getItem(namespacedKey);
      
      if (!item) {
        return false;
      }
      
      const storageItem = JSON.parse(item) as StorageItem<any>;
      
      // 更新过期时间
      storageItem.metadata.expiresAt = Date.now() + ttl;
      
      // 保存更新后的项
      this.storage.setItem(namespacedKey, JSON.stringify(storageItem));
      
      return true;
    } catch (error) {
      console.error('安全存储更新过期时间失败:', error);
      return false;
    }
  }

  /**
   * 清理过期项
   * @returns 清理的项数
   */
  public cleanExpired(): number {
    try {
      const keys = this.keys();
      let cleanedCount = 0;
      
      for (const key of keys) {
        const metadata = this.getMetadata(key);
        
        if (metadata && metadata.expiresAt && metadata.expiresAt < Date.now()) {
          this.remove(key);
          cleanedCount++;
        }
      }
      
      return cleanedCount;
    } catch (error) {
      console.error('安全存储清理过期项失败:', error);
      return 0;
    }
  }
}

// 创建默认实例
const secureStorage = new SecureStorage();

export default secureStorage;
