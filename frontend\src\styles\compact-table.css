/* 紧凑型表格样式 - 修复头部文字变形问题 */
.compact-table {
  font-size: 12px;
}

.compact-table .ant-table {
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

.compact-table .ant-table-container {
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.compact-table .ant-table-header {
  margin-bottom: 0 !important;
}

.compact-table .ant-table-body {
  margin-top: 0 !important;
  border-top: none !important;
}

.compact-table .ant-table-thead > tr > th {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0 !important;
  background-color: #fafafa !important;
  font-weight: 600 !important;
  color: rgba(0, 0, 0, 0.85) !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.compact-table .ant-table-tbody > tr > td {
  padding: 8px 12px;
  border-top: none !important;
  border-bottom: 1px solid #f0f0f0 !important;
  vertical-align: middle !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
}

.compact-table .ant-table-tbody > tr:first-child > td {
  border-top: none !important;
}

.compact-table .ant-table-thead + .ant-table-tbody {
  margin-top: 0 !important;
  border-top: none !important;
}

.compact-table .ant-table-row {
  height: 48px !important;
  transition: background-color 0.3s ease;
}

.compact-table .ant-table-row:hover > td {
  background-color: #f5f5f5;
}

/* 确保按钮在紧凑型表格中正常显示 */
.compact-table .ant-btn {
  padding: 4px 8px !important;
  height: 28px !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;
}

.compact-table .ant-btn-text {
  padding: 0 4px !important;
}

/* 确保表格内的链接正常显示 */
.compact-table .ant-table-tbody a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

.compact-table .ant-table-tbody a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 表格滚动条样式 */
.compact-table .ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.compact-table .ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.compact-table .ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.compact-table .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格排序箭头样式 */
.compact-table .ant-table-column-sorter {
  margin-left: 4px;
}

.compact-table .ant-table-column-sorter-up,
.compact-table .ant-table-column-sorter-down {
  font-size: 10px !important;
  color: #bfbfbf !important;
}

.compact-table .ant-table-column-sorter-up.active,
.compact-table .ant-table-column-sorter-down.active {
  color: #1890ff !important;
}

/* 表格加载状态 */
.compact-table .ant-spin-container {
  position: relative;
}

.compact-table .ant-spin-nested-loading > div > .ant-spin {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 空数据状态 */
.compact-table .ant-empty {
  padding: 32px 16px !important;
}

.compact-table .ant-empty-description {
  color: rgba(0, 0, 0, 0.45) !important;
  font-size: 14px !important;
}

/* 表格边框 */
.compact-table .ant-table-bordered .ant-table-thead > tr > th,
.compact-table .ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

.compact-table .ant-table-bordered .ant-table-thead > tr > th:last-child,
.compact-table .ant-table-bordered .ant-table-tbody > tr > td:last-child {
  border-right: none;
}

/* 固定列宽 */
.compact-table .ant-table-thead > tr > th:nth-child(1),
.compact-table .ant-table-tbody > tr > td:nth-child(1) {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
}

.compact-table .ant-table-thead > tr > th:nth-child(2),
.compact-table .ant-table-tbody > tr > td:nth-child(2) {
  width: 200px;
  min-width: 200px;
  max-width: 200px;
}

.compact-table .ant-table-thead > tr > th:nth-child(3),
.compact-table .ant-table-tbody > tr > td:nth-child(3) {
  width: 150px;
  min-width: 150px;
  max-width: 150px;
}

.compact-table .ant-table-thead > tr > th:nth-child(4),
.compact-table .ant-table-tbody > tr > td:nth-child(4) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.compact-table .ant-table-thead > tr > th:nth-child(5),
.compact-table .ant-table-tbody > tr > td:nth-child(5) {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
}

.compact-table .ant-table-thead > tr > th:nth-child(6),
.compact-table .ant-table-tbody > tr > td:nth-child(6) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.compact-table .ant-table-thead > tr > th:nth-child(7),
.compact-table .ant-table-tbody > tr > td:nth-child(7) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  text-align: right;
}

.compact-table .ant-table-thead > tr > th:nth-child(8),
.compact-table .ant-table-tbody > tr > td:nth-child(8) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.compact-table .ant-table-thead > tr > th:nth-child(9),
.compact-table .ant-table-tbody > tr > td:nth-child(9) {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
  text-align: right;
}

.compact-table .ant-table-thead > tr > th:nth-child(10),
.compact-table .ant-table-tbody > tr > td:nth-child(10) {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
  text-align: center;
}

.compact-table .ant-table-thead > tr > th:nth-child(11),
.compact-table .ant-table-tbody > tr > td:nth-child(11) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.compact-table .ant-table-thead > tr > th:nth-child(12),
.compact-table .ant-table-tbody > tr > td:nth-child(12) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
}

.compact-table .ant-table-thead > tr > th:nth-child(13),
.compact-table .ant-table-tbody > tr > td:nth-child(13) {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
  text-align: center;
}

/* 响应式优化 */
@media (max-width: 1600px) {
  .compact-table {
    font-size: 11px;
  }
  
  .compact-table .ant-table-thead > tr > th,
  .compact-table .ant-table-tbody > tr > td {
    padding: 6px 8px;
  }
}

@media (max-width: 1400px) {
  .compact-table {
    font-size: 10px;
  }
  
  .compact-table .ant-table-thead > tr > th,
  .compact-table .ant-table-tbody > tr > td {
    padding: 4px 6px;
  }
}

@media (max-width: 1200px) {
  .compact-table .ant-table-thead > tr > th,
  .compact-table .ant-table-tbody > tr > td {
    padding: 4px 8px !important;
  }
}

.compact-table .ant-table-column-title {
  font-weight: 600;
  font-size: 14px !important;
}
