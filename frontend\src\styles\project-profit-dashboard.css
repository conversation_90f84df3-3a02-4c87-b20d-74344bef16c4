/* Project Profit Dashboard 样式 */
.project-profit-dashboard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* 颜色变量 */
:root {
  --primary: #FF7A00;
  --primary-light: #FFF1E6;
  --text-primary: #1F2023;
  --text-secondary: #4A4A4A;
  --bg-color: #F7F8FA;
  --white: #FFFFFF;
  --border: #E2E8F0;
  --border-focus: #FF7A00;
  --error: #E53E3E;
  --success: #34C38F;
  --warning: #F6AD55;
  --gray-light: #F1F5F9;
  --gray: #CBD5E0;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --radius: 6px;
  --spacing: 24px;
  --card-background: #FFFFFF;
  --radius-md: 8px;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* 财务指标颜色 - 更简约的色调，透明度30% */
  --revenue-color: rgba(100, 116, 139, 0.3);
  --direct-cost-color: rgba(148, 163, 184, 0.3);
  --gross-profit-color: rgba(100, 116, 139, 0.3);
  --indirect-cost-color: rgba(203, 213, 225, 0.3);
  --net-profit-color: rgba(71, 85, 105, 0.3);
}

/* 瀑布图样式 */
.waterfall-chart {
  position: relative;
  height: 100%;
  display: flex;
  padding: 10px 0 40px 60px; /* 为Y轴和底部标签留出空间 */
}

/* Y轴样式 */
.y-axis {
  position: absolute;
  left: 0;
  top: 10px;
  bottom: 40px;
  width: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  padding-right: 10px;
  font-size: 11px;
  color: #666;
}

.y-tick {
  height: 20px;
  display: flex;
  align-items: center;
}

.zero-tick {
  font-weight: bold;
  color: #333;
}

/* 金额标签样式 */
.amount-labels {
  position: absolute;
  top: 0;
  left: 60px;
  right: 0;
  bottom: 40px;
  pointer-events: none;
}

.amount-label {
  position: absolute;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

/* 图表内容区域 */
.chart-content {
  position: relative;
  flex: 1;
  height: 100%;
}

/* 网格线 */
.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  width: 100%;
  height: 1px;
  background-color: rgba(200, 200, 200, 0.3);
}

/* 零线样式 - 只在图表中间显示一条 */
.zero-line {
  height: 0; /* 隐藏这个零线，使用 zero-line-main 代替 */
  background-color: transparent;
}

/* 瀑布图容器 */
.waterfall-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding-top: 20px; /* 为顶部的值留出空间 */
  padding-bottom: 30px; /* 为底部标签留出空间 */
}

/* 零线 - 主零线 */
.zero-line-main {
  position: absolute;
  left: 0;
  right: 0;
  top: 250px; /* 零线位置 - 与柱子对齐 */
  height: 2px;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 5;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3); /* 轻微阴影使线条更清晰 */
  font-weight: bold;
}

/* 柱子容器 */
.waterfall-column {
  position: absolute;
  height: 100%;
  bottom: 30px; /* 为标签留出空间 */
}

/* 柱子样式 */
.waterfall-bar {
  position: absolute;
  width: 100%;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.waterfall-bar:hover {
  opacity: 0.9;
  filter: brightness(1.05);
}

/* 不同类型柱子的颜色 */
.revenue {
  background-color: rgba(255, 122, 0, 0.8);
  border-radius: 4px;
}

.cost {
  background-color: rgba(239, 68, 68, 0.8);
  border-radius: 4px;
}

.profit {
  background-color: rgba(4, 120, 87, 0.8);
  border-radius: 4px;
}

.gross-profit {
  background-color: rgba(16, 185, 129, 0.8);
  border-radius: 4px;
}

/* 柱子上的值 */
.bar-value {
  color: rgba(0, 0, 0, 0.7);
  font-weight: 600;
  font-size: 12px;
  position: absolute;
  width: 100%;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
}

/* 柱子标签 */
.waterfall-label {
  position: absolute;
  bottom: -25px;
  font-size: 12px;
  font-weight: 500;
  color: #4a4a4a;
  text-align: center;
  width: 100%;
  white-space: nowrap;
}

/* 摘要卡片样式 */
.project-profit-dashboard .summary-card {
  background-color: var(--white);
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  padding: 16px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border);
  transition: transform 0.2s, box-shadow 0.2s;
}

.project-profit-dashboard .summary-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.project-profit-dashboard .card-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.project-profit-dashboard .card-value {
  font-size: 22px;
  font-weight: 700;
  display: flex;
  align-items: baseline;
}

.project-profit-dashboard .card-percentage {
  font-size: 12px;
  font-weight: 600;
  margin-left: 2px;
  position: relative;
  top: -2px;
}

/* 利润结构条样式 */
.project-profit-dashboard .profit-structure {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  margin-bottom: 24px;
}

.project-profit-dashboard .profit-bar-container {
  width: 100%;
  margin-bottom: 16px;
}

.project-profit-dashboard .profit-bar {
  display: flex;
  height: 48px;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.project-profit-dashboard .profit-segment {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.project-profit-dashboard .profit-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 12px;
}

.project-profit-dashboard .legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.project-profit-dashboard .legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 8px;
}

/* 成本明细表样式 */
.project-profit-dashboard .cost-breakdown {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 20px;
}

.project-profit-dashboard table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.project-profit-dashboard th {
  background-color: var(--gray-light);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
  color: var(--text-secondary);
  position: sticky;
  top: 0;
}

.project-profit-dashboard td {
  padding: 12px 16px;
  border-top: 1px solid var(--border);
  font-size: 14px;
}

.project-profit-dashboard tr:hover td {
  background-color: var(--gray-light);
}

/* 视图切换按钮样式 */
.project-profit-dashboard .view-toggle {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
}

.project-profit-dashboard .toggle-btn {
  border-radius: 0;
  margin: 0;
  min-width: 90px;
  font-size: 13px;
  font-weight: 500;
}

.project-profit-dashboard .toggle-btn:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.project-profit-dashboard .toggle-btn:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.project-profit-dashboard .toggle-btn.active {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .project-profit-dashboard .grid-cols-5 {
    grid-template-columns: repeat(2, 1fr);
  }

  .project-profit-dashboard .profit-legend {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .project-profit-dashboard .grid-cols-5 {
    grid-template-columns: 1fr;
  }

  .project-profit-dashboard .profit-segment {
    font-size: 12px;
  }
}
