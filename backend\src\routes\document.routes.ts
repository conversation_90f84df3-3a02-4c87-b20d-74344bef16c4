import { Router } from 'express';
import { getRepository } from 'typeorm';
import { Document } from '../entities/Document';
import { Project } from '../entities/Project';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

// 使用认证中间件保护所有路由
router.use(authMiddleware);

// 获取所有文档
router.get('/', async (_req, res) => {
  try {
    const documentRepository = getRepository(Document);
    const documents = await documentRepository.find({ relations: ['project', 'uploadedBy'] });
    res.json(documents);
  } catch (error) {
    res.status(500).json({ message: '获取文档列表失败', error });
  }
});

// 获取单个文档
router.get('/:id', async (req, res) => {
  try {
    const documentRepository = getRepository(Document);
    const document = await documentRepository.findOne(req.params.id, { relations: ['project', 'uploadedBy'] });
    
    if (!document) {
      return res.status(404).json({ message: '文档不存在' });
    }
    
    res.json(document);
  } catch (error) {
    res.status(500).json({ message: '获取文档失败', error });
  }
});

// 获取项目的所有文档
router.get('/project/:projectId', async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(projectId);
    
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    const documentRepository = getRepository(Document);
    const documents = await documentRepository.find({ 
      where: { project: project },
      relations: ['uploadedBy']
    });
    
    res.json(documents);
  } catch (error) {
    res.status(500).json({ message: '获取项目文档失败', error });
  }
});

// 创建新文档
router.post('/', async (req, res) => {
  try {
    const { projectId, ...documentData } = req.body;
    
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(projectId);
    
    if (!projectId || !project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    const documentRepository = getRepository(Document);
    const document = documentRepository.create({
      ...documentData,
      project,
      uploadedBy: req.user
    });
    
    const result = await documentRepository.save(document);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({ message: '创建文档失败', error });
  }
});

// 更新文档
router.put('/:id', async (req, res) => {
  try {
    const documentRepository = getRepository(Document);
    const document = await documentRepository.findOne(req.params.id, { relations: ['uploadedBy'] });
    
    if (!document) {
      return res.status(404).json({ message: '文档不存在' });
    }
    
    // 只允许文档上传者或管理员更新文档
    if (document.uploadedBy.id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '没有权限更新此文档' });
    }
    
    documentRepository.merge(document, req.body);
    const result = await documentRepository.save(document);
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: '更新文档失败', error });
  }
});

// 删除文档
router.delete('/:id', async (req, res) => {
  try {
    const documentRepository = getRepository(Document);
    const document = await documentRepository.findOne(req.params.id, { relations: ['uploadedBy'] });
    
    if (!document) {
      return res.status(404).json({ message: '文档不存在' });
    }
    
    // 只允许文档上传者或管理员删除文档
    if (document.uploadedBy.id !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: '没有权限删除此文档' });
    }
    
    await documentRepository.remove(document);
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: '删除文档失败', error });
  }
});

export default router;
