/* 主题切换组件样式 */

/* 开关模式 */
.theme-switcher-switch {
  background-color: var(--primary-light) !important;
}

.theme-switcher-switch.ant-switch-checked {
  background-color: var(--primary) !important;
}

/* 按钮模式 */
.theme-switcher-button {
  color: var(--text-primary) !important;
  transition: all var(--transition-fast);
}

.theme-switcher-button:hover {
  color: var(--primary) !important;
  background-color: var(--primary-light) !important;
}

/* 下拉菜单模式 */
.theme-switcher-dropdown {
  color: var(--text-primary) !important;
  transition: all var(--transition-fast);
}

.theme-switcher-dropdown:hover {
  color: var(--primary) !important;
  background-color: var(--primary-light) !important;
}
