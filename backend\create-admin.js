const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');

// 连接到SQLite数据库
const db = new sqlite3.Database('./ltc_project.sqlite', (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('成功连接到SQLite数据库');
});

// 检查users表是否存在，如果不存在则创建
db.run(`CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  username TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT DEFAULT 'user',
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
)`, (err) => {
  if (err) {
    console.error('创建users表失败:', err.message);
    db.close();
    process.exit(1);
  }
  
  // 检查是否已存在管理员用户
  db.get("SELECT * FROM users WHERE role = 'admin'", [], (err, row) => {
    if (err) {
      console.error('查询失败:', err.message);
      db.close();
      process.exit(1);
    }
    
    if (row) {
      console.log('超级管理员已存在:');
      console.log('用户名:', row.username);
      console.log('邮箱:', row.email);
      console.log('请使用此邮箱和您设置的密码登录');
      db.close();
      process.exit(0);
    } else {
      // 创建超级管理员用户
      const adminUser = {
        id: generateUUID(),
        username: 'admin',
        email: '<EMAIL>',
        password: bcrypt.hashSync('admin123', 8),
        role: 'admin'
      };
      
      // 插入管理员用户
      db.run(`INSERT INTO users (id, username, email, password, role) 
              VALUES (?, ?, ?, ?, ?)`, 
        [adminUser.id, adminUser.username, adminUser.email, adminUser.password, adminUser.role], 
        function(err) {
          if (err) {
            console.error('创建超级管理员失败:', err.message);
            db.close();
            process.exit(1);
          }
          
          console.log('超级管理员创建成功!');
          console.log('用户名:', adminUser.username);
          console.log('邮箱:', adminUser.email);
          console.log('密码: admin123');
          console.log('请使用以上信息登录系统');
          
          db.close();
          process.exit(0);
        }
      );
    }
  });
});

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
