@echo off
chcp 65001 >nul
echo ===================================
echo    LTC项目管理系统停止脚本
echo ===================================
echo.

:: 显示当前运行的Node.js进程
echo 当前运行的Node.js进程:
tasklist | findstr node.exe

echo.
echo 正在停止LTC项目服务...

:: 停止占用3000端口的进程（前端）
echo 停止前端服务 (端口 3000)...
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :3000') do (
    echo 停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

:: 停止占用5002端口的进程（后端）
echo 停止后端服务 (端口 5002)...
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :5002') do (
    echo 停止进程 %%a
    taskkill /f /pid %%a >nul 2>&1
)

:: 停止所有Node.js进程（可选，如果上面的方法无效）
echo.
set /p choice="是否要停止所有Node.js进程? 这会影响其他Node.js应用 (Y/N): "
if /i "%choice%"=="Y" (
    echo 正在停止所有Node.js进程...
    taskkill /f /im node.exe >nul 2>&1
    echo ✓ 已停止所有Node.js进程
) else (
    echo 跳过停止所有Node.js进程
)

echo.
timeout /t 2 >nul

:: 验证端口是否已释放
echo 验证端口状态...
netstat -ano | findstr :3000 >nul
if errorlevel 1 (
    echo ✓ 端口 3000 已释放
) else (
    echo ⚠ 端口 3000 仍被占用
)

netstat -ano | findstr :5002 >nul
if errorlevel 1 (
    echo ✓ 端口 5002 已释放
) else (
    echo ⚠ 端口 5002 仍被占用
)

echo.
echo ===================================
echo    停止完成!
echo ===================================
echo.
echo LTC项目服务已停止
echo 如需重新启动，请运行 start-ltc-new.bat
echo.
pause 