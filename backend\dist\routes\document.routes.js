"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const typeorm_1 = require("typeorm");
const Document_1 = require("../entities/Document");
const Project_1 = require("../entities/Project");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authMiddleware);
router.get('/', async (_req, res) => {
    try {
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const documents = await documentRepository.find({ relations: ['project', 'uploadedBy'] });
        res.json(documents);
    }
    catch (error) {
        res.status(500).json({ message: '获取文档列表失败', error });
    }
});
router.get('/:id', async (req, res) => {
    try {
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const document = await documentRepository.findOne(req.params.id, { relations: ['project', 'uploadedBy'] });
        if (!document) {
            return res.status(404).json({ message: '文档不存在' });
        }
        res.json(document);
    }
    catch (error) {
        res.status(500).json({ message: '获取文档失败', error });
    }
});
router.get('/project/:projectId', async (req, res) => {
    try {
        const { projectId } = req.params;
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(projectId);
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const documents = await documentRepository.find({
            where: { project: project },
            relations: ['uploadedBy']
        });
        res.json(documents);
    }
    catch (error) {
        res.status(500).json({ message: '获取项目文档失败', error });
    }
});
router.post('/', async (req, res) => {
    try {
        const { projectId, ...documentData } = req.body;
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne(projectId);
        if (!projectId || !project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const document = documentRepository.create({
            ...documentData,
            project,
            uploadedBy: req.user
        });
        const result = await documentRepository.save(document);
        res.status(201).json(result);
    }
    catch (error) {
        res.status(500).json({ message: '创建文档失败', error });
    }
});
router.put('/:id', async (req, res) => {
    try {
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const document = await documentRepository.findOne(req.params.id, { relations: ['uploadedBy'] });
        if (!document) {
            return res.status(404).json({ message: '文档不存在' });
        }
        if (document.uploadedBy.id !== req.user.id && req.user.role !== 'admin') {
            return res.status(403).json({ message: '没有权限更新此文档' });
        }
        documentRepository.merge(document, req.body);
        const result = await documentRepository.save(document);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ message: '更新文档失败', error });
    }
});
router.delete('/:id', async (req, res) => {
    try {
        const documentRepository = (0, typeorm_1.getRepository)(Document_1.Document);
        const document = await documentRepository.findOne(req.params.id, { relations: ['uploadedBy'] });
        if (!document) {
            return res.status(404).json({ message: '文档不存在' });
        }
        if (document.uploadedBy.id !== req.user.id && req.user.role !== 'admin') {
            return res.status(403).json({ message: '没有权限删除此文档' });
        }
        await documentRepository.remove(document);
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: '删除文档失败', error });
    }
});
exports.default = router;
//# sourceMappingURL=document.routes.js.map