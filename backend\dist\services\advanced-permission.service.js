"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedPermissionService = void 0;
const typeorm_1 = require("typeorm");
const PermissionTemplate_1 = require("../entities/PermissionTemplate");
const ProjectCollaborator_1 = require("../entities/ProjectCollaborator");
const User_1 = require("../entities/User");
const Project_1 = require("../entities/Project");
const errors_1 = require("../utils/errors");
class AdvancedPermissionService {
    constructor() {
        this.templateRepository = (0, typeorm_1.getRepository)(PermissionTemplate_1.PermissionTemplate);
        this.collaboratorRepository = (0, typeorm_1.getRepository)(ProjectCollaborator_1.ProjectCollaborator);
        this.userRepository = (0, typeorm_1.getRepository)(User_1.User);
        this.projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
    }
    async createTemplate(name, description, permissions, type = PermissionTemplate_1.TemplateType.CUSTOM, createdById) {
        const existingTemplate = await this.templateRepository.findOne({ where: { name } });
        if (existingTemplate) {
            throw new errors_1.BadRequestError('权限模板名称已存在');
        }
        const template = this.templateRepository.create({
            name,
            description,
            type,
            permissions,
            createdBy: createdById ? { id: createdById } : undefined
        });
        return await this.templateRepository.save(template);
    }
    async getAllTemplates() {
        return await this.templateRepository.find({
            where: { isActive: true },
            relations: ['createdBy'],
            order: { createdAt: 'DESC' }
        });
    }
    async getTemplatesByType(type) {
        return await this.templateRepository.find({
            where: { type, isActive: true },
            relations: ['createdBy'],
            order: { createdAt: 'DESC' }
        });
    }
    async applyTemplateToCollaborator(templateId, collaboratorId, appliedById) {
        const template = await this.templateRepository.findOne(templateId);
        if (!template || !template.isActive) {
            throw new errors_1.NotFoundError('权限模板不存在或已禁用');
        }
        const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
            relations: ['project', 'user']
        });
        if (!collaborator) {
            throw new errors_1.NotFoundError('协作者不存在');
        }
        await this.validateManagePermission(collaborator.project.id, appliedById);
        collaborator.permissions = template.permissions;
        return await this.collaboratorRepository.save(collaborator);
    }
    async batchApplyTemplate(templateId, collaboratorIds, appliedById) {
        const template = await this.templateRepository.findOne(templateId);
        if (!template || !template.isActive) {
            throw new errors_1.NotFoundError('权限模板不存在或已禁用');
        }
        const collaborators = await this.collaboratorRepository.findByIds(collaboratorIds, {
            relations: ['project', 'user']
        });
        const results = [];
        for (const collaborator of collaborators) {
            try {
                await this.validateManagePermission(collaborator.project.id, appliedById);
                collaborator.permissions = template.permissions;
                const saved = await this.collaboratorRepository.save(collaborator);
                results.push(saved);
            }
            catch (error) {
                console.warn(`Failed to apply template to collaborator ${collaborator.id}:`, error);
            }
        }
        return results;
    }
    async createTimeRestrictedPermission(projectId, userId, permissions, timeRestrictions, createdById) {
        await this.validateManagePermission(projectId, createdById);
        let collaborator = await this.collaboratorRepository.findOne({
            where: { project: { id: projectId }, user: { id: userId } }
        });
        if (!collaborator) {
            collaborator = this.collaboratorRepository.create({
                project: { id: projectId },
                user: { id: userId },
                role: ProjectCollaborator_1.CollaboratorRole.GUEST_EDITOR,
                status: 'accepted',
                permissions: {
                    ...permissions,
                    timeRestrictions
                }
            });
        }
        else {
            collaborator.permissions = {
                ...permissions,
                timeRestrictions
            };
        }
        return await this.collaboratorRepository.save(collaborator);
    }
    checkTimeRestrictions(timeRestrictions, currentTime = new Date()) {
        if (!timeRestrictions)
            return true;
        if (timeRestrictions.startDate && currentTime < timeRestrictions.startDate) {
            return false;
        }
        if (timeRestrictions.endDate && currentTime > timeRestrictions.endDate) {
            return false;
        }
        if (timeRestrictions.daysOfWeek && timeRestrictions.daysOfWeek.length > 0) {
            const currentDay = currentTime.getDay();
            if (!timeRestrictions.daysOfWeek.includes(currentDay)) {
                return false;
            }
        }
        if (timeRestrictions.timeRanges && timeRestrictions.timeRanges.length > 0) {
            const currentTimeStr = currentTime.toTimeString().slice(0, 5);
            const isInRange = timeRestrictions.timeRanges.some(range => {
                return currentTimeStr >= range.start && currentTimeStr <= range.end;
            });
            if (!isInRange) {
                return false;
            }
        }
        return true;
    }
    checkIpRestrictions(ipRestrictions, clientIp) {
        if (!ipRestrictions || ipRestrictions.length === 0)
            return true;
        return ipRestrictions.some(allowedIp => {
            if (allowedIp.includes('/')) {
                const [network, prefixLength] = allowedIp.split('/');
                return clientIp.startsWith(network.split('.').slice(0, parseInt(prefixLength) / 8).join('.'));
            }
            else {
                return allowedIp === clientIp || allowedIp === '*';
            }
        });
    }
    async validateAdvancedPermissions(projectId, userId, permission, clientIp, currentTime = new Date()) {
        const collaborator = await this.collaboratorRepository.findOne({
            where: { project: { id: projectId }, user: { id: userId }, status: 'accepted' }
        });
        if (!collaborator) {
            return { allowed: false, reason: '不是项目协作者' };
        }
        const permissions = collaborator.permissions;
        const hasBasicPermission = this.checkBasicPermission(permissions, permission);
        if (!hasBasicPermission) {
            return { allowed: false, reason: '没有基本权限' };
        }
        if (permissions.timeRestrictions) {
            const timeAllowed = this.checkTimeRestrictions(permissions.timeRestrictions, currentTime);
            if (!timeAllowed) {
                return { allowed: false, reason: '当前时间不允许访问' };
            }
        }
        if (permissions.ipRestrictions && clientIp) {
            const ipAllowed = this.checkIpRestrictions(permissions.ipRestrictions, clientIp);
            if (!ipAllowed) {
                return { allowed: false, reason: 'IP地址不在允许范围内' };
            }
        }
        return { allowed: true };
    }
    async batchUpdatePermissions(updates, updatedById) {
        const results = [];
        for (const update of updates) {
            try {
                const collaborator = await this.collaboratorRepository.findOne(update.collaboratorId, {
                    relations: ['project']
                });
                if (collaborator) {
                    await this.validateManagePermission(collaborator.project.id, updatedById);
                    collaborator.permissions = update.permissions;
                    const saved = await this.collaboratorRepository.save(collaborator);
                    results.push(saved);
                }
            }
            catch (error) {
                console.warn(`Failed to update collaborator ${update.collaboratorId}:`, error);
            }
        }
        return results;
    }
    async validateManagePermission(projectId, userId) {
        const collaborator = await this.collaboratorRepository.findOne({
            where: { project: { id: projectId }, user: { id: userId } }
        });
        if (!collaborator || ![ProjectCollaborator_1.CollaboratorRole.OWNER, ProjectCollaborator_1.CollaboratorRole.ADMIN].includes(collaborator.role)) {
            throw new errors_1.ForbiddenError('没有管理权限');
        }
    }
    checkBasicPermission(permissions, permission) {
        switch (permission) {
            case 'edit':
                return permissions.canEdit;
            case 'delete':
                return permissions.canDelete;
            case 'invite':
                return permissions.canInvite;
            case 'manageStages':
                return permissions.canManageStages;
            case 'uploadDocuments':
                return permissions.canUploadDocuments;
            case 'viewFinancials':
                return permissions.canViewFinancials;
            case 'comment':
                return permissions.canComment;
            case 'mention':
                return permissions.canMention;
            default:
                return false;
        }
    }
}
exports.AdvancedPermissionService = AdvancedPermissionService;
//# sourceMappingURL=advanced-permission.service.js.map