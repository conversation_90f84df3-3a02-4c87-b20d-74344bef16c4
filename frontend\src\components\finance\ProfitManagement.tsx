import React, { useState } from 'react';
import { Card, Row, Col, Typo<PERSON>, Select, Button, Space, Tooltip, Popconfirm } from 'antd';
import { BarChartOutlined, TrophyOutlined, CalendarOutlined, TableOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer } from 'recharts';

const { Text } = Typography;

interface ProfitManagementProps {
  // 可以根据需要添加属性
}



const ProfitManagement: React.FC<ProfitManagementProps> = () => {
  const [viewType, setViewType] = useState<'chart' | 'table'>('chart');
  const [timePeriod, setTimePeriod] = useState('monthly');

  // 模拟数据结构与Revenue Management一致
  const profitData = [
    { time: '07/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '08/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '09/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '10/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '11/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '12/2024', revenue: 0, cost: 0, profit: 0 },
    { time: '01/2025', revenue: 0, cost: 0, profit: 0 },
    { time: '03/2025', revenue: 0, cost: 0, profit: 0 },
    { time: '03/2025', revenue: 0, cost: 0, profit: 0 },
    { time: '04/2025', revenue: 0, cost: 0, profit: 0 },
    { time: '05/2025', revenue: 0, cost: 0, profit: 0 },
    { time: '06/2025', revenue: 0, cost: 0, profit: 0 }
  ];

  // 计算汇总数据
  const totalRevenue = profitData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCosts = profitData.reduce((sum, item) => sum + item.cost, 0);
  const totalProfit = totalRevenue - totalCosts;
  const profitMargin = totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(1) : '0.0';

  return (
    <div style={{ padding: '0 4px' }}>
      <div>
        {/* 汇总卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px', marginTop: '20px', paddingLeft: '16px', paddingRight: '16px' }}>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Total Revenue</Text>
              <Typography.Title level={4} style={{ color: '#3B82F6' }}>
                €{totalRevenue.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Total Costs</Text>
              <Typography.Title level={4} style={{ color: '#EF4444' }}>
                €{totalCosts.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Net Profit</Text>
              <Typography.Title level={4} style={{ color: '#10B981' }}>
                €{totalProfit.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Profit Margin</Text>
              <Typography.Title level={4} style={{ color: '#8B5CF6' }}>
                {profitMargin}%
              </Typography.Title>
              </Card>
            </Col>
        </Row>

        {/* View Toggle and Create Button */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type={viewType === 'chart' ? 'primary' : 'default'}
              size="small"
              icon={<BarChartOutlined />}
              onClick={() => setViewType('chart')}
              style={{ 
                borderRadius: '6px',
                fontSize: '12px',
                backgroundColor: viewType === 'chart' ? '#F97316' : undefined,
                borderColor: viewType === 'chart' ? '#F97316' : undefined
              }}
            >
              Chart View
            </Button>
            <Button
              type={viewType === 'table' ? 'primary' : 'default'}
              size="small"
              icon={<TableOutlined />}
              onClick={() => setViewType('table')}
              style={{ 
                borderRadius: '6px',
                fontSize: '12px',
                backgroundColor: viewType === 'table' ? '#F97316' : undefined,
                borderColor: viewType === 'table' ? '#F97316' : undefined
              }}
            >
              Table View
            </Button>
          </div>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {}}
            style={{
              backgroundColor: '#F97316',
              borderColor: '#F97316',
              borderRadius: '6px',
              fontWeight: '500',
              fontSize: '12px'
            }}
          >
            Add Analysis
          </Button>
        </div>

        {/* Monthly Revenue & Cost Trend */}
        <Card style={{ borderRadius: '8px' }}>
          {/* 标题区域 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            marginBottom: '20px',
            paddingBottom: '12px',
            borderBottom: '1px solid #E5E7EB'
          }}>
            <h3 style={{ 
              margin: 0, 
              fontSize: '16px', 
              fontWeight: '600', 
              color: '#374151' 
            }}>
              Monthly Revenue & Cost Trend
            </h3>
            
            {/* 时间段标签 */}
            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              {['Monthly', 'Quarterly', 'Yearly'].map((period) => (
                <span
                  key={period}
                  onClick={() => setTimePeriod(period.toLowerCase())}
                  style={{
                    cursor: 'pointer',
                    padding: '4px 8px',
                    fontSize: '13px',
                    fontWeight: '500',
                    color: timePeriod === period.toLowerCase() ? '#FF7A00' : '#6B7280',
                    borderBottom: timePeriod === period.toLowerCase() ? '2px solid #FF7A00' : '2px solid transparent',
                    transition: 'all 0.2s ease'
                  }}
                >
                  {period}
                </span>
              ))}
            </div>
          </div>

          {/* 统计数据区域 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-around', 
            marginBottom: '16px',
            padding: '12px',
            backgroundColor: '#FFFFFF',
            borderRadius: '6px',
            border: '1px solid #E5E7EB'
          }}>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Revenue</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#10B981' }}>
                €{totalRevenue}K
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Cost</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#EF4444' }}>
                €{totalCosts}K
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Net Profit</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#3B82F6' }}>
                €{totalProfit}K
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Profit Margin</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#FF7A00' }}>
                {profitMargin}%
              </Text>
            </div>
          </div>

          {viewType === 'chart' ? (
            <div style={{ height: '300px', width: '100%' }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart 
                  data={profitData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#10B981" stopOpacity={0.05}/>
                    </linearGradient>
                    <linearGradient id="costGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#EF4444" stopOpacity={0.05}/>
                    </linearGradient>
                    <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid 
                    strokeDasharray="1 1" 
                    stroke="#E5E7EB" 
                    strokeOpacity={0.5}
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="time" 
                    tick={{ fontSize: 11, fill: '#6B7280' }}
                    axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    interval={0}
                  />
                  <YAxis 
                    tick={{ fontSize: 11, fill: '#6B7280' }}
                    axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    domain={['dataMin - 2', 'dataMax + 2']}
                  />
                  <RechartsTooltip
                    formatter={(value: any, name: any) => [
                      `€${value}K`, 
                      name === 'revenue' ? 'Revenue' : 
                      name === 'cost' ? 'Cost' : 'Profit'
                    ]}
                    labelStyle={{ color: '#374151', fontWeight: '500' }}
                    contentStyle={{ 
                      backgroundColor: '#FFFFFF', 
                      border: '1px solid #D1D5DB',
                      borderRadius: '6px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      fontSize: '12px'
                    }}
                  />
                  <Legend 
                    wrapperStyle={{ 
                      color: '#6B7280', 
                      fontSize: '12px',
                      paddingTop: '16px'
                    }}
                  />
                  {/* Revenue Line */}
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#10B981" 
                    strokeWidth={2.5}
                    name="Revenue"
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#10B981', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                  {/* Cost Line */}
                  <Line 
                    type="monotone" 
                    dataKey="cost" 
                    stroke="#EF4444" 
                    strokeWidth={2.5}
                    name="Cost"
                    dot={{ fill: '#EF4444', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#EF4444', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                  {/* Profit Line */}
                  <Line 
                    type="monotone" 
                    dataKey="profit" 
                    stroke="#3B82F6" 
                    strokeWidth={2.5}
                    name="Profit"
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#3B82F6', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div style={{ 
              backgroundColor: '#FFFFFF',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <table style={{ width: '100%', fontSize: '12px' }}>
                <thead style={{ backgroundColor: '#F9FAFB' }}>
                  <tr>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Period</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Revenue</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Cost</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Gross Profit</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Profit Margin</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {profitData.map((row, index) => (
                    <tr key={index} style={{ borderBottom: index < profitData.length - 1 ? '1px solid #F3F4F6' : 'none' }}>
                      <td style={{ padding: '8px 12px', color: '#374151', fontWeight: '500', fontSize: '12px' }}>{row.time}</td>
                      <td style={{ padding: '8px 12px', color: '#10B981', fontWeight: '600', fontSize: '12px' }}>€{row.revenue.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#EF4444', fontWeight: '500', fontSize: '12px' }}>€{row.cost.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#3B82F6', fontWeight: '600', fontSize: '12px' }}>€{row.profit.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#8B5CF6', fontWeight: '500', fontSize: '12px' }}>{row.revenue > 0 ? ((row.profit / row.revenue) * 100).toFixed(1) : '0.0'}%</td>
                      <td style={{ padding: '8px 12px' }}>
                        <Space size="small">
                          <Tooltip title="Edit">
                            <Button
                              type="text"
                              icon={<EditOutlined style={{ fontSize: '12px' }} />}
                              size="small"
                              style={{ fontSize: '12px' }}
                              onClick={() => console.log('Edit profit record')}
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Popconfirm
                              title="Delete Profit Record"
                              description="Are you sure you want to delete this profit record?"
                              onConfirm={() => console.log('Delete profit record')}
                              okText="Yes"
                              cancelText="No"
                            >
                              <Button
                                type="text"
                                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                                danger
                                size="small"
                                style={{ fontSize: '12px' }}
                              />
                            </Popconfirm>
                          </Tooltip>
                        </Space>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ProfitManagement;
