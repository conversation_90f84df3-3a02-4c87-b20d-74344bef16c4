import React, { useState, useEffect, useCallback } from 'react';
import { Card, Row, Col, Typography, Button, Space, Tooltip, Popconfirm, DatePicker } from 'antd';
import { BarChartOutlined, TableOutlined, EditOutlined, DeleteOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer } from 'recharts';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { useCurrency } from '../../contexts/CurrencyContext';

// 扩展dayjs功能
dayjs.extend(isSameOrBefore);

const { RangePicker } = DatePicker;

const { Text } = Typography;

interface ProfitManagementProps {
  // 可以根据需要添加属性
}

interface ProfitDataItem {
  time: string;
  revenue: number;
  cost: number;
  profit: number;
}

interface Transaction {
  id: string;
  date: string;
  type: 'income' | 'expense';
  amount: number;
  totalAmount?: number;
  status: string;
  description?: string;
  vatAmount?: number;
  vatRate?: number;
}

const ProfitManagement: React.FC<ProfitManagementProps> = () => {
  const [viewType, setViewType] = useState<'chart' | 'table'>('chart');
  const [timePeriod, setTimePeriod] = useState('monthly');
  const [profitData, setProfitData] = useState<ProfitDataItem[]>([]);
  const [loading, setLoading] = useState(false);

  // 时间范围状态
  const [timeRange, setTimeRange] = useState<{start: dayjs.Dayjs, end: dayjs.Dayjs}>({
    start: dayjs().subtract(11, 'month').startOf('month'),
    end: dayjs().endOf('month')
  });
  const [useCustomRange, setUseCustomRange] = useState(false);

  // 使用系统货币上下文
  const { selectedCurrency, convertAmount } = useCurrency();

  // 货币检测和转换函数 - 使用系统统一汇率
  const detectAndConvertAmount = (amount: number, description?: string, transaction?: any): { convertedAmount: number; originalCurrency: string } => {
    // 优先使用transaction中的currency字段
    let detectedCurrency = transaction?.currency || transaction?.originalCurrency || 'EUR';

    // 如果没有currency字段，从描述中检测
    if (detectedCurrency === 'EUR' && description) {
      if (description.includes('USD') || description.includes('$')) {
        detectedCurrency = 'USD';
      } else if (description.includes('CNY') || description.includes('¥') || description.includes('RMB')) {
        detectedCurrency = 'CNY';
      } else if (description.includes('GBP') || description.includes('£')) {
        detectedCurrency = 'GBP';
      } else if (description.includes('JPY')) {
        detectedCurrency = 'JPY';
      }
    }

    // 使用系统汇率转换到选定货币
    const convertedAmount = convertAmount(amount, detectedCurrency, selectedCurrency);

    console.log(`🔄 Profit Management currency conversion: ${detectedCurrency} ${amount} → ${selectedCurrency} ${convertedAmount}`);

    return { convertedAmount, originalCurrency: detectedCurrency };
  };

  // 从localStorage加载交易数据并计算利润数据
  const loadProfitData = useCallback(() => {
    setLoading(true);
    try {
      const transactionsData = localStorage.getItem('finance_transactions');
      if (!transactionsData) {
        console.log('No transaction data found');
        setProfitData([]);
        setLoading(false);
        return;
      }

      const transactions: Transaction[] = JSON.parse(transactionsData);
      console.log('📊 Loading profit data from transactions:', transactions.length);

      // 根据时间周期和范围生成数据
      let periods: string[] = [];
      let dateFormat = '';
      let startDate: dayjs.Dayjs;
      let endDate: dayjs.Dayjs;

      if (useCustomRange) {
        startDate = timeRange.start;
        endDate = timeRange.end;
      } else {
        const now = dayjs();
        if (timePeriod === 'monthly') {
          startDate = now.subtract(11, 'month').startOf('month');
          endDate = now.endOf('month');
        } else if (timePeriod === 'quarterly') {
          // 计算当前季度的开始月份
          const currentQuarter = Math.floor((now.month()) / 3);
          const quarterStartMonth = currentQuarter * 3;
          startDate = now.subtract(9, 'month').month(quarterStartMonth).startOf('month');
          endDate = now.month(quarterStartMonth + 2).endOf('month');
        } else { // yearly
          startDate = now.subtract(2, 'year').startOf('year');
          endDate = now.endOf('year');
        }
      }

      if (timePeriod === 'monthly') {
        dateFormat = 'MM/YYYY';
        let current = startDate.clone();
        while (current.isSameOrBefore(endDate, 'month')) {
          periods.push(current.format(dateFormat));
          current = current.add(1, 'month');
        }
      } else if (timePeriod === 'quarterly') {
        dateFormat = '[Q]Q YYYY';
        let current = startDate.clone();
        while (current.isSameOrBefore(endDate, 'month')) {
          const quarter = Math.floor(current.month() / 3) + 1;
          const quarterStr = `Q${quarter} ${current.year()}`;
          if (!periods.includes(quarterStr)) {
            periods.push(quarterStr);
          }
          current = current.add(3, 'month');
        }
      } else { // yearly
        dateFormat = 'YYYY';
        let current = startDate.clone().startOf('year');
        while (current.isSameOrBefore(endDate, 'year')) {
          periods.push(current.format(dateFormat));
          current = current.add(1, 'year');
        }
      }

      // 按时间周期分组计算收入和支出
      const profitByPeriod = periods.map(period => {
        let revenue = 0;
        let cost = 0;

        transactions.forEach(tx => {
          // 只统计已完成的交易，排除cancelled和pending
          if (['cancelled', 'pending'].includes(tx.status.toLowerCase())) {
            return;
          }

          const txDate = dayjs(tx.date);
          let txPeriod = '';

          if (timePeriod === 'monthly') {
            txPeriod = txDate.format('MM/YYYY');
          } else if (timePeriod === 'quarterly') {
            const quarter = Math.floor(txDate.month() / 3) + 1;
            txPeriod = `Q${quarter} ${txDate.year()}`;
          } else {
            txPeriod = txDate.format('YYYY');
          }

          if (txPeriod === period) {
            const { convertedAmount } = detectAndConvertAmount(tx.totalAmount || tx.amount, tx.description, tx);

            if (tx.type === 'income') {
              // 计算不含VAT的收入
              let revenueAmount = convertedAmount;
              if (typeof tx.vatAmount === 'number') {
                revenueAmount = convertedAmount - tx.vatAmount;
              } else if (typeof tx.vatRate === 'number') {
                revenueAmount = convertedAmount / (1 + tx.vatRate / 100);
              }
              revenue += revenueAmount;
            } else if (tx.type === 'expense') {
              cost += convertedAmount;
            }
          }
        });

        const profit = revenue - cost;

        return {
          time: period,
          revenue: Math.round(revenue * 100) / 100,
          cost: Math.round(cost * 100) / 100,
          profit: Math.round(profit * 100) / 100
        };
      });

      console.log('📈 Calculated profit data:', profitByPeriod);
      setProfitData(profitByPeriod);
    } catch (error) {
      console.error('Error loading profit data:', error);
      setProfitData([]);
    } finally {
      setLoading(false);
    }
  }, [timePeriod, timeRange.start, timeRange.end, useCustomRange, convertAmount, selectedCurrency]);

  // 监听交易数据变化
  useEffect(() => {
    loadProfitData();

    // 监听localStorage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'finance_transactions') {
        console.log('🔄 Detected transaction changes, reloading profit data...');
        setTimeout(loadProfitData, 500);
      }
    };

    // 监听自定义事件
    const handleTransactionUpdate = () => {
      console.log('🔄 Detected transaction update event, reloading profit data...');
      setTimeout(loadProfitData, 500);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('transactionDataUpdated', handleTransactionUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionDataUpdated', handleTransactionUpdate);
    };
  }, [loadProfitData]);

  // 当时间周期或时间范围改变时重新加载数据
  useEffect(() => {
    loadProfitData();
  }, [timePeriod, timeRange, useCustomRange, loadProfitData]);

  // 计算汇总数据
  const totalRevenue = profitData.reduce((sum, item) => sum + item.revenue, 0);
  const totalCosts = profitData.reduce((sum, item) => sum + item.cost, 0);
  const totalProfit = totalRevenue - totalCosts;
  const profitMargin = totalRevenue > 0 ? ((totalProfit / totalRevenue) * 100).toFixed(1) : '0.0';

  // 处理时间周期变化
  const handleTimePeriodChange = (value: string) => {
    setTimePeriod(value);
    setUseCustomRange(false); // 切换周期时重置为默认范围
  };

  // 时间范围控制函数
  const shiftTimeRange = (direction: 'prev' | 'next') => {
    const { start, end } = timeRange;
    let newStart: dayjs.Dayjs;
    let newEnd: dayjs.Dayjs;

    if (timePeriod === 'monthly') {
      const months = end.diff(start, 'month') + 1;
      if (direction === 'prev') {
        newStart = start.subtract(months, 'month');
        newEnd = end.subtract(months, 'month');
      } else {
        newStart = start.add(months, 'month');
        newEnd = end.add(months, 'month');
      }
    } else if (timePeriod === 'quarterly') {
      const quarters = Math.ceil(end.diff(start, 'month') / 3);
      if (direction === 'prev') {
        newStart = start.subtract(quarters * 3, 'month');
        newEnd = end.subtract(quarters * 3, 'month');
      } else {
        newStart = start.add(quarters * 3, 'month');
        newEnd = end.add(quarters * 3, 'month');
      }
    } else { // yearly
      const years = end.diff(start, 'year') + 1;
      if (direction === 'prev') {
        newStart = start.subtract(years, 'year');
        newEnd = end.subtract(years, 'year');
      } else {
        newStart = start.add(years, 'year');
        newEnd = end.add(years, 'year');
      }
    }

    setTimeRange({ start: newStart, end: newEnd });
    setUseCustomRange(true);
  };

  // 处理自定义时间范围选择
  const handleCustomRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setTimeRange({
        start: dates[0],
        end: dates[1]
      });
      setUseCustomRange(true);
    }
  };

  // 重置到默认时间范围
  const resetToDefaultRange = () => {
    setUseCustomRange(false);
    const now = dayjs();
    if (timePeriod === 'monthly') {
      setTimeRange({
        start: now.subtract(11, 'month').startOf('month'),
        end: now.endOf('month')
      });
    } else if (timePeriod === 'quarterly') {
      const currentQuarter = Math.floor((now.month()) / 3);
      const quarterStartMonth = currentQuarter * 3;
      setTimeRange({
        start: now.subtract(9, 'month').month(quarterStartMonth).startOf('month'),
        end: now.month(quarterStartMonth + 2).endOf('month')
      });
    } else {
      setTimeRange({
        start: now.subtract(2, 'year').startOf('year'),
        end: now.endOf('year')
      });
    }
  };

  return (
    <div style={{ padding: '0 4px' }}>
      <div>
        {/* 汇总卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px', marginTop: '20px', paddingLeft: '16px', paddingRight: '16px' }}>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Total Revenue</Text>
              <Typography.Title level={4} style={{ color: '#3B82F6' }}>
                €{totalRevenue.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Total Costs</Text>
              <Typography.Title level={4} style={{ color: '#EF4444' }}>
                €{totalCosts.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Net Profit</Text>
              <Typography.Title level={4} style={{ color: '#10B981' }}>
                €{totalProfit.toLocaleString()}
              </Typography.Title>
            </Card>
          </Col>
          <Col span={6}>
            <Card style={{ background: '#f5f5f5' }}>
              <Text>Profit Margin</Text>
              <Typography.Title level={4} style={{ color: '#8B5CF6' }}>
                {profitMargin}%
              </Typography.Title>
              </Card>
            </Col>
        </Row>

        {/* View Toggle and Create Button */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              type={viewType === 'chart' ? 'primary' : 'default'}
              size="small"
              icon={<BarChartOutlined />}
              onClick={() => setViewType('chart')}
              style={{ 
                borderRadius: '6px',
                fontSize: '12px',
                backgroundColor: viewType === 'chart' ? '#F97316' : undefined,
                borderColor: viewType === 'chart' ? '#F97316' : undefined
              }}
            >
              Chart View
            </Button>
            <Button
              type={viewType === 'table' ? 'primary' : 'default'}
              size="small"
              icon={<TableOutlined />}
              onClick={() => setViewType('table')}
              style={{ 
                borderRadius: '6px',
                fontSize: '12px',
                backgroundColor: viewType === 'table' ? '#F97316' : undefined,
                borderColor: viewType === 'table' ? '#F97316' : undefined
              }}
            >
              Table View
            </Button>
          </div>
        </div>

        {/* Monthly Revenue & Cost Trend */}
        <Card style={{ borderRadius: '8px' }}>
          {/* 标题区域 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center', 
            marginBottom: '20px',
            paddingBottom: '12px',
            borderBottom: '1px solid #E5E7EB'
          }}>
            <h3 style={{
              margin: 0,
              fontSize: '16px',
              fontWeight: '600',
              color: '#374151'
            }}>
              {timePeriod.charAt(0).toUpperCase() + timePeriod.slice(1)} Revenue & Cost Trend
            </h3>
            
            {/* 时间段标签和时间范围控制 */}
            <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
              {/* 时间周期选择 */}
              <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                {['Monthly', 'Quarterly', 'Yearly'].map((period) => (
                  <span
                    key={period}
                    onClick={() => handleTimePeriodChange(period.toLowerCase())}
                    style={{
                      cursor: 'pointer',
                      padding: '4px 8px',
                      fontSize: '13px',
                      fontWeight: '500',
                      color: timePeriod === period.toLowerCase() ? '#FF7A00' : '#6B7280',
                      borderBottom: timePeriod === period.toLowerCase() ? '2px solid #FF7A00' : '2px solid transparent',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    {period}
                  </span>
                ))}
              </div>

              {/* 时间范围控制 */}
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                {/* 向前/向后按钮 */}
                <Button
                  size="small"
                  icon={<LeftOutlined />}
                  onClick={() => shiftTimeRange('prev')}
                  style={{
                    borderRadius: '4px',
                    fontSize: '12px',
                    height: '24px',
                    width: '24px',
                    padding: 0
                  }}
                  title="Previous period"
                />

                {/* 时间范围显示/选择器 */}
                <RangePicker
                  size="small"
                  value={[timeRange.start, timeRange.end]}
                  onChange={handleCustomRangeChange}
                  style={{
                    fontSize: '12px',
                    height: '24px',
                    width: '200px'
                  }}
                  format={timePeriod === 'monthly' ? 'MMM YYYY' : timePeriod === 'quarterly' ? '[Q]Q YYYY' : 'YYYY'}
                  picker={timePeriod === 'monthly' ? 'month' : timePeriod === 'quarterly' ? 'quarter' : 'year'}
                />

                <Button
                  size="small"
                  icon={<RightOutlined />}
                  onClick={() => shiftTimeRange('next')}
                  style={{
                    borderRadius: '4px',
                    fontSize: '12px',
                    height: '24px',
                    width: '24px',
                    padding: 0
                  }}
                  title="Next period"
                />

                {/* 重置按钮 */}
                {useCustomRange && (
                  <Button
                    size="small"
                    onClick={resetToDefaultRange}
                    style={{
                      borderRadius: '4px',
                      fontSize: '11px',
                      height: '24px',
                      padding: '0 8px'
                    }}
                    title="Reset to default range"
                  >
                    Reset
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* 统计数据区域 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-around', 
            marginBottom: '16px',
            padding: '12px',
            backgroundColor: '#FFFFFF',
            borderRadius: '6px',
            border: '1px solid #E5E7EB'
          }}>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Revenue</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#10B981' }}>
                €{totalRevenue.toLocaleString()}
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Total Cost</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#EF4444' }}>
                €{totalCosts.toLocaleString()}
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Net Profit</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#3B82F6' }}>
                €{totalProfit.toLocaleString()}
              </Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>Profit Margin</Text>
              <Text style={{ fontSize: '16px', fontWeight: '600', color: '#FF7A00' }}>
                {profitMargin}%
              </Text>
            </div>
          </div>

          {viewType === 'chart' ? (
            <div style={{ height: '300px', width: '100%' }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart 
                  data={profitData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#10B981" stopOpacity={0.05}/>
                    </linearGradient>
                    <linearGradient id="costGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#EF4444" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#EF4444" stopOpacity={0.05}/>
                    </linearGradient>
                    <linearGradient id="profitGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid 
                    strokeDasharray="1 1" 
                    stroke="#E5E7EB" 
                    strokeOpacity={0.5}
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="time" 
                    tick={{ fontSize: 11, fill: '#6B7280' }}
                    axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    interval={0}
                  />
                  <YAxis 
                    tick={{ fontSize: 11, fill: '#6B7280' }}
                    axisLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    tickLine={{ stroke: '#D1D5DB', strokeWidth: 1 }}
                    domain={['dataMin - 2', 'dataMax + 2']}
                  />
                  <RechartsTooltip
                    formatter={(value: any, name: any) => [
                      `€${value}K`, 
                      name === 'revenue' ? 'Revenue' : 
                      name === 'cost' ? 'Cost' : 'Profit'
                    ]}
                    labelStyle={{ color: '#374151', fontWeight: '500' }}
                    contentStyle={{ 
                      backgroundColor: '#FFFFFF', 
                      border: '1px solid #D1D5DB',
                      borderRadius: '6px',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      fontSize: '12px'
                    }}
                  />
                  <Legend 
                    wrapperStyle={{ 
                      color: '#6B7280', 
                      fontSize: '12px',
                      paddingTop: '16px'
                    }}
                  />
                  {/* Revenue Line */}
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#10B981" 
                    strokeWidth={2.5}
                    name="Revenue"
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#10B981', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                  {/* Cost Line */}
                  <Line 
                    type="monotone" 
                    dataKey="cost" 
                    stroke="#EF4444" 
                    strokeWidth={2.5}
                    name="Cost"
                    dot={{ fill: '#EF4444', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#EF4444', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                  {/* Profit Line */}
                  <Line 
                    type="monotone" 
                    dataKey="profit" 
                    stroke="#3B82F6" 
                    strokeWidth={2.5}
                    name="Profit"
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 3 }}
                    activeDot={{ r: 5, fill: '#3B82F6', strokeWidth: 2, stroke: '#FFFFFF' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div style={{ 
              backgroundColor: '#FFFFFF',
              border: '1px solid #E5E7EB',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <table style={{ width: '100%', fontSize: '12px' }}>
                <thead style={{ backgroundColor: '#F9FAFB' }}>
                  <tr>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Period</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Revenue</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Cost</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Gross Profit</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Profit Margin</th>
                    <th style={{ padding: '8px 12px', textAlign: 'left', fontWeight: '500', color: '#6B7280', borderBottom: '1px solid #E5E7EB', fontSize: '11px' }}>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {profitData.map((row, index) => (
                    <tr key={index} style={{ borderBottom: index < profitData.length - 1 ? '1px solid #F3F4F6' : 'none' }}>
                      <td style={{ padding: '8px 12px', color: '#374151', fontWeight: '500', fontSize: '12px' }}>{row.time}</td>
                      <td style={{ padding: '8px 12px', color: '#10B981', fontWeight: '600', fontSize: '12px' }}>€{row.revenue.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#EF4444', fontWeight: '500', fontSize: '12px' }}>€{row.cost.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#3B82F6', fontWeight: '600', fontSize: '12px' }}>€{row.profit.toLocaleString()}</td>
                      <td style={{ padding: '8px 12px', color: '#8B5CF6', fontWeight: '500', fontSize: '12px' }}>{row.revenue > 0 ? ((row.profit / row.revenue) * 100).toFixed(1) : '0.0'}%</td>
                      <td style={{ padding: '8px 12px' }}>
                        <Space size="small">
                          <Tooltip title="Edit">
                            <Button
                              type="text"
                              icon={<EditOutlined style={{ fontSize: '12px' }} />}
                              size="small"
                              style={{ fontSize: '12px' }}
                              onClick={() => console.log('Edit profit record')}
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Popconfirm
                              title="Delete Profit Record"
                              description="Are you sure you want to delete this profit record?"
                              onConfirm={() => console.log('Delete profit record')}
                              okText="Yes"
                              cancelText="No"
                            >
                              <Button
                                type="text"
                                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                                danger
                                size="small"
                                style={{ fontSize: '12px' }}
                              />
                            </Popconfirm>
                          </Tooltip>
                        </Space>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default ProfitManagement;
