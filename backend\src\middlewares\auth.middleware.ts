import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { getRepository } from 'typeorm';
import { User } from '../entities/User';
import { isDevelopment, securityConfig } from '../config/environment';

interface TokenPayload {
  userId: string;
  email: string;
}

declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 使用环境配置中的isDevelopment

    // 获取 Authorization 头
    const authHeader = req.headers.authorization;

    // 开发环境下的特殊处理
    if (isDevelopment) {
      // 如果提供了令牌，尝试验证它；如果没有提供或验证失败，仍然允许请求通过
      if (authHeader) {
        try {
          const parts = authHeader.split(' ');
          if (parts.length === 2 && parts[0] === 'Bearer') {
            const token = parts[1];
            const data = jwt.verify(token, securityConfig.jwt.secret) as TokenPayload;

            // 获取用户
            const userRepository = getRepository(User);
            const user = await userRepository.findOne({ where: { id: data.userId } });

            if (user) {
              // 将用户信息添加到请求对象
              req.user = user;
            }
          }
        } catch (tokenError) {
          console.log('开发环境：令牌验证失败，但允许请求通过');
        }
      } else {
        console.log('开发环境：未提供令牌，但允许请求通过');
      }

      // 在开发环境中，无论令牌验证是否成功，都允许请求通过
      return next();
    }

    // 生产环境下的严格验证

    if (!authHeader) {
      return res.status(401).json({ message: '未提供认证令牌' });
    }

    // 验证令牌格式
    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return res.status(401).json({ message: '令牌格式错误' });
    }

    const token = parts[1];

    // 验证令牌
    const data = jwt.verify(token, securityConfig.jwt.secret) as TokenPayload;

    // 获取用户
    const userRepository = getRepository(User);
    const user = await userRepository.findOne({ where: { id: data.userId } });

    if (!user) {
      return res.status(401).json({ message: '用户不存在' });
    }

    // 将用户信息添加到请求对象
    req.user = user;

    return next();
  } catch (error) {
    return res.status(401).json({ message: '无效的令牌' });
  }
};

export const checkRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // 使用环境配置中的isDevelopment

    // 开发环境下的特殊处理
    if (isDevelopment) {
      console.log('开发环境：跳过角色检查');
      return next();
    }

    if (!req.user) {
      return res.status(401).json({ message: '未认证' });
    }

    if (roles.includes(req.user.role)) {
      return next();
    }

    return res.status(403).json({ message: '没有权限执行此操作' });
  };
};
