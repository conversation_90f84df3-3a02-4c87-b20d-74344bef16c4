import React, { useState } from 'react';
import {
  Table,
  Avatar,
  Tag,
  Button,
  Space,
  Input,
  Select,
  Row,
  Col,
  Tooltip,
  Badge
} from 'antd';
import {
  UserOutlined,
  CrownOutlined,
  SafetyOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { TeamMember, TeamRole, MemberStatus } from '../../types';
import MemberPermissionModal from './MemberPermissionModal';

interface MemberListProps {
  members: TeamMember[];
  projectId: string;
  currentUserRole: TeamRole | null;
  onMemberRemoved: () => void;
  onMemberUpdated: () => void;
}

const { Search } = Input;
const { Option } = Select;

const MemberList: React.FC<MemberListProps> = ({
  members,
  projectId,
  currentUserRole,
  onMemberRemoved,
  onMemberUpdated
}) => {
  const [searchText, setSearchText] = useState('');
  const [roleFilter, setRoleFilter] = useState<TeamRole | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<MemberStatus | 'all'>('all');
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null);

  // 过滤成员
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchText.toLowerCase());
    const matchesRole = roleFilter === 'all' || member.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const openPermissionModal = (member: TeamMember) => {
    setSelectedMember(member);
    setPermissionModalVisible(true);
  };

  const getRoleIcon = (role: TeamRole) => {
    switch (role) {
      case TeamRole.OWNER:
        return <CrownOutlined style={{ color: '#faad14' }} />;
      case TeamRole.ADMIN:
        return <SafetyOutlined style={{ color: '#1890ff' }} />;
      case TeamRole.MEMBER:
        return <UserOutlined style={{ color: '#52c41a' }} />;
      case TeamRole.VIEWER:
        return <EyeOutlined style={{ color: '#8c8c8c' }} />;
      default:
        return <UserOutlined />;
    }
  };

  const getRoleColor = (role: TeamRole) => {
    switch (role) {
      case TeamRole.OWNER:
        return 'gold';
      case TeamRole.ADMIN:
        return 'blue';
      case TeamRole.MEMBER:
        return 'green';
      case TeamRole.VIEWER:
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: MemberStatus) => {
    switch (status) {
      case MemberStatus.ACTIVE:
        return 'success';
      case MemberStatus.PENDING:
        return 'warning';
      case MemberStatus.INACTIVE:
        return 'default';
      default:
        return 'default';
    }
  };



  const memberColumns = [
    {
      title: 'Member',
      key: 'member',
      width: 320,
      render: (_: any, member: TeamMember) => (
        <Space>
          <Badge 
            status={getStatusColor(member.status)} 
            offset={[-5, 5]}
          >
            <Avatar
              src={member.avatar}
              icon={<UserOutlined />}
              size={40}
            />
          </Badge>
          <div>
            <div style={{ fontWeight: 500 }}>{member.name}</div>
            <div style={{ color: '#8c8c8c', fontSize: '12px' }}>
              {member.email}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Role',
      key: 'role',
      width: 120,
      render: (_: any, member: TeamMember) => (
        <Tag color={getRoleColor(member.role)} icon={getRoleIcon(member.role)}>
          {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
        </Tag>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      width: 120,
      render: (_: any, member: TeamMember) => (
        <Tag color={getStatusColor(member.status)}>
          {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
        </Tag>
      ),
    },
    {
      title: 'Joined',
      key: 'joined',
      width: 180,
      render: (_: any, member: TeamMember) => (
        <div>
          {member.joinedAt ? (
            <div>
              <div>{new Date(member.joinedAt).toLocaleDateString()}</div>
              <div style={{ color: '#8c8c8c', fontSize: '12px' }}>
                {member.lastActiveAt && `Last active: ${new Date(member.lastActiveAt).toLocaleDateString()}`}
              </div>
            </div>
          ) : (
            <Tag color="orange">Pending</Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Permissions',
      key: 'permissions',
      width: 160,
      render: (_: any, member: TeamMember) => {
        const activePermissions = Object.entries(member.permissions)
          .filter(([_, value]) => value)
          .length;
        const totalPermissions = Object.keys(member.permissions).length;
        
        return (
          <Tooltip title="Click to manage permissions">
            <Button
              type="link"
              size="small"
              onClick={() => openPermissionModal(member)}
              style={{ 
                padding: 0, 
                height: 'auto',
                fontWeight: 500,
                color: '#1890ff'
              }}
            >
              {activePermissions}/{totalPermissions} permissions
            </Button>
          </Tooltip>
        );
      },
    },
  ];

  return (
    <div>
      {/* 搜索和过滤工具栏 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col>
          <Search
            placeholder="Search members..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 240 }}
          />
        </Col>
        <Col>
          <Select
            value={roleFilter}
            onChange={setRoleFilter}
            style={{ width: 120 }}
          >
            <Option value="all">All Roles</Option>
            {Object.values(TeamRole).map(role => (
              <Option key={role} value={role}>
                {role.charAt(0).toUpperCase() + role.slice(1)}
              </Option>
            ))}
          </Select>
        </Col>
        <Col>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">All Status</Option>
            {Object.values(MemberStatus).map(status => (
              <Option key={status} value={status}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* 成员表格 */}
      <Table
        columns={memberColumns}
        dataSource={filteredMembers}
        rowKey="id"
        pagination={{
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} members`,
        }}
        size="small"
      />

      {/* 成员权限模态框 */}
      {selectedMember && (
        <MemberPermissionModal
          visible={permissionModalVisible}
          onCancel={() => {
            setPermissionModalVisible(false);
            setSelectedMember(null);
          }}
          member={selectedMember}
          projectId={projectId}
          currentUserRole={currentUserRole}
          onPermissionUpdated={() => {
            onMemberUpdated();
            setPermissionModalVisible(false);
            setSelectedMember(null);
          }}
        />
      )}
    </div>
  );
};

export default MemberList; 