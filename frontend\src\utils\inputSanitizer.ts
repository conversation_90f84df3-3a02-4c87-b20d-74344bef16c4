/**
 * 输入净化工具
 * 用于防止XSS攻击和其他安全问题
 */

/**
 * 净化HTML字符串，移除所有HTML标签和危险字符
 * @param input 输入字符串
 * @returns 净化后的字符串
 */
export function sanitizeHTML(input: string): string {
  if (!input) return '';

  // 移除所有HTML标签
  const withoutTags = input.replace(/<[^>]*>/g, '');

  // 转义HTML实体
  return withoutTags
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * 净化普通文本输入，移除危险字符
 * @param input 输入字符串
 * @returns 净化后的字符串
 */
export function sanitizeText(input: string): string {
  if (!input) return '';

  // 移除控制字符和不可见字符
  return input.replace(/[\u0000-\u001F\u007F-\u009F]/g, '');
}

/**
 * 净化URL，确保是安全的URL
 * @param url 输入URL
 * @returns 净化后的URL，如果不安全则返回空字符串
 */
export function sanitizeURL(url: string): string {
  if (!url) return '';

  try {
    const parsed = new URL(url);

    // 只允许http和https协议
    if (parsed.protocol !== 'http:' && parsed.protocol !== 'https:') {
      return '';
    }

    return url;
  } catch (e) {
    // URL解析失败，返回空字符串
    return '';
  }
}

/**
 * 验证并净化电子邮件地址
 * @param email 输入的电子邮件地址
 * @returns 净化后的电子邮件地址，如果无效则返回空字符串
 */
export function sanitizeEmail(email: string): string {
  if (!email) return '';

  // 简单的电子邮件验证正则表达式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (emailRegex.test(email)) {
    return email.trim().toLowerCase();
  }

  return '';
}

/**
 * 验证并净化数字输入
 * @param input 输入的数字或数字字符串
 * @returns 净化后的数字，如果无效则返回null
 */
export function sanitizeNumber(input: string | number): number | null {
  if (input === null || input === undefined) return null;

  const num = typeof input === 'string' ? parseFloat(input) : input;

  if (isNaN(num) || !isFinite(num)) {
    return null;
  }

  return num;
}

/**
 * 验证并净化日期输入
 * @param input 输入的日期字符串
 * @returns 净化后的日期字符串，如果无效则返回空字符串
 */
export function sanitizeDate(input: string): string {
  if (!input) return '';

  const date = new Date(input);

  if (isNaN(date.getTime())) {
    return '';
  }

  return date.toISOString();
}

/**
 * 净化对象中的所有字符串属性
 * @param obj 输入对象
 * @returns 净化后的对象
 */
export function sanitizeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') return obj;

  const result = { ...obj };

  for (const key in result) {
    const typedKey = key as keyof T;
    if (typeof result[typedKey] === 'string') {
      result[typedKey] = sanitizeText(result[typedKey] as string) as T[keyof T];
    } else if (typeof result[typedKey] === 'object' && result[typedKey] !== null) {
      result[typedKey] = sanitizeObject(result[typedKey] as object) as T[keyof T];
    }
  }

  return result;
}
