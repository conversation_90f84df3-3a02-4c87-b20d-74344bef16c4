import React, { useState } from 'react';
import { Card, Progress, Row, Col, Button, Table, Tag, Tabs, Form, Input, DatePicker, Select, Divider, Switch, Slider } from 'antd';
import { PlusOutlined, CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined, MoreOutlined, RightOutlined } from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';
import './Execution.css';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface ExecutionProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
}

const Execution: React.FC<ExecutionProps> = ({ project, stage, onSave }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('1');

  // Mock project plan data
  const [projectPlan, setProjectPlan] = useState([
    {
      id: 'A',
      name: 'Project Initiation',
      startDate: '2023-09-28',
      endDate: '2023-10-02',
      progress: 55,
      color: '#4B96FF',
      owner: '<PERSON>',
      description: 'Project kickoff and initial planning'
    },
    {
      id: 'B',
      name: 'Requirements Gathering',
      startDate: '2023-09-30',
      endDate: '2023-10-05',
      progress: 80,
      color: '#9747FF',
      owner: 'Jane Doe',
      description: 'Collect and document all requirements'
    },
    {
      id: 'C',
      name: 'System Design',
      startDate: '2023-10-01',
      endDate: '2023-10-04',
      progress: 65,
      color: '#FF4B9B',
      owner: 'Mike Johnson',
      description: 'Create system architecture and design'
    },
    {
      id: 'D',
      name: 'Development Phase 1',
      startDate: '2023-10-03',
      endDate: '2023-10-06',
      progress: 75,
      color: '#FFB547',
      owner: 'Sarah Williams',
      description: 'Initial development of core features'
    },
  ]);

  // Mock task data
  const [tasks, setTasks] = useState([
    { id: '1', name: 'Project Kickoff Meeting', status: 'completed', dueDate: '2023-09-15', assignee: 'John Smith', priority: 'high' },
    { id: '2', name: 'Requirements Analysis', status: 'completed', dueDate: '2023-09-30', assignee: 'Jane Doe', priority: 'high' },
    { id: '3', name: 'System Design', status: 'in_progress', dueDate: '2023-10-15', assignee: 'Mike Johnson', priority: 'medium' },
    { id: '4', name: 'Development Phase 1', status: 'in_progress', dueDate: '2023-11-01', assignee: 'Sarah Williams', priority: 'medium' },
    { id: '5', name: 'Test Plan Creation', status: 'not_started', dueDate: '2023-11-15', assignee: 'John Smith', priority: 'low' },
  ]);

  // Mock risk data
  const [risks, setRisks] = useState([
    { id: '1', description: 'Frequent client requirement changes', impact: 'high', probability: 'medium', mitigation: 'Establish change control process, clarify impact on schedule and cost' },
    { id: '2', description: 'Key technical staff turnover', impact: 'high', probability: 'low', mitigation: 'Ensure knowledge sharing and documentation, establish backup personnel' },
    { id: '3', description: 'Third-party system integration delays', impact: 'medium', probability: 'high', mitigation: 'Communicate with third parties early, establish clear delivery timelines and responsibilities' },
  ]);

  const handleSave = (values: any) => {
    if (onSave) {
      onSave({
        ...values,
        tasks,
        risks,
        type: 'execution',
        status: 'in_progress',
      });
    }
  };

  // Calculate overall progress
  const calculateProgress = () => {
    const completed = tasks.filter(task => task.status === 'completed').length;
    return Math.round((completed / tasks.length) * 100);
  };

  // Helper functions for Gantt chart
  const getTaskPosition = (startDate: string) => {
    // Convert date to position percentage (28th to 6th = 10 days)
    const start = new Date(startDate);
    const baseDate = new Date('2023-09-28');
    const diffDays = Math.floor((start.getTime() - baseDate.getTime()) / (1000 * 60 * 60 * 24));
    return (diffDays / 10) * 100;
  };

  const getTaskWidth = (startDate: string, endDate: string) => {
    // Calculate width based on duration
    const start = new Date(startDate);
    const end = new Date(endDate);
    const durationDays = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    return (durationDays / 10) * 100;
  };

  const taskColumns = [
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = '';
        let text = '';
        let icon = null;

        switch (status) {
          case 'completed':
            color = 'success';
            text = 'Completed';
            icon = <CheckCircleOutlined />;
            break;
          case 'in_progress':
            color = 'processing';
            text = 'In Progress';
            icon = <ClockCircleOutlined />;
            break;
          case 'not_started':
            color = 'default';
            text = 'Not Started';
            break;
          default:
            color = 'default';
            text = status;
        }

        return <Tag color={color} icon={icon}>{text}</Tag>;
      },
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
    },
    {
      title: 'Assignee',
      dataIndex: 'assignee',
      key: 'assignee',
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        let color = '';
        let text = '';

        switch (priority) {
          case 'high':
            color = 'red';
            text = 'High';
            break;
          case 'medium':
            color = 'orange';
            text = 'Medium';
            break;
          case 'low':
            color = 'green';
            text = 'Low';
            break;
          default:
            color = 'blue';
            text = priority;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button type="link">Edit</Button>
          <Button type="link" danger>Delete</Button>
        </>
      ),
    },
  ];

  const riskColumns = [
    {
      title: 'Risk Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Impact',
      dataIndex: 'impact',
      key: 'impact',
      render: (impact: string) => {
        let color = impact === 'high' ? 'red' : impact === 'medium' ? 'orange' : 'green';
        let text = impact === 'high' ? 'High' : impact === 'medium' ? 'Medium' : 'Low';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Probability',
      dataIndex: 'probability',
      key: 'probability',
      render: (probability: string) => {
        let color = probability === 'high' ? 'red' : probability === 'medium' ? 'orange' : 'green';
        let text = probability === 'high' ? 'High' : probability === 'medium' ? 'Medium' : 'Low';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: 'Mitigation',
      dataIndex: 'mitigation',
      key: 'mitigation',
      ellipsis: true,
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_: any, record: any) => (
        <>
          <Button type="link">Edit</Button>
          <Button type="link" danger>Delete</Button>
        </>
      ),
    },
  ];

  return (
    <div className="execution-container">
      <Card className="progress-card">
        <div className="progress-container">
          <Progress
            type="circle"
            percent={calculateProgress()}
            format={percent => `${percent}%`}
            width={120}
          />
          <div className="progress-info">
            <h3>Overall Progress</h3>
            <p>Completed Tasks: {tasks.filter(task => task.status === 'completed').length} / {tasks.length} | Due Soon: {tasks.filter(task => task.status !== 'completed' && new Date(task.dueDate) < new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)).length}</p>
          </div>
        </div>
      </Card>

      <Tabs activeKey={activeTab} onChange={setActiveTab} className="execution-tabs">
        <TabPane tab="Project Plan" key="1">
          <Card
            title="Project Timeline"
            className="module-card"
            extra={
              <Button type="primary" icon={<PlusOutlined />}>
                Add Task
              </Button>
            }
          >
            <div className="gantt-container">
              {/* Gantt Chart Header */}
              <div className="gantt-header">
                <div className="gantt-task-list">
                  <div className="gantt-task-header">Project Tasks</div>
                </div>
                <div className="gantt-timeline-header">
                  {['28', '29', '30', '31', '01', '02', '03', '04', '05', '06'].map((day, index) => (
                    <div key={index} className="gantt-day">{day}</div>
                  ))}
                </div>
              </div>

              {/* Gantt Chart Body */}
              <div className="gantt-body">
                {projectPlan.map((task) => (
                  <div key={task.id} className="gantt-row">
                    <div className="gantt-task-info">
                      <div className="gantt-task-circle" style={{ backgroundColor: task.color }}>{task.id}</div>
                      <div className="gantt-task-details">
                        <div className="gantt-task-name">{task.name}</div>
                        <div className="gantt-task-description">{task.description}</div>
                      </div>
                      <Button type="text" icon={<MoreOutlined />} className="gantt-task-more" />
                    </div>
                    <div className="gantt-timeline">
                      <div
                        className="gantt-bar"
                        style={{
                          backgroundColor: task.color,
                          left: `${getTaskPosition(task.startDate)}%`,
                          width: `${getTaskWidth(task.startDate, task.endDate)}%`,
                          opacity: 0.7
                        }}
                      >
                        <div className="gantt-progress" style={{ width: `${task.progress}%` }}>
                          <div className="gantt-progress-label">{task.progress}%</div>
                        </div>
                        <div className="gantt-task-toggle">
                          <Switch size="small" defaultChecked />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Task Table */}
            <div style={{ marginTop: '24px' }}>
              <div className="section-title" style={{ marginBottom: '16px', fontWeight: 600, fontSize: '16px' }}>
                Project Tasks
              </div>
              <Table
                dataSource={tasks}
                columns={taskColumns}
                rowKey="id"
                pagination={false}
              />
            </div>
          </Card>
        </TabPane>

        <TabPane tab="Risk Management" key="2">
          <Card
            title="Project Risks"
            className="module-card"
            extra={<Button type="primary" icon={<PlusOutlined />}>Add Risk</Button>}
          >
            <Table
              columns={riskColumns}
              dataSource={risks}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>
      </Tabs>

      {/* 流转按钮区域 */}
      <div className="workflow-actions" style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 32, paddingTop: 16, borderTop: '1px solid #f0f0f0' }}>
        <Button
          type="default"
          className="btn-workflow"
          onClick={() => handleSave({})}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>
    </div>
  );
};

export default Execution;
