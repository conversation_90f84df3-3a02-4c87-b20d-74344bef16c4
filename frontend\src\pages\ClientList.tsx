import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Card, 
  Tag, 
  message,
  Popconfirm,
  Tooltip,
  Drawer,
  Checkbox,
  DatePicker
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  MenuOutlined,
  MailOutlined,
  PhoneOutlined
} from '@ant-design/icons';
import clientService from '../services/client.service';
import { Client } from '../types';
import ClientProfileModal from '../components/ClientProfileModal';
import '../styles/table-row-spacing.css';
import { enableTableColumnResize } from '../utils/tableResizer';
import type { Dayjs } from 'dayjs';

const { Option } = Select;

const ClientList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [tierFilter, setTierFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [countryFilter, setCountryFilter] = useState('all');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [columnSettingsVisible, setColumnSettingsVisible] = useState(false);
  const [profileModalVisible, setProfileModalVisible] = useState(false);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [visibleColumns, setVisibleColumns] = useState<{[key: string]: boolean}>({
    clientId: true,
    name: true,
    industry: true,
    country: true,
    tier: true,
    status: true,
    contactPerson: true,
    contact: true,
    totalRevenue: true,
    totalProjects: true,
    lastContactDate: true,
    actions: true
  });

  // 获取客户列表
  const fetchClients = async () => {
    try {
      setLoading(true);
      const data = await clientService.getAllClients();
      setClients(data);
    } catch (error) {
      console.error('Failed to fetch clients:', error);
      message.error('Failed to load clients');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  // 初始化表格列宽调整功能
  useEffect(() => {
    const timer = setTimeout(() => {
      enableTableColumnResize('.client-list-container .ant-table');
    }, 200);
    
    return () => clearTimeout(timer);
  }, [clients]);

  // 过滤数据
  const filteredClients = clients.filter(client => {
    const matchesSearch = !searchText || 
      client.name.toLowerCase().includes(searchText.toLowerCase()) ||
      client.clientId.toLowerCase().includes(searchText.toLowerCase()) ||
      client.contactPerson.toLowerCase().includes(searchText.toLowerCase());

    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    const matchesTier = tierFilter === 'all' || client.tier === tierFilter;
    const matchesIndustry = industryFilter === 'all' || client.industry === industryFilter;
    const matchesCountry = countryFilter === 'all' || client.country === countryFilter;

    // 日期范围过滤
    let matchesDateRange = true;
    if (dateRange && dateRange[0] && dateRange[1]) {
      const clientDate = new Date(client.lastContactDate || client.createdAt);
      const startDate = dateRange[0].toDate();
      const endDate = dateRange[1].toDate();
      // 设置结束日期为当天的23:59:59
      endDate.setHours(23, 59, 59, 999);
      matchesDateRange = clientDate >= startDate && clientDate <= endDate;
    }

    return matchesSearch && matchesStatus && matchesTier && matchesIndustry && matchesCountry && matchesDateRange;
  });

  // 删除客户
  const handleDelete = async (id: string) => {
    try {
      await clientService.deleteClient(id);
      message.success('Client deleted successfully');
      fetchClients(); // 刷新列表
    } catch (error) {
      console.error('Failed to delete client:', error);
      message.error('Failed to delete client');
    }
  };

  // 显示客户画像
  const handleViewProfile = async (client: Client) => {
    try {
      // 获取完整的客户信息
      const fullClientData = await clientService.getClient(client.id);
      setSelectedClient(fullClientData);
      setProfileModalVisible(true);
    } catch (error) {
      console.error('Failed to load client profile:', error);
      message.error('Failed to load client profile');
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'active': { color: 'green', text: 'Active' },
      'potential': { color: 'blue', text: 'Potential' },
      'inactive': { color: 'red', text: 'Inactive' },
      'churned': { color: 'volcano', text: 'Churned' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color} style={{ fontSize: '12px' }}>{config.text}</Tag>;
  };

  // 渲染层级标签
  const renderTierTag = (tier: string) => {
    const tierColors = { 
      'S': 'purple', 
      'V': 'gold', 
      'B': 'blue', 
      'A': 'green'
    };
    return <Tag color={tierColors[tier as keyof typeof tierColors] || 'default'} style={{ fontSize: '12px' }}>{tier}</Tag>;
  };

  // 导出数据功能
  const handleExport = () => {
    const csvContent = [
      // 表头
      ['Client ID', 'Name', 'Industry', 'Country', 'Tier', 'Status', 'Contact Person', 'Email', 'Phone', 'Total Revenue', 'Total Projects', 'Last Contact Date'].join(','),
      // 数据行
      ...filteredClients.map(client => [
        client.clientId,
        `"${client.name.replace(/"/g, '""')}"`,
        `"${client.industry}"`,
        `"${client.country}"`,
        client.tier,
        client.status,
        `"${client.contactPerson.replace(/"/g, '""')}"`,
        client.email,
        client.phone || '-',
        client.totalRevenue,
        client.totalProjects,
        client.lastContactDate ? new Date(client.lastContactDate).toLocaleDateString() : '-'
      ].join(','))
    ].join('\n');

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `clients_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('Export successful');
  };

  // 获取唯一的行业列表
  const uniqueIndustries = Array.from(new Set(clients.map(c => c.industry).filter(Boolean)));
  
  // 获取唯一的国家列表
  const uniqueCountries = Array.from(new Set(clients.map(c => c.country).filter(Boolean)));

  // 表格列定义
  const allColumns = [
    {
      title: 'Client ID',
      dataIndex: 'clientId',
      key: 'clientId',
      width: 140,
      sorter: (a: Client, b: Client) => (a.clientId || a.id || '').localeCompare(b.clientId || b.id || ''),
      render: (text: string, record: Client) => (
        <span style={{ fontSize: '12px' }}>{text || record.id || '-'}</span>
      ),
    },
    {
      title: 'Client Name',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      sorter: (a: Client, b: Client) => a.name.localeCompare(b.name),
      render: (text: string, record: Client) => (
        <Button 
          type="link" 
          onClick={() => handleViewProfile(record)} 
          style={{ padding: 0, height: 'auto', fontSize: '12px' }}
        >
          {text}
        </Button>
      ),
    },

    {
      title: 'Industry',
      dataIndex: 'industry',
      key: 'industry',
      width: 140,
      sorter: (a: Client, b: Client) => a.industry.localeCompare(b.industry),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      width: 120,
      sorter: (a: Client, b: Client) => a.country.localeCompare(b.country),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: 'Tier',
      dataIndex: 'tier',
      key: 'tier',
      width: 100,
      sorter: (a: Client, b: Client) => a.tier.localeCompare(b.tier),
      render: (tier: string) => renderTierTag(tier),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      sorter: (a: Client, b: Client) => a.status.localeCompare(b.status),
      render: (status: string) => renderStatusTag(status),
    },
    {
      title: 'Contact Person',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      width: 150,
      sorter: (a: Client, b: Client) => a.contactPerson.localeCompare(b.contactPerson),
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>{text}</span>
      ),
    },
    {
      title: 'Contact Info',
      key: 'contact',
      width: 180,
      render: (_: any, record: Client) => (
        <Space direction="vertical" size="small">
          <Space size="small">
            <MailOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
            <span style={{ fontSize: '12px' }}>{record.email}</span>
          </Space>
          {record.phone && (
            <Space size="small">
              <PhoneOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
              <span style={{ fontSize: '12px' }}>{record.phone}</span>
            </Space>
          )}
        </Space>
      ),
    },
    {
      title: 'Total Revenue',
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      width: 140,
      align: 'right' as const,
      sorter: (a: Client, b: Client) => (a.totalRevenue || 0) - (b.totalRevenue || 0),
      render: (revenue: number) => (
        <span style={{ fontSize: '12px', color: '#52c41a', fontWeight: 'bold' }}>
          €{(revenue || 0).toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Total Projects',
      dataIndex: 'totalProjects',
      key: 'totalProjects',
      width: 120,
      align: 'center' as const,
      sorter: (a: Client, b: Client) => (a.totalProjects || 0) - (b.totalProjects || 0),
      render: (projects: number) => (
        <span style={{ fontSize: '12px' }}>{projects || 0}</span>
      ),
    },
    {
      title: 'Last Contact',
      dataIndex: 'lastContactDate',
      key: 'lastContactDate',
      width: 150,
      sorter: (a: Client, b: Client) => {
        const dateA = a.lastContactDate ? new Date(a.lastContactDate).getTime() : 0;
        const dateB = b.lastContactDate ? new Date(b.lastContactDate).getTime() : 0;
        return dateA - dateB;
      },
      render: (date: string) => (
        <span style={{ fontSize: '12px' }}>
          {date ? new Date(date).toLocaleDateString() : '-'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: Client) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => navigate(`/client/edit/${record.id}`)}
              size="small"
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Client"
            description="Are you sure you want to delete this client?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                danger
                size="small"
                style={{ fontSize: '12px' }}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 过滤显示的列
  const columns = allColumns.filter(column => visibleColumns[column.key as string]);

  // 列设置功能
  const handleColumnVisibilityChange = (columnKey: string, checked: boolean) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: checked
    }));
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和新建按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '16px',
          fontWeight: 600,
          margin: 0,
          color: 'var(--text-primary)'
        }}>
          Client Management
        </h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/client/create')}
          size="large"
        >
          New Client
        </Button>
      </div>

      {/* 搜索和筛选区域 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space wrap size="middle">
          <Input
            placeholder="Search clients..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />

          <DatePicker.RangePicker
            style={{ width: 250 }}
            value={dateRange}
            onChange={setDateRange}
            placeholder={['Start Date', 'End Date']}
            allowClear
          />
          
          <Select
            style={{ width: 140 }}
            value={statusFilter}
            onChange={setStatusFilter}
            placeholder="All Status"
          >
            <Option value="all">All Status</Option>
            <Option value="active">Active</Option>
            <Option value="potential">Potential</Option>
            <Option value="inactive">Inactive</Option>
            <Option value="churned">Churned</Option>
          </Select>

          <Select
            style={{ width: 140 }}
            value={tierFilter}
            onChange={setTierFilter}
            placeholder="All Tiers"
          >
            <Option value="all">All Tiers</Option>
            <Option value="S">S - Strategic</Option>
            <Option value="V">V - Valued</Option>
            <Option value="B">B - Business</Option>
            <Option value="A">A - Average</Option>
          </Select>

          <Select
            style={{ width: 140 }}
            value={industryFilter}
            onChange={setIndustryFilter}
            placeholder="All Industries"
          >
            <Option value="all">All Industries</Option>
            {uniqueIndustries.map(industry => (
              <Option key={industry} value={industry}>{industry}</Option>
            ))}
          </Select>

          <Select
            style={{ width: 140 }}
            value={countryFilter}
            onChange={setCountryFilter}
            placeholder="All Countries"
          >
            <Option value="all">All Countries</Option>
            {uniqueCountries.map(country => (
              <Option key={country} value={country}>{country}</Option>
            ))}
          </Select>

          <Button onClick={() => {
            setSearchText('');
            setStatusFilter('all');
            setTierFilter('all');
            setIndustryFilter('all');
            setCountryFilter('all');
            setDateRange(null);
          }}>
            Clear Filters
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '16px', fontWeight: 600 }}>Clients</span>
            
            {/* 功能快捷键 */}
            <Space size="small">
              <Tooltip title="Refresh">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchClients()}
                  loading={loading}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Export">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Column Settings">
                <Button
                  icon={<MenuOutlined />}
                  onClick={() => setColumnSettingsVisible(true)}
                  size="small"
                />
              </Tooltip>
            </Space>
          </div>
        }
      >
        <div className="client-list-container">
          <Table
            columns={columns}
            dataSource={filteredClients}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `${range[0]}-${range[1]} of ${total} clients`,
              defaultPageSize: 20,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
            scroll={{ x: 1600 }}
            size="middle"
            bordered
          />
        </div>
      </Card>

      {/* 列设置抽屉 */}
      <Drawer
        title="Column Settings"
        placement="right"
        onClose={() => setColumnSettingsVisible(false)}
        open={columnSettingsVisible}
        width={300}
      >
        <div style={{ padding: '8px 0' }}>
          <div style={{ marginBottom: '16px', fontWeight: 500 }}>
            Select columns to display:
          </div>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {allColumns.map(column => (
              <Checkbox
                key={column.key}
                checked={visibleColumns[column.key as string]}
                onChange={(e) => handleColumnVisibilityChange(column.key as string, e.target.checked)}
                disabled={column.key === 'actions'} // Actions列始终显示
              >
                {column.title}
              </Checkbox>
            ))}
          </Space>
          <div style={{ marginTop: '24px', padding: '16px 0', borderTop: '1px solid #f0f0f0' }}>
            <Space>
              <Button 
                onClick={() => {
                  // 显示所有列
                  const allVisible = Object.keys(visibleColumns).reduce((acc, key) => {
                    acc[key] = true;
                    return acc;
                  }, {} as {[key: string]: boolean});
                  setVisibleColumns(allVisible);
                }}
                size="small"
              >
                Show All
              </Button>
              <Button 
                onClick={() => {
                  // 只显示基本列
                  setVisibleColumns({
                    clientId: true,
                    name: true,
                    status: true,
                    totalRevenue: true,
                    actions: true,
                    industry: false,
                    country: false,
                    tier: false,
                    contactPerson: false,
                    contact: false,
                    totalProjects: false,
                    lastContactDate: false
                  });
                }}
                size="small"
              >
                Essential Only
              </Button>
            </Space>
          </div>
        </div>
      </Drawer>

      {/* 客户画像弹框 */}
      <ClientProfileModal
        visible={profileModalVisible}
        onClose={() => {
          setProfileModalVisible(false);
          setSelectedClient(null);
        }}
        client={selectedClient}
        onClientUpdate={(updatedClient) => {
          // 重新获取客户列表以确保数据同步
          fetchClients();
          setSelectedClient(updatedClient);
        }}
      />
    </div>
  );
};

export default ClientList; 