import React, { useState } from 'react';
import {
  <PERSON>er,
  Card,
  Avatar,
  Tag,
  Badge,
  Timeline,
  List,
  Space,
  Typography,
  Button,
  Input,
  Tooltip,
  Empty,
  Divider
} from 'antd';
import {
  UserOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  EditOutlined,
  TeamOutlined,
  SendOutlined,
  EyeOutlined,
  ProjectOutlined
} from '@ant-design/icons';
import { useWebSocket } from '../hooks/useWebSocket';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface OnlineUser {
  userId: string;
  username: string;
  avatar?: string;
  lastSeen: Date;
}

interface Activity {
  id: string;
  type: string;
  title: string;
  description?: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
  timestamp: Date;
  metadata?: any;
}

interface EditingUser {
  userId: string;
  username: string;
  section: string;
  isEditing: boolean;
}

interface RealTimeCollaborationProps {
  projectId: string;
  visible: boolean;
  onClose: () => void;
}

const RealTimeCollaboration: React.FC<RealTimeCollaborationProps> = ({
  projectId,
  visible,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState<'users' | 'activity' | 'comments'>('users');
  const [commentText, setCommentText] = useState('');

  const {
    isConnected,
    onlineUsers,
    activities,
    editingUsers,
    sendComment,
    sendActivity
  } = useWebSocket({
    projectId,
    onActivity: (activity) => {
      // 活动已通过hook自动更新到状态
    },
    onComment: (comment) => {
      // 评论处理
      console.log('新评论:', comment);
    }
  });

  // 获取用户当前编辑的区域
  const getUserEditingSection = (userId: string): string[] => {
    return editingUsers
      .filter(user => user.userId === userId && user.isEditing)
      .map(user => user.section);
  };

  // 发送评论
  const handleSendComment = () => {
    if (commentText.trim()) {
      sendComment({
        projectId,
        content: commentText,
        mentions: [] // 这里可以解析@提及
      });
      setCommentText('');
    }
  };

  // 渲染在线用户状态
  const renderUserStatus = (user: OnlineUser) => {
    const editingSections = getUserEditingSection(user.userId);
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
        <Avatar 
          src={user.avatar} 
          icon={<UserOutlined />} 
          size="small"
          style={{ marginRight: 8 }}
        />
        <div style={{ flex: 1 }}>
          <div style={{ fontWeight: 500 }}>{user.username}</div>
          {editingSections.length > 0 && (
            <div style={{ fontSize: '12px', color: '#52c41a' }}>
              <EditOutlined style={{ marginRight: 4 }} />
              正在编辑: {editingSections.join(', ')}
            </div>
          )}
        </div>
        <Badge 
          status="success" 
          text="在线"
          style={{ fontSize: '12px' }}
        />
      </div>
    );
  };

  // 渲染活动图标
  const getActivityIcon = (type: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'project_created': <ProjectOutlined style={{ color: '#1890ff' }} />,
      'project_updated': <EditOutlined style={{ color: '#52c41a' }} />,
      'collaborator_added': <TeamOutlined style={{ color: '#722ed1' }} />,
      'stage_updated': <ClockCircleOutlined style={{ color: '#fa8c16' }} />,
      'document_uploaded': <ProjectOutlined style={{ color: '#13c2c2' }} />,
      'comment_added': <MessageOutlined style={{ color: '#eb2f96' }} />
    };
    return iconMap[type] || <ProjectOutlined />;
  };

  // 渲染活动时间线
  const renderActivities = () => {
    if (activities.length === 0) {
      return (
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无活动记录"
        />
      );
    }

    return (
      <Timeline mode="left" style={{ marginTop: 16 }}>
        {activities.map((activity) => (
          <Timeline.Item
            key={activity.id}
            dot={getActivityIcon(activity.type)}
            label={
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {new Date(activity.timestamp).toLocaleTimeString()}
              </Text>
            }
          >
            <Card size="small" style={{ marginBottom: 8 }}>
              <Space direction="vertical" size="small">
                <div>
                  <Text strong>{activity.title}</Text>
                </div>
                {activity.description && (
                  <Text type="secondary">{activity.description}</Text>
                )}
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar 
                    src={activity.user.avatar} 
                    icon={<UserOutlined />}
                    size="small"
                    style={{ marginRight: 8 }}
                  />
                  <Text style={{ fontSize: '12px' }}>{activity.user.username}</Text>
                </div>
              </Space>
            </Card>
          </Timeline.Item>
        ))}
      </Timeline>
    );
  };

  const tabItems = [
    {
      key: 'users',
      label: (
        <Space>
          <TeamOutlined />
          在线用户 ({onlineUsers.length})
        </Space>
      )
    },
          {
        key: 'activity',
        label: (
          <Space>
            <ProjectOutlined />
            项目活动
          </Space>
        )
      },
    {
      key: 'comments',
      label: (
        <Space>
          <MessageOutlined />
          实时评论
        </Space>
      )
    }
  ];

  return (
    <Drawer
      title={
        <Space>
          <Badge status={isConnected ? 'success' : 'error'} />
          实时协作
          {isConnected ? (
            <Tag color="green">已连接</Tag>
          ) : (
            <Tag color="red">未连接</Tag>
          )}
        </Space>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', marginBottom: 16 }}>
          {tabItems.map(tab => (
            <Button
              key={tab.key}
              type={activeTab === tab.key ? 'primary' : 'text'}
              onClick={() => setActiveTab(tab.key as any)}
              style={{ flex: 1, marginRight: 8 }}
              size="small"
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </div>

      {activeTab === 'users' && (
        <div>
          <Title level={5}>在线协作者</Title>
          {onlineUsers.length === 0 ? (
            <Empty 
              image={<UserOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
              description="暂无在线用户"
            />
          ) : (
            <List
              dataSource={onlineUsers}
              renderItem={(user) => (
                <List.Item style={{ padding: '12px 0' }}>
                  {renderUserStatus(user)}
                </List.Item>
              )}
            />
          )}
          
          {editingUsers.length > 0 && (
            <>
              <Divider />
              <Title level={5}>正在编辑</Title>
              <List
                dataSource={editingUsers.filter(user => user.isEditing)}
                renderItem={(editingUser) => (
                  <List.Item>
                    <Space>
                      <Avatar size="small" icon={<EditOutlined />} />
                      <div>
                        <div>{editingUser.username}</div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {editingUser.section}
                        </Text>
                      </div>
                    </Space>
                  </List.Item>
                )}
              />
            </>
          )}
        </div>
      )}

      {activeTab === 'activity' && (
        <div>
          <Title level={5}>项目活动</Title>
          {renderActivities()}
        </div>
      )}

      {activeTab === 'comments' && (
        <div>
          <Title level={5}>实时讨论</Title>
          
          <div style={{ marginBottom: 16 }}>
            <TextArea
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              placeholder="输入评论... (支持 @用户名 提及)"
              rows={3}
              maxLength={500}
            />
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginTop: 8 
            }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {commentText.length}/500
              </Text>
              <Button
                type="primary"
                icon={<SendOutlined />}
                size="small"
                onClick={handleSendComment}
                disabled={!commentText.trim()}
              >
                发送
              </Button>
            </div>
          </div>

          <Divider />
          
          <Empty 
            image={<MessageOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />}
            description="开始讨论项目吧!"
          />
        </div>
      )}
    </Drawer>
  );
};

export default RealTimeCollaboration; 