"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkRole = exports.authMiddleware = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const typeorm_1 = require("typeorm");
const User_1 = require("../entities/User");
const environment_1 = require("../config/environment");
const authMiddleware = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (environment_1.isDevelopment) {
            if (authHeader) {
                try {
                    const parts = authHeader.split(' ');
                    if (parts.length === 2 && parts[0] === 'Bearer') {
                        const token = parts[1];
                        const data = jsonwebtoken_1.default.verify(token, environment_1.securityConfig.jwt.secret);
                        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
                        const user = await userRepository.findOne({ where: { id: data.userId } });
                        if (user) {
                            req.user = user;
                        }
                    }
                }
                catch (tokenError) {
                    console.log('开发环境：令牌验证失败，但允许请求通过');
                }
            }
            else {
                console.log('开发环境：未提供令牌，但允许请求通过');
            }
            return next();
        }
        if (!authHeader) {
            return res.status(401).json({ message: '未提供认证令牌' });
        }
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            return res.status(401).json({ message: '令牌格式错误' });
        }
        const token = parts[1];
        const data = jsonwebtoken_1.default.verify(token, environment_1.securityConfig.jwt.secret);
        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
        const user = await userRepository.findOne({ where: { id: data.userId } });
        if (!user) {
            return res.status(401).json({ message: '用户不存在' });
        }
        req.user = user;
        return next();
    }
    catch (error) {
        return res.status(401).json({ message: '无效的令牌' });
    }
};
exports.authMiddleware = authMiddleware;
const checkRole = (roles) => {
    return (req, res, next) => {
        if (environment_1.isDevelopment) {
            console.log('开发环境：跳过角色检查');
            return next();
        }
        if (!req.user) {
            return res.status(401).json({ message: '未认证' });
        }
        if (roles.includes(req.user.role)) {
            return next();
        }
        return res.status(403).json({ message: '没有权限执行此操作' });
    };
};
exports.checkRole = checkRole;
//# sourceMappingURL=auth.middleware.js.map