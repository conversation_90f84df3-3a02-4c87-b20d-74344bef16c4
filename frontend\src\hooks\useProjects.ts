import { useState, useCallback, useEffect } from 'react';
import { Project, ProjectFilters } from '../types';
import projectService from '../services/new-project.service';
import { message } from 'antd';

export const useProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<ProjectFilters>({
    searchText: '',
    status: 'all',
    country: 'all',
    tier: 'all',
    category: 'all',
    revenue: 'all',
    probability: 'all',
    level: 'all',
    createDate: 'all'
  });

  // 加载项目列表
  const loadProjects = useCallback(async (forceRefresh = false) => {
    setLoading(true);
    try {
      const data = await projectService.getAllProjects(forceRefresh);
      setProjects(data);
    } catch (error) {
      message.error('加载项目列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 创建项目
  const createProject = useCallback(async (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newProject = await projectService.createProject(project);
      setProjects(prev => [...prev, newProject]);
      message.success('创建项目成功');
      return newProject;
    } catch (error) {
      message.error('创建项目失败');
      return null;
    }
  }, []);

  // 更新项目
  const updateProject = useCallback(async (id: string, project: Partial<Project>) => {
    try {
      const updatedProject = await projectService.updateProject(id, project);
      setProjects(prev => prev.map(p => p.id === id ? updatedProject : p));
      message.success('更新项目成功');
      return updatedProject;
    } catch (error) {
      message.error('更新项目失败');
      return null;
    }
  }, []);

  // 删除项目
  const deleteProject = useCallback(async (id: string) => {
    try {
      await projectService.deleteProject(id);
      setProjects(prev => prev.filter(p => p.id !== id));
      message.success('删除项目成功');
      return true;
    } catch (error) {
      message.error('删除项目失败');
      return false;
    }
  }, []);

  // 更新筛选条件
  const updateFilters = useCallback((newFilters: Partial<ProjectFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // 重置筛选条件
  const resetFilters = useCallback(() => {
    setFilters({
      searchText: '',
      status: 'all',
      country: 'all',
      tier: 'all',
      category: 'all',
      revenue: 'all',
      probability: 'all',
      level: 'all',
      createDate: 'all'
    });
  }, []);

  // 过滤项目
  const filteredProjects = projects.filter(project => {
    const {
      searchText,
      status,
      country,
      tier,
      category,
      revenue,
      probability,
      level,
      createDate
    } = filters;

    // 搜索文本匹配
    const matchesSearch = searchText === '' ||
      project.name.toLowerCase().includes(searchText.toLowerCase()) ||
      project.projectId.toLowerCase().includes(searchText.toLowerCase()) ||
      project.client.toLowerCase().includes(searchText.toLowerCase()) ||
      project.owner.toLowerCase().includes(searchText.toLowerCase());

    // 状态筛选
    const matchesStatus = status === 'all' || project.status === status;

    // 国家筛选
    const matchesCountry = country === 'all' || project.country === country;

    // 级别筛选
    const matchesTier = tier === 'all' || project.tier === tier;

    // 类别筛选
    const matchesCategory = category === 'all' || project.category === category;

    // 收入筛选
    let matchesRevenue = true;
    if (revenue !== 'all') {
      if (revenue === 'veryLow') {
        matchesRevenue = project.revenue < 100000;
      } else if (revenue === 'low') {
        matchesRevenue = project.revenue >= 100000 && project.revenue < 500000;
      } else if (revenue === 'medium') {
        matchesRevenue = project.revenue >= 500000 && project.revenue < 1000000;
      } else if (revenue === 'high') {
        matchesRevenue = project.revenue >= 1000000;
      }
    }

    // 概率筛选
    let matchesProbability = true;
    if (probability !== 'all') {
      if (typeof project.probability === 'string') {
        matchesProbability = project.probability === probability;
      } else if (typeof project.probability === 'number') {
        if (probability === 'A') {
          matchesProbability = project.probability >= 80 && project.probability <= 100;
        } else if (probability === 'B') {
          matchesProbability = project.probability >= 60 && project.probability < 80;
        } else if (probability === 'C') {
          matchesProbability = project.probability >= 40 && project.probability < 60;
        } else if (probability === 'D') {
          matchesProbability = project.probability < 40;
        }
      }
    }

    // Level 筛选
    const matchesLevel = level === 'all' || project.level === level;

    // 创建日期筛选
    let matchesCreateDate = true;
    if (createDate !== 'all') {
      const today = new Date();
      const projectDate = new Date(project.createdAt);

      if (createDate === 'last7days') {
        const lastWeek = new Date(today);
        lastWeek.setDate(today.getDate() - 7);
        matchesCreateDate = projectDate >= lastWeek;
      } else if (createDate === 'last30days') {
        const lastMonth = new Date(today);
        lastMonth.setDate(today.getDate() - 30);
        matchesCreateDate = projectDate >= lastMonth;
      } else if (createDate === 'last90days') {
        const lastQuarter = new Date(today);
        lastQuarter.setDate(today.getDate() - 90);
        matchesCreateDate = projectDate >= lastQuarter;
      } else if (createDate === 'thisYear') {
        const thisYear = new Date(today.getFullYear(), 0, 1);
        matchesCreateDate = projectDate >= thisYear;
      }
    }

    return matchesSearch && matchesStatus && matchesCountry && matchesTier &&
           matchesCategory && matchesRevenue && matchesProbability && matchesLevel && matchesCreateDate;
  });

  // 初始化加载
  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  return {
    projects,
    filteredProjects,
    loading,
    filters,
    loadProjects,
    createProject,
    updateProject,
    deleteProject,
    updateFilters,
    resetFilters
  };
}; 