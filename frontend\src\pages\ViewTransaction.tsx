import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  message,
  Spin,
  Row,
  Col,
  Typography,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  DollarOutlined,
  CalendarOutlined,
  UserOutlined,
  ProjectOutlined,
  BankOutlined,
  FileTextOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

// 模拟交易数据
const MOCK_TRANSACTION_DATA = {
  '1': {
    transactionId: 'TXN-2025-001',
    type: 'income' as 'income' | 'expense',
    category: 'Project Revenue',
    description: 'Digital Transformation Project Payment',
    amountWithoutVAT: 104166.67,
    vatRate: 20,
    vatAmount: 20833.33,
    totalAmount: 125000,
    client: 'Global Tech Solutions',
    project: 'Digital Transformation',
    status: 'completed',
    date: '2025-01-15',
    paymentMethod: 'Bank Transfer',
    reference: 'INV-2025-001',
    notes: 'Initial project payment received from client. This covers the first milestone of the digital transformation project.',
    tags: ['milestone', 'important'],
    createdAt: '2025-01-10T10:30:00Z',
    updatedAt: '2025-01-15T14:20:00Z'
  },
  '2': {
    transactionId: 'TXN-2025-002',
    type: 'expense' as 'income' | 'expense',
    category: 'Software & Licenses',
    description: 'Annual Software Licenses',
    amountWithoutVAT: 20833.33,
    vatRate: 20,
    vatAmount: 4166.67,
    totalAmount: 25000,
    status: 'completed',
    date: '2025-01-10',
    paymentMethod: 'Credit Card',
    reference: 'LIC-2025-001',
    notes: 'Annual renewal for development tools and software licenses.',
    tags: ['recurring', 'essential'],
    createdAt: '2025-01-08T09:15:00Z',
    updatedAt: '2025-01-10T16:45:00Z'
  }
};

const ViewTransaction: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [transaction, setTransaction] = useState<any>(null);

  // 加载交易数据
  const loadTransactionData = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      if (!id || !MOCK_TRANSACTION_DATA[id as keyof typeof MOCK_TRANSACTION_DATA]) {
        message.error('Transaction not found');
        navigate('/finance');
        return;
      }

      const transactionData = MOCK_TRANSACTION_DATA[id as keyof typeof MOCK_TRANSACTION_DATA];
      setTransaction(transactionData);
      
    } catch (error) {
      console.error('Failed to load transaction:', error);
      message.error('Failed to load transaction data');
      navigate('/finance');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadTransactionData();
    }
  }, [id]);

  // 渲染类型标签
  const renderTypeTag = (type: string) => {
    const typeConfig = {
      'income': { color: 'green', text: 'Income', icon: '💰' },
      'expense': { color: 'red', text: 'Expense', icon: '💸' }
    };
    
    const config = typeConfig[type as keyof typeof typeConfig];
    return (
      <Tag color={config.color} style={{ padding: '4px 12px' }}>
        {config.icon} {config.text}
      </Tag>
    );
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'completed': { color: 'green', text: 'Completed' },
      'pending': { color: 'orange', text: 'Pending' },
      'cancelled': { color: 'red', text: 'Cancelled' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color} style={{ padding: '4px 12px' }}>{config.text}</Tag>;
  };

  // 处理删除
  const handleDelete = () => {
    message.success('Transaction deleted successfully');
    navigate('/finance');
  };

  if (loading) {
    return (
      <div style={{ 
        padding: '24px', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin 
          size="large" 
          indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
        />
      </div>
    );
  }

  if (!transaction) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Text type="secondary">Transaction not found</Text>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和操作按钮 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/finance')}
          style={{ marginBottom: '16px' }}
        >
          Back to Transaction List
        </Button>
        
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <div style={{ 
            margin: 0, 
            color: '#262626', 
            fontSize: '20px', 
            textAlign: 'center', 
            fontWeight: '600',
            lineHeight: '1.2',
            width: '100%'
          }}>
            Transaction Details
          </div>
        </div>
        
        {/* 操作按钮区域 */}
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          <Space>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/finance/edit/${id}`)}
            >
              Edit
            </Button>
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
            >
              Delete
            </Button>
          </Space>
        </div>
      </div>

      <Row gutter={24}>
        {/* 主要信息 */}
        <Col xs={24} lg={16}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <DollarOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                <span>Transaction Information</span>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            <Descriptions column={2} size="middle">
              <Descriptions.Item label="Transaction ID" span={2}>
                <Text strong style={{ fontSize: '16px' }}>{transaction.transactionId}</Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Type">
                {renderTypeTag(transaction.type)}
              </Descriptions.Item>
              
              <Descriptions.Item label="Status">
                {renderStatusTag(transaction.status)}
              </Descriptions.Item>
              
              <Descriptions.Item label="Category">
                <Text>{transaction.category}</Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Amount without VAT">
                <Text style={{ fontSize: '16px', fontWeight: 500 }}>
                  €{transaction.amountWithoutVAT.toLocaleString()}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label={`VAT (${transaction.vatRate}%)`}>
                <Text style={{ fontSize: '16px', fontWeight: 500 }}>
                  €{transaction.vatAmount.toLocaleString()}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Total Amount" span={2}>
                <Text 
                  style={{ 
                    fontSize: '20px', 
                    fontWeight: 700,
                    color: transaction.type === 'income' ? '#52c41a' : '#ff4d4f'
                  }}
                >
                  {transaction.type === 'income' ? '+' : '-'}€{transaction.totalAmount.toLocaleString()}
                </Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="Description" span={2}>
                <Text>{transaction.description}</Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 详细信息 */}
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <FileTextOutlined style={{ marginRight: '8px', color: '#10B981' }} />
                <span>Additional Details</span>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            <Descriptions column={1} size="middle">
              {transaction.client && (
                <Descriptions.Item label="Client/Vendor">
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <UserOutlined style={{ marginRight: '8px', color: '#3B82F6' }} />
                    <Text>{transaction.client}</Text>
                  </div>
                </Descriptions.Item>
              )}
              
              {transaction.project && (
                <Descriptions.Item label="Related Project">
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ProjectOutlined style={{ marginRight: '8px', color: '#10B981' }} />
                    <Text>{transaction.project}</Text>
                  </div>
                </Descriptions.Item>
              )}
              
              <Descriptions.Item label="Transaction Date">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <CalendarOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                  <Text>{dayjs(transaction.date).format('DD/MM/YYYY')}</Text>
                </div>
              </Descriptions.Item>
              
              <Descriptions.Item label="Payment Method">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <BankOutlined style={{ marginRight: '8px', color: '#3B82F6' }} />
                  <Text>{transaction.paymentMethod}</Text>
                </div>
              </Descriptions.Item>
              
              {transaction.reference && (
                <Descriptions.Item label="Reference Number">
                  <Text code>{transaction.reference}</Text>
                </Descriptions.Item>
              )}
              
              {transaction.tags && transaction.tags.length > 0 && (
                <Descriptions.Item label="Tags">
                  <Space wrap>
                    {transaction.tags.map((tag: string) => (
                      <Tag key={tag} color="blue">{tag}</Tag>
                    ))}
                  </Space>
                </Descriptions.Item>
              )}
              
              {transaction.notes && (
                <Descriptions.Item label="Notes">
                  <Text>{transaction.notes}</Text>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>

        {/* 侧边栏信息 */}
        <Col xs={24} lg={8}>
          <Card 
            title="Quick Actions"
            style={{ marginBottom: '24px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                block 
                icon={<EditOutlined />}
                onClick={() => navigate(`/finance/edit/${id}`)}
              >
                Edit Transaction
              </Button>
              <Button 
                block 
                onClick={() => navigate('/finance/create')}
              >
                Create Similar
              </Button>
              <Button 
                danger 
                block 
                icon={<DeleteOutlined />}
                onClick={handleDelete}
              >
                Delete Transaction
              </Button>
            </Space>
          </Card>

          {/* 系统信息 */}
          <Card title="System Information">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Created">
                <Text type="secondary">
                  {dayjs(transaction.createdAt).format('DD/MM/YYYY HH:mm')}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Last Updated">
                <Text type="secondary">
                  {dayjs(transaction.updatedAt).format('DD/MM/YYYY HH:mm')}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ViewTransaction; 