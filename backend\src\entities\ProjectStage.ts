import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { Project } from './Project';

@Entity('project_stages')
export class ProjectStage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Project, project => project.stages, { onDelete: 'CASCADE' })
  project: Project;

  @Column()
  name: string;

  @Column({
    type: 'varchar',
  })
  type: 'basic_info' | 'proposal' | 'contract' | 'execution' | 'acceptance' | 'finance' | 'closeout' | 'after_sales';

  @Column({
    type: 'varchar',
    default: 'not_started'
  })
  status: 'not_started' | 'in_progress' | 'completed';

  @Column({ type: 'date', nullable: true })
  startDate: Date;

  @Column({ type: 'date', nullable: true })
  endDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  data: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
