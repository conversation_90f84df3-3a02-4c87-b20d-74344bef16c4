import { message, notification } from 'antd';
import { ApiError } from '../types';
import Toast from '../components/common/Toast';

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  API = 'api',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  TIMEOUT = 'timeout',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown'
}

// 错误详情接口
export interface ErrorDetails {
  type: ErrorType;
  code?: string | number;
  message: string;
  originalError?: any;
  timestamp: number;
  context?: Record<string, any>;
}

// 错误日志接口
interface ErrorLog {
  errors: ErrorDetails[];
  maxSize: number;
}

export class ErrorHandler {
  private static errorLog: ErrorLog = {
    errors: [],
    maxSize: 100
  };

  /**
   * 处理错误并显示消息
   * @param error 错误对象
   * @param fallbackMessage 默认错误消息
   * @param showMessage 是否显示消息提示
   * @param context 错误上下文信息
   * @returns 错误详情
   */
  static handle(
    error: unknown,
    fallbackMessage?: string,
    showMessage = true,
    context?: Record<string, any>
  ): ErrorDetails {
    // 解析错误类型和消息
    const errorDetails = this.parseError(error, fallbackMessage, context);

    // 记录错误
    this.logError(errorDetails);

    // 显示错误消息
    if (showMessage) {
      this.displayError(errorDetails);
    }

    return errorDetails;
  }

  /**
   * 解析错误对象，提取类型、代码和消息
   */
  private static parseError(
    error: unknown,
    fallbackMessage?: string,
    context?: Record<string, any>
  ): ErrorDetails {
    let type = ErrorType.UNKNOWN;
    let code: string | number | undefined = undefined;
    let message = fallbackMessage || '发生未知错误';

    // 网络错误
    if (error instanceof TypeError && error.message.includes('network')) {
      type = ErrorType.NETWORK;
      message = '网络连接错误，请检查您的网络连接';
    }
    // Axios错误
    else if (typeof error === 'object' && error !== null) {
      const err = error as any;

      // 检查是否有响应对象
      if (err.response) {
        const status = err.response.status;

        // 根据HTTP状态码确定错误类型
        if (status === 401) {
          type = ErrorType.AUTHENTICATION;
          message = '身份验证失败，请重新登录';
        } else if (status === 403) {
          type = ErrorType.AUTHORIZATION;
          message = '您没有权限执行此操作';
        } else if (status === 404) {
          type = ErrorType.NOT_FOUND;
          message = '请求的资源不存在';
        } else if (status === 422) {
          type = ErrorType.VALIDATION;
          message = '提交的数据无效';
        } else if (status >= 500) {
          type = ErrorType.SERVER;
          message = '服务器错误，请稍后再试';
        } else {
          type = ErrorType.API;
          message = err.response.data?.message || '请求处理失败';
        }

        code = status;
      }
      // 请求超时
      else if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        type = ErrorType.TIMEOUT;
        message = '请求超时，请稍后再试';
      }
      // 其他错误
      else if (err.message) {
        type = ErrorType.CLIENT;
        message = err.message;
      }
    }
    // 标准Error对象
    else if (error instanceof Error) {
      type = ErrorType.CLIENT;
      message = error.message;
    }
    // 字符串错误
    else if (typeof error === 'string') {
      type = ErrorType.CLIENT;
      message = error;
    }

    return {
      type,
      code,
      message,
      originalError: error,
      timestamp: Date.now(),
      context
    };
  }

  /**
   * 记录错误到内存日志
   */
  private static logError(errorDetails: ErrorDetails): void {
    console.error(`[${errorDetails.type.toUpperCase()}]${errorDetails.code ? ` [${errorDetails.code}]` : ''}: ${errorDetails.message}`, errorDetails);

    // 添加到错误日志
    this.errorLog.errors.push(errorDetails);

    // 如果超出最大大小，删除最旧的错误
    if (this.errorLog.errors.length > this.errorLog.maxSize) {
      this.errorLog.errors.shift();
    }

    // 可以在这里添加发送错误到服务器的逻辑
  }

  /**
   * 显示错误消息给用户
   */
  private static displayError(errorDetails: ErrorDetails): void {
    const { type, message } = errorDetails;

    // 根据错误类型选择不同的显示方式
    switch (type) {
      case ErrorType.NETWORK:
      case ErrorType.TIMEOUT:
        // 使用通知，因为这些可能需要用户采取行动
        notification.error({
          message: '连接问题',
          description: message,
          duration: 0, // 不自动关闭
        });
        break;

      case ErrorType.AUTHENTICATION:
        // 身份验证错误，可能需要重定向到登录页面
        Toast.error('身份验证失败', message);
        // 可以在这里添加重定向逻辑
        break;

      case ErrorType.SERVER:
        // 服务器错误，使用Toast
        Toast.error('服务器错误', message);
        break;

      default:
        // 其他错误使用普通消息
        if (typeof errorDetails.message === 'string') {
          notification.error({ message: '错误', description: errorDetails.message });
        } else {
          notification.error({ message: '错误', description: '发生未知错误' });
        }
    }
  }

  /**
   * 使用错误处理包装异步操作
   * @param operation 异步操作
   * @param fallbackMessage 默认错误消息
   * @param showMessage 是否显示消息提示
   * @param context 错误上下文信息
   * @param retryOptions 重试选项
   * @returns 操作结果或null
   */
  static async withErrorHandling<T>(
    operation: () => Promise<T>,
    fallbackMessage = '操作失败',
    showMessage = true,
    context?: Record<string, any>,
    retryOptions?: { maxRetries: number; delay: number }
  ): Promise<T | null> {
    let retries = 0;
    const maxRetries = retryOptions?.maxRetries || 0;
    const delay = retryOptions?.delay || 1000;

    while (true) {
      try {
        return await operation();
      } catch (error) {
        // 检查是否可以重试
        const errorDetails = this.handle(error, fallbackMessage, showMessage && retries === maxRetries, {
          ...context,
          retryCount: retries,
          maxRetries
        });

        // 如果是网络错误或超时错误，并且还有重试次数，则重试
        const canRetry = (
          errorDetails.type === ErrorType.NETWORK ||
          errorDetails.type === ErrorType.TIMEOUT ||
          errorDetails.type === ErrorType.SERVER
        ) && retries < maxRetries;

        if (canRetry) {
          retries++;
          console.log(`重试操作 (${retries}/${maxRetries})...`);

          // 等待指定的延迟时间
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        return null;
      }
    }
  }

  /**
   * 使用错误处理包装异步操作，抛出错误而不是返回null
   * @param operation 异步操作
   * @param fallbackMessage 默认错误消息
   * @param showMessage 是否显示消息提示
   * @param context 错误上下文信息
   * @returns 操作结果
   * @throws 原始错误
   */
  static async withErrorHandlingThrow<T>(
    operation: () => Promise<T>,
    fallbackMessage = '操作失败',
    showMessage = true,
    context?: Record<string, any>
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.handle(error, fallbackMessage, showMessage, context);
      throw error;
    }
  }

  /**
   * 获取错误日志
   * @returns 错误日志
   */
  static getErrorLog(): ErrorDetails[] {
    return [...this.errorLog.errors];
  }

  /**
   * 清除错误日志
   */
  static clearErrorLog(): void {
    this.errorLog.errors = [];
  }

  /**
   * 导出错误日志为JSON
   * @returns JSON字符串
   */
  static exportErrorLog(): string {
    return JSON.stringify(this.errorLog.errors, null, 2);
  }
}