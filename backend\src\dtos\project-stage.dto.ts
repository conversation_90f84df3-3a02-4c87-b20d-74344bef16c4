import { IsString, IsNotEmpty, IsOptional, IsUUID, IsEnum, IsDateString } from 'class-validator';

// 项目阶段状态枚举
export enum ProjectStageStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

// 项目阶段类型枚举
export enum ProjectStageType {
  BASIC_INFO = 'basic_info',
  PROPOSAL = 'proposal',
  CONTRACT = 'contract',
  EXECUTION = 'execution',
  ACCEPTANCE = 'acceptance',
  FINANCE = 'finance',
  CLOSEOUT = 'closeout',
  AFTER_SALES = 'after_sales'
}

/**
 * 创建项目阶段的DTO
 */
export class CreateProjectStageDto {
  @IsNotEmpty({ message: '项目ID不能为空' })
  @IsString({ message: '项目ID必须是字符串' })
  projectId: string;

  @IsNotEmpty({ message: '阶段名称不能为空' })
  @IsString({ message: '阶段名称必须是字符串' })
  name: string;

  @IsNotEmpty({ message: '阶段类型不能为空' })
  @IsEnum(ProjectStageType, { message: '阶段类型无效' })
  type: ProjectStageType;

  @IsNotEmpty({ message: '阶段状态不能为空' })
  @IsEnum(ProjectStageStatus, { message: '阶段状态无效' })
  status: ProjectStageStatus;

  @IsOptional()
  @IsDateString({}, { message: '开始日期格式无效' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: '结束日期格式无效' })
  endDate?: string;

  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  notes?: string;

  @IsOptional()
  @IsString({ message: '数据必须是JSON字符串' })
  data?: string;
}

/**
 * 更新项目阶段的DTO
 */
export class UpdateProjectStageDto {
  @IsOptional()
  @IsString({ message: '阶段名称必须是字符串' })
  name?: string;

  @IsOptional()
  @IsEnum(ProjectStageStatus, { message: '阶段状态无效' })
  status?: ProjectStageStatus;

  @IsOptional()
  @IsDateString({}, { message: '开始日期格式无效' })
  startDate?: string;

  @IsOptional()
  @IsDateString({}, { message: '结束日期格式无效' })
  endDate?: string;

  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  notes?: string;

  @IsOptional()
  @IsString({ message: '数据必须是JSON字符串' })
  data?: string;
}

/**
 * 项目阶段ID参数的DTO
 */
export class ProjectStageIdParamDto {
  @IsNotEmpty({ message: '阶段ID不能为空' })
  @IsString({ message: '阶段ID必须是字符串' })
  id: string;
}

/**
 * 项目ID参数的DTO
 */
export class ProjectIdParamDto {
  @IsNotEmpty({ message: '项目ID不能为空' })
  @IsString({ message: '项目ID必须是字符串' })
  projectId: string;
}
