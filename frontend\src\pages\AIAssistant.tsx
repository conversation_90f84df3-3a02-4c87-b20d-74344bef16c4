import React, { useState } from 'react';
import { Card, Row, Col, Button, Upload, message, Space, Typography, Divider } from 'antd';
import {
  ArrowLeftOutlined,
  CloudUploadOutlined,
  MessageOutlined,
  RobotOutlined,
  FileTextOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { Dragger } = Upload;

  const AIAssistant: React.FC = () => {
    const navigate = useNavigate();
    const [uploadLoading, setUploadLoading] = useState(false);

  // Handle file upload
  const handleFileUpload = (info: any) => {
    const { status } = info.file;
    if (status === 'uploading') {
      setUploadLoading(true);
    }
    if (status === 'done') {
      setUploadLoading(false);
      message.success(`${info.file.name} file uploaded successfully`);
      // Navigate to AI analysis page
      navigate('/ai/analysis', { state: { fileName: info.file.name } });
    } else if (status === 'error') {
      setUploadLoading(false);
      message.error(`${info.file.name} file upload failed`);
    }
  };

  // Mock upload properties
  const uploadProps = {
    name: 'file',
    multiple: true,
    action: '/api/upload', // Mock upload API
    onChange: handleFileUpload,
    beforeUpload: (file: File) => {
      const isValidType = ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'xls'].some(type => 
        file.name.toLowerCase().endsWith(type)
      );
      if (!isValidType) {
        message.error('Only PDF, Word, Excel and text files are supported!');
        return false;
      }
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        message.error('File must be smaller than 20MB!');
        return false;
      }
      return true;
    },
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Page title */}
      <div style={{ marginBottom: '48px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/finance')}
          style={{ marginBottom: '16px' }}
        >
          Back to Finance Management
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          <RobotOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
          AI Assistant
        </div>
      </div>

      {/* Feature introduction */}
      <Card style={{ 
        marginBottom: '24px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <BulbOutlined style={{ fontSize: '32px', marginBottom: '16px', color: '#FF7A00' }} />
          <Title level={3} style={{ margin: 0 }}>
            AI-Powered Business Intelligence
          </Title>
          <Paragraph type="secondary" style={{ marginTop: '8px', fontSize: '16px' }}>
            Upload documents for automatic analysis, or chat with our AI assistant for system-related help
          </Paragraph>
        </div>
      </Card>

      <Row gutter={24}>
        {/* File upload and AI analysis */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <FileTextOutlined style={{ marginRight: '8px', color: '#10B981' }} />
                <span>Document Intelligence Analysis</span>
              </div>
            }
            style={{ height: '100%' }}
            extra={
              <Button 
                type="primary" 
                size="small"
                onClick={() => navigate('/ai/analysis')}
                style={{ 
                  backgroundColor: '#FF7A00',
                  borderColor: '#FF7A00'
                }}
              >
                View Results
              </Button>
            }
          >
            <div style={{ marginBottom: '16px' }}>
              <Text strong style={{ fontSize: '16px' }}>Smart Document Processing</Text>
              <Paragraph type="secondary" style={{ marginTop: '8px' }}>
                Upload contracts, invoices, project documents or financial reports.
                Our AI will automatically extract key information and suggest where to store it in the corresponding system modules.
              </Paragraph>
            </div>

            <Dragger {...uploadProps} style={{ marginBottom: '16px' }}>
              <p className="ant-upload-drag-icon">
                <CloudUploadOutlined style={{ fontSize: '48px', color: '#FF7A00' }} />
              </p>
              <p className="ant-upload-text" style={{ fontSize: '16px', fontWeight: 500 }}>
                Click or drag files here to upload (最大上传文件大小 20MB)
              </p>
              <p className="ant-upload-hint" style={{ color: '#8c8c8c' }}>
                Supports: PDF, Word, Excel, Text files (Max: 20MB)
              </p>
            </Dragger>

            <div style={{ 
              background: '#f6ffed', 
              border: '1px solid #b7eb8f', 
              borderRadius: '6px', 
              padding: '12px',
              marginTop: '16px'
            }}>
              <Text strong style={{ color: '#10B981' }}>✨ AI Features:</Text>
              <ul style={{ margin: '8px 0 0 16px', color: '#10B981' }}>
                <li>Auto-extract client information, project details, amounts</li>
                <li>Identify contract terms and payment schedules</li>
                <li>Automatically categorize expenses and income</li>
                <li>Smart field mapping with user confirmation</li>
              </ul>
            </div>
          </Card>
        </Col>

        {/* AI Chat assistant */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <MessageOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                <span>AI Chat Assistant</span>
              </div>
            }
            style={{ height: '100%' }}
            extra={
              <Button 
                type="primary" 
                size="small"
                onClick={() => navigate('/ai/chat')}
                style={{ 
                  backgroundColor: '#FF7A00',
                  borderColor: '#FF7A00'
                }}
              >
                Start Chat
              </Button>
            }
          >
            <div style={{ marginBottom: '16px' }}>
              <Text strong style={{ fontSize: '16px' }}>Intelligent System Support</Text>
              <Paragraph type="secondary" style={{ marginTop: '8px' }}>
                Get instant help with system operations, data analysis and business insights.
                Ask questions about projects, finance, clients or how to use specific features.
              </Paragraph>
            </div>

            <div style={{ 
              background: '#fff2e8', 
              borderRadius: '8px', 
              padding: '16px',
              marginBottom: '16px',
              border: '1px solid #ffb366'
            }}>
              <Text strong style={{ color: '#FF7A00' }}>💬 Example Questions:</Text>
              <div style={{ marginTop: '12px' }}>
                {[
                  "What's the total revenue for Q4?",
                  "How to create a new project in the system?",
                  "Show me overdue payments from clients",
                  "Which projects are performing best?",
                  "Help me understand VAT calculation"
                ].map((question, index) => (
                  <div 
                    key={index}
                    style={{ 
                      background: 'white', 
                      padding: '8px 12px', 
                      borderRadius: '16px', 
                      margin: '6px 0',
                      border: '1px solid #ffe7d3',
                      fontSize: '13px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                    onClick={() => navigate('/ai/chat', { state: { initialQuestion: question } })}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = '#FF7A00';
                      e.currentTarget.style.backgroundColor = '#fff7f0';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = '#ffe7d3';
                      e.currentTarget.style.backgroundColor = 'white';
                    }}
                  >
                    "🤔 {question}"
                  </div>
                ))}
              </div>
            </div>

            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button 
                type="primary" 
                icon={<MessageOutlined />}
                size="large"
                onClick={() => navigate('/ai/chat')}
                style={{ 
                  background: 'linear-gradient(135deg, #FF7A00 0%, #F59E0B 100%)',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '0 24px'
                }}
              >
                Start Conversation
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      <Divider style={{ margin: '32px 0' }} />

      {/* Usage statistics */}
      <Card title="📊 AI Usage Overview" size="small">
        <Row gutter={16}>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#FF7A00', margin: 0 }}>23</Title>
              <Text type="secondary">Documents Analyzed</Text>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#10B981', margin: 0 }}>156</Title>
              <Text type="secondary">Chat Messages</Text>
            </div>
          </Col>
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#F59E0B', margin: 0 }}>89%</Title>
              <Text type="secondary">Accuracy Rate</Text>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default AIAssistant; 