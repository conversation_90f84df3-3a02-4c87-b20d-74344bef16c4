/**
 * 加密服务
 * 提供数据加密和解密功能
 */

// 加密配置
export interface EncryptionConfig {
  algorithm: string;       // 加密算法
  secretKey: string;       // 密钥
  ivLength: number;        // 初始化向量长度
  saltLength: number;      // 盐长度
  iterations: number;      // PBKDF2迭代次数
  keyLength: number;       // 密钥长度
}

// 默认配置
const DEFAULT_CONFIG: EncryptionConfig = {
  algorithm: 'AES-GCM',
  secretKey: 'LTC_DEFAULT_SECRET_KEY',
  ivLength: 16,
  saltLength: 16,
  iterations: 10000,
  keyLength: 32
};

/**
 * 加密服务类
 */
export class EncryptionService {
  private config: EncryptionConfig;
  private encoder = new TextEncoder();
  private decoder = new TextDecoder();
  private cryptoKey: CryptoKey | null = null;

  /**
   * 构造函数
   * @param config 加密配置
   */
  constructor(config: Partial<EncryptionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };

    // 如果环境变量中有密钥，使用环境变量中的密钥
    if (process.env.REACT_APP_ENCRYPTION_KEY) {
      this.config.secretKey = process.env.REACT_APP_ENCRYPTION_KEY;
    }

    // 初始化加密密钥
    this.initCryptoKey();
  }

  /**
   * 初始化加密密钥
   */
  private async initCryptoKey(): Promise<void> {
    try {
      // 从密钥字符串生成密钥
      const keyMaterial = await window.crypto.subtle.importKey(
        'raw',
        this.encoder.encode(this.config.secretKey),
        { name: 'PBKDF2' },
        false,
        ['deriveKey']
      );

      // 生成盐
      const salt = window.crypto.getRandomValues(new Uint8Array(this.config.saltLength));

      // 派生密钥
      this.cryptoKey = await window.crypto.subtle.deriveKey(
        {
          name: 'PBKDF2',
          salt,
          iterations: this.config.iterations,
          hash: 'SHA-256'
        },
        keyMaterial,
        { name: this.config.algorithm, length: this.config.keyLength * 8 },
        false,
        ['encrypt', 'decrypt']
      );
    } catch (error) {
      console.error('初始化加密密钥失败:', error);
      throw new Error('初始化加密服务失败');
    }
  }

  /**
   * 生成随机初始化向量
   * @returns 初始化向量
   */
  private generateIV(): Uint8Array {
    return window.crypto.getRandomValues(new Uint8Array(this.config.ivLength));
  }

  /**
   * 加密数据
   * @param data 要加密的数据
   * @returns 加密后的数据（Base64编码）
   */
  public async encrypt(data: string): Promise<string> {
    try {
      // 确保密钥已初始化
      if (!this.cryptoKey) {
        await this.initCryptoKey();
      }

      // 生成初始化向量
      const iv = this.generateIV();

      // 加密数据
      const encryptedBuffer = await window.crypto.subtle.encrypt(
        {
          name: this.config.algorithm,
          iv
        },
        this.cryptoKey!,
        this.encoder.encode(data)
      );

      // 将IV和加密数据合并
      const result = new Uint8Array(iv.length + encryptedBuffer.byteLength);
      result.set(iv);
      result.set(new Uint8Array(encryptedBuffer), iv.length);

      // 转换为Base64
      return this.arrayBufferToBase64(result);
    } catch (error) {
      console.error('加密数据失败:', error);
      throw new Error('加密数据失败');
    }
  }

  /**
   * 解密数据
   * @param encryptedData 加密的数据（Base64编码）
   * @returns 解密后的数据
   */
  public async decrypt(encryptedData: string): Promise<string> {
    try {
      // 确保密钥已初始化
      if (!this.cryptoKey) {
        await this.initCryptoKey();
      }

      // 从Base64转换为ArrayBuffer
      const data = this.base64ToArrayBuffer(encryptedData);

      // 提取IV和加密数据
      const iv = data.slice(0, this.config.ivLength);
      const encryptedBuffer = data.slice(this.config.ivLength);

      // 解密数据
      const decryptedBuffer = await window.crypto.subtle.decrypt(
        {
          name: this.config.algorithm,
          iv
        },
        this.cryptoKey!,
        encryptedBuffer
      );

      // 转换为字符串
      return this.decoder.decode(decryptedBuffer);
    } catch (error) {
      console.error('解密数据失败:', error);
      throw new Error('解密数据失败');
    }
  }

  /**
   * 加密对象
   * @param obj 要加密的对象
   * @param sensitiveFields 敏感字段列表
   * @returns 加密后的对象
   */
  public async encryptObject<T extends Record<string, any>>(
    obj: T,
    sensitiveFields: string[]
  ): Promise<T> {
    const result = { ...obj };

    for (const field of sensitiveFields) {
      if (field in result && typeof result[field as keyof T] === 'string') {
        result[field as keyof T] = await this.encrypt(result[field as keyof T] as string) as any;
      }
    }

    return result;
  }

  /**
   * 解密对象
   * @param obj 加密的对象
   * @param sensitiveFields 敏感字段列表
   * @returns 解密后的对象
   */
  public async decryptObject<T extends Record<string, any>>(
    obj: T,
    sensitiveFields: string[]
  ): Promise<T> {
    const result = { ...obj };

    for (const field of sensitiveFields) {
      if (field in result && typeof result[field as keyof T] === 'string') {
        try {
          result[field as keyof T] = await this.decrypt(result[field as keyof T] as string) as any;
        } catch (error) {
          console.warn(`解密字段 ${field} 失败，保持原值`);
        }
      }
    }

    return result;
  }

  /**
   * 将ArrayBuffer转换为Base64
   * @param buffer ArrayBuffer
   * @returns Base64字符串
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * 将Base64转换为ArrayBuffer
   * @param base64 Base64字符串
   * @returns ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): Uint8Array {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * 生成哈希值
   * @param data 要哈希的数据
   * @returns 哈希值（十六进制）
   */
  public async hash(data: string): Promise<string> {
    try {
      const hashBuffer = await window.crypto.subtle.digest(
        'SHA-256',
        this.encoder.encode(data)
      );

      // 转换为十六进制
      return Array.from(new Uint8Array(hashBuffer))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
    } catch (error) {
      console.error('生成哈希值失败:', error);
      throw new Error('生成哈希值失败');
    }
  }

  /**
   * 生成随机密码
   * @param length 密码长度
   * @param includeUppercase 是否包含大写字母
   * @param includeLowercase 是否包含小写字母
   * @param includeNumbers 是否包含数字
   * @param includeSpecial 是否包含特殊字符
   * @returns 随机密码
   */
  public generateRandomPassword(
    length = 12,
    includeUppercase = true,
    includeLowercase = true,
    includeNumbers = true,
    includeSpecial = true
  ): string {
    let charset = '';
    if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (includeNumbers) charset += '0123456789';
    if (includeSpecial) charset += '!@#$%^&*()_+~`|}{[]:;?><,./-=';

    if (charset.length === 0) {
      throw new Error('至少需要选择一种字符类型');
    }

    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);

    let result = '';
    for (let i = 0; i < length; i++) {
      result += charset.charAt(randomValues[i] % charset.length);
    }

    return result;
  }
}

// 创建默认实例
const encryptionService = new EncryptionService();

export default encryptionService;
