# 客户名称智能关联功能测试指南

## 功能概述

在Lead to Cash项目管理页面的创建和编辑项目时，客户名称输入框现在支持智能关联功能。用户可以通过输入几个字符来搜索和选择已存在的客户，系统会自动填充相关的客户信息。

## 新增功能特性

### 1. 自动完成搜索
- **智能搜索**：输入客户名称的部分字符，系统会自动搜索匹配的现有客户
- **实时过滤**：支持按客户名称或公司名称进行模糊匹配
- **下拉建议**：显示匹配的客户列表，格式为"客户名称 (等级 - 国家)"

### 2. 自动信息填充
- **一键选择**：点击下拉列表中的客户后，自动填充相关信息
- **自动映射**：客户等级、国家、联系人信息自动填入对应字段
- **智能转换**：客户等级自动转换为项目等级格式

### 3. 数据同步
- **等级映射**：SVIP→S, VIP→V, BA→B, A→A, B→B, C→A
- **信息同步**：客户姓名、国家、联系人自动填入项目表单
- **成功反馈**：选择客户后显示"Selected existing client: [客户名称]"提示

## 测试步骤

### 测试场景1: 创建项目时使用客户智能关联

1. **进入创建页面**
   - 访问 http://localhost:3000/opportunity
   - 点击"New Opportunity"按钮

2. **测试智能搜索功能**
   - 在"Client Name"字段开始输入，如输入"Global"
   - 观察下拉列表是否显示匹配的客户建议
   - 建议格式应为："Global Tech Solutions (SVIP - United States)"

3. **测试自动填充功能**
   - 点击下拉列表中的一个客户
   - 验证以下字段是否自动填充：
     - Client Name: 选中的客户名称
     - Client Tier: 转换后的等级 (如SVIP→S)
     - Country/Region: 客户的国家
     - Client Contact: 客户的联系人

4. **验证用户反馈**
   - 确认看到成功提示消息："Selected existing client: [客户名称]"

### 测试场景2: 编辑项目时更换客户

1. **进入编辑页面**
   - 在项目列表中选择一个现有项目
   - 点击编辑按钮

2. **测试客户更换**
   - 清空当前的Client Name字段
   - 开始输入新的客户名称
   - 从下拉列表中选择不同的客户

3. **验证信息更新**
   - 确认相关字段都更新为新客户的信息
   - 保存项目并验证更改生效

### 测试场景3: 搜索和过滤功能

1. **测试模糊搜索**
   - 输入客户名称的部分字符
   - 输入公司名称的部分字符
   - 验证都能正确匹配和显示

2. **测试大小写不敏感**
   - 输入小写字母，验证能匹配大写的客户名称
   - 输入大写字母，验证能匹配小写的客户名称

3. **测试清空和重置**
   - 使用输入框右侧的清空按钮(×)
   - 验证字段被清空且没有其他影响

### 测试场景4: 新客户输入

1. **测试新客户名称**
   - 输入一个不存在的客户名称
   - 验证没有匹配建议出现
   - 继续填写其他必填信息并提交
   - 确认新客户自动创建功能仍然正常工作

## 界面特性验证

### ✅ 视觉效果检查
- [ ] 客户名称字段有明确的搜索提示
- [ ] 下拉列表样式美观，信息清晰
- [ ] 选择客户后有明确的成功反馈
- [ ] 输入框有清空按钮且功能正常

### ✅ 交互体验检查
- [ ] 输入响应快速，无明显延迟
- [ ] 下拉建议准确匹配输入内容
- [ ] 键盘导航正常工作(上下箭头、回车选择)
- [ ] 点击空白区域下拉列表正确关闭

### ✅ 数据一致性检查
- [ ] 选择的客户信息正确填入表单
- [ ] 等级映射转换正确
- [ ] 客户同步创建功能不受影响
- [ ] 表单验证仍然正常工作

## 技术细节

### 实现特性
- **AutoComplete组件**：使用Ant Design的AutoComplete组件
- **实时搜索**：onSearch事件处理客户过滤
- **选择处理**：onSelect事件自动填充相关字段
- **数据映射**：智能转换客户数据到项目格式

### 性能优化
- **数据缓存**：客户列表在组件加载时一次性获取
- **过滤优化**：本地过滤，减少API调用
- **防重复**：避免重复加载客户数据

## 故障排除

### 常见问题
1. **下拉列表不显示**：检查客户数据是否正确加载
2. **搜索无结果**：验证输入的客户名称是否存在
3. **自动填充失败**：检查客户数据结构和映射逻辑
4. **性能问题**：检查客户数据量和过滤逻辑

### 调试信息
- 查看浏览器开发者工具Console
- 搜索"Failed to load existing clients"错误信息
- 检查"Selected existing client"成功消息
- 验证客户数据结构是否正确

这个功能大大提升了用户体验，减少了重复输入工作，确保了数据一致性。 