import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Form,
  Input,
  Select,
  Switch,
  TimePicker,
  DatePicker,
  Checkbox,
  Button,
  Table,
  Space,
  Tag,
  Card,
  Divider,
  Row,
  Col,
  Typography,
  message,
  Popconfirm,
  Transfer,
  Alert
} from 'antd';
import {
  SettingOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  DownloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  CheckOutlined
} from '@ant-design/icons';
// import moment from 'moment'; // 暂时不使用moment

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface PermissionTemplate {
  id: string;
  name: string;
  description: string;
  type: 'role_based' | 'custom' | 'department' | 'temporary';
  permissions: AdvancedPermissions;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
}

interface AdvancedPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canInvite: boolean;
  canManageStages: boolean;
  canUploadDocuments: boolean;
  canViewFinancials: boolean;
  canComment: boolean;
  canMention: boolean;
  moduleAccess: string[];
  stageAccess?: string[];
  timeRestrictions?: {
    startDate?: string;
    endDate?: string;
    daysOfWeek?: number[];
    timeRanges?: { start: string; end: string }[];
  };
  ipRestrictions?: string[];
  downloadRestrictions?: {
    allowDownload: boolean;
    fileTypes?: string[];
    maxFileSize?: number;
  };
}

interface AdvancedPermissionManagerProps {
  projectId: string;
  visible: boolean;
  onClose: () => void;
  selectedCollaborators?: string[];
}

const AdvancedPermissionManager: React.FC<AdvancedPermissionManagerProps> = ({
  projectId,
  visible,
  onClose,
  selectedCollaborators = []
}) => {
  const [activeTab, setActiveTab] = useState('templates');
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [templateForm] = Form.useForm();
  const [permissionForm] = Form.useForm();

  // 可用模块列表
  const availableModules = [
    { value: 'basic_info', label: '基本信息' },
    { value: 'stages', label: '项目阶段' },
    { value: 'documents', label: '文档管理' },
    { value: 'finance', label: '财务信息' },
    { value: 'team', label: '团队管理' },
    { value: 'reports', label: '报告分析' }
  ];

  // 可用阶段列表
  const availableStages = [
    { key: 'basic_info', label: '基本信息' },
    { key: 'proposal', label: '提案' },
    { key: 'contract', label: '合同' },
    { key: 'execution', label: '执行' },
    { key: 'acceptance', label: '验收' },
    { key: 'finance', label: '财务' },
    { key: 'closeout', label: '结项' },
    { key: 'after_sales', label: '售后' }
  ];

  useEffect(() => {
    if (visible) {
      fetchTemplates();
    }
  }, [visible]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取权限模板
      // const response = await advancedPermissionService.getAllTemplates();
      // setTemplates(response);
      
      // 模拟数据
      const mockTemplates: PermissionTemplate[] = [
        {
          id: '1',
          name: '项目经理',
          description: '具有项目管理的全部权限',
          type: 'role_based',
          permissions: {
            canEdit: true,
            canDelete: false,
            canInvite: true,
            canManageStages: true,
            canUploadDocuments: true,
            canViewFinancials: true,
            canComment: true,
            canMention: true,
            moduleAccess: ['basic_info', 'stages', 'documents', 'team']
          },
          isActive: true,
          isDefault: true,
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: '外部顾问',
          description: '限时访问权限，只能查看和评论',
          type: 'temporary',
          permissions: {
            canEdit: false,
            canDelete: false,
            canInvite: false,
            canManageStages: false,
            canUploadDocuments: false,
            canViewFinancials: false,
            canComment: true,
            canMention: false,
            moduleAccess: ['basic_info'],
            timeRestrictions: {
              startDate: '2024-01-01',
              endDate: '2024-12-31',
              daysOfWeek: [1, 2, 3, 4, 5], // 工作日
              timeRanges: [{ start: '09:00', end: '18:00' }]
            }
          },
          isActive: true,
          isDefault: false,
          createdAt: '2024-01-02T00:00:00Z'
        }
      ];
      setTemplates(mockTemplates);
    } catch (error) {
      message.error('获取权限模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建权限模板
  const handleCreateTemplate = async (values: any) => {
    try {
      setLoading(true);
      // TODO: 调用API创建模板
      // await advancedPermissionService.createTemplate(values);
      message.success('权限模板创建成功');
      fetchTemplates();
      templateForm.resetFields();
    } catch (error) {
      message.error('创建权限模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 应用模板到选中的协作者
  const handleApplyTemplate = async (templateId: string) => {
    if (selectedCollaborators.length === 0) {
      message.warning('请先选择要应用权限的协作者');
      return;
    }

    try {
      setLoading(true);
      // TODO: 调用API批量应用模板
      // await advancedPermissionService.batchApplyTemplate(templateId, selectedCollaborators);
      message.success(`已将权限模板应用到 ${selectedCollaborators.length} 个协作者`);
      onClose();
    } catch (error) {
      message.error('应用权限模板失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染权限开关组
  const renderPermissionSwitches = (prefix: string = 'permissions') => (
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <Form.Item name={[prefix, 'canEdit']} valuePropName="checked">
          <Switch checkedChildren="编辑" unCheckedChildren="编辑" />
        </Form.Item>
        <Text>编辑项目内容</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canDelete']} valuePropName="checked">
          <Switch checkedChildren="删除" unCheckedChildren="删除" />
        </Form.Item>
        <Text>删除项目内容</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canInvite']} valuePropName="checked">
          <Switch checkedChildren="邀请" unCheckedChildren="邀请" />
        </Form.Item>
        <Text>邀请新成员</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canManageStages']} valuePropName="checked">
          <Switch checkedChildren="阶段管理" unCheckedChildren="阶段管理" />
        </Form.Item>
        <Text>管理项目阶段</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canUploadDocuments']} valuePropName="checked">
          <Switch checkedChildren="上传文档" unCheckedChildren="上传文档" />
        </Form.Item>
        <Text>上传项目文档</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canViewFinancials']} valuePropName="checked">
          <Switch checkedChildren="查看财务" unCheckedChildren="查看财务" />
        </Form.Item>
        <Text>查看财务信息</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canComment']} valuePropName="checked">
          <Switch checkedChildren="评论" unCheckedChildren="评论" />
        </Form.Item>
        <Text>添加评论</Text>
      </Col>
      <Col span={12}>
        <Form.Item name={[prefix, 'canMention']} valuePropName="checked">
          <Switch checkedChildren="提及" unCheckedChildren="提及" />
        </Form.Item>
        <Text>@提及其他用户</Text>
      </Col>
    </Row>
  );

  // 模板列表表格列定义
  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: PermissionTemplate) => (
        <div>
          <Text strong>{text}</Text>
          {record.isDefault && <Tag color="blue" style={{ marginLeft: 8 }}>默认</Tag>}
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          'role_based': { text: '角色模板', color: 'blue' },
          'custom': { text: '自定义', color: 'green' },
          'department': { text: '部门模板', color: 'orange' },
          'temporary': { text: '临时权限', color: 'purple' }
        };
        const config = typeMap[type as keyof typeof typeMap] || { text: type, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: PermissionTemplate) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<CheckOutlined />}
            onClick={() => handleApplyTemplate(record.id)}
            disabled={selectedCollaborators.length === 0}
          >
            应用
          </Button>
          <Button
            size="small"
            icon={<CopyOutlined />}
            onClick={() => {
              // TODO: 复制模板
            }}
          >
            复制
          </Button>
          <Popconfirm
            title="确定要删除此模板吗？"
            onConfirm={() => {
              // TODO: 删除模板
            }}
          >
            <Button
              size="small"
              icon={<DeleteOutlined />}
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          高级权限管理
          {selectedCollaborators.length > 0 && (
            <Tag color="blue">{selectedCollaborators.length} 个协作者</Tag>
          )}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
      destroyOnClose
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="权限模板" key="templates">
          <div style={{ marginBottom: 16 }}>
            <Alert
              message="权限模板管理"
              description="创建和管理权限模板，可批量应用到多个协作者。选择协作者后点击应用按钮即可。"
              type="info"
              showIcon
            />
          </div>

          <Row gutter={16}>
            <Col span={16}>
              <Table
                columns={templateColumns}
                dataSource={templates}
                rowKey="id"
                loading={loading}
                pagination={{ pageSize: 5 }}
                size="small"
              />
            </Col>
            
            <Col span={8}>
              <Card title="创建权限模板" size="small">
                <Form
                  form={templateForm}
                  layout="vertical"
                  onFinish={handleCreateTemplate}
                >
                  <Form.Item
                    name="name"
                    label="模板名称"
                    rules={[{ required: true, message: '请输入模板名称' }]}
                  >
                    <Input placeholder="输入模板名称" />
                  </Form.Item>

                  <Form.Item name="description" label="描述">
                    <TextArea rows={2} placeholder="描述权限模板的用途" />
                  </Form.Item>

                  <Form.Item
                    name="type"
                    label="模板类型"
                    rules={[{ required: true, message: '请选择模板类型' }]}
                  >
                    <Select placeholder="选择类型">
                      <Option value="role_based">角色模板</Option>
                      <Option value="custom">自定义</Option>
                      <Option value="department">部门模板</Option>
                      <Option value="temporary">临时权限</Option>
                    </Select>
                  </Form.Item>

                  <Divider />

                  <Title level={5}>基本权限</Title>
                  {renderPermissionSwitches()}

                  <Divider />

                  <Form.Item name={['permissions', 'moduleAccess']} label="模块访问权限">
                    <Checkbox.Group options={availableModules} />
                  </Form.Item>

                  <Button type="primary" htmlType="submit" block loading={loading}>
                    <PlusOutlined />
                    创建模板
                  </Button>
                </Form>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="时间限制" key="time-restrictions">
          <Card title="设置时间访问限制">
            <Form
              form={permissionForm}
              layout="vertical"
              onFinish={(values) => {
                console.log('时间限制设置:', values);
                message.success('时间限制设置成功');
              }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="dateRange" label="访问日期范围">
                    <RangePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="daysOfWeek" label="允许访问的星期">
                    <Checkbox.Group>
                      <Row>
                        {['周日', '周一', '周二', '周三', '周四', '周五', '周六'].map((day, index) => (
                          <Col span={8} key={index}>
                            <Checkbox value={index}>{day}</Checkbox>
                          </Col>
                        ))}
                      </Row>
                    </Checkbox.Group>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="每日访问时间段">
                <Form.List name="timeRanges">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name }) => (
                        <Row gutter={8} key={key} align="middle">
                          <Col span={10}>
                            <Form.Item name={[name, 'start']} noStyle>
                              <TimePicker placeholder="开始时间" format="HH:mm" />
                            </Form.Item>
                          </Col>
                          <Col span={2} style={{ textAlign: 'center' }}>
                            <Text>至</Text>
                          </Col>
                          <Col span={10}>
                            <Form.Item name={[name, 'end']} noStyle>
                              <TimePicker placeholder="结束时间" format="HH:mm" />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Button type="text" danger onClick={() => remove(name)}>
                              <DeleteOutlined />
                            </Button>
                          </Col>
                        </Row>
                      ))}
                      <Button type="dashed" onClick={() => add()} block>
                        <PlusOutlined />
                        添加时间段
                      </Button>
                    </>
                  )}
                </Form.List>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab="IP限制" key="ip-restrictions">
          <Card title="设置IP访问限制">
            <Form layout="vertical">
              <Form.Item
                name="ipList"
                label="允许的IP地址"
                extra="每行一个IP地址，支持CIDR格式，如：***********/24"
              >
                <TextArea
                  rows={6}
                  placeholder="*************&#10;***********/24&#10;10.0.0.0/8"
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Button type="primary" icon={<GlobalOutlined />}>
                    应用IP限制
                  </Button>
                </Col>
                <Col span={12}>
                  <Button>
                    测试当前IP
                  </Button>
                </Col>
              </Row>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab="下载限制" key="download-restrictions">
          <Card title="设置文档下载限制">
            <Form layout="vertical">
              <Form.Item name="allowDownload" valuePropName="checked">
                <Switch checkedChildren="允许下载" unCheckedChildren="禁止下载" />
                <Text style={{ marginLeft: 8 }}>是否允许下载项目文档</Text>
              </Form.Item>

              <Form.Item name="fileTypes" label="允许下载的文件类型">
                <Checkbox.Group>
                  <Row>
                    <Col span={6}><Checkbox value="pdf">PDF</Checkbox></Col>
                    <Col span={6}><Checkbox value="doc">Word</Checkbox></Col>
                    <Col span={6}><Checkbox value="xlsx">Excel</Checkbox></Col>
                    <Col span={6}><Checkbox value="ppt">PowerPoint</Checkbox></Col>
                    <Col span={6}><Checkbox value="jpg">图片</Checkbox></Col>
                    <Col span={6}><Checkbox value="zip">压缩包</Checkbox></Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Form.Item name="maxFileSize" label="最大文件大小限制 (MB)">
                <Select placeholder="选择文件大小限制">
                  <Option value={1}>1MB</Option>
                  <Option value={5}>5MB</Option>
                  <Option value={10}>10MB</Option>
                  <Option value={50}>50MB</Option>
                  <Option value={100}>100MB</Option>
                  <Option value={-1}>无限制</Option>
                </Select>
              </Form.Item>

              <Button type="primary" icon={<DownloadOutlined />}>
                应用下载限制
              </Button>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default AdvancedPermissionManager; 