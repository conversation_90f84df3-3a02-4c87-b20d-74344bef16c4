import React, { useState } from 'react';
import './CloseoutStage.css';
import {
  Card,
  Row,
  Col,
  Progress,
  Button,
  Table,
  Tag,
  Tabs,
  Form,
  Input,
  Select,
  message,
  Checkbox,
  Modal,
  Tooltip,
  Steps
} from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  EditOutlined,
  TrophyOutlined,
  PlusOutlined,
  ProjectOutlined,
  BulbOutlined,
  CheckOutlined,
  DeleteOutlined,
  RightOutlined,
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TabPane } = Tabs;
const { Option } = Select;
const { Step } = Steps;

interface CloseoutStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

// 检查清单项接口
interface ChecklistItem {
  id: string;
  category: string;
  description: string;
  completed: boolean;
  completedBy?: string;
  completedDate?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// 项目指标接口
interface ProjectMetric {
  id?: string;
  metric: string;
  planned: string | number;
  actual: string | number;
  variance: string;
  status: 'on_target' | 'minor_variance' | 'major_variance';
}

// 经验教训接口
interface LessonLearned {
  id: string;
  category: string;
  title: string;
  description: string;
  type: 'success' | 'challenge' | 'improvement';
  impact: 'low' | 'medium' | 'high';
}

// Key Achievement接口
interface KeyAchievement {
  id?: string;
  achievement: string;
}

const CloseoutStage: React.FC<CloseoutStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('summary');
  const [saving, setSaving] = useState(false);
  const [addChecklistModalVisible, setAddChecklistModalVisible] = useState(false);
  const [addChecklistForm] = Form.useForm();
  const [editMetricModalVisible, setEditMetricModalVisible] = useState(false);
  const [editingMetric, setEditingMetric] = useState<ProjectMetric | null>(null);
  const [editMetricForm] = Form.useForm();
  
  // Lessons Learned状态管理
  const [addLessonModalVisible, setAddLessonModalVisible] = useState(false);
  const [editLessonModalVisible, setEditLessonModalVisible] = useState(false);
  const [editingLesson, setEditingLesson] = useState<LessonLearned | null>(null);
  const [addLessonForm] = Form.useForm();
  const [editLessonForm] = Form.useForm();
  
  // Closeout Progress流转状态管理
  const [currentProgressStep, setCurrentProgressStep] = useState(2); // 0: Deliverables, 1: Financial, 2: Documentation, 3: Final Review


  // 状态管理 - 新项目从空白开始
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>([]);

  const [projectMetrics, setProjectMetrics] = useState<ProjectMetric[]>([]);

  const [lessonsLearned, setLessonsLearned] = useState<LessonLearned[]>([]);

  const [keyAchievements, setKeyAchievements] = useState<KeyAchievement[]>([]);

  // 计算统计信息
  const getCloseoutStats = () => {
    const total = checklistItems.length;
    const completed = checklistItems.filter(item => item.completed).length;
    const pending = total - completed;
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    const criticalPending = checklistItems.filter(item => !item.completed && item.priority === 'critical').length;
    const onTargetMetrics = projectMetrics.filter(m => m.status === 'on_target').length;
    
    return { total, completed, pending, completionRate, criticalPending, onTargetMetrics };
  };

  const stats = getCloseoutStats();

  // Progress步骤定义
  const progressSteps = [
    {
      title: 'Deliverables Completed',
      description: 'All project deliverables completed and approved by client',
      requirements: [], // 没有前置要求
      key: 'deliverables'
    },
    {
      title: 'Financial Closure',
      description: 'Final invoicing and payment processing completed',
      requirements: ['deliverables'], // 需要完成交付物
      key: 'financial'
    },
    {
      title: 'Documentation Archive',
      description: 'Archiving project documents and knowledge base',
      requirements: ['financial'], // 需要完成财务关闭
      key: 'documentation'
    },
    {
      title: 'Final Review',
      description: 'Final project review and lessons learned session',
      requirements: ['documentation'], // 需要完成文档归档
      key: 'final_review'
    }
  ];

  // 检查步骤是否可以推进
  const canProceedToStep = (stepIndex: number) => {
    switch (stepIndex) {
      case 1: // Financial Closure
        return true; // Deliverables 总是可以完成
      case 2: // Documentation Archive
        return stats.completed >= 3; // 至少需要完成3个检查清单项目
      case 3: // Final Review
        return stats.completed >= 6 && stats.criticalPending === 0; // 需要完成大部分项目且无关键待办
      default:
        return true;
    }
  };

  // 获取步骤状态
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentProgressStep) return 'finish';
    if (stepIndex === currentProgressStep) return 'process';
    return 'wait';
  };

  // 跳转到指定步骤
  const handleProgressJump = (stepIndex: number) => {
    if (stepIndex <= currentProgressStep) {
      // 可以跳转到当前步骤或之前的步骤
      setCurrentProgressStep(stepIndex);
      message.info(`Switched to: ${progressSteps[stepIndex].title}`);
    } else if (canProceedToStep(stepIndex)) {
      // 如果满足条件，可以跳转到后续步骤
      setCurrentProgressStep(stepIndex);
      message.success(`Advanced to: ${progressSteps[stepIndex].title}`);
    } else {
      // 不满足条件时显示具体要求
      let errorMessage = '';
      switch (stepIndex) {
        case 2:
          errorMessage = 'Need to complete at least 3 checklist items to enter documentation archive stage';
          break;
        case 3:
          errorMessage = 'Need to complete most checklist items and resolve all critical pending items for final review';
          break;
        default:
          errorMessage = 'Requirements not met for this step';
      }
      message.warning(errorMessage);
    }
  };

  // 切换检查清单项状态
  const toggleChecklistItem = (id: string) => {
    setChecklistItems(items =>
      items.map(item =>
        item.id === id
          ? {
              ...item,
              completed: !item.completed,
              completedBy: !item.completed ? 'Current User' : undefined,
              completedDate: !item.completed ? new Date().toISOString().split('T')[0] : undefined
            }
          : item
      )
    );
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'on_target': { color: 'success', text: 'On Target' },
      'minor_variance': { color: 'warning', text: 'Minor Variance' },
      'major_variance': { color: 'error', text: 'Major Variance' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['on_target'];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染经验教训类型标签
  const renderLessonTypeTag = (type: string) => {
    const typeConfig = {
      'success': { color: 'success', text: 'Success', icon: <TrophyOutlined /> },
      'challenge': { color: 'warning', text: 'Challenge', icon: <ClockCircleOutlined /> },
      'improvement': { color: 'processing', text: 'Improvement', icon: <BulbOutlined /> }
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig['success'];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 检查清单表格列定义
  const checklistColumns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => {
        const priorityConfig = {
          'critical': { color: 'red', text: 'Critical' },
          'high': { color: 'orange', text: 'High' },
          'medium': { color: 'blue', text: 'Medium' },
          'low': { color: 'green', text: 'Low' }
        };
        const config = priorityConfig[priority as keyof typeof priorityConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'Status',
      dataIndex: 'completed',
      key: 'completed',
      width: 100,
      render: (completed: boolean) => (
        <Tag color={completed ? 'success' : 'default'} icon={completed ? <CheckCircleOutlined /> : <ClockCircleOutlined />}>
          {completed ? 'Completed' : 'Pending'}
        </Tag>
      )
    },
    {
      title: 'Completed By',
      dataIndex: 'completedBy',
      key: 'completedBy',
      width: 120,
      render: (text: string) => text || '-'
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_: any, record: ChecklistItem) => (
        <Checkbox
          checked={record.completed}
          onChange={() => toggleChecklistItem(record.id)}
        />
      )
    }
  ];

  // 项目指标表格列定义
  const metricsColumns = [
    {
      title: 'Metric',
      dataIndex: 'metric',
      key: 'metric',
      render: (text: string, record: ProjectMetric) => (
        <Input
          size="small"
          value={record.metric}
          onChange={e => handleMetricInputChange(record, 'metric', e.target.value)}
          style={{ minWidth: 100, fontWeight: 500 }}
        />
      )
    },
    {
      title: 'Planned',
      dataIndex: 'planned',
      key: 'planned',
      render: (text: string, record: ProjectMetric) => (
        <Input
          size="small"
          value={record.planned}
          onChange={e => handleMetricInputChange(record, 'planned', e.target.value)}
          style={{ minWidth: 80 }}
        />
      )
    },
    {
      title: 'Actual',
      dataIndex: 'actual',
      key: 'actual',
      render: (text: string, record: ProjectMetric) => (
        <Input
          size="small"
          value={record.actual}
          onChange={e => handleMetricInputChange(record, 'actual', e.target.value)}
          style={{ minWidth: 80 }}
        />
      )
    },
    {
      title: 'Variance',
      dataIndex: 'variance',
      key: 'variance',
      render: (variance: string, record: ProjectMetric) => (
        <span style={{ 
          color: record.status === 'on_target' ? '#52c41a' : 
                 record.status === 'minor_variance' ? '#fa8c16' : '#ff4d4f'
        }}>
          {variance}
        </span>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => renderStatusTag(status)
    },
    {
      title: '',
      key: 'action',
      width: 60,
      align: "center" as const,
      render: (_: any, record: ProjectMetric) => (
        <Tooltip title="删除">
          <Button type="text" danger size="small" icon={<DeleteOutlined style={{ fontSize: 18 }} />} onClick={() => handleDeleteMetric(record)} />
        </Tooltip>
      )
    }
  ];

  // Key Achievements表格列定义
  const keyAchievementsColumns = [
    {
      title: 'Achievement',
      dataIndex: 'achievement',
      key: 'achievement',
      render: (text: string, record: KeyAchievement) => (
        <Input
          size="small"
          value={record.achievement}
          onChange={e => handleAchievementInputChange(record, e.target.value)}
          style={{ minWidth: 200 }}
        />
      )
    },
    {
      title: '',
      key: 'action',
      width: 60,
      align: "center" as const,
      render: (_: any, record: KeyAchievement) => (
        <Tooltip title="删除">
          <Button type="text" danger size="small" icon={<DeleteOutlined style={{ fontSize: 18 }} />} onClick={() => handleDeleteAchievement(record)} />
        </Tooltip>
      )
    }
  ];

  // 保存处理
  const handleSave = async (values: any) => {
    setSaving(true);
    try {
      const closeoutData = {
        ...values,
        checklistItems,
        projectMetrics,
        lessonsLearned,
        keyAchievements,
        stats,
        type: 'closeout',
        status: stats.completionRate === 100 ? 'completed' : 'in_progress',
        updatedAt: new Date().toISOString()
      };

      if (onSave) {
        onSave(closeoutData);
      }

      message.success('项目收尾数据保存成功');
    } catch (error) {
      console.error('Save error:', error);
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 进入下一阶段
  const handleProceed = async () => {
    if (stats.completionRate < 80) {
      message.error('请完成至少80%的收尾检查项后再进入下一阶段');
      return;
    }

    try {
      await handleSave(form.getFieldsValue());
      
      setTimeout(() => {
        message.success('项目收尾阶段完成！正在进入售后服务阶段...');
        if (onProceed) {
          onProceed('after_sales');
        }
      }, 1000);
    } catch (error) {
      message.error('保存失败，无法进入下一阶段');
    }
  };

  const handleAddChecklistItem = () => {
    addChecklistForm.resetFields();
    setAddChecklistModalVisible(true);
  };

  const handleSaveChecklistItem = async () => {
    try {
      const values = await addChecklistForm.validateFields();
      setChecklistItems(items => [
        ...items,
        {
          id: Date.now().toString(),
          category: values.category,
          description: values.description,
          priority: values.priority,
          completed: false
        }
      ]);
      setAddChecklistModalVisible(false);
      addChecklistForm.resetFields();
    } catch {}
  };



  const handleSaveMetric = async () => {
    try {
      const values = await editMetricForm.validateFields();
      // 自动计算Variance和Status
      let variance = '';
      let status: 'on_target' | 'minor_variance' | 'major_variance' = 'on_target';
      if (editingMetric?.metric === 'Budget') {
        const planned = parseFloat((values.planned + '').replace(/[^\d.]/g, ''));
        const actual = parseFloat((values.actual + '').replace(/[^\d.]/g, ''));
        if (!isNaN(planned) && !isNaN(actual)) {
          const diff = ((actual - planned) / planned) * 100;
          variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
          if (Math.abs(diff) <= 2) status = 'on_target';
          else if (Math.abs(diff) <= 8) status = 'minor_variance';
          else status = 'major_variance';
        }
      } else if (editingMetric?.metric === 'Timeline') {
        const planned = parseFloat((values.planned + '').replace(/[^\d.]/g, ''));
        const actual = parseFloat((values.actual + '').replace(/[^\d.]/g, ''));
        if (!isNaN(planned) && !isNaN(actual)) {
          const diff = ((actual - planned) / planned) * 100;
          variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
          if (Math.abs(diff) <= 5) status = 'on_target';
          else if (Math.abs(diff) <= 10) status = 'minor_variance';
          else status = 'major_variance';
        }
      } else if (editingMetric?.metric === 'Scope') {
        const planned = parseFloat((values.planned + '').replace(/[^\d.]/g, ''));
        const actual = parseFloat((values.actual + '').replace(/[^\d.]/g, ''));
        if (!isNaN(planned) && !isNaN(actual)) {
          const diff = ((actual - planned) / planned) * 100;
          variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
          if (Math.abs(diff) <= 2) status = 'on_target';
          else if (Math.abs(diff) <= 5) status = 'minor_variance';
          else status = 'major_variance';
        }
      } else if (editingMetric?.metric === 'Quality Score') {
        const planned = parseFloat(values.planned);
        const actual = parseFloat(values.actual);
        if (!isNaN(planned) && !isNaN(actual)) {
          const diff = ((actual - planned) / planned) * 100;
          variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
          if (diff >= 0) status = 'on_target';
          else if (diff > -5) status = 'minor_variance';
          else status = 'major_variance';
        }
      } else if (editingMetric?.metric === 'Client Satisfaction') {
        // 评分格式如4.5/5
        const planned = parseFloat((values.planned + '').split('/')[0]);
        const actual = parseFloat((values.actual + '').split('/')[0]);
        if (!isNaN(planned) && !isNaN(actual)) {
          const diff = ((actual - planned) / planned) * 100;
          variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
          if (diff >= 0) status = 'on_target';
          else if (diff > -5) status = 'minor_variance';
          else status = 'major_variance';
        }
      }
      setProjectMetrics(metrics => metrics.map(m => m.metric === editingMetric?.metric ? { ...m, ...values, variance, status } : m));
      setEditMetricModalVisible(false);
      setEditingMetric(null);
    } catch {}
  };

  const handleMetricInputChange = (record: ProjectMetric, field: 'planned' | 'actual' | 'metric', value: string) => {
    let planned = field === 'planned' ? value : record.planned;
    let actual = field === 'actual' ? value : record.actual;
    let metric = field === 'metric' ? value : record.metric;
    let variance = record.variance;
    let status = record.status;
    if (metric === 'Budget') {
      const p = parseFloat((planned + '').replace(/[^\d.]/g, ''));
      const a = parseFloat((actual + '').replace(/[^\d.]/g, ''));
      if (!isNaN(p) && !isNaN(a)) {
        const diff = ((a - p) / p) * 100;
        variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
        if (Math.abs(diff) <= 2) status = 'on_target';
        else if (Math.abs(diff) <= 8) status = 'minor_variance';
        else status = 'major_variance';
      }
    } else if (metric === 'Timeline') {
      const p = parseFloat((planned + '').replace(/[^\d.]/g, ''));
      const a = parseFloat((actual + '').replace(/[^\d.]/g, ''));
      if (!isNaN(p) && !isNaN(a)) {
        const diff = ((a - p) / p) * 100;
        variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
        if (Math.abs(diff) <= 5) status = 'on_target';
        else if (Math.abs(diff) <= 10) status = 'minor_variance';
        else status = 'major_variance';
      }
    } else if (metric === 'Scope') {
      const p = parseFloat((planned + '').replace(/[^\d.]/g, ''));
      const a = parseFloat((actual + '').replace(/[^\d.]/g, ''));
      if (!isNaN(p) && !isNaN(a)) {
        const diff = ((a - p) / p) * 100;
        variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
        if (Math.abs(diff) <= 2) status = 'on_target';
        else if (Math.abs(diff) <= 5) status = 'minor_variance';
        else status = 'major_variance';
      }
    } else if (metric === 'Quality Score') {
      const p = parseFloat(planned + '');
      const a = parseFloat(actual + '');
      if (!isNaN(p) && !isNaN(a)) {
        const diff = ((a - p) / p) * 100;
        variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
        if (diff >= 0) status = 'on_target';
        else if (diff > -5) status = 'minor_variance';
        else status = 'major_variance';
      }
    } else if (metric === 'Client Satisfaction') {
      const p = parseFloat((planned + '').split('/')[0]);
      const a = parseFloat((actual + '').split('/')[0]);
      if (!isNaN(p) && !isNaN(a)) {
        const diff = ((a - p) / p) * 100;
        variance = (diff > 0 ? '+' : '') + diff.toFixed(1) + '%';
        if (diff >= 0) status = 'on_target';
        else if (diff > -5) status = 'minor_variance';
        else status = 'major_variance';
      }
    }
    setProjectMetrics(metrics => metrics.map(m => m === record ? { ...m, [field]: value, metric, variance, status } : m));
  };

  const handleDeleteMetric = (record: ProjectMetric) => {
    Modal.confirm({
      title: 'Delete Metric',
      content: 'Are you sure you want to delete this metric?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: () => {
        setProjectMetrics(metrics => metrics.filter(m => m !== record));
      }
    });
  };

  const handleAddMetric = () => {
    // 直接在表格中添加一行新的空记录
    const newMetric: ProjectMetric = {
      id: Date.now().toString(),
      metric: '',
      planned: '',
      actual: '',
      variance: '',
      status: 'on_target'
    };
    setProjectMetrics(metrics => [...metrics, newMetric]);
  };

  const handleDeleteAchievement = (record: KeyAchievement) => {
    Modal.confirm({
      title: 'Delete Achievement',
      content: 'Are you sure you want to delete this achievement?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: () => {
        setKeyAchievements(achievements => achievements.filter(a => a !== record));
      }
    });
  };

  const handleAchievementInputChange = (record: KeyAchievement, value: string) => {
    setKeyAchievements(achievements =>
      achievements.map(a =>
        a.id === record.id ? { ...a, achievement: value } : a
      )
    );
  };

  const handleAddAchievement = () => {
    // 直接在表格中添加一行新的空记录
    const newAchievement: KeyAchievement = {
      id: Date.now().toString(),
      achievement: ''
    };
    setKeyAchievements(achievements => [...achievements, newAchievement]);
  };

  // Lessons Learned处理函数
  const handleAddLesson = () => {
    addLessonForm.resetFields();
    setAddLessonModalVisible(true);
  };

  const handleSaveLesson = async () => {
    try {
      const values = await addLessonForm.validateFields();
      const newLesson: LessonLearned = {
        id: Date.now().toString(),
        category: values.category,
        title: values.title,
        description: values.description,
        type: values.type,
        impact: values.impact
      };
      setLessonsLearned(lessons => [...lessons, newLesson]);
      setAddLessonModalVisible(false);
      addLessonForm.resetFields();
      message.success('Lesson added successfully');
    } catch (error) {
      console.error('Add lesson error:', error);
    }
  };

  const handleEditLesson = (lesson: LessonLearned) => {
    setEditingLesson(lesson);
    editLessonForm.setFieldsValue(lesson);
    setEditLessonModalVisible(true);
  };

  const handleUpdateLesson = async () => {
    try {
      const values = await editLessonForm.validateFields();
      setLessonsLearned(lessons =>
        lessons.map(lesson =>
          lesson.id === editingLesson?.id
            ? { ...lesson, ...values }
            : lesson
        )
      );
      setEditLessonModalVisible(false);
      setEditingLesson(null);
      message.success('Lesson updated successfully');
    } catch (error) {
      console.error('Update lesson error:', error);
    }
  };

  const handleDeleteLesson = (lesson: LessonLearned) => {
    Modal.confirm({
      title: 'Delete Lesson',
      content: 'Are you sure you want to delete this lesson learned?',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: () => {
        setLessonsLearned(lessons => lessons.filter(l => l.id !== lesson.id));
        message.success('Lesson deleted successfully');
      }
    });
  };

  return (
    <div className="ltc-stage-content">
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 className="ltc-stage-title">
          Project Closeout
        </h1>
      </div>

      {/* 状态卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Completed Items</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{stats.completed}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#e6f7ff',
            border: '1px solid #91d5ff',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Pending Items</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ClockCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>{stats.pending}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#fff2e8',
            border: '1px solid #ffbb96',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>Critical Pending</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ClockCircleOutlined style={{ fontSize: '24px', color: '#ff4d4f' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>{stats.criticalPending}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={4}>
          <Card className="ltc-stage-card" style={{ 
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '12px'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>On-Target Metrics</div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <TrophyOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{stats.onTargetMetrics}</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={8} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <div className="ltc-progress-circle" style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              percent={stats.completionRate}
              size={80}
              strokeColor={stats.completionRate === 100 ? "#52c41a" : "#1890ff"}
            />
            <div className="ltc-progress-label" style={{ marginTop: '8px', fontSize: '14px', fontWeight: 500 }}>
              Completion
            </div>
          </div>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card className="ltc-card-content">
        <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
          {/* 检查清单 */}
          <TabPane 
            tab={
              <span>
                <CheckOutlined />
                Closeout Checklist
              </span>
            } 
            key="checklist"
          >
            <Form form={form} layout="vertical" onFinish={handleSave}>
              <Row gutter={[24, 16]}>
                <Col span={24}>
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <h3>Project Closeout Checklist</h3>
                    <div>
                      <span style={{ marginRight: '16px', fontSize: '12px', color: '#666' }}>
                        Progress: {stats.completed}/{stats.total} ({stats.completionRate}%)
                      </span>
                      <Button type="primary" icon={<PlusOutlined />} onClick={handleAddChecklistItem}>
                        Add Checklist Item
                      </Button>
                    </div>
                  </div>
                  <Table
                    columns={checklistColumns}
                    dataSource={checklistItems}
                    pagination={false}
                  />
                </Col>
              </Row>
            </Form>
          </TabPane>

          {/* 项目总结 */}
          <TabPane 
            tab={
              <span>
                <ProjectOutlined />
                Project Summary
              </span>
            } 
            key="summary"
          >
            {/* 第一排：Project Performance Summary */}
            <Row style={{ marginBottom: '20px' }}>
              <Col span={24}>
                <Card title="Project Performance Summary" className="ltc-table-title">
                  <Table
                    columns={metricsColumns}
                    dataSource={projectMetrics}
                    pagination={false}
                    size="small"
                    rowKey="id"
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button type="primary" icon={<PlusOutlined />} onClick={handleAddMetric} />
                  </div>
                </Card>
              </Col>
            </Row>

            {/* 第二排：Closeout Timeline 和 Key Achievements */}
            <Row gutter={16}>
              <Col span={12}>
                <Card 
                  title="Closeout Progress" 
                  size="small"
                >
                  <Steps 
                    direction="vertical" 
                    size="small" 
                    current={currentProgressStep}
                    onChange={handleProgressJump}
                    style={{ cursor: 'pointer' }}
                  >
                    {progressSteps.map((step, index) => (
                      <Step 
                        key={step.key}
                        title={step.title} 
                        status={getStepStatus(index)}
                        description={
                          <div>
                            <div>{step.description}</div>
                            {index === currentProgressStep && (
                              <div style={{ 
                                marginTop: '4px', 
                                fontSize: '10px', 
                                color: '#1890ff', 
                                fontWeight: 500 
                              }}>
                                Current Stage
                              </div>
                            )}
                            {index > currentProgressStep && !canProceedToStep(index) && (
                              <div style={{ 
                                marginTop: '4px', 
                                fontSize: '10px', 
                                color: '#ff4d4f' 
                              }}>
                                Requirements Not Met
                              </div>
                            )}
                          </div>
                        }
                        style={{
                          cursor: (index <= currentProgressStep || canProceedToStep(index)) ? 'pointer' : 'not-allowed'
                        }}
                      />
                    ))}
                  </Steps>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="Key Achievements" size="small">
                  <Table
                    columns={keyAchievementsColumns}
                    dataSource={keyAchievements}
                    pagination={false}
                    size="small"
                    rowKey="id"
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAchievement} />
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* 经验教训 */}
          <TabPane 
            tab={
              <span>
                <BulbOutlined />
                Lessons Learned
              </span>
            } 
            key="lessons"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  <h3>Lessons Learned</h3>
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddLesson}>
                    Add Lesson
                  </Button>
                </div>

                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  {lessonsLearned.map(lesson => (
                    <Card 
                      key={lesson.id}
                      size="small" 
                      style={{ 
                        borderRadius: '8px',
                        border: '1px solid #f0f0f0',
                        boxShadow: '0 1px 2px rgba(0,0,0,0.03)'
                      }}
                    >
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <div style={{ flex: 1 }}>
                          {/* 标题行 */}
                          <div style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: '12px', 
                            marginBottom: '8px' 
                          }}>
                            <h4 style={{ 
                              margin: 0, 
                              fontSize: '14px', 
                              fontWeight: 500,
                              color: '#262626',
                              flex: 1
                            }}>
                              {lesson.title}
                            </h4>
                            {renderLessonTypeTag(lesson.type)}
                          </div>
                          
                          {/* 分类和影响级别 */}
                          <div style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: '16px', 
                            marginBottom: '8px' 
                          }}>
                            <span style={{ 
                              fontSize: '12px', 
                              color: '#8c8c8c',
                              backgroundColor: '#fafafa',
                              padding: '2px 8px',
                              borderRadius: '4px',
                              border: '1px solid #f0f0f0'
                            }}>
                              {lesson.category}
                            </span>
                            <Tag 
                              color={lesson.impact === 'high' ? '#ff4d4f' : lesson.impact === 'medium' ? '#fa8c16' : '#52c41a'}
                              style={{ 
                                fontSize: '11px',
                                fontWeight: 400,
                                border: 'none'
                              }}
                            >
                              {lesson.impact.toUpperCase()} IMPACT
                            </Tag>
                          </div>
                          
                          {/* 描述 */}
                          <div style={{ 
                            fontSize: '13px',
                            color: '#595959',
                            lineHeight: '1.5',
                            marginTop: '4px'
                          }}>
                            {lesson.description}
                          </div>
                        </div>
                        
                        {/* 操作按钮 */}
                        <div style={{ marginLeft: '16px', display: 'flex', gap: '4px' }}>
                          <Button 
                            type="text" 
                            icon={<EditOutlined />} 
                            size="small"
                            onClick={() => handleEditLesson(lesson)}
                            style={{ 
                              color: '#8c8c8c',
                              border: 'none'
                            }}
                          />
                          <Button 
                            type="text" 
                            icon={<DeleteOutlined />} 
                            size="small"
                            onClick={() => handleDeleteLesson(lesson)}
                            style={{ 
                              color: '#ff4d4f',
                              border: 'none'
                            }}
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      <Modal
        title="Add Checklist Item"
        open={addChecklistModalVisible}
        onCancel={() => setAddChecklistModalVisible(false)}
        onOk={handleSaveChecklistItem}
        okText="Add"
        cancelText="Cancel"
        width={480}
      >
        <Form form={addChecklistForm} layout="vertical">
          <Form.Item name="category" label="Category" rules={[{ required: true, message: 'Please enter category' }]}> <Input /> </Form.Item>
          <Form.Item name="description" label="Description" rules={[{ required: true, message: 'Please enter description' }]}> <Input /> </Form.Item>
          <Form.Item name="priority" label="Priority" rules={[{ required: true, message: 'Please select priority' }]}> <Select>
            <Option value="critical">Critical</Option>
            <Option value="high">High</Option>
            <Option value="medium">Medium</Option>
            <Option value="low">Low</Option>
          </Select> </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={`编辑：${editingMetric?.metric}`}
        open={editMetricModalVisible}
        onCancel={() => setEditMetricModalVisible(false)}
        onOk={handleSaveMetric}
        okText="保存"
        cancelText="取消"
        width={400}
      >
        <Form form={editMetricForm} layout="vertical">
          <Form.Item name="planned" label="Planned" rules={[{ required: true, message: '请输入Planned' }]}> <Input /> </Form.Item>
          <Form.Item name="actual" label="Actual" rules={[{ required: true, message: '请输入Actual' }]}> <Input /> </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Add Lesson"
        open={addLessonModalVisible}
        onCancel={() => setAddLessonModalVisible(false)}
        onOk={handleSaveLesson}
        okText="Add"
        cancelText="Cancel"
        width={480}
      >
        <Form form={addLessonForm} layout="vertical">
          <Form.Item name="category" label="Category" rules={[{ required: true, message: 'Please enter category' }]}> <Input /> </Form.Item>
          <Form.Item name="title" label="Title" rules={[{ required: true, message: 'Please enter title' }]}> <Input /> </Form.Item>
          <Form.Item name="description" label="Description" rules={[{ required: true, message: 'Please enter description' }]}> <Input /> </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true, message: 'Please select type' }]}> <Select>
            <Option value="success">Success</Option>
            <Option value="challenge">Challenge</Option>
            <Option value="improvement">Improvement</Option>
          </Select> </Form.Item>
          <Form.Item name="impact" label="Impact" rules={[{ required: true, message: 'Please select impact' }]}> <Select>
            <Option value="low">Low</Option>
            <Option value="medium">Medium</Option>
            <Option value="high">High</Option>
          </Select> </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Edit Lesson"
        open={editLessonModalVisible}
        onCancel={() => setEditLessonModalVisible(false)}
        onOk={handleUpdateLesson}
        okText="Save"
        cancelText="Cancel"
        width={480}
      >
        <Form form={editLessonForm} layout="vertical">
          <Form.Item name="category" label="Category" rules={[{ required: true, message: 'Please enter category' }]}> <Input /> </Form.Item>
          <Form.Item name="title" label="Title" rules={[{ required: true, message: 'Please enter title' }]}> <Input /> </Form.Item>
          <Form.Item name="description" label="Description" rules={[{ required: true, message: 'Please enter description' }]}> <Input /> </Form.Item>
          <Form.Item name="type" label="Type" rules={[{ required: true, message: 'Please select type' }]}> <Select>
            <Option value="success">Success</Option>
            <Option value="challenge">Challenge</Option>
            <Option value="improvement">Improvement</Option>
          </Select> </Form.Item>
          <Form.Item name="impact" label="Impact" rules={[{ required: true, message: 'Please select impact' }]}> <Select>
            <Option value="low">Low</Option>
            <Option value="medium">Medium</Option>
            <Option value="high">High</Option>
          </Select> </Form.Item>
        </Form>
      </Modal>

      {/* Save & Proceed按钮 */}
      <div style={{
        position: 'fixed',
        bottom: '24px',
        right: '24px',
        zIndex: 1000
      }}>
        <Button
          type="primary"
          size="large"
          onClick={handleProceed}
          loading={saving}
          style={{
            backgroundColor: '#FF7A00',
            borderColor: '#FF7A00',
            borderRadius: '8px',
            height: '48px',
            padding: '0 24px',
            fontSize: '16px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 122, 0, 0.3)',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#E8690A';
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 16px rgba(255, 122, 0, 0.4)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#FF7A00';
            e.currentTarget.style.transform = 'translateY(0px)';
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 122, 0, 0.3)';
          }}
        >
          <RightOutlined />
          Save & Proceed
        </Button>
      </div>

    </div>
  );
};

export default CloseoutStage;
