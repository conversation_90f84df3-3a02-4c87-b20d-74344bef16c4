/* 流转按钮区域 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.btn-workflow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px !important;
  font-weight: 500;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-workflow:hover {
  background-color: #e6e6e6;
  border-color: #d9d9d9;
}

.btn-workflow .anticon {
  transition: transform 0.3s ease;
}

.btn-workflow:hover .anticon {
  transform: translateX(3px);
}

/* 优化的拖拽功能样式 */
.ant-table-tbody > tr {
  transition: all 0.2s ease !important;
}

.ant-table-tbody > tr:hover {
  background-color: rgba(24, 144, 255, 0.01) !important;
}

/* 拖拽句柄列优化 */
.ant-table-tbody > tr > td:first-child {
  padding: 8px 4px !important;
  text-align: center;
  border-right: 1px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td:first-child {
  background-color: rgba(24, 144, 255, 0.03) !important;
}

/* 拖拽状态优化 */
.ant-table-tbody > tr.sortable-ghost {
  opacity: 0.4;
}

.ant-table-tbody > tr.sortable-chosen {
  background-color: rgba(24, 144, 255, 0.02) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
}

/* 移除旧的拖拽句柄样式 */

.drag-handle {
  cursor: grab;
  color: #bfbfbf;
  font-size: 16px;
  padding: 10px 6px;
  display: inline-block;
  line-height: 1;
  transition: all 0.1s ease;
}

.drag-handle:hover {
  color: #1890ff;
  transform: scale(1.1);
} 