import React, { useState } from 'react';
import { Form, Input, Button, message, Typography, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, GithubOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import authService from '../services/auth.service';
import '../styles/Register.css';

const { Title, Text } = Typography;

const Register: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: { username: string; email: string; password: string }) => {
    try {
      setLoading(true);
      await authService.register(values.username, values.email, values.password);
      message.success('Registration successful');
      navigate('/');
    } catch (error) {
      message.error('Registration failed, please try again later');
      console.error('Register error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="register-container">
      {/* Top Navigation */}
      <div className="register-header">
        <div className="logo">
          <Link to="/">SmartLTC</Link>
        </div>
        <div className="nav-links">
          <Link to="/login" className="sign-in-link">Sign In</Link>
          <Link to="/register" className="sign-up-link">Sign Up</Link>
        </div>
      </div>

      {/* Register Card */}
      <div className="register-card-wrapper">
        <div className="register-card">
          <Title level={2} className="register-title">Sign Up</Title>
          <Text className="register-subtitle">
            Hello, please enter your details to create your account
          </Text>

          <Form
            name="register"
            className="register-form"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: 'Please enter your username' }]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Username"
                className="register-input"
              />
            </Form.Item>

            <Form.Item
              name="email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email address' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="Email"
                className="register-input"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[
                { required: true, message: 'Please enter your password' },
                { min: 6, message: 'Password must be at least 6 characters' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Password"
                className="register-input"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: 'Please confirm your password' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('The two passwords do not match'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Confirm Password"
                className="register-input"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="register-button"
              >
                Sign Up
              </Button>
            </Form.Item>

            <div className="social-login-divider">
              <Divider>
                <span className="divider-text">Or sign up with</span>
              </Divider>
            </div>

            <div className="social-login-buttons">
              <Button
                icon={<MailOutlined />}
                className="social-button gmail-button"
                loading={loading}
              >
                Gmail
              </Button>
              <Button
                icon={<GithubOutlined />}
                className="social-button github-button"
                loading={loading}
              >
                Github
              </Button>
            </div>

            <div className="login-link">
              <Text>Already have an account?</Text>
              <Link to="/login">Sign In now</Link>
            </div>
          </Form>
        </div>
      </div>

      {/* Footer */}
      <div className="register-footer">
        <div className="footer-content">
          <div className="footer-copyright">
                          <span>© 2024 SmartLTC - AI-Powered Business Operations Integrated Management Platform</span>
              <br />
              <span>Powered by Vlisoft.com • Empowering business with SmartLTC</span>
          </div>
          <div className="footer-buttons">
            <Link to="/privacy" className="footer-button">Privacy Notice</Link>
            <Link to="/terms" className="footer-button">Terms & Privacy</Link>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="decoration-line line-1"></div>
      <div className="decoration-line line-2"></div>
      <div className="decoration-line line-3"></div>
    </div>
  );
};

export default Register;
