import { Project } from '../types';

export class Compression {
  private static readonly COMPRESSED_PREFIX = 'compressed:';

  static compress(data: Project[]): string {
    try {
      const jsonString = JSON.stringify(data);
      const compressed = btoa(jsonString);
      return this.COMPRESSED_PREFIX + compressed;
    } catch (error) {
      console.error('压缩数据失败:', error);
      return '';
    }
  }

  static decompress(compressed: string): Project[] {
    try {
      if (!compressed.startsWith(this.COMPRESSED_PREFIX)) {
        return [];
      }

      const base64 = compressed.slice(this.COMPRESSED_PREFIX.length);
      const jsonString = atob(base64);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('解压数据失败:', error);
      return [];
    }
  }

  static isCompressed(data: string): boolean {
    return data.startsWith(this.COMPRESSED_PREFIX);
  }
} 