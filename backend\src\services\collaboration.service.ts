import { getRepository } from 'typeorm';
import { ProjectCollaborator, CollaboratorRole, InvitationStatus } from '../entities/ProjectCollaborator';
import { Project } from '../entities/Project';
import { User } from '../entities/User';
import { ProjectActivity, ActivityType } from '../entities/ProjectActivity';
import { BadRequestError, NotFoundError, ForbiddenError } from '../utils/errors';
import { sendInvitationEmail } from '../utils/emailService';
import { generateInvitationToken } from '../utils/tokenUtils';

export class CollaborationService {
  private collaboratorRepository = getRepository(ProjectCollaborator);
  private projectRepository = getRepository(Project);
  private userRepository = getRepository(User);
  private activityRepository = getRepository(ProjectActivity);

  /**
   * 邀请用户到项目
   */
  async inviteCollaborator(
    projectId: string,
    inviterUserId: string,
    email: string,
    role: CollaboratorRole,
    message?: string,
    expiresInDays: number = 7
  ): Promise<ProjectCollaborator> {
    // 验证邀请者权限
    await this.validateInviterPermission(projectId, inviterUserId);

    const project = await this.projectRepository.findOne(projectId);
    if (!project) {
      throw new NotFoundError('项目不存在');
    }

    // 检查是否已经是协作者
    const existingCollaborator = await this.collaboratorRepository.findOne({
      where: [
        { project: { id: projectId }, user: { email } },
        { project: { id: projectId }, invitedEmail: email }
      ]
    });

    if (existingCollaborator) {
      throw new BadRequestError('用户已经是项目协作者');
    }

    // 查找现有用户
    const existingUser = await this.userRepository.findOne({ where: { email } });

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);

    const collaborator = this.collaboratorRepository.create({
      project,
      user: existingUser,
      invitedEmail: email,
      role,
      status: InvitationStatus.PENDING,
      expiresAt,
      invitedBy: inviterUserId,
      invitationMessage: message,
      permissions: this.getDefaultPermissions(role)
    });

    const savedCollaborator = await this.collaboratorRepository.save(collaborator);

    // 发送邀请邮件
    const invitationToken = generateInvitationToken(savedCollaborator.id);
    await sendInvitationEmail(email, project.name, invitationToken, message);

    // 记录活动
    await this.recordActivity(projectId, inviterUserId, ActivityType.COLLABORATOR_ADDED, 
      `邀请 ${email} 加入项目`, { targetUser: email, role });

    return savedCollaborator;
  }

  /**
   * 接受邀请
   */
  async acceptInvitation(token: string, userId?: string): Promise<ProjectCollaborator> {
    const collaborator = await this.getCollaboratorByToken(token);
    
    if (collaborator.status !== InvitationStatus.PENDING) {
      throw new BadRequestError('邀请已经被处理');
    }

    if (collaborator.expiresAt && collaborator.expiresAt < new Date()) {
      collaborator.status = InvitationStatus.EXPIRED;
      await this.collaboratorRepository.save(collaborator);
      throw new BadRequestError('邀请已过期');
    }

    // 如果是注册用户，关联用户ID
    if (userId) {
      const user = await this.userRepository.findOne(userId);
      if (user && user.email === collaborator.invitedEmail) {
        collaborator.user = user;
      }
    }

    collaborator.status = InvitationStatus.ACCEPTED;
    const savedCollaborator = await this.collaboratorRepository.save(collaborator);

    // 记录活动
    await this.recordActivity(collaborator.project.id, userId || null, ActivityType.COLLABORATOR_ADDED,
      `${collaborator.invitedEmail} 接受了项目邀请`);

    return savedCollaborator;
  }

  /**
   * 拒绝邀请
   */
  async declineInvitation(token: string): Promise<void> {
    const collaborator = await this.getCollaboratorByToken(token);
    
    collaborator.status = InvitationStatus.DECLINED;
    await this.collaboratorRepository.save(collaborator);

    // 记录活动
    await this.recordActivity(collaborator.project.id, null, ActivityType.COLLABORATOR_REMOVED,
      `${collaborator.invitedEmail} 拒绝了项目邀请`);
  }

  /**
   * 更新协作者权限
   */
  async updateCollaboratorPermissions(
    projectId: string,
    collaboratorId: string,
    updaterUserId: string,
    newRole: CollaboratorRole,
    customPermissions?: any
  ): Promise<ProjectCollaborator> {
    await this.validateManagePermission(projectId, updaterUserId);

    const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
      relations: ['project', 'user']
    });

    if (!collaborator || collaborator.project.id !== projectId) {
      throw new NotFoundError('协作者不存在');
    }

    const oldRole = collaborator.role;
    collaborator.role = newRole;
    collaborator.permissions = customPermissions || this.getDefaultPermissions(newRole);

    const savedCollaborator = await this.collaboratorRepository.save(collaborator);

    // 记录活动
    await this.recordActivity(projectId, updaterUserId, ActivityType.PERMISSION_CHANGED,
      `更新了 ${collaborator.invitedEmail} 的权限`, { oldValue: oldRole, newValue: newRole });

    return savedCollaborator;
  }

  /**
   * 移除协作者
   */
  async removeCollaborator(
    projectId: string,
    collaboratorId: string,
    removerUserId: string
  ): Promise<void> {
    await this.validateManagePermission(projectId, removerUserId);

    const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
      relations: ['project', 'user']
    });

    if (!collaborator || collaborator.project.id !== projectId) {
      throw new NotFoundError('协作者不存在');
    }

    const targetEmail = collaborator.invitedEmail;
    await this.collaboratorRepository.remove(collaborator);

    // 记录活动
    await this.recordActivity(projectId, removerUserId, ActivityType.COLLABORATOR_REMOVED,
      `移除了协作者 ${targetEmail}`);
  }

  /**
   * 获取项目协作者列表
   */
  async getProjectCollaborators(projectId: string): Promise<ProjectCollaborator[]> {
    return await this.collaboratorRepository.find({
      where: { project: { id: projectId } },
      relations: ['user', 'project'],
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 检查用户对项目的权限
   */
  async checkPermission(projectId: string, userId: string, permission: string): Promise<boolean> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { project: { id: projectId }, user: { id: userId } },
      relations: ['user']
    });

    if (!collaborator || collaborator.status !== InvitationStatus.ACCEPTED) {
      return false;
    }

    // 检查具体权限
    const permissions = collaborator.permissions;
    switch (permission) {
      case 'edit':
        return permissions.canEdit;
      case 'delete':
        return permissions.canDelete;
      case 'invite':
        return permissions.canInvite;
      case 'manageStages':
        return permissions.canManageStages;
      case 'uploadDocuments':
        return permissions.canUploadDocuments;
      case 'viewFinancials':
        return permissions.canViewFinancials;
      default:
        return false;
    }
  }

  /**
   * 获取用户有权限的项目列表
   */
  async getUserProjects(userId: string): Promise<Project[]> {
    const collaborators = await this.collaboratorRepository.find({
      where: { user: { id: userId }, status: InvitationStatus.ACCEPTED },
      relations: ['project']
    });

    return collaborators.map(c => c.project);
  }

  // 私有方法

  private async validateInviterPermission(projectId: string, userId: string): Promise<void> {
    const hasPermission = await this.checkPermission(projectId, userId, 'invite');
    if (!hasPermission) {
      throw new ForbiddenError('没有邀请权限');
    }
  }

  private async validateManagePermission(projectId: string, userId: string): Promise<void> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { project: { id: projectId }, user: { id: userId } }
    });

    if (!collaborator || ![CollaboratorRole.OWNER, CollaboratorRole.ADMIN].includes(collaborator.role)) {
      throw new ForbiddenError('没有管理权限');
    }
  }

  private async getCollaboratorByToken(token: string): Promise<ProjectCollaborator> {
    // 这里需要实现token解析逻辑
    // 暂时简化处理
    throw new Error('Token validation not implemented');
  }

  private getDefaultPermissions(role: CollaboratorRole) {
    const permissionMap = {
      [CollaboratorRole.OWNER]: {
        canEdit: true,
        canDelete: true,
        canInvite: true,
        canManageStages: true,
        canUploadDocuments: true,
        canViewFinancials: true,
        moduleAccess: ['all']
      },
      [CollaboratorRole.ADMIN]: {
        canEdit: true,
        canDelete: false,
        canInvite: true,
        canManageStages: true,
        canUploadDocuments: true,
        canViewFinancials: true,
        moduleAccess: ['all']
      },
      [CollaboratorRole.EDITOR]: {
        canEdit: true,
        canDelete: false,
        canInvite: false,
        canManageStages: true,
        canUploadDocuments: true,
        canViewFinancials: false,
        moduleAccess: ['basic', 'documents', 'stages']
      },
      [CollaboratorRole.VIEWER]: {
        canEdit: false,
        canDelete: false,
        canInvite: false,
        canManageStages: false,
        canUploadDocuments: false,
        canViewFinancials: false,
        moduleAccess: ['basic']
      },
      [CollaboratorRole.GUEST_EDITOR]: {
        canEdit: true,
        canDelete: false,
        canInvite: false,
        canManageStages: false,
        canUploadDocuments: true,
        canViewFinancials: false,
        moduleAccess: ['basic', 'documents']
      },
      [CollaboratorRole.GUEST_VIEWER]: {
        canEdit: false,
        canDelete: false,
        canInvite: false,
        canManageStages: false,
        canUploadDocuments: false,
        canViewFinancials: false,
        moduleAccess: ['basic']
      }
    };

    return permissionMap[role];
  }

  private async recordActivity(
    projectId: string,
    userId: string | null,
    type: ActivityType,
    title: string,
    metadata?: any
  ): Promise<void> {
    if (!userId) return;

    const activity = this.activityRepository.create({
      project: { id: projectId },
      user: { id: userId },
      type,
      title,
      metadata
    });

    await this.activityRepository.save(activity);
  }
} 