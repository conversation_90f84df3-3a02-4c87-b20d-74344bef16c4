import React, { useState } from 'react';
import {
  Modal,
  Card,
  Space,
  Tag,
  Avatar,
  Divider,
  Row,
  Col,
  Typography,
  Statistic,
  Timeline,
  Button,
  Input,
  Select,
  message
} from 'antd';
import {
  UserOutlined,
  BankOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  CalendarOutlined,
  TeamOutlined,
  ProjectOutlined,
  StarOutlined,
  TagOutlined,
  EditOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { Client } from '../types';
import { useNavigate } from 'react-router-dom';
import clientService from '../services/client.service';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ClientProfileModalProps {
  visible: boolean;
  onClose: () => void;
  client: Client | null;
  onClientUpdate?: (updatedClient: Client) => void;
}

const ClientProfileModal: React.FC<ClientProfileModalProps> = ({
  visible,
  onClose,
  client,
  onClientUpdate
}) => {
  const navigate = useNavigate();
  const [editingTags, setEditingTags] = useState(false);
  const [localTags, setLocalTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  // 预定义的标签选项
  const defaultTagOptions = [
    'Strategic',
    'High-Value',
    'Long-term',
    'Premium',
    'VIP',
    'Enterprise',
    'SMB',
    'Startup',
    'Government',
    'Non-profit',
    'International',
    'Domestic',
    'Technology',
    'Financial Services', 
    'Healthcare',
    'Manufacturing',
    'Retail',
    'Education',
    'Food & Beverage',
    'Hospitality',
    'Real Estate',
    'Construction',
    'Transportation',
    'Energy',
    'Telecommunications',
    'Media & Entertainment',
    'Government',
    'Agriculture',
    'Automotive',
    'Aerospace',
    'Consulting',
    'Legal',
    'Insurance',
    'Pharmaceuticals',
    'Chemicals',
    'Textiles',
    'Mining',
    'Sports & Fitness',
    'Beauty & Personal Care',
    'Gaming',
    'E-commerce'
  ];

  React.useEffect(() => {
    if (client) {
      setLocalTags(client.tags || []);
    }
  }, [client]);

  if (!client) return null;

  // 处理编辑按钮点击
  const handleEdit = () => {
    onClose();
    navigate(`/client/edit/${client.id}`);
  };

  // 处理标签编辑
  const handleTagEdit = () => {
    setEditingTags(true);
  };

  // 保存标签
  const handleTagSave = async () => {
    try {
      // 这里可以调用API保存标签
      await clientService.updateClient(client.id, { tags: localTags });
      setEditingTags(false);
      message.success('Tags updated successfully');
      if (onClientUpdate) {
        onClientUpdate(client);
      }
    } catch (error) {
      message.error('Failed to update tags');
    }
  };

  // 取消编辑标签
  const handleTagCancel = () => {
    setLocalTags(client.tags || []);
    setEditingTags(false);
    setNewTag('');
  };

  // 添加标签
  const handleAddTag = (tag: string) => {
    if (tag && !localTags.includes(tag)) {
      setLocalTags([...localTags, tag]);
    }
    setNewTag('');
  };

  // 删除标签
  const handleRemoveTag = (tagToRemove: string) => {
    setLocalTags(localTags.filter(tag => tag !== tagToRemove));
  };

  // 选择预定义标签
  const handleSelectDefaultTag = (tag: string) => {
    handleAddTag(tag);
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'active': { color: 'green', text: 'Active' },
      'potential': { color: 'blue', text: 'Potential' },
      'inactive': { color: 'red', text: 'Inactive' },
      'churned': { color: 'volcano', text: 'Churned' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染层级标签
  const renderTierTag = (tier: string) => {
    const tierColors = { 
      'SVIP': 'purple', 
      'VIP': 'gold', 
      'BA': 'blue', 
      'A': 'green',
      'B': 'orange',
      'C': 'default'
    };
    const tierLabels = {
      'SVIP': 'Strategic VIP',
      'VIP': 'Very Important',
      'BA': 'Business Account',
      'A': 'Standard',
      'B': 'Basic',
      'C': 'Casual'
    };
    return (
      <Tag color={tierColors[tier as keyof typeof tierColors] || 'default'}>
        {tierLabels[tier as keyof typeof tierLabels] || tier}
      </Tag>
    );
  };

  // 生成联系记录时间线
  const contactTimeline = (client as any).contactRecords?.slice(0, 5).map((record: any, index: number) => ({
    children: (
      <div>
        <Text strong>{record.clientContactPerson}</Text> - <Text type="secondary">{record.internalContactPerson}</Text>
        <br />
        <Text type="secondary" style={{ fontSize: '12px' }}>{record.notes}</Text>
      </div>
    ),
    color: index === 0 ? 'green' : 'blue'
  })) || [];

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      centered
      bodyStyle={{ padding: 0 }}
      closeIcon={
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderRadius: '50%',
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#666',
          fontSize: '16px',
          fontWeight: 'bold',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          cursor: 'pointer',
          transition: 'all 0.2s ease',
        }}>
          ×
        </div>
      }
    >
      <div style={{ padding: '24px' }}>
        {/* Header Section */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '24px',
          background: 'linear-gradient(135deg, #718096 0%, #4A5568 100%)',
          padding: '20px',
          borderRadius: '8px',
          color: 'white',
          margin: '-24px -24px 24px -24px'
        }}>
          <Avatar 
            size={64} 
            icon={<UserOutlined />} 
            style={{ 
              backgroundColor: 'rgba(255,255,255,0.2)',
              marginRight: '16px'
            }}
          />
          <div style={{ flex: 1 }}>
            <Title level={3} style={{ color: 'white', margin: 0 }}>
              {client.name}
            </Title>
            <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '16px' }}>
              {client.contactPerson} • {client.name}
            </Text>
            <div style={{ marginTop: '8px' }}>
              {renderStatusTag(client.status)}
              {renderTierTag(client.tier)}
            </div>
          </div>
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
            <div style={{ marginBottom: '12px' }}>
              <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: '12px' }}>
                Client ID
              </Text>
              <br />
              <Text style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>
                {client.clientId}
              </Text>
            </div>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={handleEdit}
              style={{
                backgroundColor: 'rgba(255,255,255,0.15)',
                borderColor: 'rgba(255,255,255,0.3)',
                color: 'white',
                fontSize: '14px'
              }}
              size="small"
            >
              Edit Client
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="Total Revenue"
                value={client.totalRevenue}
                prefix={<span style={{ fontSize: '14px' }}>€</span>}
                valueStyle={{ color: '#3f8600', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="Total Projects"
                value={client.totalProjects}
                prefix={<ProjectOutlined style={{ fontSize: '14px' }} />}
                valueStyle={{ color: '#1890ff', fontSize: '20px' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="Days Since Last Contact"
                value={client.lastContactDate ? 
                  Math.floor((new Date().getTime() - new Date(client.lastContactDate).getTime()) / (1000 * 60 * 60 * 24)) : 
                  'N/A'
                }
                suffix={<span style={{ fontSize: '14px' }}>days</span>}
                valueStyle={{ color: '#722ed1', fontSize: '20px' }}
              />
            </Card>
          </Col>
        </Row>

        {/* Main Content */}
        <Row gutter={24}>
          {/* Left Column - Contact Information */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <PhoneOutlined />
                  <span>Contact Information</span>
                </Space>
              }
              size="small"
              style={{ marginBottom: '16px' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <MailOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                  <Text copyable>{client.email}</Text>
                </div>
                {client.phone && (
                  <div>
                    <PhoneOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                    <Text copyable>{client.phone}</Text>
                  </div>
                )}
                {client.contactEmail && (
                  <div>
                    <MailOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                    <Text copyable>{client.contactEmail}</Text>
                    <Text type="secondary"> (Contact)</Text>
                  </div>
                )}
                {client.contactPhone && (
                  <div>
                    <PhoneOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                    <Text copyable>{client.contactPhone}</Text>
                    <Text type="secondary"> (Contact)</Text>
                  </div>
                )}
              </Space>
            </Card>

            <Card 
              title={
                <Space>
                  <BankOutlined />
                  <span>Company Information</span>
                </Space>
              }
              size="small"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <TeamOutlined style={{ color: '#722ed1', marginRight: '8px' }} />
                  <Text strong>{client.industry}</Text>
                </div>
                <div>
                  <EnvironmentOutlined style={{ color: '#fa541c', marginRight: '8px' }} />
                  <Text>{client.country}</Text>
                </div>
                {client.website && (
                  <div>
                    <GlobalOutlined style={{ color: '#13c2c2', marginRight: '8px' }} />
                    <a href={client.website} target="_blank" rel="noopener noreferrer">
                      {client.website}
                    </a>
                  </div>
                )}
                {client.address && (
                  <div>
                    <EnvironmentOutlined style={{ color: '#fa541c', marginRight: '8px' }} />
                    <Text>{client.address}</Text>
                  </div>
                )}
              </Space>
            </Card>
          </Col>

          {/* Right Column - Activity & Tags */}
          <Col span={12}>
            {/* Tags */}
            <Card 
              title={
                <Space style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                  <Space>
                    <TagOutlined />
                    <span>Tags</span>
                  </Space>
                  {!editingTags && (
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={handleTagEdit}
                      style={{ fontSize: '12px', color: '#666' }}
                    >
                      Edit
                    </Button>
                  )}
                </Space>
              }
              size="small"
              style={{ marginBottom: '16px' }}
            >
              {editingTags ? (
                <div>
                  {/* 当前标签 */}
                  <div style={{ marginBottom: '12px' }}>
                    <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
                      Current Tags:
                    </Text>
                    <Space wrap>
                      {localTags.map((tag, index) => (
                        <Tag
                          key={index}
                          closable
                          onClose={() => handleRemoveTag(tag)}
                          color="processing"
                          style={{ fontSize: '12px' }}
                        >
                          {tag}
                        </Tag>
                      ))}
                      {localTags.length === 0 && (
                        <Text type="secondary" style={{ fontStyle: 'italic', fontSize: '12px' }}>
                          No tags assigned
                        </Text>
                      )}
                    </Space>
                  </div>

                  {/* 预定义标签选择 */}
                  <div style={{ marginBottom: '12px' }}>
                    <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
                      Quick Add:
                    </Text>
                    <Select
                      placeholder="Select from predefined tags"
                      style={{ width: '100%', fontSize: '12px' }}
                      size="small"
                      onSelect={(value: string | undefined) => {
                        if (value) {
                          handleSelectDefaultTag(value);
                        }
                      }}
                      value={undefined}
                    >
                      {defaultTagOptions
                        .filter(option => !localTags.includes(option))
                        .map(option => (
                          <Option key={option} value={option}>
                            {option}
                          </Option>
                        ))}
                    </Select>
                  </div>

                  {/* 自定义标签输入 */}
                  <div style={{ marginBottom: '12px' }}>
                    <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
                      Add Custom Tag:
                    </Text>
                    <Space.Compact style={{ width: '100%' }}>
                      <Input
                        placeholder="Enter custom tag"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onPressEnter={() => {
                          if (newTag.trim()) {
                            handleAddTag(newTag.trim());
                          }
                        }}
                        size="small"
                        style={{ fontSize: '12px' }}
                      />
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        size="small"
                        onClick={() => {
                          if (newTag.trim()) {
                            handleAddTag(newTag.trim());
                          }
                        }}
                        disabled={!newTag.trim() || localTags.includes(newTag.trim())}
                      />
                    </Space.Compact>
                  </div>

                  {/* 操作按钮 */}
                  <div style={{ textAlign: 'right' }}>
                    <Space>
                      <Button size="small" onClick={handleTagCancel}>
                        Cancel
                      </Button>
                      <Button type="primary" size="small" onClick={handleTagSave}>
                        Save Tags
                      </Button>
                    </Space>
                  </div>
                </div>
              ) : (
                <div>
                  {localTags.length > 0 ? (
                    <Space wrap>
                      {localTags.map((tag, index) => (
                        <Tag key={index} color="processing" style={{ fontSize: '12px' }}>
                          {tag}
                        </Tag>
                      ))}
                    </Space>
                  ) : (
                    <div style={{ textAlign: 'center', padding: '16px 0' }}>
                      <Text type="secondary" style={{ fontStyle: 'italic', fontSize: '12px' }}>
                        No tags assigned
                      </Text>
                      <br />
                      <Button
                        type="link"
                        size="small"
                        onClick={handleTagEdit}
                        style={{ fontSize: '12px', padding: '4px 0' }}
                      >
                        Add tags
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </Card>

            {/* Recent Activity */}
            <Card 
              title={
                <Space>
                  <CalendarOutlined />
                  <span>Recent Activity</span>
                </Space>
              }
              size="small"
            >
              {contactTimeline.length > 0 ? (
                <Timeline items={contactTimeline} />
              ) : (
                <Text type="secondary" style={{ fontStyle: 'italic' }}>
                  No recent contact records
                </Text>
              )}
            </Card>
          </Col>
        </Row>

        {/* Notes Section */}
        {client.notes && (
          <>
            <Divider />
            <Card 
              title={
                <Space>
                  <StarOutlined />
                  <span>Notes</span>
                </Space>
              }
              size="small"
            >
              <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                {client.notes}
              </Paragraph>
            </Card>
          </>
        )}

        {/* Created/Updated Info */}
        <div style={{ 
          marginTop: '16px', 
          padding: '12px', 
          background: '#fafafa', 
          borderRadius: '6px',
          fontSize: '12px',
          color: '#666'
        }}>
          <Row>
            <Col span={12}>
              <Text type="secondary">
                Created: {new Date(client.createdAt).toLocaleDateString()}
              </Text>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <Text type="secondary">
                Last Updated: {new Date(client.updatedAt).toLocaleDateString()}
              </Text>
            </Col>
          </Row>
        </div>
      </div>
    </Modal>
  );
};

export default ClientProfileModal; 