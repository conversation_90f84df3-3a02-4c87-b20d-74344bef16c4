import dayjs from 'dayjs';

// 统一的日期格式
export const DATE_FORMAT = 'DD/MM/YYYY';
export const DATETIME_FORMAT = 'DD/MM/YYYY HH:mm';

/**
 * 格式化日期为DD/MM/YYYY格式
 * @param date 日期字符串、Date对象或dayjs对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: string | Date | dayjs.Dayjs | null | undefined): string => {
  if (!date) return '';
  return dayjs(date).format(DATE_FORMAT);
};

/**
 * 格式化日期时间为DD/MM/YYYY HH:mm格式
 * @param date 日期字符串、Date对象或dayjs对象
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (date: string | Date | dayjs.Dayjs | null | undefined): string => {
  if (!date) return '';
  return dayjs(date).format(DATETIME_FORMAT);
};

/**
 * 解析日期字符串为dayjs对象
 * @param dateString 日期字符串
 * @returns dayjs对象
 */
export const parseDate = (dateString: string): dayjs.Dayjs => {
  return dayjs(dateString);
};

/**
 * 获取当前日期的格式化字符串
 * @returns DD/MM/YYYY格式的当前日期
 */
export const getCurrentDateFormatted = (): string => {
  return dayjs().format(DATE_FORMAT);
};

/**
 * 检查日期是否有效
 * @param date 日期字符串或Date对象
 * @returns 是否为有效日期
 */
export const isValidDate = (date: string | Date): boolean => {
  return dayjs(date).isValid();
};

/**
 * 获取相对时间显示
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export const getRelativeTime = (date: string | Date): string => {
  const now = dayjs();
  const targetDate = dayjs(date);
  const diffInMinutes = now.diff(targetDate, 'minute');

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return formatDate(date);
}; 