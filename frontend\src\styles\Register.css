/* 整体容器 */
.register-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f9f6f2;
  position: relative;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
}

/* 顶部导航 */
.register-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  width: 100%;
  z-index: 10;
  box-sizing: border-box;
  max-width: 100%;
}

.logo a {
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: #000;
  text-decoration: none;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.sign-in-link, .sign-up-link {
  color: #000;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  margin-right: 15px;
  white-space: nowrap;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sign-in-link {
  color: #666;
}

.sign-in-link:hover {
  color: #000;
  background-color: rgba(0, 0, 0, 0.05);
}

.sign-up-link {
  color: #FF7A00;
}

.sign-up-link:hover {
  color: #FF7A00;
  background-color: rgba(255, 122, 0, 0.1);
}

/* 注册卡片 */
.register-card-wrapper {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  z-index: 5;
}

.register-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  text-align: center;
}

.register-title {
  margin-bottom: 10px !important;
  font-size: 16px !important;
  font-weight: var(--font-weight-semibold) !important;
  line-height: var(--line-height-tight) !important;
}

/* 强制覆盖Ant Design的Title样式 */
.register-card .ant-typography h2.register-title {
  font-size: 16px !important;
}

.ant-typography.register-title {
  font-size: 16px !important;
}

.register-subtitle {
  display: block;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: #666;
  margin-bottom: 30px;
}

.register-form {
  text-align: left;
}

.register-input {
  height: 50px;
  border-radius: 8px;
}

.register-button {
  width: 100%;
  height: 50px;
  border-radius: 8px;
  font-weight: var(--font-weight-medium);
  font-size: 14px !important;
  background-color: #ffcb8c;
  border: none;
  color: #000;
}

/* 强制覆盖Ant Design Button样式 */
.ant-btn.register-button {
  font-size: 14px !important;
}

.register-card .ant-btn.register-button {
  font-size: 14px !important;
}

.register-button:hover {
  background-color: #ffc27b !important;
  color: #000 !important;
}

/* 社交登录 */
.social-login-divider {
  margin: 20px 0;
}

.divider-text {
  color: #999;
  font-size: var(--font-size-sm);
}

.social-login-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 30px;
}

.social-button {
  flex: 1;
  height: 45px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
  background-color: white;
  color: #333;
  font-size: 14px !important;
  font-weight: var(--font-weight-medium);
}

/* 强制覆盖社交按钮的Ant Design样式 */
.ant-btn.social-button {
  font-size: 14px !important;
}

.register-card .ant-btn.social-button {
  font-size: 14px !important;
}

.social-button:hover {
  background-color: #f5f5f5 !important;
  color: #333 !important;
}

.gmail-button {
  border-color: #DB4437;
  color: #DB4437;
}

.gmail-button:hover {
  background-color: rgba(219, 68, 55, 0.05) !important;
  color: #DB4437 !important;
}

.github-button {
  border-color: #24292e;
  color: #24292e;
}

.github-button:hover {
  background-color: rgba(36, 41, 46, 0.05) !important;
  color: #24292e !important;
}

.login-link {
  text-align: center;
  margin-top: 20px;
}

.login-link a {
  margin-left: 5px;
  color: #ffcb8c;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* 页脚 */
.register-footer {
  padding: 20px;
  text-align: center;
  z-index: 10;
  font-size: var(--font-size-sm);
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.footer-copyright {
  color: #666 !important;
  font-size: 14px !important;
  line-height: var(--line-height-normal);
  margin-top: -5px;
}

.footer-copyright span {
  color: #666 !important;
  font-size: 14px !important;
}

.footer-buttons {
  display: flex;
  gap: 16px;
}

.footer-button {
  background-color: #718096;
  color: white !important;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.footer-button:hover {
  background-color: #2d3748 !important;
  color: white !important;
  transform: translateY(-1px);
}

/* 装饰元素 */
.decoration-line {
  position: absolute;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  z-index: 1;
}

.line-1 {
  width: 200px;
  height: 100px;
  top: 100px;
  left: 200px;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.line-2 {
  width: 300px;
  height: 150px;
  top: 200px;
  right: 100px;
  border-top: none;
  border-right: none;
  border-bottom: none;
}

.line-3 {
  width: 200px;
  height: 100px;
  bottom: 150px;
  right: 300px;
  border-top: none;
  border-left: none;
  border-right: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .register-header {
    padding: 15px 20px;
  }

  .register-card {
    padding: 30px 20px;
  }

  .decoration-line {
    display: none;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .footer-button {
    font-size: var(--font-size-base);
    padding: 0 10px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .register-header {
    padding: 10px 15px;
  }

  .nav-links {
    gap: 5px;
  }

  .sign-up-link {
    font-size: var(--font-size-base);
  }

  .logo a {
    font-size: var(--font-size-xl);
  }
}
