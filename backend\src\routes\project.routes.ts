import { Router } from 'express';
import { getRepository } from 'typeorm';
import { Project } from '../entities/Project';
import { authMiddleware } from '../middlewares/auth.middleware';

const router = Router();

// 使用认证中间件保护所有路由
router.use(authMiddleware);

// Get all projects
router.get('/', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const projects = await projectRepository.find();
    res.json(projects);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching projects', error });
  }
});

// Get project by ID
router.get('/:id', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json(project);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching project', error });
  }
});

// Create new project
router.post('/', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = projectRepository.create(req.body);
    const result = await projectRepository.save(project);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error creating project', error });
  }
});

// Update project
router.put('/:id', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    projectRepository.merge(project, req.body);
    const result = await projectRepository.save(project);
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: 'Error updating project', error });
  }
});

// Delete project
router.delete('/:id', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    await projectRepository.remove(project);
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Error deleting project', error });
  }
});

// Team Management Routes
// Get project team members
router.get('/:id/team/members', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // 返回模拟数据（因为当前没有真实的团队成员关联）
    const mockMembers = [
      {
        id: '1',
        userId: 'user1',
        projectId: req.params.id,
        email: '<EMAIL>',
        name: 'John Doe',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=random',
        role: 'owner',
        status: 'active',
        invitedBy: 'system',
        invitedAt: new Date().toISOString(),
        joinedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: true,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        id: '2',
        userId: 'user2',
        projectId: req.params.id,
        email: '<EMAIL>',
        name: 'Jane Smith',
        avatar: 'https://ui-avatars.com/api/?name=Jane+Smith&background=random',
        role: 'admin',
        status: 'active',
        invitedBy: 'user1',
        invitedAt: new Date().toISOString(),
        joinedAt: new Date().toISOString(),
        lastActiveAt: new Date().toISOString(),
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: false,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      }
    ];

    res.json(mockMembers);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching team members', error });
  }
});

// Get project team stats
router.get('/:id/team/stats', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // 返回模拟统计数据
    const mockStats = {
      totalMembers: 2,
      activeMembers: 2,
      pendingInvitations: 0,
      roleDistribution: {
        owner: 1,
        admin: 1,
        member: 0,
        viewer: 0
      }
    };

    res.json(mockStats);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching team stats', error });
  }
});

// Get current user role in project
router.get('/:id/team/my-role', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // 返回模拟角色数据（开发时默认为owner）
    res.json({ role: 'owner' });
  } catch (error) {
    res.status(500).json({ message: 'Error fetching user role', error });
  }
});

// Get pending invitations
router.get('/:id/team/invitations', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // 返回空的邀请列表
    res.json([]);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching invitations', error });
  }
});

// Invite member to project
router.post('/:id/team/invite', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const { email, role, message } = req.body;

    // 模拟邀请成功
    const mockInvitation = {
      id: Date.now().toString(),
      projectId: req.params.id,
      email,
      role,
      invitedBy: 'current-user',
      invitedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
      token: 'mock-token',
      status: 'pending'
    };

    res.status(201).json(mockInvitation);
  } catch (error) {
    res.status(500).json({ message: 'Error sending invitation', error });
  }
});

// Remove member from project
router.delete('/:id/team/members/:memberId', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // 模拟删除成功
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: 'Error removing member', error });
  }
});

// Update member role
router.put('/:id/team/members/:memberId/role', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const { role } = req.body;

    // 模拟更新成功
    const mockUpdatedMember = {
      id: req.params.memberId,
      userId: 'user1',
      projectId: req.params.id,
      email: '<EMAIL>',
      name: 'Example User',
      role,
      status: 'active',
      permissions: {
        canViewProject: true,
        canEditProject: role !== 'viewer',
        canDeleteProject: role === 'owner',
        canManageTeam: ['owner', 'admin'].includes(role),
        canManageFinance: ['owner', 'admin'].includes(role),
        canExportData: role !== 'viewer',
        canViewReports: true,
      }
    };

    res.json(mockUpdatedMember);
  } catch (error) {
    res.status(500).json({ message: 'Error updating member role', error });
  }
});

// Update member permissions
router.put('/:id/team/members/:memberId/permissions', async (req, res) => {
  try {
    const projectRepository = getRepository(Project);
    const project = await projectRepository.findOne(req.params.id);

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const { permissions } = req.body;

    // 模拟更新成功
    const mockUpdatedMember = {
      id: req.params.memberId,
      userId: 'user1',
      projectId: req.params.id,
      email: '<EMAIL>',
      name: 'Example User',
      role: 'member',
      status: 'active',
      permissions
    };

    res.json(mockUpdatedMember);
  } catch (error) {
    res.status(500).json({ message: 'Error updating member permissions', error });
  }
});

export default router;