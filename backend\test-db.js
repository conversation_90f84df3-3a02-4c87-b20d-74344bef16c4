const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('ltc_project.sqlite');

db.serialize(() => {
  db.run("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)");
  db.run("INSERT INTO test (name) VALUES ('Test connection')");
  
  db.each("SELECT id, name FROM test", (err, row) => {
    if (err) {
      console.error('Error querying database:', err);
    } else {
      console.log(`${row.id}: ${row.name}`);
    }
  });
});

db.close((err) => {
  if (err) {
    console.error('Error closing database:', err);
  } else {
    console.log('Database connection closed successfully');
  }
});
