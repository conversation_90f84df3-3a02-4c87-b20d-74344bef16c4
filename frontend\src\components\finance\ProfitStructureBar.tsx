import React from 'react';

const ProfitStructureBar: React.FC = () => {
  // 使用 dangerouslySetInnerHTML 来渲染静态 HTML
  const staticHtml = `
    <div class="mb-4">

      <!-- 水平条形图 -->
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 1rem;">
        <tbody>
          <tr>
            <td width="60%" style="padding: 0;">
              <div style="background-color: #EF4444; color: white; text-align: center; padding: 0.5rem; border-top-left-radius: 0.375rem; border-bottom-left-radius: 0.375rem;">Direct Costs 60%</div>
            </td>
            <td width="20%" style="padding: 0;">
              <div style="background-color: #F59E0B; color: white; text-align: center; padding: 0.5rem;">Indirect Costs 20%</div>
            </td>
            <td width="20%" style="padding: 0;">
              <div style="background-color: #047857; color: white; text-align: center; padding: 0.5rem; border-top-right-radius: 0.375rem; border-bottom-right-radius: 0.375rem;">Net Profit 20%</div>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 图例 -->
      <div style="display: flex; flex-wrap: wrap; gap: 1.5rem;">
        <div style="display: flex; align-items: center;">
          <div style="width: 0.75rem; height: 0.75rem; border-radius: 0.125rem; margin-right: 0.5rem; background-color: #3B82F6;"></div>
          <span style="font-size: 0.75rem; color: #4B5563;">Total Revenue (100%)</span>
        </div>
        <div style="display: flex; align-items: center;">
          <div style="width: 0.75rem; height: 0.75rem; border-radius: 0.125rem; margin-right: 0.5rem; background-color: #EF4444;"></div>
          <span style="font-size: 0.75rem; color: #4B5563;">Direct Costs (60%)</span>
        </div>
        <div style="display: flex; align-items: center;">
          <div style="width: 0.75rem; height: 0.75rem; border-radius: 0.125rem; margin-right: 0.5rem; background-color: #10B981;"></div>
          <span style="font-size: 0.75rem; color: #4B5563;">Gross Profit (40%)</span>
        </div>
        <div style="display: flex; align-items: center;">
          <div style="width: 0.75rem; height: 0.75rem; border-radius: 0.125rem; margin-right: 0.5rem; background-color: #F59E0B;"></div>
          <span style="font-size: 0.75rem; color: #4B5563;">Indirect Costs (20%)</span>
        </div>
        <div style="display: flex; align-items: center;">
          <div style="width: 0.75rem; height: 0.75rem; border-radius: 0.125rem; margin-right: 0.5rem; background-color: #047857;"></div>
          <span style="font-size: 0.75rem; color: #4B5563;">Net Profit (20%)</span>
        </div>
      </div>
    </div>
  `;

  return <div dangerouslySetInnerHTML={{ __html: staticHtml }} />;
};

export default ProfitStructureBar;
