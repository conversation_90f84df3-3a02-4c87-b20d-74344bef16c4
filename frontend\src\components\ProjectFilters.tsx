import React, { useCallback } from 'react';
import { Input, Select, Space, Button } from 'antd';
import { SearchOutlined, FilterOutlined, ReloadOutlined } from '@ant-design/icons';
import { ProjectFilters as ProjectFiltersType } from '../types';
import '../styles/ProjectFilters.css';

const { Option } = Select;

interface ProjectFiltersProps {
  filters: ProjectFiltersType;
  onFilterChange: (filters: Partial<ProjectFiltersType>) => void;
  onReset: () => void;
}

const ProjectFilters: React.FC<ProjectFiltersProps> = React.memo(({
  filters,
  onFilterChange,
  onReset
}) => {
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange({ searchText: e.target.value });
  }, [onFilterChange]);

  const handleStatusChange = useCallback((value: string) => {
    onFilterChange({ status: value });
  }, [onFilterChange]);

  const handleCountryChange = useCallback((value: string) => {
    onFilterChange({ country: value });
  }, [onFilterChange]);

  const handleTierChange = useCallback((value: string) => {
    onFilterChange({ tier: value });
  }, [onFilterChange]);

  const handleCategoryChange = useCallback((value: string) => {
    onFilterChange({ category: value });
  }, [onFilterChange]);

  const handleRevenueChange = useCallback((value: string) => {
    onFilterChange({ revenue: value });
  }, [onFilterChange]);

  const handleProbabilityChange = useCallback((value: string) => {
    onFilterChange({ probability: value });
  }, [onFilterChange]);

  const handleLevelChange = useCallback((value: string) => {
    onFilterChange({ level: value });
  }, [onFilterChange]);

  const handleCreateDateChange = useCallback((value: string) => {
    onFilterChange({ createDate: value });
  }, [onFilterChange]);

  return (
    <div className="project-filters">
      <Space wrap>
        <Input
          placeholder="搜索项目..."
          prefix={<SearchOutlined />}
          value={filters.searchText}
          onChange={handleSearchChange}
          allowClear
        />

        <Select
          value={filters.status}
          onChange={handleStatusChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有状态</Select.Option>
          <Select.Option value="planning">规划中</Select.Option>
          <Select.Option value="inProgress">进行中</Select.Option>
          <Select.Option value="completed">已完成</Select.Option>
          <Select.Option value="onHold">已暂停</Select.Option>
        </Select>

        <Select
          value={filters.country}
          onChange={handleCountryChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有国家</Select.Option>
          <Select.Option value="CN">中国</Select.Option>
          <Select.Option value="US">美国</Select.Option>
          <Select.Option value="UK">英国</Select.Option>
          <Select.Option value="DE">德国</Select.Option>
        </Select>

        <Select
          value={filters.tier}
          onChange={handleTierChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有级别</Select.Option>
          <Select.Option value="A">A级</Select.Option>
          <Select.Option value="B">B级</Select.Option>
          <Select.Option value="C">C级</Select.Option>
        </Select>

        <Select
          value={filters.category}
          onChange={handleCategoryChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有类别</Select.Option>
          <Select.Option value="software">软件</Select.Option>
          <Select.Option value="hardware">硬件</Select.Option>
          <Select.Option value="service">服务</Select.Option>
        </Select>

        <Select
          value={filters.revenue}
          onChange={handleRevenueChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有收入</Select.Option>
          <Select.Option value="veryLow">极低</Select.Option>
          <Select.Option value="low">低</Select.Option>
          <Select.Option value="medium">中</Select.Option>
          <Select.Option value="high">高</Select.Option>
        </Select>

        <Select
          value={filters.probability}
          onChange={handleProbabilityChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有概率</Select.Option>
          <Select.Option value="veryLow">极低</Select.Option>
          <Select.Option value="low">低</Select.Option>
          <Select.Option value="medium">中</Select.Option>
          <Select.Option value="high">高</Select.Option>
        </Select>

        <Select
          value={filters.level}
          onChange={handleLevelChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有等级</Select.Option>
          <Select.Option value="1">1级</Select.Option>
          <Select.Option value="2">2级</Select.Option>
          <Select.Option value="3">3级</Select.Option>
        </Select>

        <Select
          value={filters.createDate}
          onChange={handleCreateDateChange}
          style={{ width: 120 }}
        >
          <Select.Option value="all">所有时间</Select.Option>
          <Select.Option value="last7days">最近7天</Select.Option>
          <Select.Option value="last30days">最近30天</Select.Option>
          <Select.Option value="last90days">最近90天</Select.Option>
          <Select.Option value="thisYear">今年</Select.Option>
        </Select>

        <Button
          icon={<ReloadOutlined />}
          onClick={onReset}
        >
          重置
        </Button>
      </Space>
    </div>
  );
});

ProjectFilters.displayName = 'ProjectFilters';

export default ProjectFilters;