.project-filters {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.project-filters .ant-space {
  width: 100%;
}

.project-filters .ant-input-affix-wrapper {
  border-radius: 6px;
}

.project-filters .ant-input-affix-wrapper:hover,
.project-filters .ant-input-affix-wrapper:focus {
  border-color: #1890ff;
}

.project-filters .ant-select .ant-select-selector {
  border-radius: 6px;
}

.project-filters .ant-select:hover .ant-select-selector {
  border-color: #1890ff;
}

.project-filters .ant-select-suffix {
  color: #8c8c8c;
}

.project-filters .ant-select-item {
  padding: 8px 12px;
}

.project-filters .ant-select-item-option-selected {
  background-color: #e6f7ff;
  color: #1890ff;
}

.project-filters .ant-select-item-option-active {
  background-color: #f5f5f5;
}
