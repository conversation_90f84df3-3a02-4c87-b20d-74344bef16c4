/* 侧边栏样式 */
.sidebar {
  background-color: var(--card-background);
  box-shadow: var(--shadow-sm);
  position: sticky;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 100;
  border-right: 1px solid var(--border);
  transition: all var(--transition-normal);
}

.sidebar-logo {
  min-height: 70px; /* 使用min-height代替固定height，确保有足够空间 */
  padding: 18px 12px; /* 进一步增加上下padding */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid var(--border);
  margin-bottom: 16px;
  transition: all var(--transition-normal);
}

.sidebar-logo:hover {
  background-color: var(--primary-light);
  opacity: 0.9;
}

.sidebar-logo-collapsed {
  padding: 20px 0;
  justify-content: center;
}

.sidebar-logo-icon {
  font-size: 24px;
  color: var(--primary);
  margin-right: 10px;
  transition: margin var(--transition-normal);
}

.sidebar-logo-icon-collapsed {
  margin-right: 0;
}

.sidebar-logo-text {
  margin: 0;
  font-size: calc(18px * 1.1); /* 增加10% */
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: visible; /* 允许文字完全显示 */
  text-overflow: ellipsis;
  line-height: 1.2; /* 增加行高 */
}

.sidebar-trigger {
  position: absolute;
  right: -12px;
  top: 70px; /* 调整位置匹配新的LOGO高度 */
  width: 24px;
  height: 24px;
  background: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  z-index: 101;
  color: #fff;
  transition: transform var(--transition-fast);
}

.sidebar-trigger:hover {
  transform: scale(1.1);
}

.sidebar-menu {
  height: calc(100% - 86px); /* 调整高度计算匹配新的LOGO区域 */
  border-right: 0;
  background: transparent;
  overflow-y: auto;
  padding: 0 8px;
}

/* 菜单组样式 */
.sidebar-menu .ant-menu {
  border-right: 0;
  background: transparent;
  margin-bottom: 0;
}

/* 菜单项样式 */
.ant-menu-item {
  margin: 4px 0 !important;
  border-radius: 0 !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important; /* 从18px缩小20%：18px * 0.8 = 14.4px ≈ 14px */
}

.ant-menu-submenu-title {
  margin: 4px 0 !important;
  border-radius: 0 !important;
  height: 40px !important;
  line-height: 40px !important;
  font-size: 14px !important; /* 从18px缩小20%：18px * 0.8 = 14.4px ≈ 14px */
}

.ant-menu-item-selected {
  background-color: var(--primary-light) !important;
  color: var(--primary) !important;
  font-weight: 500 !important;
}

.ant-menu-item-active {
  color: var(--primary) !important;
}

.ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: var(--primary) !important;
  font-weight: 500 !important;
}

.ant-menu-submenu-active > .ant-menu-submenu-title {
  color: var(--primary) !important;
}

.ant-menu-item .anticon,
.ant-menu-submenu-title .anticon {
  font-size: 18px !important;
}

/* 子菜单样式 */
.ant-menu-sub {
  background-color: transparent !important;
}

.ant-menu-sub .ant-menu-item {
  padding-left: 40px !important;
  height: 36px !important;
  line-height: 36px !important;
  font-size: 13px !important; /* 从16px缩小20%：16px * 0.8 = 12.8px ≈ 13px */
}

/* Finance图标样式 */
.finance-icon {
  color: var(--primary) !important;
}

/* 当Finance菜单项被选中时，图标变为白色 */
.ant-menu-item-selected .finance-icon,
.ant-menu-submenu-selected .finance-icon {
  color: var(--primary) !important;
}

/* 当Finance菜单项被展开时，图标保持主色调 */
.ant-menu-submenu-open:not(.ant-menu-submenu-selected) .finance-icon {
  color: var(--primary) !important;
}

/* 快捷图标样式 */
.finance-shortcut-icon {
  position: relative;
  z-index: 1;
}

.finance-shortcut-icon:hover {
  background-color: var(--primary) !important;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.finance-shortcut-icon:hover .anticon {
  color: white !important;
}

.finance-shortcut-icon::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background-color: transparent;
  z-index: -1;
  transition: all 0.3s;
}

.finance-shortcut-icon:hover::after {
  background-color: var(--primary-light);
}

/* 自定义分割线样式 */
.sidebar-divider {
  margin: 16px 8px;
  padding: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.08) 25%, 
    rgba(0, 0, 0, 0.12) 50%, 
    rgba(0, 0, 0, 0.08) 75%, 
    transparent 100%
  );
  border: none;
  position: relative;
  overflow: visible;
  transition: all 0.3s ease;
}

/* 收起状态下的分割线 */
.sidebar-collapsed .sidebar-divider {
  margin: 12px 16px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.1) 30%, 
    rgba(0, 0, 0, 0.15) 50%, 
    rgba(0, 0, 0, 0.1) 70%, 
    transparent 100%
  );
}

/* 分割线中央装饰点 */
.sidebar-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, var(--primary), rgba(var(--primary-rgb), 0.7));
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.3s ease;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
}

/* 收起状态下的装饰点 */
.sidebar-collapsed .sidebar-divider::before {
  width: 4px;
  height: 4px;
  opacity: 0.8;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.9);
}

/* 悬停效果 */
.sidebar-divider:hover {
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0, 0, 0, 0.1) 25%, 
    rgba(0, 0, 0, 0.15) 50%, 
    rgba(0, 0, 0, 0.1) 75%, 
    transparent 100%
  );
}

.sidebar-divider:hover::before {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.9), 0 0 8px rgba(var(--primary-rgb), 0.3);
}

/* 隐藏默认的Menu divider */
.ant-menu-item-divider {
  display: none !important;
}

/* 右键上下文菜单样式 */
.context-menu {
  position: fixed;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 8px 0;
  min-width: 180px;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.context-menu-item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(0, 0, 0, 0.85);
  transition: all 0.2s ease;
  margin: 2px 6px;
  border-radius: 6px;
  font-weight: 400;
  line-height: 1.5;
}

.context-menu-item:hover {
  background-color: #f0f7ff;
  color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.context-menu-item:active {
  transform: translateY(0px);
  background-color: #e6f7ff;
}

.context-menu-item .menu-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.context-menu-item:hover .menu-icon {
  opacity: 1;
  color: #1890ff;
}

/* 菜单项之间的分隔线 */
.context-menu-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 6px 12px;
}

/* 菜单进入动画 */
.context-menu {
  animation: contextMenuSlideIn 0.2s ease-out;
}

@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 菜单项增强样式 - 支持右键点击 */
.menu-item-wrapper {
  width: 100%;
  height: 100%;
  display: block;
  user-select: none;
}

.menu-item-wrapper:hover {
  background-color: transparent;
}

/* 右键菜单功能不需要额外的菜单项样式修改 */

/* 工具提示样式 */
.sidebar-tooltip {
  font-size: 12px;
}

.sidebar-tooltip .ant-tooltip-inner {
  font-size: 12px;
}
