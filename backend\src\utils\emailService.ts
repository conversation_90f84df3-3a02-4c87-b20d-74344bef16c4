// 邮件服务 - 暂时使用模拟实现
export async function sendInvitationEmail(
  email: string,
  projectName: string,
  invitationToken: string,
  message?: string
): Promise<void> {
  // 这里应该集成真实的邮件服务（如 SendGrid, Nodemailer 等）
  // 暂时只是控制台输出
  console.log(`
====== 邀请邮件 ======
收件人: ${email}
项目: ${projectName}
邀请令牌: ${invitationToken}
消息: ${message || '无'}
邀请链接: ${process.env.FRONTEND_URL || 'http://localhost:3000'}/invitation?token=${invitationToken}
====================
  `);

  // 模拟邮件发送延迟
  await new Promise(resolve => setTimeout(resolve, 100));
} 