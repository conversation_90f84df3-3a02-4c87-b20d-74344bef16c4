import React, { useState } from 'react';
import { 
  Card, 
  Tabs, 
  Table, 
  Form, 
  Input, 
  Button, 
  Select, 
  Switch, 
  Modal, 
  message, 
  Typography, 
  Space,
  Tag,
  Progress,
  Statistic,
  List,
  Avatar,
  Drawer,
  Row,
  Col,
  Alert,
  Badge,
  Upload
} from 'antd';
import { 
  UserOutlined, 
  SettingOutlined, 
  ApiOutlined, 
  MonitorOutlined,
  SecurityScanOutlined,
  BranchesOutlined,
  DatabaseOutlined,
  NotificationOutlined,
  TeamOutlined,
  HistoryOutlined,
  SyncOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  CloudUploadOutlined,
  ReloadOutlined,

} from '@ant-design/icons';


const { TabPane } = Tabs;
const { Option } = Select;
const { Text } = Typography;
const { TextArea } = Input;


interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  department: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  createdAt: string;
}

interface BusinessRule {
  id: string;
  name: string;
  type: 'probability' | 'workflow' | 'notification' | 'automation';
  condition: string;
  action: string;
  status: 'active' | 'inactive';
  priority: 'high' | 'medium' | 'low';
}

interface Integration {
  id: string;
  name: string;
  type: 'CRM' | 'ERP' | 'Email' | 'Calendar' | 'Finance' | 'Marketing';
  status: 'connected' | 'disconnected' | 'error';
  lastSync: string;
  syncStatus: 'success' | 'failed' | 'running';
}

interface SystemMetric {
  name: string;
  value: number;
  unit: string;
  status: 'normal' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  description: string;
}

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('users');
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRule, setSelectedRule] = useState<BusinessRule | null>(null);
  const [form] = Form.useForm();
  
  // 模拟数据
  const [users, setUsers] = useState<User[]>([
    {
      id: '1',
      username: 'admin',
      email: '<EMAIL>',
      role: 'System Administrator',
      department: 'IT',
      status: 'active',
      lastLogin: '2024-01-15 10:30:00',
      createdAt: '2023-01-01'
    },
    {
      id: '2',
      username: 'sales_manager',
      email: '<EMAIL>',
      role: 'Sales Manager',
      department: 'Sales',
      status: 'active',
      lastLogin: '2024-01-15 09:15:00',
      createdAt: '2023-02-15'
    },
    {
      id: '3',
      username: 'project_manager',
      email: '<EMAIL>',
      role: 'Project Manager',
      department: 'Operations',
      status: 'active',
      lastLogin: '2024-01-14 16:45:00',
      createdAt: '2023-03-01'
    }
  ]);

  const [businessRules, setBusinessRules] = useState<BusinessRule[]>([
    {
      id: '1',
      name: 'High-Value Opportunity Auto Alert',
      type: 'notification',
      condition: 'opportunity_value > 100000',
      action: 'notify_sales_director',
      status: 'active',
      priority: 'high'
    },
    {
      id: '2',
      name: 'Long-Term Stagnant Opportunity Marking',
      type: 'automation',
      condition: 'days_since_last_update > 30',
      action: 'mark_as_stale',
      status: 'active',
      priority: 'medium'
    },
    {
      id: '3',
      name: 'Contract Approval Workflow',
      type: 'workflow',
      condition: 'contract_value > 50000',
      action: 'require_manager_approval',
      status: 'active',
      priority: 'high'
    }
  ]);

  const [integrations, setIntegrations] = useState<Integration[]>([
    {
      id: '1',
      name: 'Salesforce CRM',
      type: 'CRM',
      status: 'connected',
      lastSync: '2024-01-15 10:00:00',
      syncStatus: 'success'
    },
    {
      id: '2',
      name: 'SAP ERP',
      type: 'ERP',
      status: 'connected',
      lastSync: '2024-01-15 09:30:00',
      syncStatus: 'success'
    },
    {
      id: '3',
      name: 'Outlook Email',
      type: 'Email',
      status: 'connected',
      lastSync: '2024-01-15 10:15:00',
      syncStatus: 'success'
    },
    {
      id: '4',
      name: 'QuickBooks',
      type: 'Finance',
      status: 'error',
      lastSync: '2024-01-14 15:22:00',
      syncStatus: 'failed'
    }
  ]);

  const systemMetrics: SystemMetric[] = [
    {
      name: 'CPU Usage',
      value: 45,
      unit: '%',
      status: 'normal',
      trend: 'stable',
      description: 'Server CPU usage status'
    },
    {
      name: 'Memory Usage',
      value: 68,
      unit: '%',
      status: 'warning',
      trend: 'up',
      description: 'Server memory usage status'
    },
    {
      name: 'Disk Usage',
      value: 82,
      unit: '%',
      status: 'critical',
      trend: 'up',
      description: 'Server disk usage status'
    },
    {
      name: 'Database Connections',
      value: 23,
      unit: '',
      status: 'normal',
      trend: 'stable',
      description: 'Current active database connections'
    }
  ];

  const userColumns = [
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
      render: (text: string, record: User) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color={role.includes('Administrator') ? 'red' : role.includes('Manager') ? 'blue' : 'green'}>
          {role}
        </Tag>
      )
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : status === 'inactive' ? 'orange' : 'red'}>
          {status === 'active' ? 'Active' : status === 'inactive' ? 'Inactive' : 'Suspended'}
        </Tag>
      )
    },
    {
      title: 'Last Login',
      dataIndex: 'lastLogin',
      key: 'lastLogin'
    },
    {
      title: 'Actions',
      key: 'action',
      render: (text: any, record: User) => (
        <Space>
          <Button 
            type="link" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewUser(record)}
          >
            View
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditUser(record)}
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteUser(record)}
          >
            Delete
          </Button>
        </Space>
      )
    }
  ];

  const ruleColumns = [
    {
      title: 'Rule Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const colorMap = {
          probability: 'blue',
          workflow: 'green',
          notification: 'orange',
          automation: 'purple'
        };
        const textMap = {
          probability: 'Probability',
          workflow: 'Workflow',
          notification: 'Notification',
          automation: 'Automation'
        };
        return <Tag color={colorMap[type as keyof typeof colorMap]}>{textMap[type as keyof typeof textMap]}</Tag>;
      }
    },
    {
      title: 'Condition',
      dataIndex: 'condition',
      key: 'condition',
      render: (text: string) => <Text code>{text}</Text>
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={priority === 'high' ? 'red' : priority === 'medium' ? 'orange' : 'green'}>
          {priority === 'high' ? 'High' : priority === 'medium' ? 'Medium' : 'Low'}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? 'Active' : 'Inactive'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'action',
      render: (text: any, record: BusinessRule) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditRule(record)}
          >
            Edit
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteRule(record)}
          >
            Delete
          </Button>
        </Space>
      )
    }
  ];

  const integrationColumns = [
    {
      title: 'Integration Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Integration) => (
        <Space>
          <Avatar 
            icon={
              record.type === 'CRM' ? <TeamOutlined /> :
              record.type === 'ERP' ? <DatabaseOutlined /> :
              record.type === 'Email' ? <NotificationOutlined /> :
              <ApiOutlined />
            } 
          />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag>{type}</Tag>
    },
    {
      title: 'Connection Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge 
          status={status === 'connected' ? 'success' : status === 'disconnected' ? 'default' : 'error'} 
          text={status === 'connected' ? 'Connected' : status === 'disconnected' ? 'Disconnected' : 'Error'}
        />
      )
    },
    {
      title: 'Sync Status',
      dataIndex: 'syncStatus',
      key: 'syncStatus',
      render: (syncStatus: string) => (
        <Tag color={syncStatus === 'success' ? 'green' : syncStatus === 'failed' ? 'red' : 'blue'}>
          {syncStatus === 'success' ? 'Success' : syncStatus === 'failed' ? 'Failed' : 'Running'}
        </Tag>
      )
    },
    {
      title: 'Last Sync',
      dataIndex: 'lastSync',
      key: 'lastSync'
    },
    {
      title: 'Actions',
      key: 'action',
      render: (text: any, record: Integration) => (
        <Space>
          <Button 
            type="link" 
            icon={<SyncOutlined />} 
            size="small"
            onClick={() => handleSyncIntegration(record)}
          >
            Sync
          </Button>
          <Button 
            type="link" 
            icon={<SettingOutlined />} 
            size="small"
            onClick={() => handleConfigureIntegration(record)}
          >
            Configure
          </Button>
          <Button 
            type="link" 
            danger 
            icon={<DeleteOutlined />} 
            size="small"
            onClick={() => handleDeleteIntegration(record)}
          >
            Delete
          </Button>
        </Space>
      )
    }
  ];

  // 事件处理函数
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setDrawerVisible(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    form.setFieldsValue(user);
    setModalVisible(true);
  };

  const handleDeleteUser = (user: User) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete user ${user.username}?`,
      onOk: () => {
        setUsers(users.filter(u => u.id !== user.id));
        message.success('User deleted successfully');
      }
    });
  };

  const handleEditRule = (rule: BusinessRule) => {
    setSelectedRule(rule);
    form.setFieldsValue(rule);
    setModalVisible(true);
  };

  const handleDeleteRule = (rule: BusinessRule) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete rule ${rule.name}?`,
      onOk: () => {
        setBusinessRules(businessRules.filter(r => r.id !== rule.id));
        message.success('Rule deleted successfully');
      }
    });
  };

  const handleSyncIntegration = (integration: Integration) => {
    message.info(`Syncing ${integration.name}...`);
    // This should call the actual sync API
  };

  const handleConfigureIntegration = (integration: Integration) => {
    message.info(`Configuring ${integration.name}...`);
    // This should open the configuration page
  };

  const handleDeleteIntegration = (integration: Integration) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete integration ${integration.name}?`,
      onOk: () => {
        setIntegrations(integrations.filter(i => i.id !== integration.id));
        message.success('Integration deleted successfully');
      }
    });
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (activeTab === 'users') {
        if (selectedUser) {
          // Update user
          setUsers(users.map(u => u.id === selectedUser.id ? { ...u, ...values } : u));
          message.success('User updated successfully');
        } else {
          // Create new user
          const newUser: User = {
            id: Date.now().toString(),
            ...values,
            status: 'active',
            lastLogin: '-',
            createdAt: new Date().toISOString().split('T')[0]
          };
          setUsers([...users, newUser]);
          message.success('User created successfully');
        }
      } else if (activeTab === 'business') {
        if (selectedRule) {
          // Update rule
          setBusinessRules(businessRules.map(r => r.id === selectedRule.id ? { ...r, ...values } : r));
          message.success('Rule updated successfully');
        } else {
          // Create new rule
          const newRule: BusinessRule = {
            id: Date.now().toString(),
            ...values,
            status: 'active'
          };
          setBusinessRules([...businessRules, newRule]);
          message.success('Rule created successfully');
        }
      }
      setModalVisible(false);
      setSelectedUser(null);
      setSelectedRule(null);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setModalVisible(false);
    setSelectedUser(null);
    setSelectedRule(null);
    form.resetFields();
  };

  return (
    <div className="ltc-stage-content">
      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          tabBarExtraContent={
            activeTab === 'users' || activeTab === 'business' ? (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                                 {activeTab === 'users' ? 'Add User' : 'Add Rule'}
              </Button>
            ) : null
          }
        >
                     {/* User Management */}
          <TabPane 
            tab={
              <span>
                <UserOutlined />
                User Management
              </span>
            } 
            key="users"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                             <Alert
                message="User Management"
                description="Manage system users, role permissions and organizational structure. Ensure user access rights follow the principle of least privilege."
                type="info"
                showIcon
              />
              
                             <Row gutter={16} style={{ marginBottom: '24px' }}>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Total Users" 
                      value={users.length} 
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Active Users" 
                      value={users.filter(u => u.status === 'active').length} 
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Administrators" 
                      value={users.filter(u => u.role.includes('Administrator')).length} 
                      valueStyle={{ color: '#fa541c' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card style={{ minWidth: '200px' }}>
                    <Statistic 
                      title="Today's Logins" 
                      value={2} 
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Table 
                columns={userColumns} 
                dataSource={users} 
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            </Space>
          </TabPane>

                     {/* Business Rules Configuration */}
          <TabPane 
            tab={
              <span>
                <BranchesOutlined />
                Business Rules
              </span>
            } 
            key="business"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                             <Alert
                message="Business Rules Configuration"
                description="Configure business rules in LTC process, including probability calculation, workflow processes, automation tasks and notification rules."
                type="info"
                showIcon
              />
              
                             <Row gutter={16} style={{ marginBottom: '24px' }}>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Total Rules" 
                      value={businessRules.length} 
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Active Rules" 
                      value={businessRules.filter(r => r.status === 'active').length} 
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="High Priority" 
                      value={businessRules.filter(r => r.priority === 'high').length} 
                      valueStyle={{ color: '#fa541c' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card style={{ minWidth: '200px' }}>
                    <Statistic 
                      title="Automation Rules" 
                      value={businessRules.filter(r => r.type === 'automation').length} 
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Table 
                columns={ruleColumns} 
                dataSource={businessRules} 
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            </Space>
          </TabPane>

                     {/* Integration Management */}
          <TabPane 
            tab={
              <span>
                <ApiOutlined />
                Integration Management
              </span>
            } 
            key="integrations"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                             <Alert
                message="Third-party System Integration"
                description="Manage integrations with third-party platforms such as CRM, ERP, email systems. Ensure data synchronization accuracy and timeliness."
                type="info"
                showIcon
              />
              
                             <Row gutter={16} style={{ marginBottom: '24px' }}>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Total Integrations" 
                      value={integrations.length} 
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Connected" 
                      value={integrations.filter(i => i.status === 'connected').length}
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic 
                      title="Connection Errors" 
                      value={integrations.filter(i => i.status === 'error').length}
                      valueStyle={{ color: '#fa541c' }}
                    />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card style={{ minWidth: '200px' }}>
                    <Statistic 
                      title="Sync Success Rate" 
                      value={Math.round(integrations.filter(i => i.syncStatus === 'success').length / integrations.length * 100)}
                      suffix="%"
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Table 
                columns={integrationColumns} 
                dataSource={integrations} 
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            </Space>
          </TabPane>

                     {/* System Monitoring */}
          <TabPane 
            tab={
              <span>
                <MonitorOutlined />
                System Monitoring
              </span>
            } 
            key="monitoring"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                             <Alert
                message="System Performance Monitoring"
                description="Real-time monitoring of system performance metrics to ensure stable operation of SmartLTC platform."
                type="info"
                showIcon
              />
              
              <Row gutter={16}>
                {systemMetrics.map((metric, index) => (
                  <Col span={6} key={index}>
                    <Card>
                      <Statistic
                        title={metric.name}
                        value={metric.value}
                        suffix={metric.unit}
                        valueStyle={{
                          color: metric.status === 'normal' ? '#3f8600' : 
                                 metric.status === 'warning' ? '#faad14' : '#cf1322'
                        }}
                      />
                      <Progress 
                        percent={metric.unit === '%' ? metric.value : (metric.value / 100) * 100}
                        status={
                          metric.status === 'normal' ? 'success' : 
                          metric.status === 'warning' ? 'active' : 'exception'
                        }
                        showInfo={false}
                        size="small"
                      />
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        {metric.description}
                      </Text>
                    </Card>
                  </Col>
                ))}
              </Row>
              
                             <Card title="System Activity Logs" extra={<Button icon={<ReloadOutlined />} size="small">Refresh</Button>}>
                <List
                  itemLayout="horizontal"
                                     dataSource={[
                     {
                       title: 'User Login',
                       description: 'admin user logged into system',
                       time: '10:30:00',
                       type: 'info'
                     },
                     {
                       title: 'Data Sync',
                       description: 'Salesforce CRM data synchronization completed',
                       time: '10:00:00',
                       type: 'success'
                     },
                     {
                       title: 'System Warning',
                       description: 'Memory usage exceeded warning threshold',
                       time: '09:45:00',
                       type: 'warning'
                     },
                     {
                       title: 'Integration Error',
                       description: 'QuickBooks connection failed',
                       time: '09:30:00',
                       type: 'error'
                     }
                   ]}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            icon={
                              item.type === 'success' ? <CheckCircleOutlined /> :
                              item.type === 'warning' ? <WarningOutlined /> :
                              item.type === 'error' ? <CloseCircleOutlined /> :
                              <HistoryOutlined />
                            }
                            style={{
                              backgroundColor: 
                                item.type === 'success' ? '#52c41a' :
                                item.type === 'warning' ? '#faad14' :
                                item.type === 'error' ? '#ff4d4f' :
                                '#1890ff'
                            }}
                          />
                        }
                        title={item.title}
                        description={item.description}
                      />
                      <div>{item.time}</div>
                    </List.Item>
                  )}
                />
              </Card>
            </Space>
          </TabPane>

                     {/* Security & Compliance */}
          <TabPane 
            tab={
              <span>
                <SecurityScanOutlined />
                Security & Compliance
              </span>
            } 
            key="security"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Alert
                message="Security & Compliance Management"
                description="Configure system security policies, data access control and compliance requirements to ensure compliance with industry standards and regulatory requirements."
                type="info"
                showIcon
              />
              
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Security Policies" size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>Password Complexity Requirements</span>
                        <Switch defaultChecked />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>Two-Factor Authentication</span>
                        <Switch defaultChecked />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>Session Timeout</span>
                        <Switch defaultChecked />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>IP Whitelist</span>
                        <Switch />
                      </div>
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Compliance Checks" size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }} size="middle">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>GDPR Compliance</span>
                        <Tag color="green">Passed</Tag>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>SOX Compliance</span>
                        <Tag color="green">Passed</Tag>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>Data Audit</span>
                        <Tag color="orange">In Progress</Tag>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '8px 0' }}>
                        <span style={{ fontWeight: 500 }}>Security Scan</span>
                        <Tag color="green">Completed</Tag>
                      </div>
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Access Control" size="small" style={{ height: '100%' }}>
                    <Table
                      size="small"
                      columns={[
                        { title: 'Resource', dataIndex: 'resource', key: 'resource' },
                        { title: 'Role', dataIndex: 'role', key: 'role' },
                        { title: 'Permissions', dataIndex: 'permission', key: 'permission', render: (permissions: string[]) => (
                            <Space>
                              {permissions.map(p => <Tag key={p}>{p}</Tag>)}
                            </Space>
                          )
                        },
                        { title: 'Status', dataIndex: 'status', key: 'status', render: (status: string) => (
                            <Tag color={status === 'active' ? 'green' : 'red'}>{status === 'active' ? 'Active' : 'Inactive'}</Tag>
                          )
                        }
                      ]}
                      dataSource={[
                        { key: '1', resource: 'Opportunity Management', role: 'Sales Manager', permission: ['View', 'Edit', 'Delete'], status: 'active' },
                        { key: '2', resource: 'Contract Management', role: 'Legal', permission: ['View', 'Approve'], status: 'active' },
                        { key: '3', resource: 'Financial Data', role: 'Finance', permission: ['View', 'Edit'], status: 'active' },
                        { key: '4', resource: 'System Settings', role: 'Administrator', permission: ['Full Access'], status: 'active' }
                      ]}
                      pagination={false}
                    />
                  </Card>
                </Col>
              </Row>
            </Space>
          </TabPane>

          {/* Data Management */}
          <TabPane 
            tab={
              <span>
                <DatabaseOutlined />
                Data Management
              </span>
            } 
            key="data"
          >
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Alert
                message="Data Management & Backup"
                description="Manage system data, configure backup strategies, data cleanup and migration tasks."
                type="info"
                showIcon
              />
              
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Data Statistics" size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Statistic title="Opportunity Data" value={1234} suffix="records" valueStyle={{ color: '#1890ff' }} />
                      <Statistic title="Client Data" value={567} suffix="records" valueStyle={{ color: '#52c41a' }} />
                      <Statistic title="Contract Data" value={89} suffix="records" valueStyle={{ color: '#fa541c' }} />
                      <Statistic title="Document Data" value={2345} suffix="files" valueStyle={{ color: '#722ed1' }} />
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Backup Status" size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Last Backup</span>
                        <Text>2024-01-15 02:00</Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Backup Size</span>
                        <Text>2.5 GB</Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Backup Status</span>
                        <Tag color="green">Success</Tag>
                      </div>
                      <Button type="primary" size="small" block style={{ marginTop: '16px' }}>Backup Now</Button>
                    </Space>
                  </Card>
                </Col>
                <Col xs={24} sm={24} md={8} lg={8} xl={8}>
                  <Card title="Data Cleanup" size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Temporary Files</span>
                        <Text>156 MB</Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Log Files</span>
                        <Text>890 MB</Text>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 500 }}>Cache Data</span>
                        <Text>234 MB</Text>
                      </div>
                      <Button type="primary" danger size="small" block style={{ marginTop: '16px' }}>Clean Data</Button>
                    </Space>
                  </Card>
                </Col>
              </Row>
              

            </Space>
          </TabPane>
        </Tabs>
      </Card>

      {/* Modal - For adding/editing users and business rules */}
      <Modal
        title={
          activeTab === 'users' 
            ? (selectedUser ? 'Edit User' : 'Add User')
            : (selectedRule ? 'Edit Rule' : 'Add Business Rule')
        }
        visible={modalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          {activeTab === 'users' && (
            <>
              <Form.Item name="username" label="Username" rules={[{ required: true }]}>
                <Input prefix={<UserOutlined />} />
              </Form.Item>
              <Form.Item name="email" label="Email" rules={[{ required: true, type: 'email' }]}>
                <Input prefix={<NotificationOutlined />} />
              </Form.Item>
              <Form.Item name="role" label="Role" rules={[{ required: true }]}>
                <Select>
                  <Option value="System Administrator">System Administrator</Option>
                  <Option value="Sales Manager">Sales Manager</Option>
                  <Option value="Project Manager">Project Manager</Option>
                  <Option value="Sales Representative">Sales Representative</Option>
                  <Option value="Finance Manager">Finance Manager</Option>
                </Select>
              </Form.Item>
              <Form.Item name="department" label="Department" rules={[{ required: true }]}>
                <Select
                  placeholder="Select department"
                  mode="tags"
                  maxTagCount={1}
                  showSearch={true}
                >
                  <Option value="Executive Office">Executive Office</Option>
                  <Option value="Information Technology">Information Technology</Option>
                  <Option value="Software Development">Software Development</Option>
                  <Option value="Sales">Sales</Option>
                  <Option value="Marketing">Marketing</Option>
                  <Option value="Operations">Operations</Option>
                  <Option value="Finance">Finance</Option>
                  <Option value="Human Resources">Human Resources</Option>
                  <Option value="Legal">Legal</Option>
                  <Option value="Customer Service">Customer Service</Option>
                </Select>
              </Form.Item>
            </>
          )}
          
          {activeTab === 'business' && (
            <>
              <Form.Item name="name" label="Rule Name" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="type" label="Rule Type" rules={[{ required: true }]}>
                <Select>
                  <Option value="probability">Probability Calculation</Option>
                  <Option value="workflow">Workflow</Option>
                  <Option value="notification">Notification</Option>
                  <Option value="automation">Automation</Option>
                </Select>
              </Form.Item>
              <Form.Item name="condition" label="Trigger Condition" rules={[{ required: true }]}>
                <TextArea rows={3} placeholder="e.g.: opportunity_value > 100000" />
              </Form.Item>
              <Form.Item name="action" label="Action" rules={[{ required: true }]}>
                <TextArea rows={3} placeholder="e.g.: notify_sales_director" />
              </Form.Item>
              <Form.Item name="priority" label="Priority" rules={[{ required: true }]}>
                <Select>
                  <Option value="high">High</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="low">Low</Option>
                </Select>
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Drawer - For viewing user details */}
      <Drawer
        title="User Details"
        placement="right"
        onClose={() => setDrawerVisible(false)}
        visible={drawerVisible}
        width={400}
      >
        {selectedUser && (
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Card size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Username:</Text>
                  <Text>{selectedUser.username}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Email:</Text>
                  <Text>{selectedUser.email}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Role:</Text>
                  <Tag>{selectedUser.role}</Tag>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Department:</Text>
                  <Text>{selectedUser.department}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Status:</Text>
                  <Tag color={selectedUser.status === 'active' ? 'green' : 'red'}>
                    {selectedUser.status === 'active' ? 'Active' : 'Inactive'}
                  </Tag>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Last Login:</Text>
                  <Text>{selectedUser.lastLogin}</Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Text strong>Created At:</Text>
                  <Text>{selectedUser.createdAt}</Text>
                </div>
              </Space>
            </Card>
            
            <Card title="Activity Log" size="small">
              <List
                size="small"
                dataSource={[
                  { action: 'Login System', time: '2024-01-15 10:30:00' },
                  { action: 'View Opportunity List', time: '2024-01-15 10:25:00' },
                  { action: 'Update Client Information', time: '2024-01-15 10:20:00' },
                  { action: 'Create New Opportunity', time: '2024-01-15 10:15:00' }
                ]}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      title={item.action}
                      description={item.time}
                    />
                  </List.Item>
                )}
              />
            </Card>
          </Space>
        )}
      </Drawer>
    </div>
  );
};

export default Settings;
