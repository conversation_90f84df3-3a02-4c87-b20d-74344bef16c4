import React, { useState, useEffect } from 'react';
import { Card, Button } from 'antd';
import { FinanceStage } from '../ltc-stages';
import type { Project, ProjectStage } from '../../types';
import { PlusOutlined, DollarOutlined } from '@ant-design/icons';
import FinanceStepIndicator from './FinanceStepIndicator';
import FinancePage from './FinancePage';

interface FinanceModuleProps {
  project?: Project;
  stages?: ProjectStage[];
  onSave?: (data: any) => void;
}

const FinanceModule: React.FC<FinanceModuleProps> = ({ project, stages, onSave }) => {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [financeSteps, setFinanceSteps] = useState<{ key: string; title: string }[]>([
    { key: 'overview', title: 'Finance Overview' }
  ]);

  // Find all finance-related stages
  useEffect(() => {
    if (stages && stages.length > 0) {
      const financeStages = stages.filter(stage => stage.type === 'finance');

      if (financeStages.length > 0) {
        const newSteps = [
          { key: 'overview', title: 'Finance Overview' },
          ...financeStages.map((stage, index) => ({
            key: stage.id,
            title: `Finance ${index + 1}`
          }))
        ];
        setFinanceSteps(newSteps);
      }
    }
  }, [stages]);

  const handleAddFinanceStep = () => {
    // Create a new finance step
    const newStepKey = `new-finance-${Date.now()}`;
    const newSteps = [...financeSteps, { key: newStepKey, title: `Finance ${financeSteps.length}` }];
    setFinanceSteps(newSteps);
    setCurrentStep(newSteps.length - 1);
  };

  const handleSaveFinanceData = (data: any) => {
    if (onSave) {
      onSave({
        ...data,
        type: 'finance',
        status: data.status || 'in_progress',
      });
    }
  };

  const renderFinanceStage = (stageId?: string) => {
    // 传递project数据给FinancePage组件
    return <FinancePage project={project} />;
  };

  return (
    <div className="finance-module-wrapper">
      <div style={{ marginTop: 0 }}>
        {renderFinanceStage(financeSteps[currentStep]?.key)}
      </div>
    </div>
  );
};

export default FinanceModule;
