import React, { useState } from 'react';
import { Card, Button } from 'antd';
import '../../styles/project-profit-dashboard.css';

// 定义财务数据类型
interface FinancialData {
  label: string;
  value: number;
  formattedValue: string;
  percent: string;
  percentValue: number;
  textColorClass: string;
  bgColorClass: string;
  backgroundColor: string;
  borderColor: string;
}

const ProjectProfitDashboard: React.FC = () => {
  // 视图状态
  const [activeView, setActiveView] = useState<'chart' | 'table'>('chart');

  // 财务数据 - 新项目从空白状态开始，不再设置示例数据
  const financialData: FinancialData[] = [];

  return (
    <div className="project-profit-dashboard">
      <h1 className="text-xl font-semibold mb-4">Project Profitability Dashboard</h1>

      {/* 1. 摘要卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {financialData.map((item, idx) => (
          <div key={idx} className="bg-white rounded-md shadow p-4 flex flex-col">
            <span className="text-sm font-medium text-gray-600 mb-2">{item.label}</span>
            <span className={`text-xl font-bold ${item.textColorClass}`}>{item.formattedValue}</span>
            <span className={`text-sm font-semibold ${item.textColorClass}`}>{item.percent}</span>
          </div>
        ))}
      </div>

      {/* 2. 利润结构条 */}
      <Card className="mb-6">
        <h2 className="text-base font-semibold text-gray-800 mb-4">Profit Structure</h2>

        {/* 水平分段条 */}
        <div className="w-full mb-4">
          <div className="flex w-full h-12 rounded-lg overflow-hidden shadow-sm">
            <div className="flex items-center justify-center text-gray-800 font-semibold text-base w-[60%]" style={{ backgroundColor: 'rgba(148, 163, 184, 0.3)' }}>
              Direct Costs 60%
            </div>
            <div className="flex items-center justify-center text-gray-800 font-semibold text-base w-[20%]" style={{ backgroundColor: 'rgba(203, 213, 225, 0.3)' }}>
              Indirect Costs 20%
            </div>
            <div className="flex items-center justify-center text-gray-800 font-semibold text-base w-[20%]" style={{ backgroundColor: 'rgba(71, 85, 105, 0.3)' }}>
              Net Profit 20%
            </div>
          </div>
        </div>

        {/* 图例 */}
        <div className="flex flex-wrap gap-4 mt-3">
          {financialData.map((item, idx) => (
            <div key={idx} className="flex items-center">
              <div className={`w-4 h-4 rounded ${item.bgColorClass} mr-2`}></div>
              <span className="text-sm">{item.label} ({item.percent})</span>
            </div>
          ))}
        </div>
      </Card>

      {/* 3. 成本明细 - 图表和表格视图 */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-base font-semibold text-gray-800">Cost Breakdown</h2>
          <div className="view-toggle">
            <Button
              type={activeView === 'chart' ? 'primary' : 'default'}
              size="small"
              className={`toggle-btn ${activeView === 'chart' ? 'active' : ''}`}
              onClick={() => setActiveView('chart')}
            >
              Chart View
            </Button>
            <Button
              type={activeView === 'table' ? 'primary' : 'default'}
              size="small"
              className={`toggle-btn ${activeView === 'table' ? 'active' : ''}`}
              onClick={() => setActiveView('table')}
            >
              Table View
            </Button>
          </div>
        </div>

        {/* 图表视图 - 瀑布图样式 */}
        <div style={{ display: activeView === 'chart' ? 'block' : 'none', height: '400px' }}>
          <div className="waterfall-chart">
            {/* Y轴刻度 */}
            <div className="y-axis">
              <div className="y-tick">€100,000</div>
              <div className="y-tick">€80,000</div>
              <div className="y-tick">€60,000</div>
              <div className="y-tick">€40,000</div>
              <div className="y-tick">€20,000</div>
              <div className="y-tick zero-tick">€0</div>
              <div className="y-tick">€-20,000</div>
              <div className="y-tick">€-40,000</div>
              <div className="y-tick">€-60,000</div>
            </div>

            {/* 图表内容 */}
            <div className="chart-content">
              {/* 水平网格线 - 只保留零线上方的网格线 */}
              <div className="grid-lines">
                <div className="grid-line"></div>
                <div className="grid-line"></div>
                <div className="grid-line"></div>
                <div className="grid-line"></div>
                <div className="grid-line"></div>
                <div className="grid-line zero-line"></div>
                {/* 移除零线下方的网格线 */}
              </div>

              {/* 瀑布图 */}
              <div className="waterfall-container">
                {/* 总收入柱 */}
                <div className="waterfall-column" style={{ left: '8%', width: '14%' }}>
                  <div className="waterfall-bar revenue" style={{ height: '150px', top: '100px' }}>
                    <div className="bar-value">€100,000</div>
                  </div>
                  <div className="waterfall-label">Total Revenue</div>
                </div>

                {/* 直接成本柱 - 负值 */}
                <div className="waterfall-column" style={{ left: '28%', width: '14%' }}>
                  <div className="waterfall-bar cost" style={{ height: '90px', top: '250px' }}>
                    <div className="bar-value">€-60,000</div>
                  </div>
                  <div className="waterfall-label">Direct Costs</div>
                </div>

                {/* 间接成本柱 - 负值 */}
                <div className="waterfall-column" style={{ left: '48%', width: '14%' }}>
                  <div className="waterfall-bar cost" style={{ height: '30px', top: '250px', backgroundColor: 'rgba(233, 137, 137, 0.8)' }}>
                    <div className="bar-value">€-20,000</div>
                  </div>
                  <div className="waterfall-label">Indirect Costs</div>
                </div>

                {/* 净利润柱 - 正值 */}
                <div className="waterfall-column" style={{ left: '68%', width: '14%' }}>
                  <div className="waterfall-bar profit" style={{ height: '30px', top: '220px' }}>
                    <div className="bar-value">€20,000</div>
                  </div>
                  <div className="waterfall-label">Net Profit</div>
                </div>

                {/* 毛利润柱 - 正值 */}
                <div className="waterfall-column" style={{ left: '88%', width: '14%' }}>
                  <div className="waterfall-bar gross-profit" style={{ height: '60px', top: '190px' }}>
                    <div className="bar-value">€40,000</div>
                  </div>
                  <div className="waterfall-label">Gross Profit</div>
                </div>

                {/* 零线 - 加粗显示 */}
                <div className="zero-line-main" style={{ height: '2px', backgroundColor: 'rgba(0, 0, 0, 0.7)' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* 表格视图 */}
        <div style={{ display: activeView === 'table' ? 'block' : 'none' }}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">Category</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">Amount (€)</th>
                  <th className="px-4 py-3 text-left text-sm font-semibold text-gray-600">% of Revenue</th>
                </tr>
              </thead>
              <tbody>
                {financialData.map((item, idx) => (
                  <tr key={idx} className="hover:bg-gray-50">
                    <td className="border-t px-4 py-3 text-sm"><strong>{item.label}</strong></td>
                    <td className={`border-t px-4 py-3 text-sm ${item.textColorClass}`}>{item.formattedValue}</td>
                    <td className="border-t px-4 py-3 text-sm">{item.percent}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProjectProfitDashboard;
