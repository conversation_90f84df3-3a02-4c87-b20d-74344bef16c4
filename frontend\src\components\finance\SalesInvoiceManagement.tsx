import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  message, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Select, 
  DatePicker, 
  Row, 
  Col, 
  Tooltip, 
  Popconfirm, 
  Tag,
  Statistic
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  DollarOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined,
  SyncOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { SalesInvoice, Project } from '../../types';
import financeDataSync from '../../services/financeDataSync';
import dayjs from 'dayjs';

interface SalesInvoiceManagementProps {
  project?: Project;
}

const SalesInvoiceManagement: React.FC<SalesInvoiceManagementProps> = ({ project }) => {
  const [invoices, setInvoices] = useState<SalesInvoice[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState<SalesInvoice | null>(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  
  const projectId = project?.id;

  // 加载数据
  useEffect(() => {
    loadInvoices();
  }, [projectId]);

  const loadInvoices = async () => {
    setLoading(true);
    try {
      // 获取所有发票并按项目过滤
      const allInvoices = financeDataSync.getAllSalesInvoices();
      let filteredInvoices = allInvoices;
      
      if (projectId) {
        filteredInvoices = allInvoices.filter(invoice => invoice.projectId === projectId);
      }
      
      setInvoices(filteredInvoices);
    } catch (error) {
      console.error('Failed to load invoices:', error);
      message.error('Failed to load sales invoices');
    } finally {
      setLoading(false);
    }
  };

  // 计算汇总数据
  const calculateInvoiceSummary = (invoices: SalesInvoice[]) => {
    const totalInvoices = invoices.length;
    const totalAmount = invoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);
    const paidAmount = invoices.reduce((sum, invoice) => sum + (invoice.paidAmount || 0), 0);
    const pendingAmount = totalAmount - paidAmount;
    
    const paidInvoices = invoices.filter(invoice => invoice.paymentStatus === 'paid').length;
    const unpaidInvoices = invoices.filter(invoice => invoice.paymentStatus === 'unpaid').length;
    const partialInvoices = invoices.filter(invoice => invoice.paymentStatus === 'partial').length;
    
    const overdueInvoices = invoices.filter(invoice => {
      return invoice.paymentStatus !== 'paid' && dayjs().isAfter(dayjs(invoice.dueDate));
    }).length;

    return {
      totalInvoices,
      totalAmount,
      paidAmount,
      pendingAmount,
      paidInvoices,
      unpaidInvoices,
      partialInvoices,
      overdueInvoices
    };
  };

  const summary = calculateInvoiceSummary(invoices);

  // 显示模态框
  const showInvoiceModal = (invoice?: SalesInvoice) => {
    if (invoice) {
      setEditingInvoice(invoice);
      form.setFieldsValue({
        ...invoice,
        invoiceDate: invoice.invoiceDate ? dayjs(invoice.invoiceDate) : null,
        dueDate: invoice.dueDate ? dayjs(invoice.dueDate) : null,
      });
    } else {
      setEditingInvoice(null);
      form.resetFields();
      
      // 为新发票设置默认值
      const invoiceNumber = financeDataSync.generateInvoiceNumber();
      form.setFieldsValue({
        invoiceNumber,
        vatRate: 21,
        paymentStatus: 'unpaid',
        invoiceDate: dayjs(),
        dueDate: dayjs().add(30, 'day'),
      });
    }
    setIsModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingInvoice(null);
    form.resetFields();
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 计算总金额（含VAT）
      const vatAmount = values.amount * (values.vatRate / 100);
      const totalAmount = values.amount + vatAmount;
      
      const invoiceData: Partial<SalesInvoice> = {
        ...values,
        totalAmount,
        vatAmount,
        invoiceDate: values.invoiceDate ? values.invoiceDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        dueDate: values.dueDate ? values.dueDate.format('YYYY-MM-DD') : dayjs().add(14, 'day').format('YYYY-MM-DD'),
        projectId: projectId || '',
        createdAt: editingInvoice?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (editingInvoice) {
        // 更新发票
        await financeDataSync.updateSalesInvoice(editingInvoice.id, invoiceData);
        message.success('Sales invoice updated successfully');
      } else {
        // 创建新发票
        await financeDataSync.createSalesInvoice(invoiceData);
        message.success('Sales invoice created successfully');
      }

      handleCancel();
      loadInvoices();
    } catch (error) {
      console.error('Failed to save invoice:', error);
      message.error('Failed to save sales invoice');
    }
  };

  // 删除发票
  const handleDeleteInvoice = async (invoiceId: string) => {
    try {
      await financeDataSync.deleteSalesInvoice(invoiceId);
      message.success('Sales invoice deleted successfully');
      loadInvoices();
    } catch (error) {
      console.error('Failed to delete invoice:', error);
      message.error('Failed to delete sales invoice');
    }
  };

  // 手动同步
  const handleManualSync = async () => {
    setLoading(true);
    try {
      if (projectId) {
        await financeDataSync.syncProjectRevenueProgress(projectId);
        message.success('Revenue data synchronized successfully');
      } else {
        message.warning('Please select a project to sync');
      }
      loadInvoices();
    } catch (error) {
      console.error('Sync failed:', error);
      message.error('Synchronization failed');
    } finally {
      setLoading(false);
    }
  };

  // 获取支付状态显示
  const getPaymentStatusDisplay = (status: string) => {
    const statusMap = {
      'paid': { color: 'success', icon: <CheckCircleOutlined />, text: 'Paid' },
      'partial': { color: 'warning', icon: <ClockCircleOutlined />, text: 'Partial' },
      'unpaid': { color: 'error', icon: <ExclamationCircleOutlined />, text: 'Unpaid' },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || statusMap.unpaid;
    return <Tag color={config.color} icon={config.icon}>{config.text}</Tag>;
  };

  // 格式化货币
  const formatCurrency = (amount: number, currency = 'EUR') => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  return (
    <div style={{ padding: '0px', fontFamily: 'var(--font-family)' }}>
      {/* 汇总卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Total Invoices"
              value={summary.totalInvoices}
              prefix={<BarChartOutlined style={{ color: '#3B82F6' }} />}
              valueStyle={{ color: '#3B82F6', fontSize: '20px', fontWeight: '600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Total Amount"
              value={summary.totalAmount}
              prefix={<DollarOutlined style={{ color: '#10B981' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#10B981', fontSize: '20px', fontWeight: '600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Paid Amount"
              value={summary.paidAmount}
              prefix={<CheckCircleOutlined style={{ color: '#059669' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#059669', fontSize: '20px', fontWeight: '600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Pending Amount"
              value={summary.pendingAmount}
              prefix={<ClockCircleOutlined style={{ color: '#F59E0B' }} />}
              formatter={(value) => formatCurrency(Number(value))}
              valueStyle={{ color: '#F59E0B', fontSize: '20px', fontWeight: '600' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细状态卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Paid Invoices"
              value={summary.paidInvoices}
              valueStyle={{ color: '#10B981', fontSize: '18px', fontWeight: '500' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Unpaid Invoices"
              value={summary.unpaidInvoices}
              valueStyle={{ color: '#EF4444', fontSize: '18px', fontWeight: '500' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Partial Payments"
              value={summary.partialInvoices}
              valueStyle={{ color: '#F59E0B', fontSize: '18px', fontWeight: '500' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small" style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}>
            <Statistic
              title="Overdue Invoices"
              value={summary.overdueInvoices}
              valueStyle={{ color: '#DC2626', fontSize: '18px', fontWeight: '500' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 操作按钮和表格 */}
      <Card 
        title="Sales Invoice Management" 
        style={{ borderRadius: 'var(--radius-md)', border: 'var(--border)' }}
        extra={
          <Space>
            <Button 
              type="default" 
              icon={<SyncOutlined />} 
              onClick={handleManualSync}
              loading={loading}
              style={{ fontSize: '14px' }}
            >
              Sync Data
            </Button>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              onClick={() => showInvoiceModal()}
              style={{ fontSize: '14px' }}
            >
              New Invoice
            </Button>
          </Space>
        }
      >
        {/* 表格容器 */}
        <div style={{ 
          border: '1px solid #f0f0f0', 
          borderRadius: '6px', 
          overflow: 'hidden',
          marginTop: '16px'
        }}>
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#fafafa' }}>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Invoice No.</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Revenue Item</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Amount (Excl. VAT)</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Total Amount</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Invoice Date</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Due Date</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Status</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderRight: '1px solid #f0f0f0', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Paid Amount</th>
                  <th style={{ padding: '12px', textAlign: 'left', fontSize: '12px', fontWeight: '600', color: '#374151' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {invoices.length === 0 ? (
                  <tr>
                    <td colSpan={9} style={{ padding: '40px', textAlign: 'center', color: '#6B7280' }}>
                      <BarChartOutlined style={{ fontSize: '24px', marginBottom: '8px', display: 'block' }} />
                      No sales invoices found
                    </td>
                  </tr>
                ) : (
                  invoices.map((invoice, index) => (
                    <tr key={invoice.id} style={{ borderBottom: index < invoices.length - 1 ? '1px solid #F3F4F6' : 'none' }}>
                      <td style={{ padding: '8px 12px', color: '#374151', fontWeight: '500', fontSize: '12px' }}>{invoice.invoiceNumber}</td>
                      <td style={{ padding: '8px 12px', color: '#374151', fontSize: '12px' }}>{invoice.revenueItem}</td>
                      <td style={{ padding: '8px 12px', color: '#3B82F6', fontWeight: '600', fontSize: '12px' }}>{formatCurrency(invoice.amount)}</td>
                      <td style={{ padding: '8px 12px', color: '#374151', fontWeight: '500', fontSize: '12px' }}>{formatCurrency(invoice.totalAmount)}</td>
                      <td style={{ padding: '8px 12px', color: '#6B7280', fontSize: '12px' }}>{invoice.invoiceDate}</td>
                      <td style={{ padding: '8px 12px', color: '#6B7280', fontSize: '12px' }}>{invoice.dueDate}</td>
                      <td style={{ padding: '8px 12px' }}>{getPaymentStatusDisplay(invoice.paymentStatus)}</td>
                      <td style={{ padding: '8px 12px', color: '#10B981', fontWeight: '500', fontSize: '12px' }}>{formatCurrency(invoice.paidAmount || 0)}</td>
                      <td style={{ padding: '8px 12px' }}>
                        <Space size="small">
                          <Tooltip title="Edit">
                            <Button 
                              type="text" 
                              icon={<EditOutlined />} 
                              size="small"
                              onClick={() => showInvoiceModal(invoice)}
                              style={{ fontSize: '12px' }}
                            />
                          </Tooltip>
                          <Tooltip title="Delete">
                            <Popconfirm
                              title="Delete Sales Invoice"
                              description="Are you sure you want to delete this sales invoice?"
                              onConfirm={() => handleDeleteInvoice(invoice.id)}
                              okText="Yes"
                              cancelText="No"
                            >
                              <Button 
                                type="text" 
                                icon={<DeleteOutlined />} 
                                size="small"
                                danger
                                style={{ fontSize: '12px' }}
                              />
                            </Popconfirm>
                          </Tooltip>
                        </Space>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </Card>

      {/* 新增/编辑发票模态框 */}
      <Modal
        title={editingInvoice ? 'Edit Sales Invoice' : 'New Sales Invoice'}
        visible={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button key="submit" type="primary" onClick={handleSubmit}>
            {editingInvoice ? 'Update' : 'Create'}
          </Button>
        ]}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ fontSize: '14px' }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Invoice Number"
                name="invoiceNumber"
                rules={[{ required: true, message: 'Please input invoice number!' }]}
              >
                <Input placeholder="INV-2025-001" style={{ fontSize: '14px' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Revenue Item"
                name="revenueItem"
                rules={[{ required: true, message: 'Please input revenue item!' }]}
              >
                <Input placeholder="NRC-WEB, MRC-HOSTING, etc." style={{ fontSize: '14px' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Amount (Excl. VAT)"
                name="amount"
                rules={[{ required: true, message: 'Please input amount!' }]}
              >
                <InputNumber
                  style={{ width: '100%', fontSize: '14px' }}
                  formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/€\s?|(,*)/g, '') as any}
                  min={0}
                  step={0.01}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="VAT Rate (%)"
                name="vatRate"
                rules={[{ required: true, message: 'Please input VAT rate!' }]}
              >
                <InputNumber
                  style={{ width: '100%', fontSize: '14px' }}
                  min={0}
                  max={100}
                  step={0.1}
                  precision={1}
                  formatter={value => `${value}%`}
                  parser={value => value!.replace('%', '') as any}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Payment Status"
                name="paymentStatus"
                rules={[{ required: true, message: 'Please select payment status!' }]}
              >
                <Select style={{ fontSize: '14px' }}>
                  <Select.Option value="unpaid">Unpaid</Select.Option>
                  <Select.Option value="partial">Partial Payment</Select.Option>
                  <Select.Option value="paid">Paid</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Invoice Date"
                name="invoiceDate"
                rules={[{ required: true, message: 'Please select invoice date!' }]}
              >
                <DatePicker style={{ width: '100%', fontSize: '14px' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Due Date"
                name="dueDate"
                rules={[{ required: true, message: 'Please select due date!' }]}
              >
                <DatePicker style={{ width: '100%', fontSize: '14px' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Paid Amount"
                name="paidAmount"
              >
                <InputNumber
                  style={{ width: '100%', fontSize: '14px' }}
                  formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/€\s?|(,*)/g, '') as any}
                  min={0}
                  step={0.01}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            label="Description"
            name="description"
          >
            <Input.TextArea 
              placeholder="Invoice description or notes"
              rows={3}
              style={{ fontSize: '14px' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SalesInvoiceManagement; 