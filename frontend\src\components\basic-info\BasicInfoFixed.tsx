import React, { useState, useEffect } from "react";
import { Card, Form, Input, Select, Row, Col, Button, message, Alert } from 'antd';
import { SaveOutlined } from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { Option } = Select;

interface BasicInfoProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
  id?: string;
}

const BasicInfoFixed: React.FC<BasicInfoProps> = ({ project, stage, onSave, onProceed, id }) => {
  const [clientForm] = Form.useForm();
  const [dataLoaded, setDataLoaded] = useState(false);
  const [debugInfo, setDebugInfo] = useState('');

  // 🚀 核心修复：统一数据加载逻辑，避免Form状态冲突
  useEffect(() => {
    console.log('=== BasicInfoFixed loading data ===');
    setDebugInfo('Loading data...');
    
    const loadData = async () => {
      try {
        let formData = {
          clientName: '',
          clientTier: 'a',
          address: '',
          keyStakeholder: '',
          contactPhone: '',
          email: ''
        };
        
        // 项目数据映射
        if (project) {
          formData = {
            ...formData,
            clientName: project.client || '',
            clientTier: project.tier === 'S' ? 'svip' : 
                       project.tier === 'V' ? 'vip' : 
                       project.tier === 'B' ? 'ba' : 'a'
          };
          setDebugInfo(`Project data mapped: ${project.client}`);
        }
        
        // localStorage数据
        if (id) {
          try {
            const localData = localStorage.getItem(`project_stages_${id}`);
            if (localData) {
              const stages = JSON.parse(localData);
              const basicStage = stages.find((s: any) => s.type === 'basic_info');
              if (basicStage?.data) {
                const stageData = typeof basicStage.data === 'string' ? 
                  JSON.parse(basicStage.data) : basicStage.data;
                formData = { ...formData, ...stageData };
                setDebugInfo(`localStorage data loaded`);
              }
            }
          } catch (e) {
            console.warn('localStorage error:', e);
          }
        }
        
        // 延迟设置表单数据，确保DOM就绪
        setTimeout(() => {
          console.log('Setting form data:', formData);
          clientForm.setFieldsValue(formData);
          setDataLoaded(true);
          setDebugInfo(`Data loaded successfully. Client: ${formData.clientName}`);
        }, 200);
        
      } catch (error) {
        console.error('Error loading data:', error);
        setDebugInfo(`Error: ${error}`);
      }
    };
    
    loadData();
  }, [project?.id, id]);

  // 🔧 强制启用所有输入框
  useEffect(() => {
    if (!dataLoaded) return;
    
    const forceEnable = () => {
      // 添加CSS强制样式
      const style = document.createElement('style');
      style.textContent = `
        .basic-info-fixed input,
        .basic-info-fixed textarea {
          pointer-events: auto !important;
          cursor: text !important;
          background-color: white !important;
          color: rgba(0, 0, 0, 0.88) !important;
          opacity: 1 !important;
        }
      `;
      document.head.appendChild(style);
      
      // DOM操作强制启用
      const inputs = document.querySelectorAll('.basic-info-fixed input, .basic-info-fixed textarea');
      inputs.forEach((input: any) => {
        input.disabled = false;
        input.readOnly = false;
        input.removeAttribute('disabled');
        input.removeAttribute('readonly');
        input.style.pointerEvents = 'auto';
        input.style.cursor = 'text';
        input.style.backgroundColor = 'white';
      });
      
      console.log(`✅ Force enabled ${inputs.length} inputs`);
      setDebugInfo(prev => `${prev} | Enabled ${inputs.length} inputs`);
    };
    
    setTimeout(forceEnable, 100);
  }, [dataLoaded]);

  const handleSave = async () => {
    try {
      const values = await clientForm.validateFields();
      console.log('Saving form data:', values);
      
      if (onSave) {
        onSave(values);
      }
      
      // 保存到localStorage
      if (id) {
        const stageKey = `project_stages_${id}`;
        const existingStages = JSON.parse(localStorage.getItem(stageKey) || '[]');
        const basicStage = {
          id: `basic_info_${Date.now()}`,
          projectId: id,
          type: 'basic_info',
          data: JSON.stringify(values),
          updatedAt: new Date().toISOString()
        };
        
        const updatedStages = existingStages.filter((s: any) => s.type !== 'basic_info');
        updatedStages.push(basicStage);
        localStorage.setItem(stageKey, JSON.stringify(updatedStages));
      }
      
      message.success('Information saved successfully!');
    } catch (error) {
      console.error('Save error:', error);
      message.error('Failed to save information');
    }
  };

  return (
    <div className="basic-info-fixed">
      <Card 
        title="Basic Information (Fixed Version)"
        extra={
          <Button 
            size="small"
            onClick={() => {
              const inputs = document.querySelectorAll('.basic-info-fixed input');
              inputs.forEach((input: any) => {
                input.disabled = false;
                input.style.pointerEvents = 'auto';
                input.style.cursor = 'text';
                input.style.backgroundColor = 'white';
              });
              message.success('Manual enable completed!');
            }}
          >
            🔧 Manual Enable
          </Button>
        }
      >
        <Alert
          message="Debug Information"
          description={debugInfo}
          type="info"
          style={{ marginBottom: 20 }}
        />
        
        <Form form={clientForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="clientName" label="Client Name">
                <Input 
                  placeholder="Enter client name"
                  onChange={(e) => {
                    console.log('✅ Client name changed:', e.target.value);
                    setDebugInfo(prev => `${prev} | Client name: ${e.target.value}`);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="clientTier" label="Client Tier">
                <Select placeholder="Select tier">
                  <Option value="svip">SVIP</Option>
                  <Option value="vip">VIP</Option>
                  <Option value="ba">BA</Option>
                  <Option value="a">A</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="address" label="Address">
                <Input 
                  placeholder="Enter address"
                  onChange={(e) => {
                    console.log('✅ Address changed:', e.target.value);
                    setDebugInfo(prev => `${prev} | Address: ${e.target.value}`);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="keyStakeholder" label="Key Stakeholder">
                <Input 
                  placeholder="Enter key stakeholder"
                  onChange={(e) => {
                    console.log('✅ Key stakeholder changed:', e.target.value);
                    setDebugInfo(prev => `${prev} | Stakeholder: ${e.target.value}`);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="contactPhone" label="Contact Phone">
                <Input 
                  placeholder="Enter contact phone"
                  onChange={(e) => {
                    console.log('✅ Contact phone changed:', e.target.value);
                    setDebugInfo(prev => `${prev} | Phone: ${e.target.value}`);
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="email" label="Email">
                <Input 
                  placeholder="Enter email"
                  onChange={(e) => {
                    console.log('✅ Email changed:', e.target.value);
                    setDebugInfo(prev => `${prev} | Email: ${e.target.value}`);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
        
        <div style={{ marginTop: 20, textAlign: 'right' }}>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            onClick={handleSave}
          >
            Save Information
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default BasicInfoFixed;
