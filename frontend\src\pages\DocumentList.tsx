import React, { useEffect, useState } from 'react';
import { Card, Table, Space, Input, Select, Button, Tag, message, Modal, Form, Upload, DatePicker } from 'antd';
import { SearchOutlined, PlusOutlined, DeleteOutlined, DownloadOutlined, FileOutlined, FilePdfOutlined, FileExcelOutlined, FileWordOutlined, FileImageOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import documentService from '../services/document.service';
import projectService from '../services/new-project.service';
import { Document, Project } from '../types';
import moment from 'moment';

const { Option } = Select;
const { confirm } = Modal;
const { Dragger } = Upload;

const DocumentList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    fetchDocuments();
    fetchProjects();
  }, []);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const data = await documentService.getAllDocuments();
      setDocuments(data);
    } catch (error) {
      console.error('Error fetching documents:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      const data = await projectService.getAllProjects();
      setProjects(data);
    } catch (error) {
      console.error('Error fetching projects:', error);
      message.error('获取项目列表失败');
    }
  };

  const handleDelete = (id: string) => {
    confirm({
      title: '确认删除',
      content: '您确定要删除这个文档吗？此操作不可逆。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await documentService.deleteDocument(id);
          message.success('文档已删除');
          fetchDocuments();
        } catch (error) {
          console.error('Error deleting document:', error);
          message.error('删除文档失败');
        }
      },
    });
  };

  const showModal = () => {
    setIsModalVisible(true);
    form.resetFields();
    setFileList([]);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleUpload = async () => {
    try {
      const values = await form.validateFields();
      
      if (fileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }
      
      setUploading(true);
      
      // 上传文件
      const file = fileList[0].originFileObj;
      const uploadResult = await documentService.uploadFile(file);
      
      // 创建文档记录
      const documentData = {
        name: values.name || file.name,
        description: values.description,
        projectId: values.projectId,
        category: values.category,
        ...uploadResult
      };
      
      await documentService.createDocument(documentData);
      
      message.success('文档上传成功');
      setIsModalVisible(false);
      fetchDocuments();
    } catch (error) {
      console.error('Error uploading document:', error);
      message.error('文档上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 过滤文档
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = 
      doc.name.toLowerCase().includes(searchText.toLowerCase()) ||
      (doc.description && doc.description.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || doc.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FilePdfOutlined style={{ fontSize: 20, color: '#f5222d' }} />;
    } else if (fileType.includes('excel') || fileType.includes('sheet') || fileType.includes('csv')) {
      return <FileExcelOutlined style={{ fontSize: 20, color: '#52c41a' }} />;
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return <FileWordOutlined style={{ fontSize: 20, color: '#1890ff' }} />;
    } else if (fileType.includes('image')) {
      return <FileImageOutlined style={{ fontSize: 20, color: '#722ed1' }} />;
    } else {
      return <FileOutlined style={{ fontSize: 20 }} />;
    }
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Document) => (
        <Space>
          {getFileIcon(record.fileType)}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => {
        let color = '';
        let text = '';
        
        switch (category) {
          case 'contract':
            color = 'blue';
            text = '合同';
            break;
          case 'proposal':
            color = 'green';
            text = '提案';
            break;
          case 'report':
            color = 'purple';
            text = '报告';
            break;
          case 'invoice':
            color = 'orange';
            text = '发票';
            break;
          default:
            color = 'default';
            text = '其他';
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '项目',
      dataIndex: 'project',
      key: 'project',
      render: (project: Project) => (
        project ? <a onClick={() => navigate(`/projects/${project.id}`)}>{project.name}</a> : '未关联项目'
      ),
    },
    {
      title: '上传者',
      dataIndex: 'uploadedBy',
      key: 'uploadedBy',
      render: (user: any) => user.username,
    },
    {
      title: '上传日期',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => moment(date).format('YYYY-MM-DD'),
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number) => {
        if (size < 1024) {
          return `${size} B`;
        } else if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(2)} KB`;
        } else {
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        }
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Document) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<DownloadOutlined />} 
            onClick={() => window.open(record.fileUrl, '_blank')}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  const uploadProps = {
    onRemove: (file: any) => {
      setFileList([]);
    },
    beforeUpload: (file: any) => {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        message.error('文件大小不能超过20MB!');
        return false;
      }
      setFileList([file]);
      return false;
    },
    fileList,
  };

  return (
    <div>
      <Card>
        <Space style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索文档"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200 }}
          />
          <Select 
            defaultValue="all" 
            style={{ width: 120 }}
            value={categoryFilter}
            onChange={(value) => setCategoryFilter(value)}
          >
            <Option value="all">所有类别</Option>
            <Option value="contract">合同</Option>
            <Option value="proposal">提案</Option>
            <Option value="report">报告</Option>
            <Option value="invoice">发票</Option>
            <Option value="other">其他</Option>
          </Select>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={showModal}
          >
            上传文档
          </Button>
        </Space>
        <Table 
          columns={columns} 
          dataSource={filteredDocuments} 
          rowKey="id"
          loading={loading}
        />
      </Card>

      <Modal
        title="上传文档"
        visible={isModalVisible}
        onCancel={handleCancel}
        onOk={handleUpload}
        okText="上传"
        cancelText="取消"
        confirmLoading={uploading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="文档名称"
          >
            <Input placeholder="留空将使用文件名" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} />
          </Form.Item>
          <Form.Item
            name="projectId"
            label="关联项目"
            rules={[{ required: true, message: '请选择关联项目' }]}
          >
            <Select placeholder="选择项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>{project.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="category"
            label="文档类别"
            rules={[{ required: true, message: '请选择文档类别' }]}
          >
            <Select placeholder="选择类别">
              <Option value="contract">合同</Option>
              <Option value="proposal">提案</Option>
              <Option value="report">报告</Option>
              <Option value="invoice">发票</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item label="文件">
            <Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <FileOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传 (最大上传文件大小 20MB)</p>
              <p className="ant-upload-hint">支持单个文件上传 (最大20MB)</p>
            </Dragger>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DocumentList;
