/* Closeout Stage Styles */

.closeout-section {
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.closeout-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #e8e8e8;
}

.closeout-section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.closeout-section-content {
  padding: 16px;
  background-color: #fff;
}

.toggle-button {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.toggle-button:hover {
  background-color: #f0f0f0;
  border-color: #d9d9d9;
}

.toggle-button span {
  font-size: 16px;
  line-height: 1;
  color: #1890ff;
}

/* Progress styles */
.progress-container {
  text-align: center;
  padding: 16px 0;
}

.progress-info {
  margin-top: 16px;
}

.progress-info h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.progress-info p {
  color: rgba(0, 0, 0, 0.65);
  margin: 0;
}

/* Checklist styles */
.checklist-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.checklist-item-completed {
  text-decoration: line-through;
  color: rgba(0, 0, 0, 0.45);
}

/* Form styles */
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 流转按钮区域 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.btn-workflow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px !important;
  font-weight: 500;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-workflow:hover {
  background-color: #e6e6e6;
  border-color: #d9d9d9;
}

.btn-workflow .anticon {
  transition: transform 0.3s ease;
}

.btn-workflow:hover .anticon {
  transform: translateX(3px);
}
