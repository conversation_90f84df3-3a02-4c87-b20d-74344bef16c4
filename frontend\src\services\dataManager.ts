/**
 * 🚀 紧急数据管理器 (Emergency Data Manager)
 * 立即修复当前数据架构的关键问题
 * 
 * 解决的紧急问题：
 * 1. 统一存储键命名
 * 2. 数据同步一致性
 * 3. 基础缓存机制
 * 4. 错误处理增强
 */

import { Project, ProjectStage } from '../types';

export interface DataManagerConfig {
  cacheTimeout: number;
  autoSaveInterval: number;
  maxRetries: number;
  enableDebugLogs: boolean;
}

class DataManager {
  private static instance: DataManager;
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private pendingOperations = new Map<string, Promise<any>>();
  
  // 🔧 统一存储键命名 - 解决存储混乱问题
  private readonly STORAGE_KEYS = {
    PROJECTS: 'dm_projects_v2',
    STAGES: 'dm_project_stages_v2',
    METADATA: 'dm_metadata_v2'
  } as const;

  private readonly config: DataManagerConfig = {
    cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
    autoSaveInterval: 30 * 1000,  // 30秒自动保存（优化后）
    maxRetries: 3,
    enableDebugLogs: true
  };

  private constructor() {
    this.initializeDataManager();
  }

  public static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  /**
   * 🚀 初始化数据管理器
   */
  private initializeDataManager(): void {
    try {
      this.log('🔄 Initializing Emergency Data Manager...');
      
      // 1. 迁移旧数据
      this.migrateOldData();
      
      // 2. 设置错误监听
      this.setupErrorHandling();
      
      // 3. 验证数据完整性
      this.validateDataIntegrity();
      
      this.log('✅ Emergency Data Manager initialized successfully');
    } catch (error) {
      console.error('❌ DataManager initialization failed:', error);
      this.handleCriticalError(error);
    }
  }

  /**
   * 📦 迁移旧数据 - 解决存储键混乱问题
   */
  private migrateOldData(): void {
    this.log('🔄 Starting data migration...');
    
    try {
      // 迁移项目数据
      const projectSources = ['projects', 'ltc_projects', 'dal_projects'];
      const allProjects: Project[] = [];
      
      projectSources.forEach(source => {
        try {
          const data = this.getFromStorage(source, []);
          if (Array.isArray(data) && data.length > 0) {
            allProjects.push(...data);
            this.log(`📦 Migrated ${data.length} projects from ${source}`);
          }
        } catch (error) {
          this.log(`⚠️ Failed to migrate from ${source}: ${error}`);
        }
      });

      // 去重和保存
      if (allProjects.length > 0) {
        const uniqueProjects = this.deduplicateById(allProjects);
        this.setToStorage(this.STORAGE_KEYS.PROJECTS, uniqueProjects);
        this.log(`✅ Migrated ${uniqueProjects.length} unique projects`);
      }

      // 迁移阶段数据
      this.migrateStageData();
      
      // 清理旧数据
      this.cleanupOldStorage();
      
    } catch (error) {
      console.error('❌ Data migration failed:', error);
      throw error;
    }
  }

  /**
   * 📋 迁移阶段数据
   */
  private migrateStageData(): void {
    const stageKeys = Object.keys(localStorage).filter(key => key.startsWith('project_stages_'));
    const consolidatedStages: Record<string, ProjectStage[]> = {};
    
    stageKeys.forEach(key => {
      try {
        const projectId = key.replace('project_stages_', '');
        const stages = this.getFromStorage(key, []);
        
        if (Array.isArray(stages) && stages.length > 0) {
          consolidatedStages[projectId] = stages;
        }
      } catch (error) {
        this.log(`⚠️ Failed to migrate stages from ${key}: ${error}`);
      }
    });

    if (Object.keys(consolidatedStages).length > 0) {
      this.setToStorage(this.STORAGE_KEYS.STAGES, consolidatedStages);
      this.log(`✅ Migrated stages for ${Object.keys(consolidatedStages).length} projects`);
    }
  }

  /**
   * 🧹 清理旧存储
   */
  private cleanupOldStorage(): void {
    const keysToCleanup = [
      'projects', 'ltc_projects', 'dal_projects', 'ltc_clients', 'dal_clients'
    ];
    
    keysToCleanup.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        this.log(`🧹 Cleaned up old key: ${key}`);
      }
    });
    
    // 清理旧的project_stages_*键
    Object.keys(localStorage)
      .filter(key => key.startsWith('project_stages_'))
      .forEach(key => {
        localStorage.removeItem(key);
        this.log(`🧹 Cleaned up old stage key: ${key}`);
      });
  }

  /**
   * 🏗️ 项目数据操作 - 原子性操作解决一致性问题
   */
  
  public getAllProjects(): Project[] {
    const cacheKey = 'all_projects';
    
    // 检查缓存
    const cached = this.getFromCache<Project[]>(cacheKey);
    if (cached) {
      return cached;
    }
    
    // 从存储加载
    const projects = this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []);
    this.setToCache(cacheKey, projects, this.config.cacheTimeout);
    
    return projects;
  }

  public getProjectById(id: string): Project | null {
    if (!id) return null;
    
    const cacheKey = `project_${id}`;
    const cached = this.getFromCache<Project>(cacheKey);
    if (cached) return cached;
    
    const projects = this.getAllProjects();
    const project = projects.find(p => p.id === id);
    
    if (project) {
      this.setToCache(cacheKey, project, this.config.cacheTimeout);
    }
    
    return project || null;
  }

  /**
   * 💾 原子性保存项目 - 解决数据一致性问题
   */
  public async saveProject(project: Project): Promise<boolean> {
    const operationId = `save_project_${project.id}_${Date.now()}`;
    
    // 防止重复操作
    if (this.pendingOperations.has(operationId)) {
      return this.pendingOperations.get(operationId)!;
    }
    
    const operation = this.executeSaveProject(project);
    this.pendingOperations.set(operationId, operation);
    
    try {
      const result = await operation;
      this.pendingOperations.delete(operationId);
      return result;
    } catch (error) {
      this.pendingOperations.delete(operationId);
      throw error;
    }
  }

  private async executeSaveProject(project: Project): Promise<boolean> {
    try {
      this.log(`💾 Saving project: ${project.id}`);
      
      // 1. 验证数据
      if (!this.validateProject(project)) {
        throw new Error('Project validation failed');
      }
      
      // 2. 获取当前数据
      const projects = this.getAllProjects();
      const existingIndex = projects.findIndex(p => p.id === project.id);
      
      // 3. 标准化项目数据
      const normalizedProject: Project = {
        ...project,
        updatedAt: new Date().toISOString()
      };
      
      // 4. 原子性更新
      const updatedProjects = [...projects];
      if (existingIndex >= 0) {
        updatedProjects[existingIndex] = normalizedProject;
      } else {
        updatedProjects.unshift(normalizedProject);
      }
      
      // 5. 保存到存储
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, updatedProjects);
      
      // 6. 清理相关缓存
      this.invalidateCache(['all_projects', `project_${project.id}`]);
      
      this.log(`✅ Project saved successfully: ${project.id}`);
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to save project ${project.id}:`, error);
      return false;
    }
  }

  /**
   * 📋 项目阶段操作 - 统一阶段数据管理
   */
  
  public getProjectStages(projectId: string): ProjectStage[] {
    if (!projectId) return [];
    
    const cacheKey = `stages_${projectId}`;
    const cached = this.getFromCache<ProjectStage[]>(cacheKey);
    if (cached) return cached;
    
    const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    const stages = allStages[projectId] || [];
    
    this.setToCache(cacheKey, stages, this.config.cacheTimeout);
    return stages;
  }

  /**
   * 💾 原子性保存阶段数据
   */
  public async saveProjectStages(projectId: string, stages: ProjectStage[]): Promise<boolean> {
    try {
      this.log(`💾 Saving stages for project: ${projectId}`);
      
      // 1. 获取所有阶段数据
      const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      
      // 2. 更新特定项目的阶段
      allStages[projectId] = stages.map(stage => ({
        ...stage,
        updatedAt: new Date().toISOString()
      }));
      
      // 3. 原子性保存
      this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
      
      // 4. 清理缓存
      this.invalidateCache([`stages_${projectId}`]);
      
      this.log(`✅ Stages saved successfully for project: ${projectId}`);
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to save stages for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * 🔧 同步项目和阶段数据 - 解决数据一致性问题
   */
  public async syncProjectAndStages(projectId: string, projectData?: Partial<Project>, stageData?: Partial<ProjectStage>): Promise<boolean> {
    try {
      this.log(`🔄 Syncing project and stages: ${projectId}`);
      
      let success = true;
      
      // 同步项目数据
      if (projectData) {
        const project = this.getProjectById(projectId);
        if (project) {
          const updatedProject = { ...project, ...projectData };
          success = await this.saveProject(updatedProject);
        }
      }
      
      // 同步阶段数据
      if (stageData && success) {
        const stages = this.getProjectStages(projectId);
        const updatedStages = stages.map(stage => 
          stage.type === stageData.type ? { ...stage, ...stageData } : stage
        );
        success = await this.saveProjectStages(projectId, updatedStages);
      }
      
      this.log(`${success ? '✅' : '❌'} Sync completed for project: ${projectId}`);
      return success;
      
    } catch (error) {
      console.error(`❌ Sync failed for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * 🚨 错误处理和恢复机制
   */
  
  private setupErrorHandling(): void {
    // 监听存储错误
    window.addEventListener('storage', (e) => {
      if (e.key?.startsWith('dm_')) {
        this.log(`🔄 Storage changed: ${e.key}`);
        this.invalidateCache(['all_projects', 'all_stages']);
      }
    });
    
    // 监听页面卸载
    window.addEventListener('beforeunload', () => {
      this.flushPendingOperations();
    });
  }

  private handleCriticalError(error: any): void {
    console.error('🚨 Critical DataManager error:', error);
    
    // 尝试恢复基本功能
    try {
      this.cache.clear();
      this.log('🔧 Cache cleared, attempting recovery...');
    } catch (recoveryError) {
      console.error('❌ Recovery failed:', recoveryError);
    }
  }

  private flushPendingOperations(): void {
    if (this.pendingOperations.size > 0) {
      this.log(`⚠️ ${this.pendingOperations.size} pending operations during unload`);
    }
  }

  /**
   * 🔍 数据验证
   */
  
  private validateProject(project: Project): boolean {
    return !!(
      project.id &&
      project.name &&
      project.client &&
      typeof project.revenue === 'number'
    );
  }

  private validateDataIntegrity(): void {
    try {
      const projects = this.getAllProjects();
      const stages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      
      let issueCount = 0;
      
      // 检查项目数据
      projects.forEach(project => {
        if (!this.validateProject(project)) {
          issueCount++;
          this.log(`⚠️ Invalid project data: ${project.id}`);
        }
      });
      
      // 检查阶段数据
      Object.keys(stages).forEach(projectId => {
        if (!projects.find(p => p.id === projectId)) {
          issueCount++;
          this.log(`⚠️ Orphaned stages for project: ${projectId}`);
        }
      });
      
      this.log(`${issueCount === 0 ? '✅' : '⚠️'} Data integrity check: ${issueCount} issues found`);
      
    } catch (error) {
      console.error('❌ Data integrity check failed:', error);
    }
  }

  /**
   * 🧰 工具方法
   */
  
  private deduplicateById<T extends { id: string }>(items: T[]): T[] {
    const seen = new Set<string>();
    return items.filter(item => {
      if (seen.has(item.id)) return false;
      seen.add(item.id);
      return true;
    });
  }

  private getFromCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data as T;
  }

  private setToCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private invalidateCache(keys: string[]): void {
    keys.forEach(key => this.cache.delete(key));
  }

  private getFromStorage(key: string, defaultValue: any = null): any {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      this.log(`⚠️ Failed to read storage key ${key}: ${error}`);
      return defaultValue;
    }
  }

  private setToStorage(key: string, value: any): void {
    try {
      const serialized = JSON.stringify(value);
      localStorage.setItem(key, serialized);
    } catch (error) {
      console.error(`❌ Failed to write storage key ${key}:`, error);
      throw error;
    }
  }

  private log(message: string): void {
    if (this.config.enableDebugLogs) {
      console.log(`[DataManager] ${message}`);
    }
  }

  /**
   * 🎯 公共API
   */
  
  public getSystemStatus(): any {
    const projects = this.getAllProjects();
    const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    
    return {
      projectsCount: projects.length,
      stagesCount: Object.keys(allStages).length,
      cacheSize: this.cache.size,
      pendingOperations: this.pendingOperations.size,
      storageKeys: Object.values(this.STORAGE_KEYS),
      lastCheck: new Date().toISOString()
    };
  }

  public clearAllData(): boolean {
    try {
      Object.values(this.STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      this.cache.clear();
      this.pendingOperations.clear();
      this.log('🧹 All data cleared');
      return true;
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
      return false;
    }
  }

  public exportData(): any {
    return {
      projects: this.getAllProjects(),
      stages: this.getFromStorage(this.STORAGE_KEYS.STAGES, {}),
      metadata: this.getFromStorage(this.STORAGE_KEYS.METADATA, {}),
      timestamp: new Date().toISOString()
    };
  }
}

// 导出单例实例
export const dataManager = DataManager.getInstance();
export default dataManager; 