# Basic Information 修复故障排除指南

## 🔍 检查修复是否生效

### 1. 浏览器缓存清理
**如果界面没有变化，请先清理浏览器缓存：**

1. **Chrome/Edge**：
   - 按 `Ctrl + Shift + Delete`
   - 选择"缓存的图片和文件"
   - 点击"清除数据"

2. **或者使用硬刷新**：
   - 按 `Ctrl + F5` 或 `Ctrl + Shift + R`

### 2. 服务器重启验证
**已执行的重启操作：**
- ✅ 停止所有Node.js进程
- ✅ 重新启动前端服务器（npm start）
- ✅ 重新启动后端服务器（npm run dev）

### 3. 调试步骤

#### 步骤1：检查控制台日志
1. 打开浏览器开发者工具（F12）
2. 进入任意项目详情页面
3. 点击Basic Info阶段
4. 查看控制台，应该看到：
   ```
   BasicInfoStage useEffect triggered
   Project data: {client: "...", tier: "...", ...}
   Auto-mapping project data to form fields...
   Auto-mapped form data: {...}
   ✅ Successfully auto-filled form with project data
   ```

#### 步骤2：验证数据映射
在控制台中，检查映射的数据是否正确：
- `clientName` = 项目的client字段
- `clientTier` = S→SVIP, V→VIP, B→BA, A→A
- `country` = 项目的country字段
- `primaryContact` = 项目的owner字段
- `industry` = 根据category推断

#### 步骤3：检查UI图标清理
查看Basic Info阶段的4个标签页：
- Client Information（应该没有👤图标）
- Opportunity Information（应该没有📋图标）
- Solution & Requirements（应该没有📄图标）
- Project Timeline（应该没有📅图标）

## 🛠 如果问题仍然存在

### 问题1：项目数据没有自动填充
**可能原因：**
1. 项目数据为空或格式不正确
2. 项目ID无法找到对应数据
3. 数据映射逻辑错误

**调试方法：**
1. 检查控制台是否有项目数据输出
2. 验证项目的tier字段格式（应该是S/V/B/A，不是T1/T2/T3）
3. 确认项目有基础字段：client, name, revenue等

### 问题2：界面图标仍然显示
**可能原因：**
1. 浏览器缓存未清理
2. 组件未重新编译
3. 代码修改未保存

**解决方案：**
1. 强制刷新浏览器（Ctrl+Shift+R）
2. 检查编译状态是否有错误
3. 重新启动开发服务器

### 问题3：字段映射不正确
**检查映射规则：**
```typescript
// 客户等级映射
project.tier === 'S' ? 'SVIP' : 
project.tier === 'V' ? 'VIP' : 
project.tier === 'B' ? 'BA' : 
project.tier === 'A' ? 'A' : 'A'

// 行业推断
project.category === 'Software' ? 'Technology' :
project.category === 'Service' ? 'Financial Services' :
project.category === 'Product' ? 'Manufacturing' : 'Technology'
```

## 📝 测试用例

### 创建测试项目
1. 进入Opportunity页面
2. 点击"Create New Opportunity" 
3. 填写基础信息：
   - Name: "Test Project"
   - Client: "Test Client"
   - Tier: "S"
   - Category: "Software"
   - Revenue: 100000
   - Country: "Germany"
   - Owner: "John Doe"

4. 保存项目
5. 进入项目详情页
6. 点击Basic Info阶段

### 预期结果
**Client Information标签应自动填充：**
- Client Name: "Test Client"
- Client Tier: "SVIP"（因为tier是S）
- Industry: "Technology"（因为category是Software）
- Country: "Germany"
- Primary Contact: "John Doe"

**Opportunity Information标签应自动填充：**
- Opportunity Name: "Test Project"
- Total Value: €100,000
- Priority: "High"（因为tier是S）

## 🎯 验证清单

- [ ] 浏览器缓存已清理
- [ ] 服务器已重启
- [ ] 控制台显示项目数据加载成功
- [ ] Client Information表单自动填充
- [ ] Opportunity Information表单自动填充
- [ ] 标签页图标已移除（干净简洁）
- [ ] 数据映射逻辑正确（S→SVIP等）
- [ ] 没有控制台错误信息

如果所有项目都勾选完成，说明修复成功！如果仍有问题，请提供具体的错误信息或控制台输出。 