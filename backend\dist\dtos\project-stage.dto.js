"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectIdParamDto = exports.ProjectStageIdParamDto = exports.UpdateProjectStageDto = exports.CreateProjectStageDto = exports.ProjectStageType = exports.ProjectStageStatus = void 0;
const class_validator_1 = require("class-validator");
var ProjectStageStatus;
(function (ProjectStageStatus) {
    ProjectStageStatus["NOT_STARTED"] = "not_started";
    ProjectStageStatus["IN_PROGRESS"] = "in_progress";
    ProjectStageStatus["COMPLETED"] = "completed";
    ProjectStageStatus["PENDING"] = "pending";
    ProjectStageStatus["APPROVED"] = "approved";
    ProjectStageStatus["REJECTED"] = "rejected";
})(ProjectStageStatus = exports.ProjectStageStatus || (exports.ProjectStageStatus = {}));
var ProjectStageType;
(function (ProjectStageType) {
    ProjectStageType["BASIC_INFO"] = "basic_info";
    ProjectStageType["PROPOSAL"] = "proposal";
    ProjectStageType["CONTRACT"] = "contract";
    ProjectStageType["EXECUTION"] = "execution";
    ProjectStageType["ACCEPTANCE"] = "acceptance";
    ProjectStageType["FINANCE"] = "finance";
    ProjectStageType["CLOSEOUT"] = "closeout";
    ProjectStageType["AFTER_SALES"] = "after_sales";
})(ProjectStageType = exports.ProjectStageType || (exports.ProjectStageType = {}));
class CreateProjectStageDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '项目ID不能为空' }),
    (0, class_validator_1.IsUUID)('4', { message: '项目ID必须是有效的UUID' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "projectId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '阶段名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '阶段名称必须是字符串' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '阶段类型不能为空' }),
    (0, class_validator_1.IsEnum)(ProjectStageType, { message: '阶段类型无效' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '阶段状态不能为空' }),
    (0, class_validator_1.IsEnum)(ProjectStageStatus, { message: '阶段状态无效' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '开始日期格式无效' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '结束日期格式无效' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '数据必须是JSON字符串' }),
    __metadata("design:type", String)
], CreateProjectStageDto.prototype, "data", void 0);
exports.CreateProjectStageDto = CreateProjectStageDto;
class UpdateProjectStageDto {
}
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '阶段名称必须是字符串' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ProjectStageStatus, { message: '阶段状态无效' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '开始日期格式无效' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '结束日期格式无效' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '数据必须是JSON字符串' }),
    __metadata("design:type", String)
], UpdateProjectStageDto.prototype, "data", void 0);
exports.UpdateProjectStageDto = UpdateProjectStageDto;
class ProjectStageIdParamDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '阶段ID不能为空' }),
    (0, class_validator_1.IsUUID)('4', { message: '阶段ID必须是有效的UUID' }),
    __metadata("design:type", String)
], ProjectStageIdParamDto.prototype, "id", void 0);
exports.ProjectStageIdParamDto = ProjectStageIdParamDto;
class ProjectIdParamDto {
}
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: '项目ID不能为空' }),
    (0, class_validator_1.IsUUID)('4', { message: '项目ID必须是有效的UUID' }),
    __metadata("design:type", String)
], ProjectIdParamDto.prototype, "projectId", void 0);
exports.ProjectIdParamDto = ProjectIdParamDto;
//# sourceMappingURL=project-stage.dto.js.map