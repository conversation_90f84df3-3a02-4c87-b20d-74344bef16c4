const { createConnection } = require('typeorm');
const bcrypt = require('bcryptjs');

// 直接定义数据库配置
const config = {
  type: 'sqlite',
  database: 'ltc_project.sqlite',
  entities: [
    __dirname + '/entities/*.js',
    __dirname + '/entities/*.ts'
  ],
  synchronize: true,
  logging: false
};

async function createAdminUser() {
  try {
    // 连接数据库
    const connection = await createConnection(config);
    console.log('数据库连接成功');

    // 获取用户仓库
    const userRepository = connection.getRepository('User');

    // 检查是否已存在管理员用户
    const existingAdmin = await userRepository.findOne({ where: { role: 'admin' } });

    if (existingAdmin) {
      console.log('超级管理员已存在:', existingAdmin.username);
      console.log('邮箱:', existingAdmin.email);
      console.log('请使用此邮箱和您设置的密码登录');
      process.exit(0);
    }

    // 创建超级管理员用户
    const adminUser = {
      username: 'admin',
      email: '<EMAIL>',
      password: bcrypt.hashSync('admin123', 8),
      role: 'admin'
    };

    // 保存用户
    const savedUser = await userRepository.save(adminUser);

    console.log('超级管理员创建成功!');
    console.log('用户名:', savedUser.username);
    console.log('邮箱:', savedUser.email);
    console.log('密码: admin123');
    console.log('请使用以上信息登录系统');

    process.exit(0);
  } catch (error) {
    console.error('创建超级管理员失败:', error);
    process.exit(1);
  }
}

createAdminUser();
