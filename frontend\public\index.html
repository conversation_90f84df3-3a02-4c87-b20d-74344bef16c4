<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="LTC Project Management System"
    />
    <!-- 内容安全策略 (移除frame-ancestors，因为在meta中不支持) -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://connect.facebook.net https://apis.google.com https://accounts.google.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' data: https://*.fbcdn.net https://*.googleapis.com;
      font-src 'self' https://fonts.gstatic.com;
      connect-src 'self' https://localhost:* http://localhost:* https://*.facebook.com https://*.googleapis.com;
      frame-src 'self' https://*.facebook.com https://accounts.google.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    " />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <!-- 防护脚本 - 阻止扩展程序干扰 -->
    <script>
      (function() {
        'use strict';
        
        console.log('🛡️ Initializing enhanced protection against extension interference...');
        
        // 1. 强化JSON.parse保护
        const originalJSONParse = JSON.parse;
        JSON.parse = function(text, reviver) {
          // 检查各种无效输入
          if (text === undefined || text === null || text === 'undefined' || text === 'null' || text === '') {
            console.warn('🚫 Blocked invalid JSON.parse input:', text);
            return null; // 返回null而不是抛出错误
          }
          
                  // 检查非字符串输入，但允许对象直接返回
        if (typeof text !== 'string') {
          // 🔧 修复：如果已经是对象，直接返回，不要阻止
          if (typeof text === 'object' && text !== null) {
            console.log('✅ JSON.parse received object, returning directly:', typeof text);
            return text;
          }
          console.warn('🚫 Blocked invalid JSON.parse input:', typeof text, text);
          return null;
        }
          
          try {
            return originalJSONParse.call(this, text, reviver);
          } catch (error) {
            console.warn('🚫 JSON.parse error caught and handled:', error.message);
            return null;
          }
        };
        
        // 2. 保护localStorage
        const originalGetItem = localStorage.getItem;
        const originalSetItem = localStorage.setItem;
        
        localStorage.getItem = function(key) {
          try {
            const value = originalGetItem.call(this, key);
            // 🔧 修复：只清理真正无效的数据，不要删除可能的有效JSON字符串
            if (value === 'undefined' || value === 'null') {
              console.log('🧹 Cleaning invalid localStorage key:', key);
              localStorage.removeItem(key);
              return null;
            }
            return value;
          } catch (error) {
            console.warn('🛡️ Protected localStorage.getItem:', error);
            return null;
          }
        };
        
        localStorage.setItem = function(key, value) {
          try {
            // 🔧 修复：只阻止字符串形式的'undefined'和'null'，允许空对象等有效JSON
            if (value === 'undefined' || value === 'null') {
              console.log('🚫 Blocked invalid localStorage.setItem:', key, value);
              return;
            }
            return originalSetItem.call(this, key, value);
          } catch (error) {
            console.warn('🛡️ Protected localStorage.setItem:', error);
          }
        };
        
        // 3. 创建安全的存储访问器
        window.safeGetItem = function(key) {
          try {
            const value = localStorage.getItem(key);
            if (!value || value === 'undefined' || value === 'null') {
              return null;
            }
            return JSON.parse(value);
          } catch (error) {
            console.warn('🛡️ safeGetItem failed for key:', key, error);
            localStorage.removeItem(key);
            return null;
          }
        };
        
        window.safeSetItem = function(key, value) {
          try {
            if (value === undefined || value === null) {
              localStorage.removeItem(key);
              return;
            }
            localStorage.setItem(key, JSON.stringify(value));
          } catch (error) {
            console.warn('🛡️ safeSetItem failed for key:', key, error);
          }
        };
        
        // 4. 全局错误处理
        window.addEventListener('error', function(event) {
          if (event.filename && event.filename.includes('chrome-extension://')) {
            console.warn('🚫 Blocked extension error:', event.message);
            event.preventDefault();
            return false;
          }
          
          if (event.error && event.error.message) {
            const errorMsg = event.error.message.toLowerCase();
            // 🔧 修复：只在严重的JSON解析错误时才清理，不要因为普通错误就清空所有数据
            if (errorMsg.includes('unexpected token') || errorMsg.includes('malformed json')) {
              console.warn('🧹 Serious JSON parsing error detected, gentle cleanup...');
              try {
                // 🔧 修复：不要完全清空localStorage，只清理明确损坏的项
                gentleStorageCleanup();
                
                // 🔧 修复：不要强制刷新页面
                console.log('🔧 Gentle cleanup completed, continuing normally...');
              } catch (e) {
                console.error('❌ Failed to clean storage:', e);
              }
              event.preventDefault();
              return false;
            }
          }
        });
        
        // 5. 拦截未处理的Promise rejections
        window.addEventListener('unhandledrejection', function(event) {
          if (event.reason && event.reason.message) {
            const reason = event.reason.message.toLowerCase();
            if (reason.includes('json') || reason.includes('undefined')) {
              console.warn('🚫 Blocked unhandled JSON promise rejection:', event.reason);
              event.preventDefault();
              return false;
            }
          }
        });
        
        // 6. 温和的存储清理 - 只清理真正损坏的数据
        function gentleStorageCleanup() {
          try {
            console.log('🧹 Starting gentle storage cleanup...');
            
            // 清理localStorage - 只清理明确损坏的数据
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key) {
                const value = originalGetItem.call(localStorage, key);
                // 🔧 修复：只清理字符串形式的'undefined'和'null'，保留有效的JSON数据
                if (value === 'undefined' || value === 'null') {
                  keysToRemove.push(key);
                }
              }
            }
            
            keysToRemove.forEach(key => {
              console.log('🗑️ Removing invalid key:', key);
              localStorage.removeItem(key);
            });
            
            // 清理sessionStorage - 同样温和处理
            const sessionKeysToRemove = [];
            for (let i = 0; i < sessionStorage.length; i++) {
              const key = sessionStorage.key(i);
              if (key) {
                const value = sessionStorage.getItem(key);
                if (value === 'undefined' || value === 'null') {
                  sessionKeysToRemove.push(key);
                }
              }
            }
            
            sessionKeysToRemove.forEach(key => {
              console.log('🗑️ Removing invalid session key:', key);
              sessionStorage.removeItem(key);
            });
            
            console.log('✅ Gentle storage cleanup completed');
          } catch (error) {
            console.warn('⚠️ Storage cleanup partially failed:', error);
          }
        }
        
        // 🔧 修复：注释掉立即执行和定期清理，避免干扰正常数据
        // gentleStorageCleanup();
        
        // 🔧 修复：大幅延长清理间隔，从30秒改为10分钟，并且更温和
        // setInterval(gentleStorageCleanup, 10 * 60 * 1000);
        
        // 8. 监听Chrome扩展的消息并阻止
        window.addEventListener('message', function(event) {
          if (event.origin.includes('chrome-extension://')) {
            console.warn('🚫 Blocked extension message:', event.origin);
            event.stopPropagation();
            return false;
          }
        });
        
        console.log('✅ Enhanced protection system active');
        
        // 添加一个全局标志，让React应用知道保护已激活
        window.__PROTECTION_ACTIVE__ = true;
      })();
    </script>
    <title>LTC Project Management</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->

    <!-- Facebook SDK -->
    <script>
      window.fbAsyncInit = function() {
        if (window.FB) {
          FB.init({
            appId            : '123456789012345', // 临时 ID，需要替换为真实的 Facebook App ID
            autoLogAppEvents : true,
            xfbml            : true,
            version          : 'v18.0'
          });
        }
      };
    </script>
    <!-- 暂时注释掉 Facebook SDK 脚本，以排除它是否导致问题 -->
    <!-- <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script> -->
  </body>
</html>
