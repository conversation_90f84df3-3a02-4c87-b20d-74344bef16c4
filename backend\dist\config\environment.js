"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loggingConfig = exports.databaseConfig = exports.serverConfig = exports.securityConfig = exports.isTest = exports.isProduction = exports.isDevelopment = exports.NODE_ENV = exports.Environment = void 0;
var Environment;
(function (Environment) {
    Environment["DEVELOPMENT"] = "development";
    Environment["PRODUCTION"] = "production";
    Environment["TEST"] = "test";
})(Environment = exports.Environment || (exports.Environment = {}));
exports.NODE_ENV = process.env.NODE_ENV || Environment.DEVELOPMENT;
exports.isDevelopment = exports.NODE_ENV === Environment.DEVELOPMENT;
exports.isProduction = exports.NODE_ENV === Environment.PRODUCTION;
exports.isTest = exports.NODE_ENV === Environment.TEST;
exports.securityConfig = {
    jwt: {
        secret: process.env.JWT_SECRET || 'your_jwt_secret_key',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    },
    cors: {
        origin: exports.isProduction
            ? process.env.CORS_ORIGIN || 'https://your-production-domain.com'
            : 'http://localhost:3000',
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: [
            'Content-Type',
            'Authorization',
            'x-request-id',
            'X-Requested-With',
            'x-csrf-token'
        ]
    },
    password: {
        saltRounds: 10
    },
    bypassAuth: exports.isDevelopment,
    rateLimit: {
        windowMs: 15 * 60 * 1000,
        max: exports.isProduction ? 100 : 1000,
        message: '请求过于频繁，请稍后再试'
    }
};
exports.serverConfig = {
    port: process.env.PORT || 5002,
    host: process.env.HOST || 'localhost'
};
exports.databaseConfig = {
    type: process.env.DB_TYPE || 'sqlite',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'postgres',
    database: process.env.DB_DATABASE || 'ltc_project',
    synchronize: !exports.isProduction,
    logging: exports.isDevelopment
};
exports.loggingConfig = {
    level: exports.isProduction ? 'info' : 'debug',
    format: exports.isProduction ? 'json' : 'pretty',
    enabled: true
};
exports.default = {
    environment: exports.NODE_ENV,
    isDevelopment: exports.isDevelopment,
    isProduction: exports.isProduction,
    isTest: exports.isTest,
    security: exports.securityConfig,
    server: exports.serverConfig,
    database: exports.databaseConfig,
    logging: exports.loggingConfig
};
//# sourceMappingURL=environment.js.map