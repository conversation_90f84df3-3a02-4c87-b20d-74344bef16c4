"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyGoogleIdToken = exports.verifyGoogleToken = void 0;
const google_auth_library_1 = require("google-auth-library");
const client = new google_auth_library_1.OAuth2Client(process.env.GOOGLE_CLIENT_ID || '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com');
async function verifyGoogleToken(token) {
    try {
        const response = await fetch(`https://www.googleapis.com/oauth2/v3/userinfo?access_token=${token}`);
        if (!response.ok) {
            throw new Error('Failed to fetch user info from Google');
        }
        const userInfo = await response.json();
        return {
            googleId: userInfo.sub,
            email: userInfo.email,
            username: userInfo.name || userInfo.email.split('@')[0],
            verified: userInfo.email_verified
        };
    }
    catch (error) {
        console.error('Error verifying Google token:', error);
        throw new Error('Invalid Google token');
    }
}
exports.verifyGoogleToken = verifyGoogleToken;
async function verifyGoogleIdToken(idToken) {
    var _a;
    try {
        const ticket = await client.verifyIdToken({
            idToken,
            audience: process.env.GOOGLE_CLIENT_ID || '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
        });
        const payload = ticket.getPayload();
        if (!payload) {
            throw new Error('Empty payload from Google token verification');
        }
        return {
            googleId: payload.sub,
            email: payload.email,
            username: payload.name || ((_a = payload.email) === null || _a === void 0 ? void 0 : _a.split('@')[0]) || '',
            verified: payload.email_verified
        };
    }
    catch (error) {
        console.error('Error verifying Google ID token:', error);
        throw new Error('Invalid Google ID token');
    }
}
exports.verifyGoogleIdToken = verifyGoogleIdToken;
//# sourceMappingURL=google-auth.service.js.map