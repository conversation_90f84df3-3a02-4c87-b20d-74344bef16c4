const express = require('express');
const cors = require('cors');

const app = express();

// 中间件
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
}));
app.use(express.json());

// 模拟通知数据
const mockNotifications = [
  {
    id: '1',
    title: 'New team member joined',
    message: '<PERSON> has joined the "Web Development" project team.',
    type: 'team',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
    projectId: 'proj-1',
    projectName: 'Web Development',
    actionUrl: '/projects/proj-1/team',
    priority: 'medium'
  },
  {
    id: '2',
    title: 'Project milestone completed',
    message: 'Phase 1 of the "Mobile App" project has been completed successfully.',
    type: 'success',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
    projectId: 'proj-2',
    projectName: 'Mobile App',
    actionUrl: '/projects/proj-2',
    priority: 'high'
  },
  {
    id: '3',
    title: 'Document requires review',
    message: 'The contract document for "Enterprise Solution" needs your review.',
    type: 'warning',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
    projectId: 'proj-3',
    projectName: 'Enterprise Solution',
    actionUrl: '/projects/proj-3/documents',
    priority: 'medium'
  }
];

// 通知路由
app.get('/api/notifications', (req, res) => {
  const unreadCount = mockNotifications.filter(n => !n.isRead).length;
  
  res.json({
    notifications: mockNotifications,
    stats: {
      unreadCount,
      totalCount: mockNotifications.length,
      lastReadAt: new Date().toISOString()
    }
  });
});

app.get('/api/notifications/stats', (req, res) => {
  const unreadCount = mockNotifications.filter(n => !n.isRead).length;
  
  res.json({
    unreadCount,
    totalCount: mockNotifications.length,
    lastReadAt: new Date().toISOString()
  });
});

app.put('/api/notifications/:id/read', (req, res) => {
  const { id } = req.params;
  const notification = mockNotifications.find(n => n.id === id);
  
  if (notification) {
    notification.isRead = true;
    res.json({ 
      message: 'Notification marked as read',
      notificationId: id,
      readAt: new Date().toISOString()
    });
  } else {
    res.status(404).json({ message: 'Notification not found' });
  }
});

app.put('/api/notifications/read-all', (req, res) => {
  mockNotifications.forEach(n => n.isRead = true);
  
  res.json({ 
    message: 'All notifications marked as read',
    readAt: new Date().toISOString(),
    count: mockNotifications.length
  });
});

app.delete('/api/notifications/:id', (req, res) => {
  const { id } = req.params;
  const index = mockNotifications.findIndex(n => n.id === id);
  
  if (index !== -1) {
    mockNotifications.splice(index, 1);
    res.status(204).send();
  } else {
    res.status(404).json({ message: 'Notification not found' });
  }
});

// 启动服务器
const PORT = 5002;
app.listen(PORT, () => {
  console.log(`Test notification server is running on port ${PORT}`);
}); 