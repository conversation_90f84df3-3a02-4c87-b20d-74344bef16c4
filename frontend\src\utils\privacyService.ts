import encryptionService from './encryptionService';

/**
 * 数据类型枚举
 */
export enum DataType {
  PERSONAL = 'personal',           // 个人数据
  SENSITIVE = 'sensitive',         // 敏感数据
  FINANCIAL = 'financial',         // 财务数据
  CONTACT = 'contact',             // 联系方式
  IDENTIFICATION = 'identification', // 身份识别数据
  LOCATION = 'location',           // 位置数据
  HEALTH = 'health',               // 健康数据
  BIOMETRIC = 'biometric',         // 生物识别数据
  BEHAVIORAL = 'behavioral',       // 行为数据
  PREFERENCE = 'preference',       // 偏好数据
  GENERAL = 'general'              // 一般数据
}

/**
 * 数据处理操作枚举
 */
export enum DataOperation {
  COLLECT = 'collect',             // 收集
  STORE = 'store',                 // 存储
  PROCESS = 'process',             // 处理
  SHARE = 'share',                 // 分享
  TRANSFER = 'transfer',           // 传输
  DELETE = 'delete',               // 删除
  ANONYMIZE = 'anonymize',         // 匿名化
  PSEUDONYMIZE = 'pseudonymize',   // 假名化
  ENCRYPT = 'encrypt',             // 加密
  DECRYPT = 'decrypt'              // 解密
}

/**
 * 数据处理目的枚举
 */
export enum DataPurpose {
  SERVICE_PROVISION = 'service_provision',   // 提供服务
  ANALYTICS = 'analytics',                   // 分析
  MARKETING = 'marketing',                   // 营销
  SECURITY = 'security',                     // 安全
  LEGAL = 'legal',                           // 法律要求
  IMPROVEMENT = 'improvement',               // 改进服务
  RESEARCH = 'research',                     // 研究
  PERSONALIZATION = 'personalization'        // 个性化
}

/**
 * 数据处理记录
 */
export interface DataProcessingRecord {
  timestamp: number;               // 时间戳
  dataType: DataType;              // 数据类型
  operation: DataOperation;        // 操作
  purpose: DataPurpose;            // 目的
  dataSubject?: string;            // 数据主体（用户ID等）
  dataController?: string;         // 数据控制者（操作者ID等）
  details?: string;                // 详细信息
}

/**
 * 敏感字段配置
 */
export interface SensitiveFieldsConfig {
  [key: string]: {
    type: DataType;                // 数据类型
    shouldEncrypt: boolean;        // 是否加密
    shouldMask: boolean;           // 是否掩码
    shouldAnonymize: boolean;      // 是否匿名化
    retentionPeriod?: number;      // 保留期限（毫秒）
  };
}

/**
 * 默认敏感字段配置
 */
const DEFAULT_SENSITIVE_FIELDS: SensitiveFieldsConfig = {
  'email': {
    type: DataType.CONTACT,
    shouldEncrypt: true,
    shouldMask: true,
    shouldAnonymize: false
  },
  'phone': {
    type: DataType.CONTACT,
    shouldEncrypt: true,
    shouldMask: true,
    shouldAnonymize: false
  },
  'address': {
    type: DataType.PERSONAL,
    shouldEncrypt: true,
    shouldMask: false,
    shouldAnonymize: false
  },
  'creditCard': {
    type: DataType.FINANCIAL,
    shouldEncrypt: true,
    shouldMask: true,
    shouldAnonymize: false
  },
  'ssn': {
    type: DataType.IDENTIFICATION,
    shouldEncrypt: true,
    shouldMask: true,
    shouldAnonymize: false
  },
  'password': {
    type: DataType.SENSITIVE,
    shouldEncrypt: true,
    shouldMask: true,
    shouldAnonymize: false
  },
  'dateOfBirth': {
    type: DataType.PERSONAL,
    shouldEncrypt: true,
    shouldMask: false,
    shouldAnonymize: false
  }
};

/**
 * 隐私服务配置
 */
export interface PrivacyServiceConfig {
  enableLogging: boolean;          // 是否启用日志
  enableEncryption: boolean;       // 是否启用加密
  enableMasking: boolean;          // 是否启用掩码
  enableAnonymization: boolean;    // 是否启用匿名化
  logStorageKey: string;           // 日志存储键
  sensitiveFields: SensitiveFieldsConfig; // 敏感字段配置
  consentRequired: boolean;        // 是否需要同意
}

/**
 * 默认隐私服务配置
 */
const DEFAULT_CONFIG: PrivacyServiceConfig = {
  enableLogging: true,
  enableEncryption: true,
  enableMasking: true,
  enableAnonymization: false,
  logStorageKey: 'privacy_logs',
  sensitiveFields: DEFAULT_SENSITIVE_FIELDS,
  consentRequired: true
};

/**
 * 隐私服务类
 */
export class PrivacyService {
  private config: PrivacyServiceConfig;
  private processingLogs: DataProcessingRecord[] = [];
  private userConsent: Record<DataPurpose, boolean> = {
    [DataPurpose.SERVICE_PROVISION]: true,
    [DataPurpose.ANALYTICS]: false,
    [DataPurpose.MARKETING]: false,
    [DataPurpose.SECURITY]: true,
    [DataPurpose.LEGAL]: true,
    [DataPurpose.IMPROVEMENT]: false,
    [DataPurpose.RESEARCH]: false,
    [DataPurpose.PERSONALIZATION]: false
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<PrivacyServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.loadLogs();
    this.loadConsent();
  }

  /**
   * 加载处理日志
   */
  private loadLogs(): void {
    try {
      const logs = localStorage.getItem(this.config.logStorageKey);
      if (logs) {
        this.processingLogs = JSON.parse(logs);
      }
    } catch (error) {
      console.error('加载隐私日志失败:', error);
    }
  }

  /**
   * 保存处理日志
   */
  private saveLogs(): void {
    try {
      localStorage.setItem(this.config.logStorageKey, JSON.stringify(this.processingLogs));
    } catch (error) {
      console.error('保存隐私日志失败:', error);
    }
  }

  /**
   * 加载用户同意设置
   */
  private loadConsent(): void {
    try {
      const consent = localStorage.getItem('user_consent');
      if (consent) {
        this.userConsent = { ...this.userConsent, ...JSON.parse(consent) };
      }
    } catch (error) {
      console.error('加载用户同意设置失败:', error);
    }
  }

  /**
   * 保存用户同意设置
   */
  private saveConsent(): void {
    try {
      localStorage.setItem('user_consent', JSON.stringify(this.userConsent));
    } catch (error) {
      console.error('保存用户同意设置失败:', error);
    }
  }

  /**
   * 记录数据处理操作
   * @param record 处理记录
   */
  public logProcessing(record: Omit<DataProcessingRecord, 'timestamp'>): void {
    if (!this.config.enableLogging) return;

    const fullRecord: DataProcessingRecord = {
      ...record,
      timestamp: Date.now()
    };

    this.processingLogs.push(fullRecord);
    this.saveLogs();
  }

  /**
   * 获取处理日志
   * @returns 处理日志
   */
  public getProcessingLogs(): DataProcessingRecord[] {
    return [...this.processingLogs];
  }

  /**
   * 清除处理日志
   */
  public clearProcessingLogs(): void {
    this.processingLogs = [];
    this.saveLogs();
  }

  /**
   * 设置用户同意
   * @param purpose 目的
   * @param consent 是否同意
   */
  public setConsent(purpose: DataPurpose, consent: boolean): void {
    this.userConsent[purpose] = consent;
    this.saveConsent();
  }

  /**
   * 检查用户是否同意
   * @param purpose 目的
   * @returns 是否同意
   */
  public hasConsent(purpose: DataPurpose): boolean {
    return this.userConsent[purpose] === true;
  }

  /**
   * 掩码数据
   * @param data 数据
   * @param visibleStart 可见起始字符数
   * @param visibleEnd 可见结束字符数
   * @param maskChar 掩码字符
   * @returns 掩码后的数据
   */
  public maskData(
    data: string,
    visibleStart = 0,
    visibleEnd = 0,
    maskChar = '*'
  ): string {
    if (!this.config.enableMasking) return data;
    if (!data) return data;

    const start = data.slice(0, visibleStart);
    const end = visibleEnd > 0 ? data.slice(-visibleEnd) : '';
    const masked = maskChar.repeat(Math.max(0, data.length - visibleStart - visibleEnd));

    return start + masked + end;
  }

  /**
   * 处理对象中的敏感字段
   * @param obj 对象
   * @returns 处理后的对象
   */
  public async processSensitiveData<T extends Record<string, any>>(obj: T): Promise<T> {
    if (!obj) return obj;

    const result = { ...obj };
    const fieldsToEncrypt: string[] = [];

    // 处理每个敏感字段
    for (const [field, config] of Object.entries(this.config.sensitiveFields)) {
      if (field in result && typeof result[field] === 'string') {
        // 掩码
        if (config.shouldMask && this.config.enableMasking) {
          if (field === 'email') {
            // 特殊处理电子邮件
            const fieldValue = result[field as keyof T] as string;
            const [username, domain] = fieldValue.split('@');
            if (username && domain) {
              const maskedUsername = this.maskData(username, 2, 0);
              result[field as keyof T] = `${maskedUsername}@${domain}` as any;
            }
          } else if (field === 'phone') {
            // 特殊处理电话号码
            const fieldValue = result[field as keyof T] as string;
            result[field as keyof T] = this.maskData(fieldValue, 0, 4) as any;
          } else if (field === 'creditCard') {
            // 特殊处理信用卡
            const fieldValue = result[field as keyof T] as string;
            result[field as keyof T] = this.maskData(fieldValue, 0, 4) as any;
          } else {
            // 一般掩码
            const fieldValue = result[field as keyof T] as string;
            result[field as keyof T] = this.maskData(fieldValue, 2, 2) as any;
          }
        }

        // 收集需要加密的字段
        if (config.shouldEncrypt && this.config.enableEncryption) {
          fieldsToEncrypt.push(field);
        }
      }
    }

    // 加密敏感字段
    if (fieldsToEncrypt.length > 0 && this.config.enableEncryption) {
      return await encryptionService.encryptObject(result, fieldsToEncrypt);
    }

    return result;
  }

  /**
   * 解密对象中的敏感字段
   * @param obj 对象
   * @returns 解密后的对象
   */
  public async decryptSensitiveData<T extends Record<string, any>>(obj: T): Promise<T> {
    if (!obj) return obj;

    const fieldsToDecrypt: string[] = [];

    // 收集需要解密的字段
    for (const [field, config] of Object.entries(this.config.sensitiveFields)) {
      if (field in obj && typeof obj[field] === 'string' && config.shouldEncrypt) {
        fieldsToDecrypt.push(field);
      }
    }

    // 解密敏感字段
    if (fieldsToDecrypt.length > 0 && this.config.enableEncryption) {
      return await encryptionService.decryptObject(obj, fieldsToDecrypt);
    }

    return { ...obj };
  }
}

// 创建默认实例
const privacyService = new PrivacyService();

export default privacyService;
