/* 简约通知面板样式 */
.notification-panel-simple .ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
}

.notification-panel-simple .ant-drawer-body {
  padding: 0;
  background-color: #fafafa;
}

.notification-item-simple {
  background-color: #fff;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
}

.notification-item-simple:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.notification-item-simple.unread {
  background-color: #fff7e6;
  border-left: 3px solid #1890ff;
}

.notification-item-simple.unread:hover {
  background-color: #fff2e8;
}

.notification-item-simple .ant-list-item-meta {
  align-items: flex-start;
}

.notification-item-simple .ant-list-item-meta-avatar {
  margin-right: 12px;
}

.notification-badge {
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-badge:hover {
  transform: scale(1.05);
}

/* 暗色主题适配 */
[data-theme='dark'] .notification-panel-simple .ant-drawer-body {
  background-color: #141414;
}

[data-theme='dark'] .notification-item-simple {
  background-color: #1f1f1f;
  border-bottom-color: #303030;
}

[data-theme='dark'] .notification-item-simple:hover {
  background-color: #262626;
}

[data-theme='dark'] .notification-item-simple.unread {
  background-color: #2b1d0d;
  border-left-color: #1890ff;
}

[data-theme='dark'] .notification-item-simple.unread:hover {
  background-color: #362510;
}

[data-theme='dark'] .notification-panel-simple .ant-drawer-header {
  border-bottom-color: #303030;
}

/* 消息详情弹窗样式 */
.notification-detail-modal .ant-modal-header {
  border-bottom: 1px solid var(--border-color, #f0f0f0);
  padding: 16px 24px;
}

.notification-detail-modal .ant-modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.notification-detail-modal .ant-modal-footer {
  border-top: 1px solid var(--border-color, #f0f0f0);
  padding: 12px 24px;
}

/* 消息详情卡片悬停效果 */
.notification-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 4px;
}

.notification-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 详情弹窗内容样式 */
.notification-detail-modal .ant-descriptions-item-label {
  font-weight: 600;
  background-color: #fafafa;
}

.notification-detail-modal .ant-descriptions-item-content {
  word-break: break-word;
}

/* 暗色主题适配 */
[data-theme='dark'] .notification-detail-modal .ant-modal-header {
  border-bottom-color: #303030;
}

[data-theme='dark'] .notification-detail-modal .ant-modal-footer {
  border-top-color: #303030;
}

[data-theme='dark'] .notification-detail-modal .ant-descriptions-item-label {
  background-color: #141414;
  color: #fff;
} 