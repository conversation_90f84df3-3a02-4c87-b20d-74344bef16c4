/**
 * User service for handling profile data operations
 */

interface UserProfile {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  department: string;
  position: string;
  avatar: string;
  bio: string;
  location: string;
  timezone: string;
  language: string;
  joinDate: string;
  lastLogin: string;
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  projectUpdates: boolean;
  teamMentions: boolean;
  systemMaintenance: boolean;
  weeklyDigest: boolean;
  marketingEmails: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  passwordLastChanged: string;
  loginSessions: Array<{
    id: string;
    device: string;
    location: string;
    lastAccess: string;
    current: boolean;
  }>;
}

interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

class UserService {
  private storageKey = 'smartltc_user_profile';
  private notificationKey = 'smartltc_notification_settings';
  private securityKey = 'smartltc_security_settings';

  // Get user profile
  async getUserProfile(): Promise<UserProfile> {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Default user profile
      const defaultProfile: UserProfile = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'J',
        lastName: 'Admin',
        phone: '+31 138 0013 8000',
        department: 'Information Technology',
        position: 'System Administrator',
        avatar: '',
        bio: 'Responsible for SmartLTC system management and maintenance, ensuring stable system operation.',
        location: 'Den Haag',
        timezone: 'Asia/Shanghai',
        language: 'en-US',
        joinDate: '2023-01-01',
        lastLogin: new Date().toISOString()
      };
      
      await this.saveUserProfile(defaultProfile);
      return defaultProfile;
    } catch (error) {
      console.error('Error loading user profile:', error);
      throw new Error('Failed to load user profile');
    }
  }

  // Save user profile
  async saveUserProfile(profile: UserProfile): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      localStorage.setItem(this.storageKey, JSON.stringify(profile));
      console.log('User profile saved successfully:', profile);
    } catch (error) {
      console.error('Error saving user profile:', error);
      throw new Error('Failed to save user profile');
    }
  }

  // Update specific profile fields
  async updateProfile(updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const currentProfile = await this.getUserProfile();
      const updatedProfile = { ...currentProfile, ...updates };
      await this.saveUserProfile(updatedProfile);
      return updatedProfile;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  // Upload avatar
  async uploadAvatar(file: File): Promise<string> {
    try {
      // Simulate file upload
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Convert to base64 for demo purposes
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw new Error('Failed to upload avatar');
    }
  }

  // Change password
  async changePassword(passwordData: PasswordChangeData): Promise<void> {
    try {
      // Simulate API call for password validation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, you'd validate the current password against the backend
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        throw new Error('Passwords do not match');
      }
      
      if (passwordData.newPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }
      
      // Update security settings
      const securitySettings = await this.getSecuritySettings();
      securitySettings.passwordLastChanged = new Date().toISOString().split('T')[0];
      await this.saveSecuritySettings(securitySettings);
      
      console.log('Password changed successfully');
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  // Get notification settings
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const stored = localStorage.getItem(this.notificationKey);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Default notification settings
      const defaultSettings: NotificationSettings = {
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        projectUpdates: true,
        teamMentions: true,
        systemMaintenance: true,
        weeklyDigest: false,
        marketingEmails: false,
        quietHoursEnabled: true,
        quietHoursStart: '22:00',
        quietHoursEnd: '08:00'
      };
      
      await this.saveNotificationSettings(defaultSettings);
      return defaultSettings;
    } catch (error) {
      console.error('Error loading notification settings:', error);
      throw new Error('Failed to load notification settings');
    }
  }

  // Save notification settings
  async saveNotificationSettings(settings: NotificationSettings): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 600));
      
      localStorage.setItem(this.notificationKey, JSON.stringify(settings));
      console.log('Notification settings saved successfully:', settings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
      throw new Error('Failed to save notification settings');
    }
  }

  // Get security settings
  async getSecuritySettings(): Promise<SecuritySettings> {
    try {
      const stored = localStorage.getItem(this.securityKey);
      if (stored) {
        return JSON.parse(stored);
      }
      
      // Default security settings
      const defaultSettings: SecuritySettings = {
        twoFactorEnabled: false,
        passwordLastChanged: '2024-01-01',
        loginSessions: [
          {
            id: '1',
            device: 'Chrome on Windows',
            location: 'Den Haag, Netherlands',
            lastAccess: new Date().toLocaleString(),
            current: true
          },
          {
            id: '2',
            device: 'Safari on iPhone',
            location: 'Den Haag, Netherlands',
            lastAccess: new Date(Date.now() - 24 * 60 * 60 * 1000).toLocaleString(),
            current: false
          }
        ]
      };
      
      await this.saveSecuritySettings(defaultSettings);
      return defaultSettings;
    } catch (error) {
      console.error('Error loading security settings:', error);
      throw new Error('Failed to load security settings');
    }
  }

  // Save security settings
  async saveSecuritySettings(settings: SecuritySettings): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      localStorage.setItem(this.securityKey, JSON.stringify(settings));
      console.log('Security settings saved successfully:', settings);
    } catch (error) {
      console.error('Error saving security settings:', error);
      throw new Error('Failed to save security settings');
    }
  }

  // Toggle two-factor authentication
  async toggleTwoFactor(enabled: boolean): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const securitySettings = await this.getSecuritySettings();
      securitySettings.twoFactorEnabled = enabled;
      await this.saveSecuritySettings(securitySettings);
      
      console.log('Two-factor authentication', enabled ? 'enabled' : 'disabled');
    } catch (error) {
      console.error('Error toggling two-factor authentication:', error);
      throw new Error('Failed to update two-factor authentication');
    }
  }

  // Logout from specific session
  async logoutSession(sessionId: string): Promise<void> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const securitySettings = await this.getSecuritySettings();
      securitySettings.loginSessions = securitySettings.loginSessions.filter(
        session => session.id !== sessionId
      );
      await this.saveSecuritySettings(securitySettings);
      
      console.log('Session logged out successfully:', sessionId);
    } catch (error) {
      console.error('Error logging out session:', error);
      throw new Error('Failed to logout session');
    }
  }

  // Get user activity log (for future use)
  async getUserActivity(): Promise<any[]> {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 400));
      
      return [
        {
          id: '1',
          action: 'Profile Updated',
          timestamp: new Date().toISOString(),
          details: 'Updated contact information'
        },
        {
          id: '2',
          action: 'Password Changed',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          details: 'Password changed successfully'
        }
      ];
    } catch (error) {
      console.error('Error loading user activity:', error);
      throw new Error('Failed to load user activity');
    }
  }
}

const userService = new UserService();
export default userService; 