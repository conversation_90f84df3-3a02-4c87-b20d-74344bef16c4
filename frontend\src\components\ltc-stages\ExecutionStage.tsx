import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import './ExecutionStage.css';
import {
  Card,
  Row,
  Col,
  Progress,
  Button,
  Table,
  Tag,
  Tabs,
  Form,
  Input,
  Select,
  DatePicker,
  Space,
  Statistic,
  Modal,
  message,
  Tooltip,
  Timeline,
  List,

  Slider,
  Popconfirm,
  Typography,
  AutoComplete,
  InputNumber,
  Checkbox
} from 'antd';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  RightOutlined,
  ProjectOutlined,
  TeamOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  AlertOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  AimOutlined,
  SaveOutlined,
  UpOutlined,
  DownOutlined,
  CheckOutlined,
  FileTextOutlined,
  CalculatorOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import type { Project, ProjectStage } from '../../types';
import { DATE_FORMAT, formatDate } from '../../utils/dateUtils';
import financeService, { type Invoice } from '../../services/finance.service';
import authService from '../../services/auth.service';
import notificationService from '../../services/notification.service';
import { useCurrency } from '../../contexts/CurrencyContext';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

interface ExecutionStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

// 任务接口
interface Task {
  id: string;
  name: string;
  description: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignee: string;
  startDate: string;
  dueDate: string;
  progress: number;
  dependencies?: string[];
  order?: number; // 添加排序字段
}

// 团队成员接口
interface TeamMember {
  id: string;
  name: string;
  role: string;
  email: string;
  workload: number;
  availability: 'available' | 'busy' | 'unavailable';
}

// 里程碑接口
interface Milestone {
  id: string;
  name: string;
  description: string;
  targetDate: string;
  status: 'upcoming' | 'in_progress' | 'completed' | 'delayed';
  progress: number;
}

// 预算项接口
interface BudgetItem {
  id: string;
  budgetItem: string; // 改名从 category 到 budgetItem
  category?: string; // 保留用于向后兼容
  description: string;
  currency: string; // 新增货币字段
  plannedAmount: number;
  spentAmount: number;
  // VAT related fields
  amountExclVat?: number;
  vatRate?: number;
  vatAmount?: number;
  // Budget utilization tracking fields (similar to Collection Process)
  utilizationRate?: number; // 预算使用率百分比 (0-100)
  totalExpensed?: number; // 总支出金额 (from expense invoices and transactions)
  remainingBudget?: number; // 剩余预算
  expenseStatus?: 'pending' | 'partial' | 'fully_utilized' | 'over_budget'; // 自动计算状态
}

// 风险接口
interface Risk {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'business' | 'operational' | 'financial' | 'external';
  probability: 'low' | 'medium' | 'high' | 'critical';
  impact: 'low' | 'medium' | 'high' | 'critical';
  status: 'identified' | 'analyzing' | 'mitigating' | 'monitoring' | 'closed';
  owner: string;
  identifiedDate: string;
  targetResolutionDate?: string;
  mitigationPlan?: string;
  contingencyPlan?: string;
  riskScore: number; // 计算得出的风险分数
}

// 拖拽覆盖层组件 - 用户友好版本
const DragOverlayContent: React.FC<{ task: Task }> = ({ task }) => {
  return (
    <div
      style={{
        padding: '12px 16px',
        backgroundColor: '#fff',
        border: '2px solid #1890ff',
        borderRadius: '6px',
        boxShadow: '0 4px 12px rgba(24, 144, 255, 0.2)',
        fontSize: '14px',
        color: '#333',
        opacity: 0.95,
        maxWidth: '300px',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        transform: 'rotate(-1deg)', // 轻微旋转增加动感
        transition: 'all 0.2s ease'
      }}
    >
      <span style={{ color: '#1890ff', fontSize: '16px' }}>📋</span>
      <div>
        <div style={{ fontWeight: 500, marginBottom: '2px' }}>{task.name}</div>
        <div style={{ color: '#666', fontSize: '12px' }}>
          {task.assignee} • {task.progress}% complete
        </div>
      </div>
    </div>
  );
};

// 可拖拽的表格行组件
// 简化的进度条组件 - 只支持点击定位
const ProgressSlider: React.FC<{
  taskId: string;
  progress: number;
  config: any;
  onChange: (taskId: string, progress: number) => void;
}> = ({ taskId, progress, config, onChange }) => {
  const progressBarRef = useRef<HTMLDivElement>(null);

  // 处理点击事件 - 修复taskId为undefined的问题
  const handleClick = useCallback((e: React.MouseEvent) => {
    // 完全阻止冒泡和默认行为，防止触发行拖拽
    e.stopPropagation();
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation();
    
    // 增加额外的安全检查，确保taskId存在
    if (!taskId) {
      console.error('ProgressSlider: taskId is undefined!');
      return;
    }
    
    // 仅在开发模式下输出进度条点击日志
    // if (isDevelopmentMode) console.log('Progress bar clicked for task:', taskId);
    
    if (progressBarRef.current) {
      const rect = progressBarRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const width = rect.width;
      
      // 优化100%点击：如果点击位置在95%以后，直接设为100%
      let newProgress = (clickX / width) * 100;
      if (newProgress >= 95) {
        newProgress = 100;
      } else {
        newProgress = Math.round(newProgress);
      }
      
      const clampedProgress = Math.max(0, Math.min(100, newProgress));
      
      // if (isDevelopmentMode) console.log('Progress updated to:', clampedProgress);
      // 确保调用时传递正确的taskId
      onChange(taskId, clampedProgress);
    }
  }, [taskId, onChange]); // 明确添加taskId到依赖数组

  // 强化事件隔离，防止与拖拽冲突
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    // if (isDevelopmentMode) console.log('Progress bar mouse down for task:', taskId);
  }, [taskId]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
  }, []);

  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
  }, []);

  // 在渲染时也检查taskId的有效性
  if (!taskId) {
    console.error('ProgressSlider rendered with undefined taskId!');
    return <div style={{ color: 'red', fontSize: '12px' }}>Error: Invalid task ID</div>;
  }

  return (
    <div 
      style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '8px',
        width: '100%'
      }}
    >
      <div 
        ref={progressBarRef}
        style={{ 
          flex: 1, 
          height: '28px', // 增加高度便于点击
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          padding: '6px 4px', // 增加内边距
          borderRadius: '4px',
          transition: 'background-color 0.2s ease'
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onContextMenu={(e) => e.preventDefault()}
        onDragStart={(e) => e.preventDefault()}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(24, 144, 255, 0.04)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
      >
        {/* 简化的进度条 */}
        <div
          style={{
            width: '100%',
            height: '10px', // 稍微增加进度条高度
            backgroundColor: '#f0f0f0',
            borderRadius: '5px',
            position: 'relative',
            border: '1px solid #d9d9d9'
          }}
        >
          <div
            style={{
              width: `${progress}%`,
              height: '100%',
              backgroundColor: config.color,
              borderRadius: '4px',
              transition: 'width 0.2s ease'
            }}
          />
        </div>
      </div>
      
      <span style={{
         minWidth: '45px',
         textAlign: 'right',
         fontSize: '13px',
         fontWeight: '500',
         color: config.color
       }}>
         {progress}%
       </span>
    </div>
  );
};

const DraggableTableRow: React.FC<any> = ({ index, ...props }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition: isDragging ? 'none' : transition,
    cursor: 'default', // 移除拖拽光标，只在拖拽句柄上显示
    opacity: isDragging ? 0.7 : 1,
    ...(isDragging ? { 
      position: 'relative', 
      zIndex: 1000,
      backgroundColor: 'rgba(24, 144, 255, 0.02)',
      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      borderRadius: '4px'
    } : {}),
  };

  // 不在整行上绑定拖拽事件，只在特定元素上绑定
  return (
    <tr ref={setNodeRef} style={style} {...attributes} {...props}>
      {React.Children.map(props.children, (child, index) => {
        // 将拖拽事件(listeners)只附加到第一个单元格(拖拽句柄列)
        if (index === 0) {
          return React.cloneElement(child as React.ReactElement, { ...listeners });
        }
        return child;
      })}
    </tr>
  );
};

const ExecutionStage: React.FC<ExecutionStageProps> = ({ project, stage, onSave, onProceed }) => {
  // 开发模式控制日志输出
  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  
  const [form] = Form.useForm();
  const [milestoneForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('milestone');
  const [saving, setSaving] = useState(false);
  const [showAddMilestoneModal, setShowAddMilestoneModal] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState<Milestone | null>(null);
  const [showCreateTaskModal, setShowCreateTaskModal] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [taskForm] = Form.useForm();
  const [taskFilter, setTaskFilter] = useState('all');
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [forceRender, setForceRender] = useState(0); // 强制重新渲染的状态
  const [riskFilter, setRiskFilter] = useState('all');
  const [totalSpent, setTotalSpent] = useState(0);
  const [spentByCategory, setSpentByCategory] = useState<{ [key: string]: number }>({}); // 按分类保存总支出
  
  // 拖拽传感器配置 - 专门优化问题3
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 降低激活距离，让拖拽更灵敏
        delay: 0, // 移除延迟，确保拖拽立即响应
        tolerance: 5, // 增加容错范围，提高拖拽成功率
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 数据持久化Key
  const getStorageKey = useCallback((type: string) => {
    const projectId = project?.id || 'default';
    return `execution_stage_${type}_${projectId}`;
  }, [project?.id]);
  
  // 预算相关状态
  const [showBudgetItemModal, setShowBudgetItemModal] = useState(false);
  const [editingBudgetItem, setEditingBudgetItem] = useState<BudgetItem | null>(null);
  const [budgetItemForm] = Form.useForm();
  
  // 团队成员相关状态
  const [showTeamMemberModal, setShowTeamMemberModal] = useState(false);
  const [editingTeamMember, setEditingTeamMember] = useState<TeamMember | null>(null);
  const [teamMemberForm] = Form.useForm();
  
  // 风险管理相关状态
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [editingRisk, setEditingRisk] = useState<Risk | null>(null);
  const [riskForm] = Form.useForm();

  // 货币上下文
  const { selectedCurrency, supportedCurrencies } = useCurrency();

  // 状态管理 - 新项目从空白开始
  const [tasks, setTasks] = useState<Task[]>([]);

  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  const [milestones, setMilestones] = useState<Milestone[]>([]);

  // 预算项数据
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([]);

  // 风险数据
  const [risks, setRisks] = useState<Risk[]>([]);

  // 数据持久化逻辑
  // 数据清理和修复函数
  const cleanAndRepairData = useCallback(() => {
    try {
      const savedTasks = localStorage.getItem(getStorageKey('tasks'));
      console.log('🔍 Checking localStorage data integrity...');
      
      if (savedTasks) {
        const parsedTasks = JSON.parse(savedTasks);
        console.log('📋 Raw tasks from localStorage:', parsedTasks);
        
        if (parsedTasks.length > 0) {
          // 深度验证和修复任务数据
          const repairedTasks = parsedTasks.map((task: any, index: number) => {
            const isInvalidId = !task.id || typeof task.id !== 'string' || task.id.trim() === '';
            
            if (isInvalidId) {
              console.warn(`🚨 Task ${index} has invalid ID, repairing:`, task);
              return {
                ...task,
                id: crypto.randomUUID(),
                name: task.name || `Task ${index + 1}`,
                status: task.status || 'not_started',
                progress: typeof task.progress === 'number' ? task.progress : 0,
                priority: task.priority || 'medium',
                assignee: task.assignee || 'Unassigned',
                description: task.description || '',
                startDate: task.startDate || '',
                dueDate: task.dueDate || ''
              };
            }
            
            // 验证其他关键字段
            return {
              ...task,
              progress: typeof task.progress === 'number' ? Math.max(0, Math.min(100, task.progress)) : 0,
              status: ['not_started', 'in_progress', 'completed', 'blocked'].includes(task.status) ? task.status : 'not_started',
              priority: ['low', 'medium', 'high', 'critical'].includes(task.priority) ? task.priority : 'medium'
            };
          });
          
          // 检查是否有数据被修复
          const needsRepair = parsedTasks.some((task: any) => 
            !task.id || typeof task.id !== 'string' || task.id.trim() === ''
          );
          
          if (needsRepair) {
            console.log('✅ Data repaired, saving back to localStorage');
            localStorage.setItem(getStorageKey('tasks'), JSON.stringify(repairedTasks));
          }
          
          console.log('📦 Final validated tasks:', repairedTasks);
          setTasks(repairedTasks);
        }
      } else {
        console.log('📭 No saved tasks found in localStorage');
      }
    } catch (error) {
      console.error('💥 Critical data repair error:', error);
      // 如果修复失败，清空损坏的数据
      localStorage.removeItem(getStorageKey('tasks'));
      setTasks([]);
    }
  }, [getStorageKey]);

  useEffect(() => {
    const loadData = () => {
      try {
        // 首先运行数据清理和修复
        cleanAndRepairData();

        // 加载里程碑数据
        const savedMilestones = localStorage.getItem(getStorageKey('milestones'));
        if (savedMilestones) {
          const parsedMilestones = JSON.parse(savedMilestones);
          if (parsedMilestones.length > 0) {
            setMilestones(parsedMilestones);
          }
        }

        // 加载团队成员数据
        const savedTeamMembers = localStorage.getItem(getStorageKey('teamMembers'));
        if (savedTeamMembers) {
          const parsedTeamMembers = JSON.parse(savedTeamMembers);
          if (parsedTeamMembers.length > 0) {
            setTeamMembers(parsedTeamMembers);
          }
        }

        // 加载预算项目数据
        const savedBudgetItems = localStorage.getItem(getStorageKey('budgetItems'));
        if (savedBudgetItems) {
          const parsedBudgetItems = JSON.parse(savedBudgetItems);
          if (parsedBudgetItems.length > 0) {
            setBudgetItems(parsedBudgetItems);
          }
        }

        // 加载风险数据
        const savedRisks = localStorage.getItem(getStorageKey('risks'));
        if (savedRisks) {
          const parsedRisks = JSON.parse(savedRisks);
          if (parsedRisks.length > 0) {
            setRisks(parsedRisks);
          }
        }

        console.log('✅ ExecutionStage: All data loaded successfully');
      } catch (error) {
        console.error('💥 ExecutionStage: Critical error loading data:', error);
        // 如果localStorage数据损坏，清空数据重新开始
        console.log('🧹 Clearing all corrupted localStorage data');
        localStorage.removeItem(getStorageKey('tasks'));
        localStorage.removeItem(getStorageKey('milestones'));
        localStorage.removeItem(getStorageKey('teamMembers'));
        localStorage.removeItem(getStorageKey('budgetItems'));
        localStorage.removeItem(getStorageKey('risks'));
      }
    };

    loadData();
  }, [project?.id, cleanAndRepairData]);

  // 自动保存数据到localStorage
  useEffect(() => {
    if (tasks.length > 0) {
      // 在保存前验证所有任务的ID有效性
      const validTasks = tasks.filter(task => 
        task && task.id && typeof task.id === 'string' && task.id.trim() !== ''
      );
      
      if (validTasks.length !== tasks.length) {
        console.warn(`Filtered out ${tasks.length - validTasks.length} tasks with invalid IDs before saving`);
      }
      
      if (validTasks.length > 0) {
        localStorage.setItem(getStorageKey('tasks'), JSON.stringify(validTasks));
      }
    }
  }, [tasks, project?.id, getStorageKey]);

  useEffect(() => {
    if (milestones.length > 0) {
      localStorage.setItem(getStorageKey('milestones'), JSON.stringify(milestones));
    }
  }, [milestones, project?.id, getStorageKey]);

  useEffect(() => {
    if (teamMembers.length > 0) {
      localStorage.setItem(getStorageKey('teamMembers'), JSON.stringify(teamMembers));
    }
  }, [teamMembers, project?.id, getStorageKey]);

  useEffect(() => {
    if (budgetItems.length > 0) {
      localStorage.setItem(getStorageKey('budgetItems'), JSON.stringify(budgetItems));
    }
  }, [budgetItems, project?.id, getStorageKey]);

  useEffect(() => {
    if (risks.length > 0) {
      localStorage.setItem(getStorageKey('risks'), JSON.stringify(risks));
    }
  }, [risks, project?.id, getStorageKey]);

  // 使用useMemo缓存统计计算，防止无限渲染
  const stats = useMemo(() => {
    const completed = tasks.filter(t => t.status === 'completed').length;
    const inProgress = tasks.filter(t => t.status === 'in_progress').length;
    const blocked = tasks.filter(t => t.status === 'blocked').length;
    const overdue = tasks.filter(t => {
      const dueDate = new Date(t.dueDate);
      const today = new Date();
      return dueDate < today && t.status !== 'completed';
    }).length;

    return { completed, inProgress, blocked, overdue };
  }, [tasks]);

  const overallProgress = useMemo(() => {
    if (tasks.length === 0) return 0;
    
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const inProgressTasks = tasks.filter(task => task.status === 'in_progress');
    
    let totalProgress = completedTasks * 100;
    inProgressTasks.forEach(task => {
      totalProgress += task.progress;
    });
    
    return Math.round(totalProgress / totalTasks);
  }, [tasks]);

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'completed': { color: 'success', text: 'Completed', icon: <CheckCircleOutlined /> },
      'in_progress': { color: 'processing', text: 'In Progress', icon: <ClockCircleOutlined /> },
      'not_started': { color: 'default', text: 'Not Started', icon: null },
      'blocked': { color: 'error', text: 'Blocked', icon: <ExclamationCircleOutlined /> },
      'upcoming': { color: 'warning', text: 'Upcoming', icon: null },
      'delayed': { color: 'error', text: 'Delayed', icon: <AlertOutlined /> }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['not_started'];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 渲染优先级标签
  const renderPriorityTag = (priority: string) => {
    const priorityConfig = {
      'critical': { color: 'red', text: 'Critical' },
      'high': { color: 'orange', text: 'High' },
      'medium': { color: 'blue', text: 'Medium' },
      'low': { color: 'green', text: 'Low' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig['medium'];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 任务表格列定义
  const taskColumns = [
    {
      title: '',
      key: 'drag',
      width: 30,
      render: () => <div className="drag-handle">⋮⋮</div>, // 只渲染UI，不再调用Hook
    },
    {
      title: 'Task Name',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Task) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ color: '#666', marginTop: '2px' }}>
            {record.description}
          </div>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => renderStatusTag(status)
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => renderPriorityTag(priority)
    },
    {
      title: 'Assignee',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120
    },
    {
      title: 'Progress',
      dataIndex: 'progress',
      key: 'progress',
      width: 160,
      render: (progress: number, record: Task) => {
        // 添加调试信息，确保record.id存在
        if (!record || !record.id) {
          console.error('Progress render: Invalid record or missing id', record);
          return <div style={{ color: 'red', fontSize: '12px' }}>Error: Invalid task</div>;
        }
        
        // if (isDevelopmentMode) console.log(`Rendering progress for task: ${record.id} (${record.name}), progress: ${progress}%`);
        
        // 优化的颜色映射系统
        const getProgressConfig = (value: number) => {
          if (value === 100) return { 
            color: '#27ae60', 
            bgColor: '#f0f9f0', 
            shadowColor: 'rgba(39, 174, 96, 0.1)',
            icon: ''
          };
          if (value >= 75) return { 
            color: '#2ecc71', 
            bgColor: '#f0f9f0', 
            shadowColor: 'rgba(46, 204, 113, 0.1)',
            icon: ''
          };
          if (value >= 50) return { 
            color: '#f39c12', 
            bgColor: '#fef9e7', 
            shadowColor: 'rgba(243, 156, 18, 0.1)',
            icon: ''
          };
          if (value >= 25) return { 
            color: '#e67e22', 
            bgColor: '#fef4e7', 
            shadowColor: 'rgba(230, 126, 34, 0.1)',
            icon: ''
          };
          return { 
            color: '#d9d9d9', 
            bgColor: '#fafafa', 
            shadowColor: 'rgba(217, 217, 217, 0.1)',
            icon: ''
          };
        };

        const config = getProgressConfig(progress);

        return (
          <ProgressSlider
            taskId={record.id}
            progress={progress}
            config={config}
            onChange={handleTaskProgressChange}
          />
        );
      }
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 100,
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 180,
      render: (_: any, record: Task, index: number) => {
        // 验证任务ID的有效性
        if (!record || !record.id || typeof record.id !== 'string' || record.id.trim() === '') {
          console.error('Actions render: Invalid task record', record);
          return <div style={{ color: 'red', fontSize: '12px' }}>Invalid Task</div>;
        }
        
        // 获取任务在完整任务列表中的实际位置，而不是过滤后的位置
        const actualIndex = tasks.findIndex(task => task.id === record.id);
        
        // 确保找到了任务
        if (actualIndex === -1) {
          console.error('Actions render: Task not found in tasks array', record.id);
          return <div style={{ color: 'red', fontSize: '12px' }}>Task Not Found</div>;
        }
        
        const isFirst = actualIndex === 0;
        const isLast = actualIndex === tasks.length - 1;
        const isCompleted = record.status === 'completed';
        
        // if (isDevelopmentMode) console.log(`Task ${record.name} (${record.id}): actualIndex=${actualIndex}, isFirst=${isFirst}, isLast=${isLast}`);
        
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {/* 上下移动按钮组 - 垂直排列 */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
              <Tooltip title="Move Up">
                <Button 
                  type="text" 
                  icon={<UpOutlined />} 
                  size="small"
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Up button clicked for task:', record.id, 'isFirst:', isFirst);
                    if (!isFirst) {
                      handleMoveTaskUp(record.id);
                    }
                  }}
                  disabled={isFirst}
                  style={{ 
                    color: isFirst ? '#d9d9d9' : '#1890ff',
                    padding: '2px 4px',
                    height: '20px',
                    minWidth: '20px',
                    cursor: isFirst ? 'not-allowed' : 'pointer',
                    border: 'none',
                    background: 'transparent',
                    transition: 'all 0.1s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isFirst) {
                      e.currentTarget.style.backgroundColor = '#e6f7ff';
                      e.currentTarget.style.color = '#096dd9';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = isFirst ? '#d9d9d9' : '#1890ff';
                  }}
                />
              </Tooltip>
              <Tooltip title="Move Down">
                <Button 
                  type="text" 
                  icon={<DownOutlined />} 
                  size="small"
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Down button clicked for task:', record.id, 'isLast:', isLast);
                    if (!isLast) {
                      handleMoveTaskDown(record.id);
                    }
                  }}
                  disabled={isLast}
                  style={{ 
                    color: isLast ? '#d9d9d9' : '#1890ff',
                    padding: '2px 4px',
                    height: '20px',
                    minWidth: '20px',
                    cursor: isLast ? 'not-allowed' : 'pointer',
                    border: 'none',
                    background: 'transparent',
                    transition: 'all 0.1s ease'
                  }}
                  onMouseEnter={(e) => {
                    if (!isLast) {
                      e.currentTarget.style.backgroundColor = '#e6f7ff';
                      e.currentTarget.style.color = '#096dd9';
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = isLast ? '#d9d9d9' : '#1890ff';
                  }}
                />
              </Tooltip>
            </div>
            
            {/* 快速完成按钮 */}
            <Tooltip title={isCompleted ? "Mark as In Progress" : "Mark as Completed"}>
              <Button 
                type="text" 
                icon={<CheckOutlined />} 
                size="small"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleQuickCompleteTask(record.id);
                }}
                style={{
                  color: isCompleted ? '#52c41a' : '#666',
                  backgroundColor: isCompleted ? '#f6ffed' : 'transparent',
                  border: isCompleted ? '1px solid #b7eb8f' : 'none',
                  padding: '2px 4px',
                  height: '24px',
                  minWidth: '24px',
                  borderRadius: '4px',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  if (isCompleted) {
                    e.currentTarget.style.backgroundColor = '#e6fffb';
                    e.currentTarget.style.borderColor = '#87e8de';
                    e.currentTarget.style.color = '#13c2c2';
                  } else {
                    e.currentTarget.style.backgroundColor = '#f6ffed';
                    e.currentTarget.style.borderColor = '#b7eb8f';
                    e.currentTarget.style.color = '#52c41a';
                  }
                }}
                onMouseLeave={(e) => {
                  if (isCompleted) {
                    e.currentTarget.style.backgroundColor = '#f6ffed';
                    e.currentTarget.style.borderColor = '#b7eb8f';
                    e.currentTarget.style.color = '#52c41a';
                  } else {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                    e.currentTarget.style.color = '#666';
                  }
                }}
              />
            </Tooltip>
            
            {/* 编辑和删除按钮 */}
            <Space size="small">
              <Tooltip title="Edit Task">
                <Button 
                  type="text" 
                  icon={<EditOutlined />} 
                  size="small"
                  onClick={() => handleEditTask(record)}
                  style={{
                    padding: '2px 4px',
                    height: '24px',
                    minWidth: '24px'
                  }}
                />
              </Tooltip>
              <Popconfirm
                title="Are you sure you want to delete this task?"
                onConfirm={() => handleDeleteTask(record.id)}
                okText="Yes"
                cancelText="No"
              >
                <Tooltip title="Delete Task">
                  <Button 
                    type="text" 
                    icon={<DeleteOutlined />} 
                    size="small"
                    danger
                    style={{
                      padding: '2px 4px',
                      height: '24px',
                      minWidth: '24px'
                    }}
                  />
                </Tooltip>
              </Popconfirm>
            </Space>
          </div>
        );
      }
    }
  ];

  // 保存处理
  const handleSave = async (values: any) => {
    setSaving(true);
    try {
      const executionData = {
        ...values,
        tasks,
        teamMembers,
        milestones,
        overallProgress,
        stats,
        type: 'execution',
        status: 'in_progress',
        updatedAt: new Date().toISOString()
      };

      if (onSave) {
        onSave(executionData);
      }

      message.success('执行阶段数据保存成功');
    } catch (error) {
      console.error('Save error:', error);
      message.error('保存失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 进入下一阶段
  const handleProceed = async () => {
    try {
      await handleSave(form.getFieldsValue());
      
      setTimeout(() => {
        message.success('执行阶段完成！正在进入验收阶段...');
        if (onProceed) {
          onProceed('acceptance');
        }
      }, 1000);
    } catch (error) {
      message.error('请完成所有必要任务后再继续');
    }
  };

  // 处理添加里程碑
  const handleAddMilestone = () => {
    setEditingMilestone(null);
    milestoneForm.resetFields();
    
    // 设置默认日期 - 基于最后一个milestone的日期
    if (milestones.length > 0) {
      // 获取最新的milestone日期
      const sortedMilestones = [...milestones].sort((a, b) => 
        new Date(b.targetDate).getTime() - new Date(a.targetDate).getTime()
      );
      const lastMilestone = sortedMilestones[0];
      
      if (lastMilestone.targetDate) {
        // 从最后一个milestone的日期开始，默认设置为该日期的下一天
        const lastDate = dayjs(lastMilestone.targetDate);
        const nextDate = lastDate.add(1, 'day');
        
        milestoneForm.setFieldsValue({
          targetDate: nextDate
        });
      }
    }
    
    setShowAddMilestoneModal(true);
  };

  // 处理创建新任务
  const handleCreateTask = () => {
    setEditingTask(null);
    taskForm.resetFields();
    setShowCreateTaskModal(true);
  };

  // 处理编辑任务
  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    taskForm.setFieldsValue({
      name: task.name,
      description: task.description,
      priority: task.priority,
      assignee: task.assignee,
      startDate: task.startDate ? dayjs(task.startDate) : null,
      dueDate: task.dueDate ? dayjs(task.dueDate) : null,
      status: task.status
    });
    setShowCreateTaskModal(true);
  };

  // 保存任务
  const handleSaveTask = async (values: any) => {
    try {
      let formattedValues = {
        ...values,
        startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : '',
        dueDate: values.dueDate ? values.dueDate.format('YYYY-MM-DD') : ''
      };

      // 🔄 简化的状态和进度同步逻辑（无提示消息）
      if (formattedValues.status === 'completed' && formattedValues.progress !== 100) {
        formattedValues.progress = 100;
      } else if (formattedValues.status === 'not_started' && formattedValues.progress > 0) {
        formattedValues.progress = 0;
      } else if (formattedValues.status === 'in_progress' && formattedValues.progress === 0) {
        formattedValues.progress = 10;
      } else if (formattedValues.status === 'in_progress' && formattedValues.progress === 100) {
        formattedValues.status = 'completed';
      }

      if (editingTask) {
        // 编辑现有任务
        setTasks(prev => prev.map(task => 
          task.id === editingTask.id 
            ? { ...task, ...formattedValues }
            : task
        ));
        message.success('Task updated successfully');
      } else {
        // 添加新任务
        let newTaskProgress = 0; // 默认进度
        
        // 根据状态设置合适的初始进度
        if (formattedValues.status === 'completed') {
          newTaskProgress = 100;
        } else if (formattedValues.status === 'in_progress') {
          newTaskProgress = formattedValues.progress || 10; // 如果没有指定进度，默认10%
        } else if (formattedValues.status === 'not_started') {
          newTaskProgress = 0;
        }
        
        const newTask: Task = {
          id: crypto.randomUUID(), // 使用 crypto.randomUUID() 保证ID的唯一性
          ...formattedValues,
          progress: newTaskProgress
        };
        console.log('Creating new task with synced status/progress:', newTask);
        setTasks(prev => [...prev, newTask]);
        message.success('Task created successfully');
      }
      setShowCreateTaskModal(false);
      taskForm.resetFields();
    } catch (error) {
      console.error('Error saving task:', error);
      message.error('Failed to save task');
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId: string) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
    message.success('Task deleted successfully');
  };

  // 快速标记任务为完成/进行中
  const handleQuickCompleteTask = useCallback((taskId: string) => {
    setTasks(prev => {
      const updatedTasks = prev.map(task => {
        if (task.id === taskId) {
          const wasCompleted = task.status === 'completed';
          
          if (wasCompleted) {
            // 如果已经是完成状态，则切换回进行中状态
            console.log(`🔄 Reverting task ${task.name} back to in_progress from completed`);
            message.info(`Task "${task.name}" marked as in progress`);
            return {
              ...task,
              status: 'in_progress' as const,
              progress: Math.max(task.progress - 10, 90) // 将进度降低到90%
            };
          } else {
            // 标记为完成
            console.log(`✅ Quick completing task: ${task.name}`);
            message.success(`Task "${task.name}" marked as completed!`);
            return {
              ...task,
              status: 'completed' as const,
              progress: 100 // 自动设置进度为100%
            };
          }
        }
        return task;
      });
      
      // 立即保存到localStorage
      try {
        localStorage.setItem(getStorageKey('tasks'), JSON.stringify(updatedTasks));
        console.log('✅ Quick action saved to localStorage');
      } catch (error) {
        console.error('Failed to save quick action:', error);
      }
      
      return updatedTasks;
    });
  }, [getStorageKey]);

  // 筛选任务 - 加强数据验证和修复
  const getFilteredTasks = useCallback(() => {
    // 首先检查是否有无效ID的任务，如果有则修复
    const invalidTasks = tasks.filter(task => 
      !task || !task.id || typeof task.id !== 'string' || task.id.trim() === ''
    );
    
    if (invalidTasks.length > 0) {
      console.error(`Found ${invalidTasks.length} tasks with invalid IDs:`, invalidTasks);
      
      // 自动修复无效ID的任务
      const repairedTasks = tasks.map(task => {
        if (!task || !task.id || typeof task.id !== 'string' || task.id.trim() === '') {
          console.warn('Auto-repairing task with invalid ID:', task);
          const repairedTask: Task = {
            ...task,
            id: crypto.randomUUID(),
            name: task?.name || 'Unnamed Task',
            status: (task?.status || 'not_started') as Task['status'],
            progress: typeof task?.progress === 'number' ? task.progress : 0,
            assignee: task?.assignee || 'Unassigned',
            description: task?.description || '',
            priority: (task?.priority || 'medium') as Task['priority'],
            startDate: task?.startDate || '',
            dueDate: task?.dueDate || ''
          };
          return repairedTask;
        }
        return task;
      });
      
      // 更新任务状态并保存
      setTasks(repairedTasks);
      
      // 立即保存修复后的数据
      try {
        localStorage.setItem(getStorageKey('tasks'), JSON.stringify(repairedTasks));
        console.log('Auto-repaired tasks saved to localStorage');
      } catch (error) {
        console.error('Failed to save repaired tasks:', error);
      }
      
      // 基于修复后的任务进行筛选
      return repairedTasks.filter(task => {
        if (taskFilter === 'all') return true;
        return task.status === taskFilter;
      });
    }
    
    // 正常筛选有效任务
    return tasks.filter(task => {
      // 最后的安全检查
      if (!task || !task.id || typeof task.id !== 'string' || task.id.trim() === '') {
        console.warn('Filtering out task with invalid ID:', task);
        return false;
      }
      
      // 按状态筛选
      if (taskFilter === 'all') return true;
      return task.status === taskFilter;
    });
  }, [tasks, taskFilter, getStorageKey]);

  // 使用useMemo缓存filteredTasks，防止每次渲染都重新计算
  const filteredTasks = useMemo(() => {
    return getFilteredTasks();
  }, [getFilteredTasks]);

  // 同步任务状态和进度的函数
  const syncTaskStatusAndProgress = useCallback(() => {
    const tasksNeedingSync = tasks.filter(task => {
      // 检查状态和进度不一致的情况
      return (task.status === 'completed' && task.progress !== 100) ||
             (task.status === 'not_started' && task.progress > 0) ||
             (task.status === 'in_progress' && (task.progress === 0 || task.progress === 100));
    });

    if (tasksNeedingSync.length > 0) {
      console.log(`🔄 Found ${tasksNeedingSync.length} tasks with inconsistent status/progress, syncing...`);
      
      setTasks(prevTasks => {
        const syncedTasks = prevTasks.map(task => {
          // 如果状态是completed但进度不是100%，自动设置为100%
          if (task.status === 'completed' && task.progress !== 100) {
            console.log(`📈 Auto-setting progress to 100% for completed task: ${task.name}`);
            return { ...task, progress: 100 };
          }
          
          // 如果状态是not_started但进度大于0，设置进度为0
          if (task.status === 'not_started' && task.progress > 0) {
            console.log(`📉 Auto-setting progress to 0% for not started task: ${task.name}`);
            return { ...task, progress: 0 };
          }
          
          // 如果状态是in_progress但进度为0，设置进度为适当值
          if (task.status === 'in_progress' && task.progress === 0) {
            console.log(`📊 Auto-setting progress to 10% for in progress task: ${task.name}`);
            return { ...task, progress: 10 };
          }
          
          // 如果状态是in_progress但进度为100%，更新状态为completed
          if (task.status === 'in_progress' && task.progress === 100) {
            console.log(`✅ Auto-setting status to completed for 100% progress task: ${task.name}`);
            return { ...task, status: 'completed' as const };
          }
          
          return task;
        });
        
        // 如果有同步更改，保存到localStorage
        const hasChanges = syncedTasks.some((task, index) => 
          task.progress !== prevTasks[index].progress || task.status !== prevTasks[index].status
        );
        
        if (hasChanges) {
          try {
            localStorage.setItem(getStorageKey('tasks'), JSON.stringify(syncedTasks));
            console.log('✅ Synced tasks saved to localStorage');
            // 移除message提示以减少界面干扰
            // message.success(`Synchronized ${tasksNeedingSync.length} task(s) status and progress`);
          } catch (error) {
            console.error('Failed to save synced tasks:', error);
          }
        }
        
        return syncedTasks;
      });
    }
  }, [tasks, getStorageKey]);

  // 禁用定期数据完整性检查以防止界面跳动
  // 只在特定操作后进行数据检查，而不是定时检查
  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     const invalidTasks = tasks.filter(task => 
  //       !task || !task.id || typeof task.id !== 'string' || task.id.trim() === ''
  //     );
      
  //     if (invalidTasks.length > 0) {
  //       console.warn('🚨 Periodic check found tasks with invalid IDs:', invalidTasks);
  //       cleanAndRepairData();
  //     }
      
  //     // 同时检查状态和进度的一致性
  //     const inconsistentTasks = tasks.filter(task => {
  //       return (task.status === 'completed' && task.progress !== 100) ||
  //              (task.status === 'not_started' && task.progress > 0);
  //     });
      
  //     if (inconsistentTasks.length > 0) {
  //       console.warn('🔄 Periodic check found tasks with inconsistent status/progress');
  //       syncTaskStatusAndProgress();
  //     }
  //   }, 30000); // 每30秒检查一次

  //   return () => clearInterval(interval);
  // }, [tasks, cleanAndRepairData, syncTaskStatusAndProgress]);

  // 禁用组件加载后的自动同步以防止界面跳动
  // 改为只在用户操作时进行同步
  // useEffect(() => {
  //   if (tasks.length > 0) {
  //     // 延迟执行，确保任务数据已完全加载
  //     const timeoutId = setTimeout(() => {
  //       syncTaskStatusAndProgress();
  //     }, 1000);
      
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [tasks.length, syncTaskStatusAndProgress]);

  // 处理任务进度更新
  const handleTaskProgressChange = useCallback((taskId: string, progress: number) => {
    console.log(`Updating progress for task ${taskId} to ${progress}%`);
    
    // 验证taskId是否有效
    if (!taskId || typeof taskId !== 'string' || taskId.trim() === '') {
      console.error('handleTaskProgressChange: Invalid taskId', taskId);
      return;
    }
    
    setTasks(prev => {
      const updatedTasks = prev.map(task => {
        if (task.id === taskId) {
          console.log(`Found task to update: ${task.name}, old progress: ${task.progress}%, new progress: ${progress}%`);
          // 确保在更新任务时保持ID完整性
          const updatedTask: Task = { 
            ...task, 
            id: task.id, // 明确保持原ID
            progress: Math.round(progress), // 确保是整数
            // 根据进度自动更新状态
            status: progress === 100 ? 'completed' as const : 
                   progress > 0 ? 'in_progress' as const : 'not_started' as const
          };
          
          // 验证更新后的任务ID仍然有效
          if (!updatedTask.id || typeof updatedTask.id !== 'string' || updatedTask.id.trim() === '') {
            console.error('Task ID became invalid after update:', updatedTask);
            // 如果ID无效，重新生成一个新的ID
            updatedTask.id = crypto.randomUUID();
            console.log('Generated new ID for task:', updatedTask.id);
          }
          
          return updatedTask;
        }
        return task;
      });
      
      // 验证所有任务都有有效的ID
      const validatedTasks: Task[] = updatedTasks.map(task => {
        if (!task.id || typeof task.id !== 'string' || task.id.trim() === '') {
          console.warn('Found task with invalid ID during progress update, regenerating:', task);
          return {
            ...task,
            id: crypto.randomUUID()
          };
        }
        return task;
      });
      
      // 立即保存到localStorage
      try {
        localStorage.setItem(getStorageKey('tasks'), JSON.stringify(validatedTasks));
        console.log('Tasks saved to localStorage after progress update');
      } catch (error) {
        console.error('Failed to save tasks to localStorage:', error);
      }
      
      return validatedTasks;
    });
  }, [getStorageKey]);

  // 处理任务拖拽排序
  const handleDragStart = (event: any) => {
    const { active } = event;
    const task = tasks.find(task => task.id === active.id);
    setActiveTask(task || null);
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    console.log('Drag end - Active:', active?.id, 'Over:', over?.id);
    
    // 清除活跃任务状态
    setActiveTask(null);

    // 检查over是否存在，以及active和over的id是否不同
    if (over && active.id !== over.id) {
      console.log('Moving task from', active.id, 'to', over.id);
      
      // 立即更新状态，强制React重新渲染
      setTasks((prevItems) => {
        const oldIndex = prevItems.findIndex((item) => item.id === active.id);
        const newIndex = prevItems.findIndex((item) => item.id === over.id);
        
        console.log('Moving from index', oldIndex, 'to index', newIndex);
        
        // 确保索引有效
        if (oldIndex === -1 || newIndex === -1) {
          console.log('Invalid indices, aborting move');
          return prevItems;
        }
        
        // 使用最可靠的数组移动方法，确保立即生效
        const newItems = [...prevItems];
        const movedItem = newItems[oldIndex];
        
        // 移除原位置的元素
        newItems.splice(oldIndex, 1);
        // 在新位置插入元素
        newItems.splice(newIndex, 0, movedItem);
        
        // 立即更新order字段
        const updatedItems = newItems.map((item, index) => ({
          ...item,
          order: index + 1
        }));
        
        console.log('New task order:', updatedItems.map(item => item.name));
        
        // 异步保存到localStorage，不影响UI更新
        setTimeout(() => {
          try {
            localStorage.setItem(getStorageKey('tasks'), JSON.stringify(updatedItems));
            console.log('Tasks saved after drag and drop');
          } catch (error) {
            console.error('Failed to save tasks to localStorage:', error);
          }
        }, 0);
        
        return updatedItems;
      });
      
      // 立即强制重新渲染，确保拖拽效果立即生效
      setForceRender(prev => prev + 1);
      
      // 延迟显示成功消息，确保UI先更新
      setTimeout(() => {
        message.success('Task order updated successfully!');
      }, 100);
    } else {
      console.log('Drag cancelled or same position');
    }
  };

  // 上移任务
  const handleMoveTaskUp = useCallback((taskId: string) => {
    console.log('handleMoveTaskUp called with taskId:', taskId);
    
    setTasks((prevItems) => {
      const currentIndex = prevItems.findIndex((item) => item.id === taskId);
      
      // 如果已经是第一个，不能再上移
      if (currentIndex <= 0) {
        console.log('Task is already at the top, currentIndex:', currentIndex);
        message.warning('Task is already at the top');
        return prevItems;
      }
      
      console.log(`Moving task ${taskId} up from index ${currentIndex} to ${currentIndex - 1}`);
      
      // 创建新的数组引用，确保React能检测到变化
      const newItems = [...prevItems];
      const [movedItem] = newItems.splice(currentIndex, 1);
      newItems.splice(currentIndex - 1, 0, movedItem);
      
      // 更新order字段
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));
      
      // 立即保存到localStorage
      requestAnimationFrame(() => {
        try {
          localStorage.setItem(getStorageKey('tasks'), JSON.stringify(updatedItems));
          console.log('Tasks saved to localStorage after move up');
        } catch (error) {
          console.error('Failed to save tasks to localStorage:', error);
        }
      });
      
      console.log('New task order:', updatedItems.map(item => item.name));
      return updatedItems;
    });
  }, [getStorageKey]);

  // 下移任务
  const handleMoveTaskDown = useCallback((taskId: string) => {
    console.log('handleMoveTaskDown called with taskId:', taskId);
    
    setTasks((prevItems) => {
      const currentIndex = prevItems.findIndex((item) => item.id === taskId);
      
      // 如果已经是最后一个，不能再下移
      if (currentIndex >= prevItems.length - 1 || currentIndex === -1) {
        console.log('Task is already at the bottom or not found, currentIndex:', currentIndex, 'total items:', prevItems.length);
        message.warning('Task is already at the bottom');
        return prevItems;
      }
      
      console.log(`Moving task ${taskId} down from index ${currentIndex} to ${currentIndex + 1}`);
      
      // 创建新的数组引用，确保React能检测到变化
      const newItems = [...prevItems];
      const [movedItem] = newItems.splice(currentIndex, 1);
      newItems.splice(currentIndex + 1, 0, movedItem);
      
      // 更新order字段
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));
      
      // 立即保存到localStorage
      requestAnimationFrame(() => {
        try {
          localStorage.setItem(getStorageKey('tasks'), JSON.stringify(updatedItems));
          console.log('Tasks saved to localStorage after move down');
        } catch (error) {
          console.error('Failed to save tasks to localStorage:', error);
        }
      });
      
      console.log('New task order:', updatedItems.map(item => item.name));
      return updatedItems;
    });
  }, [getStorageKey]);

  // 处理编辑里程碑
  const handleEditMilestone = (milestone: Milestone) => {
    setEditingMilestone(milestone);
    milestoneForm.setFieldsValue({
      name: milestone.name,
      description: milestone.description,
      targetDate: milestone.targetDate ? dayjs(milestone.targetDate) : null
    });
    setShowAddMilestoneModal(true);
  };

  // 保存里程碑
  const handleSaveMilestone = async (values: any) => {
    try {
      // 处理日期格式
      const formattedValues = {
        ...values,
        targetDate: values.targetDate ? values.targetDate.format('YYYY-MM-DD') : ''
      };

      if (editingMilestone) {
        // 编辑现有里程碑
        setMilestones(prev => prev.map(milestone => 
          milestone.id === editingMilestone.id 
            ? { ...milestone, ...formattedValues }
            : milestone
        ));
        message.success('Milestone updated successfully');
      } else {
        // 添加新里程碑，设置默认状态和进度
        const newMilestone: Milestone = {
          id: Date.now().toString(),
          ...formattedValues,
          status: 'upcoming',  // 默认状态为即将开始
          progress: 0          // 默认进度为0%
        };
        setMilestones(prev => [...prev, newMilestone]);
        message.success('Milestone added successfully');
      }
      setShowAddMilestoneModal(false);
      milestoneForm.resetFields();
    } catch (error) {
      message.error('Operation failed, please try again');
    }
  };

  // 删除里程碑
  const handleDeleteMilestone = (milestoneId: string) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: 'Are you sure you want to delete this milestone? This action cannot be undone.',
      onOk: () => {
        setMilestones(prev => prev.filter(m => m.id !== milestoneId));
        message.success('Milestone deleted successfully');
      }
    });
  };

  // 处理状态变更
  const handleStatusChange = (milestoneId: string, newStatus: string) => {
    setMilestones(prev => prev.map(milestone => 
      milestone.id === milestoneId 
        ? { 
            ...milestone, 
            status: newStatus as 'upcoming' | 'in_progress' | 'completed' | 'delayed',
            progress: newStatus === 'completed' ? 100 : milestone.progress
          }
        : milestone
    ));
    message.success('Status updated successfully');
  };



  // 直接拖拽更新进度
  const handleProgressSliderChange = (milestoneId: string, progress: number) => {
    setMilestones(prev => prev.map(milestone => 
      milestone.id === milestoneId ? { ...milestone, progress } : milestone
    ));
  };

  // 删除团队成员
  const handleDeleteTeamMember = (memberId: string) => {
    setTeamMembers(prev => prev.filter(member => member.id !== memberId));
    message.success('Team member removed successfully');
  };

  const handleCreateTeamMember = () => {
    setEditingTeamMember(null);
    teamMemberForm.resetFields();
    setShowTeamMemberModal(true);
  };

  const handleEditTeamMember = (member: TeamMember) => {
    setEditingTeamMember(member);
    teamMemberForm.setFieldsValue({
      name: member.name,
      role: member.role,
      email: member.email
    });
    setShowTeamMemberModal(true);
  };

  const handleSaveTeamMember = async (values: any) => {
    try {
      if (editingTeamMember) {
        setTeamMembers(prev => prev.map(member => 
          member.id === editingTeamMember.id 
            ? { ...member, ...values, workload: member.workload, availability: member.availability }
            : member
        ));
        message.success('Team member updated successfully');
      } else {
        const newTeamMember: TeamMember = {
          id: Date.now().toString(),
          ...values,
          workload: 0,
          availability: 'available' as const
        };
        setTeamMembers(prev => [...prev, newTeamMember]);
        message.success('Team member added successfully');
      }
      setShowTeamMemberModal(false);
      teamMemberForm.resetFields();
      setEditingTeamMember(null);
    } catch (error) {
      message.error('Failed to save team member');
    }
  };

  // 预算相关处理函数
  const handleCreateBudgetItem = () => {
    setEditingBudgetItem(null);
    budgetItemForm.resetFields();
    // Set default values for new budget item
    budgetItemForm.setFieldsValue({ 
      currency: selectedCurrency,
      vatRate: 20
    });
    setShowBudgetItemModal(true);
  };

  const handleEditBudgetItem = (item: BudgetItem) => {
    setEditingBudgetItem(item);
    
    // For backward compatibility: if VAT fields don't exist, calculate them from plannedAmount
    let amountExclVat = item.amountExclVat;
    let vatRate = item.vatRate ?? 20; // Default VAT rate - 使用 ?? 操作符正确处理 0 值
    let vatAmount = item.vatAmount;
    
    if (!amountExclVat && item.plannedAmount) {
      // Assume plannedAmount includes VAT, calculate backwards
      amountExclVat = item.plannedAmount / (1 + vatRate / 100);
      vatAmount = item.plannedAmount - amountExclVat;
    }
    
    budgetItemForm.setFieldsValue({
      budgetItem: item.budgetItem || item.category, // 向后兼容，优先使用新字段
      description: item.description,
      currency: item.currency || selectedCurrency, // 兼容旧数据
      plannedAmount: item.plannedAmount,
      amountExclVat: amountExclVat,
      vatRate: vatRate,
      vatAmount: vatAmount
    });
    setShowBudgetItemModal(true);
  };

  const handleSaveBudgetItem = async (values: any) => {
    try {
      if (editingBudgetItem) {
        setBudgetItems(prev => prev.map(item => 
          item.id === editingBudgetItem.id 
            ? { ...item, ...values }
            : item
        ));
        message.success('Budget item updated successfully');
      } else {
        const newBudgetItem: BudgetItem = {
          id: Date.now().toString(),
          ...values,
          spentAmount: 0
        };
        setBudgetItems(prev => [...prev, newBudgetItem]);
        message.success('Budget item created successfully');
      }
      setShowBudgetItemModal(false);
      budgetItemForm.resetFields();
      setEditingBudgetItem(null);
    } catch (error) {
      message.error('Failed to save budget item');
    }
  };

  const handleDeleteBudgetItem = (itemId: string) => {
    setBudgetItems(prev => prev.filter(item => item.id !== itemId));
    message.success('Budget item deleted successfully');
  };

  // 使用全局货币上下文的汇率和转换函数，确保一致性
  const { convertAmount, getCurrencySymbol } = useCurrency();

  // 计算预算总览数据
  const getBudgetOverview = () => {
    const totalPlanned = budgetItems.reduce((sum, item) => {
      const convertedAmount = convertAmount(item.plannedAmount, item.currency, selectedCurrency);
      return sum + convertedAmount;
    }, 0);
    
    // 计算实际的多货币支出总额，转换到选定货币
    let totalSpentInSelectedCurrency = 0;
    budgetItems.forEach((budgetItem) => {
      const budgetItemName = getBudgetItemName(budgetItem);
      const actualSpent = spentByCategory[budgetItemName] || 0;
      // actualSpent已经是预算项的货币，需要转换到选定货币
      const convertedSpent = convertAmount(actualSpent, budgetItem.currency, selectedCurrency);
      totalSpentInSelectedCurrency += convertedSpent;
    });

    const remaining = totalPlanned - totalSpentInSelectedCurrency;
    // 修复：不限制utilization在100%，显示实际百分比
    const utilization = totalPlanned > 0 ? Math.round((totalSpentInSelectedCurrency / totalPlanned) * 100) : 0;
    
    console.log('🔍 Budget Overview Debug:', {
      totalPlanned,
      totalSpentInSelectedCurrency,
      remaining,
      utilization: `${utilization}%`,
      selectedCurrency,
      spentByCategory
    });
    
    return { totalPlanned, totalSpent: totalSpentInSelectedCurrency, remaining, utilization };
  };

  // 获取预算项名称，优先使用新字段，向后兼容旧字段
  const getBudgetItemName = (item: BudgetItem): string => {
    return item.budgetItem || item.category || '';
  };

  // 计算单个预算项的利用率 (类似 Collection Process 逻辑)
  const calculateBudgetUtilization = (budgetItem: BudgetItem) => {
    try {
      const projectId = project?.id || 'default';
      
      // 1. 获取 Expense Invoices 数据
      const expenseInvoicesData = localStorage.getItem('finance_expense_invoices');
      const expenseInvoices = expenseInvoicesData ? JSON.parse(expenseInvoicesData) : [];
      
      // 2. 获取 Transaction 数据 (expense 类型)
      const transactionsData = localStorage.getItem('finance_transactions');
      const transactions = transactionsData ? JSON.parse(transactionsData) : [];
      const expenseTransactions = transactions.filter((tx: any) => tx.type === 'expense');
      
      const budgetItemName = getBudgetItemName(budgetItem);
      
      // 3. 查找与当前预算项关联的 Expense Invoices
      const matchingExpenseInvoices = expenseInvoices.filter((invoice: any) => {
        return (
          // 通过 budgetCategoryId 匹配
          invoice.budgetCategoryId === budgetItem.id ||
          // 通过 category 字段匹配
          invoice.category === budgetItemName ||
          // 通过描述中包含预算项名称匹配
          (invoice.description && invoice.description.includes(budgetItemName))
        );
      });
      
      // 4. 查找与预算项关联的 Expense Transactions
      const matchingExpenseTransactions = expenseTransactions.filter((tx: any) => {
        return (
          // 通过 budgetItemId 匹配
          tx.budgetItemId === budgetItem.id ||
          // 通过 category 字段匹配
          tx.category === budgetItemName ||
          // 通过描述中包含预算项名称匹配
          (tx.description && tx.description.includes(budgetItemName)) ||
          // 通过项目关联匹配
          (tx.project === projectId && tx.description && tx.description.includes(budgetItemName))
        );
      });
      
      // 5. 计算总支出金额 (类似 Collection Process 计算已收款)
      let totalExpensed = 0;
      
      // 从已确认的 Expense Invoices 计算
      matchingExpenseInvoices.forEach((invoice: any) => {
        if (['Paid', 'Confirmed', 'Processed'].includes(invoice.status)) {
          totalExpensed += invoice.totalAmount || invoice.amount || 0;
        }
      });
      
      // 从已完成的 Expense Transactions 计算
      matchingExpenseTransactions.forEach((tx: any) => {
        if (['completed', 'paid', 'confirmed', 'processed'].includes(tx.status.toLowerCase())) {
          totalExpensed += tx.totalAmount || tx.amount || 0;
        }
      });
      
      // 6. 计算利用率 (类似 Collection Process 计算进度)
      const plannedAmount = budgetItem.plannedAmount || 0;
      const utilizationRate = plannedAmount > 0 ? Math.round((totalExpensed / plannedAmount) * 100) : 0;
      const remainingBudget = Math.max(0, plannedAmount - totalExpensed);
      
      // 7. 确定状态 (类似 Collection Process 状态逻辑)
      let expenseStatus = 'pending';
      if (utilizationRate >= 100) {
        expenseStatus = utilizationRate > 100 ? 'over_budget' : 'fully_utilized';
      } else if (utilizationRate > 0) {
        expenseStatus = 'partial';
      }
      
      // 8. 调试日志
      console.log(`💰 Budget Utilization for "${budgetItemName}":`, {
        plannedAmount,
        totalExpensed,
        utilizationRate: `${utilizationRate}%`,
        expenseStatus,
        matchingInvoices: matchingExpenseInvoices.length,
        matchingTransactions: matchingExpenseTransactions.length
      });
      
      return {
        utilizationRate: Math.min(utilizationRate, 999), // 防止显示过大的百分比
        totalExpensed,
        remainingBudget,
        expenseStatus
      };
      
    } catch (error) {
      console.error('Error calculating budget utilization:', error);
      return {
        utilizationRate: 0,
        totalExpensed: 0,
        remainingBudget: budgetItem.plannedAmount || 0,
        expenseStatus: 'pending'
      };
    }
  };

  // 获取预算数据
  const budgetOverview = getBudgetOverview();

  // 风险管理相关函数
  const calculateRiskScore = (probability: string, impact: string): number => {
    const probabilityScore = { low: 1, medium: 2, high: 3, critical: 4 };
    const impactScore = { low: 1, medium: 2, high: 3, critical: 4 };
    return probabilityScore[probability as keyof typeof probabilityScore] * 
           impactScore[impact as keyof typeof impactScore];
  };

  const riskStats = useMemo(() => {
    const total = risks.length;
    const critical = risks.filter(r => r.riskScore >= 12).length;
    const high = risks.filter(r => r.riskScore >= 9 && r.riskScore < 12).length;
    const medium = risks.filter(r => r.riskScore >= 6 && r.riskScore < 9).length;
    const low = risks.filter(r => r.riskScore < 6).length;
    const active = risks.filter(r => ['identified', 'analyzing', 'mitigating'].includes(r.status)).length;
    const closed = risks.filter(r => r.status === 'closed').length;
    
    return { total, critical, high, medium, low, active, closed };
  }, [risks]);

  const renderRiskCategoryTag = (category: string) => {
    const categoryConfig = {
      'technical': { color: 'blue', text: 'Technical' },
      'business': { color: 'green', text: 'Business' },
      'operational': { color: 'orange', text: 'Operational' },
      'financial': { color: 'red', text: 'Financial' },
      'external': { color: 'purple', text: 'External' }
    };
    const config = categoryConfig[category as keyof typeof categoryConfig] || categoryConfig['technical'];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderRiskStatusTag = (status: string) => {
    const statusConfig = {
      'identified': { color: 'default', text: 'Identified' },
      'analyzing': { color: 'processing', text: 'Analyzing' },
      'mitigating': { color: 'warning', text: 'Mitigating' },
      'monitoring': { color: 'success', text: 'Monitoring' },
      'closed': { color: 'success', text: 'Closed' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['identified'];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const renderRiskScoreTag = (score: number) => {
    if (score >= 12) return <Tag color="red">Critical ({score})</Tag>;
    if (score >= 9) return <Tag color="orange">High ({score})</Tag>;
    if (score >= 6) return <Tag color="blue">Medium ({score})</Tag>;
    return <Tag color="green">Low ({score})</Tag>;
  };

  const handleCreateRisk = () => {
    setEditingRisk(null);
    riskForm.resetFields();
    setShowRiskModal(true);
  };

  const handleEditRisk = (risk: Risk) => {
    setEditingRisk(risk);
    riskForm.setFieldsValue({
      ...risk,
      identifiedDate: dayjs(risk.identifiedDate),
      targetResolutionDate: risk.targetResolutionDate ? dayjs(risk.targetResolutionDate) : null
    });
    setShowRiskModal(true);
  };

  const handleSaveRisk = async (values: any) => {
    try {
      const riskScore = calculateRiskScore(values.probability, values.impact);
      const riskData = {
        ...values,
        identifiedDate: values.identifiedDate ? values.identifiedDate.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        targetResolutionDate: values.targetResolutionDate ? values.targetResolutionDate.format('YYYY-MM-DD') : undefined,
        riskScore
      };

      if (editingRisk) {
        setRisks(prev => prev.map(risk => 
          risk.id === editingRisk.id ? { ...risk, ...riskData } : risk
        ));
        message.success('Risk updated successfully');
      } else {
        const newRisk = {
          id: Date.now().toString(),
          ...riskData
        };
        setRisks(prev => [...prev, newRisk]);
        message.success('Risk added successfully');
      }

      setShowRiskModal(false);
      riskForm.resetFields();
      setEditingRisk(null);
    } catch (error) {
      console.error('Error saving risk:', error);
      message.error('Failed to save risk. Please check all fields and try again.');
    }
  };

  const handleDeleteRisk = (riskId: string) => {
    setRisks(prev => prev.filter(risk => risk.id !== riskId));
    message.success('Risk deleted successfully');
  };

  const handleRiskStatusChange = (riskId: string, newStatus: Risk['status']) => {
    setRisks(prev => prev.map(risk => 
      risk.id === riskId ? { ...risk, status: newStatus } : risk
    ));
    message.success('Risk status updated');
  };

  // 各页签的保存函数
  const handleSaveMilestones = async () => {
    setSaving(true);
    try {
      const milestoneData = {
        milestones: milestones,
        activeTab: 'milestone',
        timestamp: new Date().toISOString()
      };
      
      if (onSave) await onSave(milestoneData);
      message.success('Milestones saved successfully!');
    } catch (error) {
      message.error('Failed to save milestones. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveTasks = async () => {
    setSaving(true);
    try {
      const taskData = {
        tasks: tasks,
        activeTab: 'tasks',
        timestamp: new Date().toISOString()
      };
      
      if (onSave) await onSave(taskData);
      message.success('Tasks saved successfully!');
    } catch (error) {
      message.error('Failed to save tasks. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveRisks = async () => {
    setSaving(true);
    try {
      const riskData = {
        risks: risks,
        activeTab: 'risks',
        timestamp: new Date().toISOString()
      };
      
      if (onSave) await onSave(riskData);
      message.success('Risks saved successfully!');
    } catch (error) {
      message.error('Failed to save risks. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveTeamMembers = async () => {
    setSaving(true);
    try {
      const teamData = {
        teamMembers: teamMembers,
        activeTab: 'team',
        timestamp: new Date().toISOString()
      };
      
      if (onSave) await onSave(teamData);
      message.success('Team members saved successfully!');
    } catch (error) {
      message.error('Failed to save team members. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveBudgetItems = async () => {
    setSaving(true);
    try {
      const budgetData = {
        budgetItems: budgetItems,
        activeTab: 'budget',
        timestamp: new Date().toISOString()
      };
      
      if (onSave) await onSave(budgetData);
      message.success('Budget items saved successfully!');
    } catch (error) {
      message.error('Failed to save budget items. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // 使用useMemo优化风险数据过滤
  const filteredRisks = useMemo(() => {
    return risks.filter(risk => {
      if (riskFilter === 'all') return true;
      if (riskFilter === 'active') return ['identified', 'analyzing', 'mitigating'].includes(risk.status);
      if (riskFilter === 'critical') return risk.riskScore >= 12;
      if (riskFilter === 'high') return risk.riskScore >= 9 && risk.riskScore < 12;
      if (riskFilter === 'closed') return risk.status === 'closed';
      return risk.status === riskFilter;
    });
  }, [risks, riskFilter]);

  // 风险表格列定义
  const riskColumns = [
    {
      title: 'Risk Title',
      dataIndex: 'title',
      key: 'title',
      width: '30%',
      render: (text: string, record: Risk) => (
        <>
          <div style={{ fontWeight: 500, marginBottom: '2px', lineHeight: '1.4' }}>{text}</div>
          {record.description && (
            <div style={{ color: '#666', fontSize: '12px', lineHeight: '1.3' }}>
              {record.description}
            </div>
          )}
        </>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: '12%',
      render: (category: string) => renderRiskCategoryTag(category)
    },
    {
      title: 'Risk Score',
      dataIndex: 'riskScore',
      key: 'riskScore',
      width: '12%',
      render: (score: number) => renderRiskScoreTag(score),
      sorter: (a: Risk, b: Risk) => b.riskScore - a.riskScore
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: '15%',
      render: (status: Risk['status'], record: Risk) => (
        <Select
          value={status}
          onChange={(value: Risk['status']) => handleRiskStatusChange(record.id, value)}
          size="small"
          style={{ width: '100%' }}
        >
          <Option value="identified">Identified</Option>
          <Option value="analyzing">Analyzing</Option>
          <Option value="mitigating">Mitigating</Option>
          <Option value="monitoring">Monitoring</Option>
          <Option value="closed">Closed</Option>
        </Select>
      )
    },
    {
      title: 'Owner',
      dataIndex: 'owner',
      key: 'owner',
      width: '12%'
    },
    {
      title: 'Target Date',
      dataIndex: 'targetResolutionDate',
      key: 'targetResolutionDate',
      width: '12%',
      render: (date: string) => date ? formatDate(date) : '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '7%',
      render: (_: any, record: Risk) => (
        <Space size="small">
          <Tooltip title="Edit Risk">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEditRisk(record)}
            />
          </Tooltip>
          <Tooltip title="Delete Risk">
            <Popconfirm
              title="Delete Risk"
              description="Are you sure you want to delete this risk?"
              onConfirm={() => handleDeleteRisk(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  // 从Finance模块加载总支出
  useEffect(() => {
    const fetchSpentData = async () => {
      if (!project?.id || !budgetItems || budgetItems.length === 0) {
        setSpentByCategory({});
        setTotalSpent(0);
        return;
      }
      let transactions: any[] = [];
      try {
        const txKey = 'finance_transactions';
        const data = localStorage.getItem(txKey);
        if (data) {
          const rawList = JSON.parse(data);
          transactions = rawList.filter((item: any) => {
            // 增强项目匹配逻辑：支持多种项目标识符
            const projectMatch = !item.project || 
                                item.project === project.id || 
                                item.project === project?.name ||
                                item.project === 'Web Development' ||  // 明确匹配Web Development项目
                                String(item.project).toLowerCase().includes('web development');
            const isExpense = (item.type === 'expense' || item.type === 'EXPENSE');
            const isCompleted = (item.status === 'completed' || item.status === 'COMPLETED');
            
            console.log(`🔍 Transaction filtering:`, {
              transactionId: item.transactionId,
              project: item.project,
              currentProject: { id: project.id, name: project?.name },
              projectMatch,
              isExpense,
              isCompleted,
              included: projectMatch && isExpense && isCompleted
            });
            
            return projectMatch && isExpense && isCompleted;
          });
        }
      } catch (e) {
        console.error('Failed to parse local finance_transactions:', e);
        transactions = [];
      }
      if (transactions.length === 0) {
        try {
          const allInvoices: Invoice[] = await financeService.getInvoices(project.id);
          transactions = allInvoices.filter(
            (invoice: Invoice) =>
              (invoice.type === 'expense' || invoice.type === 'EXPENSE') &&
              (invoice.status === 'completed' || invoice.status === 'COMPLETED')
          ).map((invoice: Invoice) => ({
            category: invoice.revenueItem || '', // Invoice无category字段
            amount: invoice.totalAmount,
            currency: 'EUR', // Invoice无currency字段，假定为EUR
          }));
        } catch (error) {
          console.error('Failed to fetch total spent from finance module:', error);
          message.error('Failed to load expense data from Finance module');
          setSpentByCategory({});
          setTotalSpent(0);
          return;
        }
      }
          const spentMap: { [key: string]: number } = {};
    let total = 0;
    
    // 调试信息：显示加载的交易数据
    console.log('🔍 Budget Sync Debug - Loaded transactions:', transactions);
    console.log('🔍 Budget Sync Debug - Budget items:', budgetItems);
    
    budgetItems.forEach((budgetItem) => {
      const budgetItemName = getBudgetItemName(budgetItem);
      const cat = budgetItemName.toLowerCase().trim();
      let sum = 0;
      let matchedTransactions: any[] = [];
      
                    transactions.forEach((tx) => {
        const txCat = String(tx.category || '').toLowerCase().trim();
        const budgetItemRef = tx.budgetItem || '';
        
        // 增强匹配逻辑：支持category匹配和budgetItem字段匹配
        const categoryMatch = cat && txCat && cat === txCat;
        const budgetItemMatch = budgetItemRef && String(budgetItemRef).toLowerCase().trim() === cat;
        
        if (categoryMatch || budgetItemMatch) {
          // 强化金额字段获取：支持多种可能的字段名
          let rawAmount = tx.amount || tx.totalAmount || tx.value || 0;
          // 如果金额是字符串，移除货币符号和逗号
          if (typeof rawAmount === 'string') {
            rawAmount = rawAmount.replace(/[^\d.-]/g, '');
          }
          let txAmount = Number(rawAmount) || 0;
          
          // 强化币种获取：支持多种可能的字段名和检测逻辑
          let txCurrency = tx.currency || tx.originalCurrency || 'EUR';
          
          // 如果仍无币种且有描述，尝试从描述检测
          if (!tx.currency && !tx.originalCurrency && tx.description) {
            const desc = String(tx.description).toLowerCase();
            if (desc.includes('cny') || desc.includes('¥') || desc.includes('人民币')) {
              txCurrency = 'CNY';
            } else if (desc.includes('usd') || desc.includes('$') || desc.includes('美元')) {
              txCurrency = 'USD';
            }
          }
          
          console.log(`🔍 Processing transaction:`, {
            transactionId: tx.transactionId,
            category: tx.category,
            budgetItem: tx.budgetItem,
            matchType: categoryMatch ? 'category' : 'budgetItem',
            rawAmount,
            txAmount,
            txCurrency,
            budgetCurrency: budgetItem.currency,
            description: tx.description
          });
          
          if (txCurrency !== budgetItem.currency) {
            const originalAmount = txAmount;
            txAmount = convertAmount(txAmount, txCurrency, budgetItem.currency);
            console.log(`💱 Currency conversion: ${originalAmount} ${txCurrency} → ${txAmount} ${budgetItem.currency}`);
          }
          sum += txAmount;
          matchedTransactions.push({ ...tx, convertedAmount: txAmount, originalAmount: rawAmount });
        } else {
          // 调试信息：显示不匹配的transaction
          console.log(`❌ Transaction not matched:`, {
            transactionId: tx.transactionId,
            txCategory: txCat,
            budgetItemRef: budgetItemRef,
            budgetCategory: cat,
            description: tx.description
          });
        }
      });
      
      spentMap[budgetItemName] = sum;
      total += sum;
      
      // 调试信息：显示每个预算项的匹配情况
      console.log(`💡 Matched transactions for "${budgetItemName}" (${cat}):`, matchedTransactions);
      console.log(`💰 Budget Category "${budgetItemName}" (${cat}):`, {
        plannedAmount: budgetItem.plannedAmount,
        currency: budgetItem.currency,
        matchedTransactions,
        totalSpent: sum
      });
    });
    
    setSpentByCategory(spentMap);
    setTotalSpent(total);
    
    // 调试信息：显示最终结果
    console.log('🔍 Final spentByCategory result:', spentMap);
    };
    fetchSpentData();
  }, [project?.id, budgetItems, forceRender]);

  return (
    <div className="ltc-stage-content">
      <style>{`
        .custom-slider .ant-slider-mark,
        .custom-slider .ant-slider-mark-text,
        .custom-slider .ant-slider-dot,
        .custom-slider .ant-slider-step {
          display: none !important;
        }
        .custom-slider::before,
        .custom-slider::after {
          display: none !important;
        }
      `}</style>
      {/* 添加/编辑里程碑模态框 */}
      <Modal
        title={editingMilestone ? 'Edit Milestone' : 'Add Milestone'}
        open={showAddMilestoneModal}
        onCancel={() => {
          setShowAddMilestoneModal(false);
          milestoneForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={milestoneForm}
          layout="vertical"
          onFinish={handleSaveMilestone}
        >
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item
                label="Milestone Name"
                name="name"
                rules={[{ required: true, message: 'Please enter milestone name' }]}
              >
                <Input 
                  placeholder="Enter milestone name" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Description"
                name="description"
                rules={[{ required: true, message: 'Please enter description' }]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="Enter milestone description" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Target Date"
                name="targetDate"
                rules={[{ required: true, message: 'Please select target date' }]}
              >
                <DatePicker 
                  style={{ width: '100%', fontSize: '14px' }} 
                  placeholder="Select target date"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
          </Row>
          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setShowAddMilestoneModal(false)}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingMilestone ? 'Update' : 'Add'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 添加/编辑风险模态框 */}
      <Modal
        title={editingRisk ? 'Edit Risk' : 'Add Risk'}
        open={showRiskModal}
        onCancel={() => {
          setShowRiskModal(false);
          riskForm.resetFields();
          setEditingRisk(null);
        }}
        footer={null}
        width={800}
      >
        <Form
          form={riskForm}
          layout="vertical"
          onFinish={handleSaveRisk}
        >
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item
                label={<span>Risk Title <span style={{ color: 'red' }}>*</span></span>}
                name="title"
                rules={[{ required: true, message: 'Please enter risk title' }]}
              >
                <Input 
                  placeholder="Enter risk title" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<span>Category <span style={{ color: 'red' }}>*</span></span>}
                name="category"
                rules={[{ required: true, message: 'Please select category' }]}
              >
                <Select 
                  placeholder="Select category"
                  style={{ fontSize: '14px' }}
                >
                  <Option value="technical">Technical</Option>
                  <Option value="business">Business</Option>
                  <Option value="operational">Operational</Option>
                  <Option value="financial">Financial</Option>
                  <Option value="external">External</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Description"
                name="description"
                // rules={[{ required: true, message: 'Please enter description' }]}
              >
                <TextArea 
                  rows={3} 
                  placeholder="Enter detailed risk description" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span>Probability <span style={{ color: 'red' }}>*</span></span>}
                name="probability"
                rules={[{ required: true, message: 'Please select probability' }]}
              >
                <Select 
                  placeholder="Select probability"
                  style={{ fontSize: '14px' }}
                >
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span>Impact <span style={{ color: 'red' }}>*</span></span>}
                name="impact"
                rules={[{ required: true, message: 'Please select impact' }]}
              >
                <Select 
                  placeholder="Select impact"
                  style={{ fontSize: '14px' }}
                >
                  <Option value="low">Low</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="high">High</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={<span>Status <span style={{ color: 'red' }}>*</span></span>}
                name="status"
                rules={[{ required: true, message: 'Please select status' }]}
                initialValue="identified"
              >
                <Select 
                  placeholder="Select status"
                  style={{ fontSize: '14px' }}
                >
                  <Option value="identified">Identified</Option>
                  <Option value="analyzing">Analyzing</Option>
                  <Option value="mitigating">Mitigating</Option>
                  <Option value="monitoring">Monitoring</Option>
                  <Option value="closed">Closed</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<span>Risk Owner <span style={{ color: 'red' }}>*</span></span>}
                name="owner"
                rules={[{ required: true, message: 'Please enter risk owner' }]}
              >
                <Input 
                  placeholder="Enter risk owner name" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Target Resolution Date"
                name="targetResolutionDate"
              >
                <DatePicker 
                  style={{ width: '100%', fontSize: '14px' }} 
                  placeholder="Select target date"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Identified Date"
                name="identifiedDate"
                // rules={[{ required: true, message: 'Please select identified date' }]}
              >
                <DatePicker 
                  style={{ width: '100%', fontSize: '14px' }} 
                  placeholder="Select identified date"
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Mitigation Plan"
                name="mitigationPlan"
              >
                <TextArea 
                  rows={3} 
                  placeholder="Enter mitigation plan details" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Contingency Plan"
                name="contingencyPlan"
              >
                <TextArea 
                  rows={3} 
                  placeholder="Enter contingency plan details" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => {
                setShowRiskModal(false);
                riskForm.resetFields();
                setEditingRisk(null);
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRisk ? 'Update' : 'Add'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 页面标题 */}
      <h1 className="ltc-stage-title" style={{ marginBottom: '24px' }}>
        Project Execution
      </h1>

      {/* 状态卡片和进度圆环 - 垂直居中对齐 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }} align="middle">
        {/* 左侧：数据卡片 */}
        <Col xs={24} lg={16}>
          {/* 任务卡片 */}
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Completed Tasks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <CheckCircleOutlined style={{ fontSize: '20px', color: '#52c41a' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#52c41a' }}>{stats.completed}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#e6f7ff', border: '1px solid #91d5ff', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>In Progress</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <ClockCircleOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#1890ff' }}>{stats.inProgress}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#fff2e8', border: '1px solid #ffbb96', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Blocked Tasks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <ExclamationCircleOutlined style={{ fontSize: '20px', color: '#ff4d4f' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#ff4d4f' }}>{stats.blocked}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Overdue Tasks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <AlertOutlined style={{ fontSize: '20px', color: '#fa8c16' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#fa8c16' }}>{stats.overdue}</div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
          {/* 风险卡片 */}
          <Row gutter={[16, 16]} style={{ marginTop: '11px' }}>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#fff1f0', border: '1px solid #ffa39e', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Critical Risks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <ExclamationCircleOutlined style={{ fontSize: '20px', color: '#ff4d4f' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#ff4d4f' }}>{riskStats.critical}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#fffbe6', border: '1px solid #ffe58f', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>High Risks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <AlertOutlined style={{ fontSize: '20px', color: '#faad14' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#faad14' }}>{riskStats.high}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#e6f7ff', border: '1px solid #91d5ff', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Active Risks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <ClockCircleOutlined style={{ fontSize: '20px', color: '#1890ff' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#1890ff' }}>{riskStats.active}</div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="ltc-stage-card" bodyStyle={{ padding: '12px' }} style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: '12px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666', marginBottom: '6px' }}>Closed Risks</div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                    <CheckCircleOutlined style={{ fontSize: '20px', color: '#52c41a' }} />
                    <div style={{ fontSize: '26px', fontWeight: 'bold', color: '#52c41a' }}>{riskStats.closed}</div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* 右侧：进度圆环 */}
        <Col xs={24} lg={8}>
          <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center', height: '100%' }}>
            <div className="ltc-progress-circle" style={{ textAlign: 'center' }}>
              <Progress type="circle" percent={overallProgress} size={80} strokeColor="#52c41a" />
              <div className="ltc-progress-label" style={{ marginTop: '8px', fontSize: '14px', fontWeight: 500 }}>
                Project Progress
              </div>
            </div>
            <div className="ltc-progress-circle" style={{ textAlign: 'center' }}>
              <Progress type="circle"
                percent={Math.min(budgetOverview.utilization, 100)}
                size={80}
                strokeColor={budgetOverview.utilization > 100 ? '#ff4d4f' : budgetOverview.utilization > 90 ? '#faad14' : '#1890ff'}
                format={() => `${budgetOverview.utilization}%`}
              />
              <div className="ltc-progress-label" style={{ marginTop: '8px', fontSize: '14px', fontWeight: 500 }}>
                Budget Utilization
              </div>
            </div>
          </div>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card className="ltc-card-content">
        <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
          {/* 里程碑管理 */}
          <TabPane 
            tab={
              <span>
                <AimOutlined />
                Milestone
              </span>
            } 
            key="milestone"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Card className="ltc-table-title">
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'flex-end' }}>
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />}
                      onClick={handleAddMilestone}
                    >
                      Add Milestone
                    </Button>
                  </div>
                  <Timeline>
                    {milestones.map(milestone => (
                      <Timeline.Item
                        key={milestone.id}
                        color={milestone.status === 'completed' ? 'green' : 
                               milestone.status === 'in_progress' ? 'blue' : 
                               milestone.status === 'delayed' ? 'red' : 'gray'}
                      >
                        <div>
                          <div style={{ fontWeight: 500, marginBottom: '4px', fontSize: '16px' }}>
                            {milestone.name}
                          </div>
                          <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>
                            {milestone.description}
                          </div>
                          <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                            Target Date: {formatDate(milestone.targetDate)}
                          </div>
                          {milestone.status === 'in_progress' && (
                             <div style={{ marginTop: '8px', marginBottom: '8px' }}>
                               <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                                 Progress: {milestone.progress}%
                               </div>
                               <Slider
                                 min={0}
                                 max={100}
                                 value={milestone.progress}
                                 onChange={(value) => handleProgressSliderChange(milestone.id, value)}
                                 className="custom-slider"
                                 style={{ width: '100%' }}
                                 tooltip={{ formatter: (value) => `${value}%` }}
                                 dots={false}
                                 handleStyle={{
                                   borderColor: '#1890ff',
                                   backgroundColor: '#fff',
                                   boxShadow: `0 1px 3px rgba(0,0,0,0.1)`,
                                   width: '14px',
                                   height: '14px',
                                   marginTop: '-1px',
                                   cursor: 'grab',
                                   transition: 'all 0.2s ease',
                                   border: `2px solid #1890ff`,
                                   borderRadius: '50%'
                                 }}
                               />
                             </div>
                            )}
                          <div style={{ marginTop: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                              <Select
                                value={milestone.status}
                                onChange={(value) => handleStatusChange(milestone.id, value)}
                                size="small"
                                style={{ width: 120 }}
                              >
                                <Option value="upcoming">Upcoming</Option>
                                <Option value="in_progress">In Progress</Option>
                                <Option value="completed">Completed</Option>
                                <Option value="delayed">Delayed</Option>
                              </Select>

                            </div>
                            <div>
                              <Space size="small">
                                <Tooltip title="Edit Milestone">
                                  <Button 
                                    type="text" 
                                    icon={<EditOutlined />} 
                                    size="small"
                                    onClick={() => handleEditMilestone(milestone)}
                                  />
                                </Tooltip>
                                <Tooltip title="Delete Milestone">
                                  <Button 
                                    type="text" 
                                    icon={<DeleteOutlined />} 
                                    size="small"
                                    danger
                                    onClick={() => handleDeleteMilestone(milestone.id)}
                                  />
                                </Tooltip>
                              </Space>
                            </div>
                          </div>
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </Card>
              </Col>
            </Row>
            {/* Save按钮 */}
            <Row style={{ marginTop: '24px' }}>
              <Col span={24} style={{ textAlign: 'left' }}>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveMilestones}
                  loading={saving}
                  style={{
                    backgroundColor: '#FF7A00',
                    borderColor: '#FF7A00',
                    borderRadius: '8px',
                    height: '40px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Save Milestones
                </Button>
              </Col>
            </Row>
          </TabPane>

          {/* 任务管理 */}
          <TabPane 
            tab={
              <span>
                <ProjectOutlined />
                Task Management
              </span>
            } 
            key="tasks"
          >
            <Form form={form} layout="vertical" onFinish={handleSave}>
              <Row gutter={[24, 16]}>
                <Col span={24}>
                  {/* 删除Tip提示区域，仅保留按钮和筛选器 */}
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />}
                      onClick={handleCreateTask}
                      style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
                    >
                      Create New Task
                    </Button>
                    <Select 
                      value={taskFilter} 
                      onChange={setTaskFilter}
                      style={{ width: 120, marginLeft: 12 }}
                    >
                      <Option value="all">All Tasks</Option>
                      <Option value="completed">Completed</Option>
                      <Option value="in_progress">In Progress</Option>
                      <Option value="not_started">Not Started</Option>
                      <Option value="blocked">Blocked</Option>
                    </Select>
                  </div>
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                  >
                    <SortableContext
                      items={filteredTasks.filter(task => task && task.id).map(task => task.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <Table
                        columns={taskColumns}
                        dataSource={filteredTasks}
                        pagination={{ pageSize: 10 }}
                        rowKey="id"
                        components={{
                          body: {
                            row: DraggableTableRow,
                          },
                        }}
                        key={forceRender}
                      />
                    </SortableContext>
                    <DragOverlay>
                      {activeTask ? (
                        <DragOverlayContent task={activeTask} />
                      ) : null}
                    </DragOverlay>
                  </DndContext>
                </Col>
              </Row>
              {/* Save按钮 */}
              <Row style={{ marginTop: '24px' }}>
                <Col span={24} style={{ textAlign: 'left' }}>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSaveTasks}
                    loading={saving}
                    style={{
                      backgroundColor: '#FF7A00',
                      borderColor: '#FF7A00',
                      borderRadius: '8px',
                      height: '40px',
                      paddingLeft: '24px',
                      paddingRight: '24px',
                      fontSize: '14px',
                      fontWeight: '500'
                    }}
                  >
                    Save Tasks
                  </Button>
                </Col>
              </Row>
            </Form>
          </TabPane>

          {/* 风险管理 */}
          <TabPane 
            tab={
              <span>
                <ExclamationCircleOutlined />
                Risk Management
              </span>
            } 
            key="risks"
          >
            {(() => {
              // riskStats已在组件顶部使用useMemo定义
              return (
                <Row gutter={[24, 16]}>
                  {/* Risk Overview */}
                  <Col span={24}>
                    <Card title="Risk Overview" className="ltc-table-title">
                      <Row gutter={[16, 16]}>
                        <Col span={4}>
                          <Statistic 
                            title="Total Risks" 
                            value={riskStats.total} 
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Col>
                        <Col span={4}>
                          <Statistic 
                            title="Critical" 
                            value={riskStats.critical} 
                            valueStyle={{ color: '#ff4d4f' }}
                          />
                        </Col>
                        <Col span={4}>
                          <Statistic 
                            title="High" 
                            value={riskStats.high} 
                            valueStyle={{ color: '#fa8c16' }}
                          />
                        </Col>
                        <Col span={4}>
                          <Statistic 
                            title="Medium" 
                            value={riskStats.medium} 
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Col>
                        <Col span={4}>
                          <Statistic 
                            title="Active" 
                            value={riskStats.active} 
                            valueStyle={{ color: '#faad14' }}
                          />
                        </Col>
                        <Col span={4}>
                          <Statistic 
                            title="Closed" 
                            value={riskStats.closed} 
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </Col>
                      </Row>
                    </Card>
                  </Col>

                  {/* Risk Management */}
                  <Col span={24}>
                    <Card 
                      title="Risk Register" 
                      className="ltc-table-title"
                      extra={
                        <Space>
                          <Button 
                            type="primary" 
                            icon={<PlusOutlined />} 
                            onClick={handleCreateRisk}
                            style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
                          >
                            Add Risk
                          </Button>
                          <Select 
                            value={riskFilter} 
                            onChange={setRiskFilter}
                            style={{ width: 120 }}
                          >
                            <Option value="all">All Risks</Option>
                            <Option value="active">Active</Option>
                            <Option value="critical">Critical</Option>
                            <Option value="high">High</Option>
                            <Option value="closed">Closed</Option>
                          </Select>
                        </Space>
                      }
                    >
                      <Table
                        columns={riskColumns}
                        dataSource={filteredRisks}
                        pagination={{ pageSize: 10 }}
                        rowKey="id"
                        scroll={{ x: 'max-content' }}
                        tableLayout="fixed"
                        expandable={{
                          expandedRowRender: (record: Risk) => (
                            <div style={{ padding: '16px', backgroundColor: '#fafafa' }}>
                              <Row gutter={[16, 16]}>
                                                                 <Col span={12}>
                                   <div style={{ marginBottom: '12px' }}>
                                     <Text strong>Mitigation Plan:</Text>
                                     <div style={{ marginTop: '4px', color: '#666' }}>
                                       {record.mitigationPlan || 'No mitigation plan defined'}
                                     </div>
                                   </div>
                                 </Col>
                                 <Col span={12}>
                                   <div style={{ marginBottom: '12px' }}>
                                     <Text strong>Contingency Plan:</Text>
                                     <div style={{ marginTop: '4px', color: '#666' }}>
                                       {record.contingencyPlan || 'No contingency plan defined'}
                                     </div>
                                   </div>
                                 </Col>
                                 <Col span={12}>
                                   <div>
                                     <Text strong>Identified Date:</Text>
                                     <div style={{ marginTop: '4px', color: '#666' }}>
                                       {formatDate(record.identifiedDate)}
                                     </div>
                                   </div>
                                 </Col>
                                 <Col span={12}>
                                   <div>
                                     <Text strong>Probability × Impact:</Text>
                                     <div style={{ marginTop: '4px', color: '#666' }}>
                                       {record.probability.charAt(0).toUpperCase() + record.probability.slice(1)} × {record.impact.charAt(0).toUpperCase() + record.impact.slice(1)} = {record.riskScore}
                                     </div>
                                   </div>
                                 </Col>
                              </Row>
                            </div>
                          ),
                          rowExpandable: (record: Risk) => !!(record.mitigationPlan || record.contingencyPlan)
                        }}
                      />
                    </Card>
                  </Col>
                </Row>
              );
            })()}
            {/* Save按钮 */}
            <Row style={{ marginTop: '24px' }}>
              <Col span={24} style={{ textAlign: 'left' }}>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveRisks}
                  loading={saving}
                  style={{
                    backgroundColor: '#FF7A00',
                    borderColor: '#FF7A00',
                    borderRadius: '8px',
                    height: '40px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Save Risks
                </Button>
              </Col>
            </Row>
          </TabPane>

          {/* 团队管理 */}
          <TabPane 
            tab={
              <span>
                <TeamOutlined />
                Team Management
              </span>
            } 
            key="team"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Card 
                  className="ltc-table-title"
                  title="Team Members"
                  extra={
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />} 
                      onClick={handleCreateTeamMember}
                      style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
                    >
                      Add Team Member
                    </Button>
                  }
                >
                  <List
                    grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4, xl: 5, xxl: 6 }}
                    dataSource={teamMembers}
                    renderItem={(member) => (
                      <List.Item>
                        <Card 
                          size="small"
                          style={{ 
                            backgroundColor: '#fafafa',
                            border: '1px solid #e8e8e8',
                            borderRadius: '8px',
                            position: 'relative'
                          }}
                          bodyStyle={{ padding: '16px' }}
                        >
                          <div style={{ position: 'absolute', top: '8px', right: '8px', display: 'flex', gap: '4px' }}>
                            <Button
                              type="text"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={() => handleEditTeamMember(member)}
                              style={{
                                width: '24px',
                                height: '24px',
                                padding: 0,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}
                            />
                            <Popconfirm
                              title="Remove Team Member"
                              description="Are you sure you want to remove this team member?"
                              onConfirm={() => handleDeleteTeamMember(member.id)}
                              okText="Yes"
                              cancelText="No"
                              placement="topRight"
                            >
                              <Button
                                type="text"
                                danger
                                size="small"
                                icon={<DeleteOutlined />}
                                style={{
                                  width: '24px',
                                  height: '24px',
                                  padding: 0,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center'
                                }}
                              />
                            </Popconfirm>
                          </div>
                          <div style={{ textAlign: 'center', paddingTop: '8px' }}>
                            <div style={{ fontWeight: 500, marginBottom: '8px', fontSize: '14px' }}>
                              {member.name}
                            </div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {member.role}
                            </div>
                          </div>
                        </Card>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>
            </Row>
            {/* Save按钮 */}
            <Row style={{ marginTop: '24px' }}>
              <Col span={24} style={{ textAlign: 'left' }}>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveTeamMembers}
                  loading={saving}
                  style={{
                    backgroundColor: '#FF7A00',
                    borderColor: '#FF7A00',
                    borderRadius: '8px',
                    height: '40px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Save Team Members
                </Button>
              </Col>
            </Row>
          </TabPane>

          {/* 预算管理 */}
          <TabPane 
            tab={
              <span>
                <DollarOutlined />
                Budget Management
              </span>
            } 
            key="budget"
          >
            {(() => {
              const budgetOverview = getBudgetOverview();
              return (
                <Row gutter={[24, 16]}>
                  {/* Budget Overview */}
                  <Col span={24}>
                    <Card title="Budget Overview" className="ltc-table-title">
                      <Row gutter={[16, 16]}>
                        <Col span={6}>
                          <Statistic 
                            title="Total Planned" 
                            value={budgetOverview.totalPlanned} 
                            precision={0}
                            prefix={getCurrencySymbol(selectedCurrency)} 
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic 
                            title="Total Spent" 
                            value={budgetOverview.totalSpent} 
                            precision={0}
                            prefix={getCurrencySymbol(selectedCurrency)} 
                            valueStyle={{ color: '#ff4d4f' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic 
                            title="Remaining" 
                            value={budgetOverview.remaining} 
                            precision={0}
                            prefix={getCurrencySymbol(selectedCurrency)} 
                            valueStyle={{ color: budgetOverview.remaining >= 0 ? '#52c41a' : '#ff4d4f' }}
                          />
                        </Col>
                        <Col span={6}>
                          <div>
                            <div style={{ marginBottom: '8px', fontSize: '14px', color: '#666' }}>Budget Utilization</div>
                            <Progress 
                              percent={Math.min(budgetOverview.utilization, 100)} 
                              strokeColor={budgetOverview.utilization > 100 ? '#ff4d4f' : budgetOverview.utilization > 90 ? '#faad14' : '#52c41a'}
                              format={() => `${budgetOverview.utilization}%`}
                            />
                          </div>
                        </Col>
                      </Row>
                    </Card>
                  </Col>

                  {/* Budget Items */}
                  <Col span={24}>
                    <Card 
                      title={`Budget Items - Total Budget: ${getCurrencySymbol(selectedCurrency)}${budgetOverview.totalPlanned.toLocaleString()}`} 
                      className="ltc-table-title"
                      extra={
                        <Button 
                          type="primary" 
                          icon={<PlusOutlined />} 
                          onClick={handleCreateBudgetItem}
                          style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
                        >
                          Add Budget Item
                        </Button>
                      }
                    >
                      <Table
                        dataSource={budgetItems}
                        className="budget-items-table"
                        columns={[
                          {
                            title: 'Budget Item',
                            dataIndex: 'budgetItem',
                            key: 'budgetItem',
                            width: 150,
                            render: (_, record) => getBudgetItemName(record),
                          },
                          {
                            title: 'Description',
                            dataIndex: 'description',
                            key: 'description',
                            ellipsis: true,
                          },
                          {
                            title: '% of Total',
                            key: 'percentage',
                            width: 80,
                            align: 'right',
                            render: (_, record) => {
                              const itemAmountInGlobalCurrency = convertAmount(record.plannedAmount, record.currency, selectedCurrency);
                              const percentage = budgetOverview.totalPlanned > 0 
                                ? Math.round((itemAmountInGlobalCurrency / budgetOverview.totalPlanned) * 100) 
                                : 0;
                              return `${percentage}%`;
                            },
                          },
                          {
                            title: 'Planned Amount',
                            dataIndex: 'plannedAmount',
                            key: 'plannedAmount',
                            width: 150,
                            align: 'right',
                            render: (amount, record) => `${getCurrencySymbol(record.currency)}${amount.toLocaleString()}`,
                          },
                          {
                            title: 'Spent',
                            dataIndex: 'spentAmount',
                            key: 'spent',
                            width: 120,
                            align: 'right',
                            render: (_, record) => {
                              // 获取实际支出（已按预算项币种转换）
                              const budgetItemName = getBudgetItemName(record);
                              const actualSpent = spentByCategory[budgetItemName] || 0;
                              // 使用预算项的币种显示
                              return `${getCurrencySymbol(record.currency)}${actualSpent.toLocaleString()}`;
                            },
                          },
                          {
                            title: 'Remaining',
                            key: 'remaining',
                            width: 120,
                            align: 'right',
                            render: (_, record) => {
                              // 获取实际支出（已按预算项币种转换）
                              const budgetItemName = getBudgetItemName(record);
                              const actualSpent = spentByCategory[budgetItemName] || 0;
                              const remaining = record.plannedAmount - actualSpent;
                              return (
                                <span style={{ color: remaining >= 0 ? '#52c41a' : '#ff4d4f' }}>
                                  {getCurrencySymbol(record.currency)}{remaining.toLocaleString()}
                                </span>
                              );
                            },
                          },
                          {
                            title: 'Spending Progress',
                            key: 'progress',
                            width: 120,
                            render: (_, record) => {
                              // 获取实际支出（已按预算项币种转换）
                              const budgetItemName = getBudgetItemName(record);
                              const actualSpent = spentByCategory[budgetItemName] || 0;
                              const progress = record.plannedAmount > 0 ? Math.round((actualSpent / record.plannedAmount) * 100) : 0;
                              // 调试信息：显示计算过程
                              console.log(`💰 Budget Progress Debug: ${budgetItemName}`, {
                                actualSpent,
                                plannedAmount: record.plannedAmount,
                                progress: `${progress}%`,
                                spentByCategoryData: spentByCategory
                              });
                              return (
                                <Progress
                                  percent={Math.min(progress, 100)}
                                  size="small"
                                  strokeColor={progress > 100 ? '#ff4d4f' : progress > 90 ? '#faad14' : '#52c41a'}
                                  format={() => `${progress}%`}
                                />
                              );
                            },
                          },
                          {
                            title: 'Actions',
                            key: 'actions',
                            width: 100,
                            align: 'center',
                            render: (_, record) => (
                              <Space size="small">
                                <Button
                                  type="link"
                                  icon={<EditOutlined />}
                                  onClick={() => handleEditBudgetItem(record)}
                                  size="small"
                                />
                                <Popconfirm
                                  title="Delete Budget Item"
                                  description="Are you sure you want to delete this budget item?"
                                  onConfirm={() => handleDeleteBudgetItem(record.id)}
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <Button
                                    type="link"
                                    danger
                                    icon={<DeleteOutlined />}
                                    size="small"
                                  />
                                </Popconfirm>
                              </Space>
                            ),
                          },
                        ]}
                        pagination={false}
                        size="small"
                        showHeader={true}
                        tableLayout="fixed"
                      />
                    </Card>
                  </Col>


                </Row>
              );
            })()}
            {/* Save按钮 */}
            <Row style={{ marginTop: '24px' }}>
              <Col span={24} style={{ textAlign: 'left' }}>
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSaveBudgetItems}
                  loading={saving}
                  style={{
                    backgroundColor: '#FF7A00',
                    borderColor: '#FF7A00',
                    borderRadius: '8px',
                    height: '40px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Save Budget Items
                </Button>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建/编辑任务模态框 */}
      <Modal
        title={editingTask ? "Edit Task" : "Create New Task"}
        open={showCreateTaskModal}
        onCancel={() => {
          setShowCreateTaskModal(false);
          taskForm.resetFields();
          setEditingTask(null);
        }}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form form={taskForm} layout="vertical" onFinish={handleSaveTask}>
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item
                label="Task Name"
                name="name"
              >
                <Input placeholder="Enter task name" style={{ fontSize: '14px' }}/>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Description"
                name="description"
              >
                <TextArea rows={3} placeholder="Enter task description" style={{ fontSize: '14px' }}/>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Priority"
                name="priority"
              >
                <Select placeholder="Select priority" style={{ fontSize: '14px' }}>
                  <Option value="high">High</Option>
                  <Option value="medium">Medium</Option>
                  <Option value="low">Low</Option>
                  <Option value="critical">Critical</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Status"
                name="status"
              >
                <Select placeholder="Select status" style={{ fontSize: '14px' }}>
                  <Option value="not_started">Not Started</Option>
                  <Option value="in_progress">In Progress</Option>
                  <Option value="completed">Completed</Option>
                  <Option value="blocked">Blocked</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Assignee"
                name="assignee"
              >
                <AutoComplete
                  options={teamMembers.map(member => ({ value: member.name }))}
                  placeholder="Select or enter assignee"
                  filterOption={(inputValue, option) =>
                    option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Start Date"
                name="startDate"
              >
                <DatePicker style={{ width: '100%', fontSize: '14px' }} placeholder="Select start date" format="DD/MM/YYYY"/>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Due Date"
                name="dueDate"
              >
                <DatePicker style={{ width: '100%', fontSize: '14px' }} placeholder="Select due date" format="DD/MM/YYYY"/>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setShowCreateTaskModal(false);
                taskForm.resetFields();
                setEditingTask(null);
              }}>
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
              >
                {editingTask ? 'Update Task' : 'Create Task'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建/编辑预算项模态框 */}
      <Modal
        title={editingBudgetItem ? "Edit Budget Item" : "Create Budget Item"}
        open={showBudgetItemModal}
        onCancel={() => {
          setShowBudgetItemModal(false);
          setEditingBudgetItem(null);
          budgetItemForm.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={budgetItemForm}
          layout="vertical"
          onFinish={handleSaveBudgetItem}
        >
          {/* 基本信息分组 */}
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <FileTextOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                <span>Basic Information</span>
              </div>
            } 
            style={{ marginBottom: '24px' }}
            size="small"
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label={<span>Budget Item <span style={{ color: 'red' }}>*</span></span>}
                  name="budgetItem"
                  rules={[{ required: true, message: 'Please enter budget item name' }]}
                >
                  <Input placeholder="e.g., Development Team, Marketing" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              label="Description"
              name="description"
            >
              <TextArea rows={2} placeholder="Enter budget item description" />
            </Form.Item>
          </Card>

          {/* 金额设置分组 */}
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <DollarOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                <span>Amount Configuration</span>
              </div>
            } 
            style={{ marginBottom: '24px' }}
            size="small"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label={<span>Currency <span style={{ color: 'red' }}>*</span></span>}
                  name="currency"
                  initialValue={selectedCurrency}
                  rules={[{ required: true, message: 'Please select a currency' }]}
                >
                  <Select>
                    {supportedCurrencies.map(c => <Option key={c} value={c}>{c}</Option>)}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>Amount (Excl. VAT) <span style={{ color: 'red' }}>*</span></span>}
                  name="amountExclVat"
                  rules={[
                    { required: true, message: 'Please enter amount excluding VAT' },
                    { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="0.00"
                    min={0}
                    precision={2}
                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, '')) as any}
                    onChange={(value) => {
                      const vatRate = budgetItemForm.getFieldValue('vatRate') || 0;
                      if (value) {
                        const vatAmount = (value * vatRate) / 100;
                        const totalAmount = value + vatAmount;
                        budgetItemForm.setFieldsValue({
                          vatAmount: vatAmount,
                          plannedAmount: totalAmount
                        });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={<span>VAT Rate (%) <span style={{ color: 'red' }}>*</span></span>}
                  name="vatRate"
                  initialValue={20}
                  rules={[
                    { required: true, message: 'Please enter VAT rate' },
                    { type: 'number', min: 0, max: 100, message: 'VAT rate must be between 0% and 100%' }
                  ]}
                >
                  <InputNumber
                    style={{ width: '100%' }}
                    placeholder="20.0"
                    min={0}
                    max={100}
                    precision={1}
                    suffix="%"
                    onChange={(value) => {
                      const amountExclVat = budgetItemForm.getFieldValue('amountExclVat');
                      if (amountExclVat && value !== null) {
                        const vatAmount = (amountExclVat * value) / 100;
                        const totalAmount = amountExclVat + vatAmount;
                        budgetItemForm.setFieldsValue({
                          vatAmount: vatAmount,
                          plannedAmount: totalAmount
                        });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 计算结果分组 */}
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <CalculatorOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                <span>Calculated Amounts</span>
              </div>
            } 
            style={{ marginBottom: '24px', backgroundColor: '#f9f9f9' }}
            size="small"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="vatAmount"
                  label="VAT Amount"
                >
                  <InputNumber 
                    style={{ width: '100%' }} 
                    disabled 
                    placeholder="0.00" 
                    precision={2}
                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, '')) as any}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="plannedAmount"
                  label={<span>Total Amount (Incl. VAT) <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Total amount is required' }]}
                >
                  <InputNumber 
                    style={{ width: '100%' }} 
                    disabled 
                    placeholder="0.00" 
                    precision={2}
                    formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, '')) as any}
                  />
                </Form.Item>
              </Col>
            </Row>
            <div style={{ 
              padding: '12px', 
              backgroundColor: '#e6f7ff', 
              border: '1px solid #91d5ff', 
              borderRadius: '6px',
              fontSize: '12px',
              color: '#1890ff'
            }}>
              <InfoCircleOutlined style={{ marginRight: '8px' }} />
              These amounts are calculated automatically based on the amount excluding VAT and VAT rate.
            </div>
          </Card>

          <div style={{ textAlign: 'right', marginTop: 24, borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
            <Space size="middle">
              <Button onClick={() => setShowBudgetItemModal(false)} size="large">
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit" 
                size="large"
                style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00', minWidth: '120px' }}
              >
                {editingBudgetItem ? 'Update Budget Item' : 'Create Budget Item'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>



      {/* 创建/编辑团队成员模态框 */}
      <Modal
        title={editingTeamMember ? "Edit Team Member" : "Add Team Member"}
        open={showTeamMemberModal}
        onCancel={() => {
          setShowTeamMemberModal(false);
          teamMemberForm.resetFields();
          setEditingTeamMember(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={teamMemberForm}
          layout="vertical"
          onFinish={handleSaveTeamMember}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={<span>Full Name <span style={{ color: 'red' }}>*</span></span>}
                name="name"
                rules={[{ required: true, message: 'Please enter team member name' }]}
              >
                <Input 
                  placeholder="Enter full name" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<span>Role <span style={{ color: 'red' }}>*</span></span>}
                name="role"
                rules={[{ required: true, message: 'Please enter or select a role' }]}
              >
                <AutoComplete
                  options={[
                    { value: 'Project Manager' },
                    { value: 'Business Analyst' },
                    { value: 'Solutions Architect' },
                    { value: 'Frontend Developer' },
                    { value: 'Backend Developer' },
                    { value: 'QA Engineer' },
                    { value: 'DevOps Engineer' },
                    { value: 'UI/UX Designer' },
                  ]}
                  placeholder="Enter or select a role"
                  filterOption={(inputValue, option) =>
                    option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                >
                  <Input style={{ fontSize: '14px' }} />
                </AutoComplete>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label={<span>Email Address <span style={{ color: 'red' }}>*</span></span>}
                name="email"
                rules={[
                  { required: true, message: 'Please enter email address' },
                  { type: 'email', message: 'Please enter a valid email address' }
                ]}
              >
                <Input 
                  placeholder="Enter email address" 
                  style={{ fontSize: '14px' }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setShowTeamMemberModal(false);
                teamMemberForm.resetFields();
                setEditingTeamMember(null);
              }}>
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
              >
                {editingTeamMember ? 'Update Team Member' : 'Add Team Member'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Save & Proceed 按钮 */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>
    </div>
  );
};

export default ExecutionStage;
