/* 自定义组件样式 - 现代简约风格 */

/* 卡片组件 */
.stat-card {
  border-radius: var(--radius-md);
  background-color: var(--card-background);
  box-shadow: var(--shadow-sm);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  border: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-card__icon {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 24px;
  opacity: 0.15;
  color: var(--primary);
}

.stat-card__title {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-card__value {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.stat-card__trend {
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-top: auto;
}

.stat-card__trend--up {
  color: var(--success);
}

.stat-card__trend--down {
  color: var(--danger);
}

.stat-card__trend-icon {
  margin-right: 4px;
}

/* 项目卡片 */
.project-card {
  border-radius: var(--radius-md);
  background-color: var(--card-background);
  box-shadow: var(--shadow-sm);
  padding: 20px;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  border: 1px solid var(--border);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-light);
}

.project-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.project-card__title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.project-card__id {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 8px;
}

.project-card__client {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.project-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.project-card__status {
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.project-card__status--planning {
  background-color: var(--info);
  color: white;
}

.project-card__status--progress {
  background-color: var(--primary);
  color: white;
}

.project-card__status--completed {
  background-color: var(--success);
  color: white;
}

.project-card__status--hold {
  background-color: var(--warning);
  color: white;
}

.project-card__owner {
  display: flex;
  align-items: center;
}

.project-card__owner-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  background-color: var(--primary-light);
  color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.project-card__owner-name {
  font-size: 13px;
  color: var(--text-secondary);
}

/* 自定义表格 */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--card-background);
  box-shadow: var(--shadow-sm);
}

.modern-table th {
  background-color: var(--gray-light);
  color: var(--text-secondary);
  font-weight: 600;
  text-align: left;
  padding: 12px 16px;
  font-size: 14px;
}

.modern-table td {
  padding: 12px 16px;
  border-top: 1px solid var(--border);
  color: var(--text-primary);
  font-size: 14px;
}

.modern-table tr:hover td {
  background-color: var(--hover);
}

/* 自定义标签 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
}

.tag--primary {
  background-color: var(--primary-light);
  color: var(--primary);
}

.tag--success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.tag--warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.tag--danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
}

/* 自定义进度条 */
.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--gray-light);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-bar__fill {
  height: 100%;
  border-radius: var(--radius-sm);
  background-color: var(--primary);
  transition: width var(--transition-normal);
}

/* 现代化统计卡片 */
.modern-stat-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.modern-stat-card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
}

.modern-stat-card .ant-card-body {
  padding: 20px !important;
}

/* Dashboard欢迎区域 */
.dashboard-welcome {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 0 -24px 32px -24px;
  padding: 32px 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.dashboard-welcome::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 优化快速操作按钮 */
.quick-action-btn {
  transition: all 0.3s ease !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.quick-action-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

/* 项目卡片悬停效果优化 */
.project-item-hover {
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 4px 0;
}

.project-item-hover:hover {
  background-color: var(--hover);
  transform: translateX(4px);
  border-left: 3px solid var(--primary);
  padding-left: 13px !important;
}
