import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Table, Modal, Form, Input, DatePicker, Select, Button, InputNumber, message, Card, Row, Col, Typography, Space, Tooltip, Popconfirm, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import financeService from '../../services/finance.service';
import { syncAllRevenueItemProgresses } from '../../services/revenueCollectionSync';
import '../../styles/finance-page.css';
import '../../styles/ltc-stages.css';
import type { Project, RevenueItem } from '../../types';

const { Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// The NrcItem and MrcItem types are not exported from types/index.ts, 
// so we define local interfaces based on the structure provided by finance.service.ts
// This is a temporary workaround until the main types are exported.
interface NrcItem {
  id: string;
  name: string;
  description: string;
  currency: string;
  amount: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  notes: string;
  paymentStatus: string;
    collectionProgress?: number;
    totalReceived?: number;
}

interface MrcItem {
  id: string;
  name: string;
  description: string;
  cycle: string;
  unitPrice: number;
  vatRate: number;
  totalUnitPrice: number;
  startDate: string;
  endDate: string;
  totalCycleRevenue: number;
  received: number;
  pending: number;
  status: string;
  paymentStatus: string;
  currency: string;
  collectionProgress?: number;
  totalReceived?: number;
  totalCycleRevenueWithVat?: number;
}

type CombinedItem = (NrcItem & { type: 'nrc' }) | (MrcItem & { type: 'mrc', amount: number, totalAmount: number });


interface RevenueManagementProps {
  project?: Project;
}

const RevenueManagement: React.FC<RevenueManagementProps> = ({ project }) => {
  const projectId = project?.id;

  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editingItem, setEditingItem] = useState<CombinedItem | null>(null);
  const [financialSummary, setFinancialSummary] = useState({
    totalValue: 0,
    receivedValue: 0,
    pendingValue: 0,
    receivedPercentage: 0
  });
  const [form] = Form.useForm();
  const itemType = Form.useWatch('type', form);
  const [revenueItems, setRevenueItems] = useState<CombinedItem[]>([]);
  const [loading, setLoading] = useState(false);

  const getCurrencySymbol = (currencyCode?: string) => ({'EUR': '€', 'USD': '$', 'GBP': '£', 'CNY': '¥', 'JPY': '¥'}[currencyCode || 'EUR'] || '€');

  // 计算MRC在统计期内的总收入
  const calculateMrcTotalRevenue = (mrcItem: MrcItem): number => {
    // 如果没有日期数据，使用unitPrice作为fallback
    if (!mrcItem.startDate || !mrcItem.endDate) {
      console.warn(`MRC item "${mrcItem.name}" missing dates, using unitPrice: ${mrcItem.unitPrice}`);
      return mrcItem.unitPrice || 0;
    }
    
    const startDate = dayjs(mrcItem.startDate);
    const endDate = dayjs(mrcItem.endDate);
    const unitPrice = mrcItem.unitPrice || 0;
    
    if (!startDate.isValid() || !endDate.isValid()) {
      console.warn(`MRC item "${mrcItem.name}" has invalid dates, using unitPrice: ${unitPrice}`);
      return unitPrice;
    }
    
    let totalCycles = 0;
    switch (mrcItem.cycle) {
      case 'monthly':
        totalCycles = Math.max(0, endDate.diff(startDate, 'month') + 1);
        break;
      case 'quarterly':
        totalCycles = Math.max(0, Math.ceil(endDate.diff(startDate, 'month') / 3));
        break;
      case 'annually':
        totalCycles = Math.max(0, Math.ceil(endDate.diff(startDate, 'year')));
        break;
      default:
        totalCycles = 1;
    }
    
    const totalRevenue = unitPrice * totalCycles;
    // 移除频繁的console.log以停止无限循环输出
    return totalRevenue;
  };

  const calculateFinancialSummary = useCallback((items: CombinedItem[]) => {
    let totalValue = 0, receivedValue = 0;
    items.forEach(item => {
      if (item.type === 'nrc') {
        totalValue += item.totalAmount || item.amount;
      } else {
        // 对于MRC，使用计算的统计期内总收入
        const mrcTotalRevenue = calculateMrcTotalRevenue(item);
        totalValue += mrcTotalRevenue;
      }
      // 🔧 修复：使用新的基于Invoice的received计算
      receivedValue += item.totalReceived || 0;
    });
    const pendingValue = totalValue - receivedValue;
    const receivedPercentage = totalValue > 0 ? (receivedValue / totalValue) * 100 : 0;
    setFinancialSummary({ totalValue, receivedValue, pendingValue, receivedPercentage });
  }, []);

  // 🆕 新增：基于Invoice付款状态同步Revenue Item的收款数据
  const syncRevenueItemsWithInvoices = useCallback((revenueItems: CombinedItem[]): CombinedItem[] => {
    console.log('🔄 Syncing revenue items with invoice payment status...');
    
    // 获取所有已付款的发票
    const invoicesData = localStorage.getItem('finance_invoices');
    if (!invoicesData) {
      console.log('❌ No invoice data found');
      return revenueItems;
    }

    let invoices: any[] = [];
    try {
      invoices = JSON.parse(invoicesData);
    } catch (error) {
      console.error('Failed to parse invoice data:', error);
      return revenueItems;
    }

    console.log('📊 Processing invoices for revenue sync:', invoices.length);

    // 按revenue item分组统计已付款金额
    const revenuePaymentMap = new Map<string, number>();

    invoices.forEach(invoice => {
      if (invoice.status === 'Paid' && invoice.paidAmount > 0 && invoice.revenueItem) {
        console.log(`🔍 Processing paid invoice ${invoice.number}: "${invoice.revenueItem}" - €${invoice.paidAmount}`);
        
        // 🔧 智能匹配逻辑：处理简单发票和混合发票
        if (invoice.type === 'Mixed') {
          // 🔧 修复：智能解析混合发票，支持百分比和月份数
          console.log(`🔍 Parsing mixed invoice payment for ${invoice.number}: "${invoice.revenueItem}"`);
          const parts = invoice.revenueItem.split(',').map((part: string) => part.trim());
          
          parts.forEach((part: string) => {
            for (const item of revenueItems) {
              if (part.includes(item.name)) {
                // 检查是否有百分比信息
                const percentageMatch = part.match(/(\d+)%/);
                const monthsMatch = part.match(/(\d+)\s*months?/i);
                
                if (percentageMatch) {
                  // 按百分比分配
                  const percentage = parseInt(percentageMatch[1]);
                  let itemTotalAmount = 0;
                  if (item.type === 'nrc') {
                    itemTotalAmount = item.totalAmount || item.amount || 0;
                  } else {
                    itemTotalAmount = calculateMrcTotalRevenue(item);
                  }
                  const allocationAmount = (itemTotalAmount * percentage) / 100;
                  
                  const currentReceived = revenuePaymentMap.get(item.name) || 0;
                  revenuePaymentMap.set(item.name, currentReceived + allocationAmount);
                  
                  console.log(`💰 Mixed Invoice ${invoice.number}: ${item.name} + €${allocationAmount} (${percentage}% of €${itemTotalAmount})`);
                } else if (monthsMatch && item.type === 'mrc') {
                  // 🔧 新增：按月份数分配 MRC 项目
                  const months = parseInt(monthsMatch[1]);
                  const unitPrice = (item as any).unitPrice || 50;
                  const allocationAmount = unitPrice * months;
                  
                  const currentReceived = revenuePaymentMap.get(item.name) || 0;
                  revenuePaymentMap.set(item.name, currentReceived + allocationAmount);
                  
                  console.log(`💰 Mixed Invoice ${invoice.number}: ${item.name} + €${allocationAmount} (${months} months × €${unitPrice})`);
                } else {
                  // 如果没有百分比或月份，按比例分配发票金额
                  const matchingItems = parts.filter((p: string) => 
                    revenueItems.some(ri => p.includes(ri.name))
                  ).length;
                  const allocationAmount = invoice.paidAmount / matchingItems;
                  
                  const currentReceived = revenuePaymentMap.get(item.name) || 0;
                  revenuePaymentMap.set(item.name, currentReceived + allocationAmount);
                  
                  console.log(`💰 Mixed Invoice ${invoice.number}: ${item.name} + €${allocationAmount} (equal split)`);
                }
                break;
              }
            }
          });
        } else {
          // 单一发票：使用智能匹配
          let matchedRevenueName = null;
          
          // 尝试直接匹配Revenue Item names
          for (const item of revenueItems) {
            // 方式1: 检查Revenue Item名称是否包含在Invoice revenueItem中
            if (invoice.revenueItem.includes(item.name)) {
              matchedRevenueName = item.name;
              console.log(`✅ Found match by inclusion: "${item.name}" in "${invoice.revenueItem}"`);
              break;
            }
            
            // 方式2: 检查Invoice revenueItem是否以Revenue Item名称开头
            if (invoice.revenueItem.startsWith(item.name)) {
              matchedRevenueName = item.name;
              console.log(`✅ Found match by prefix: "${item.name}" starts "${invoice.revenueItem}"`);
              break;
            }
            
            // 方式3: 模糊匹配（移除空格和特殊字符）
            const normalizedInvoiceItem = invoice.revenueItem.replace(/[\s\-_]/g, '').toLowerCase();
            const normalizedRevenueName = item.name.replace(/[\s\-_]/g, '').toLowerCase();
            if (normalizedInvoiceItem.includes(normalizedRevenueName) || normalizedRevenueName.includes(normalizedInvoiceItem)) {
              matchedRevenueName = item.name;
              console.log(`✅ Found match by normalization: "${item.name}" ~ "${invoice.revenueItem}"`);
              break;
            }
          }
          
          if (matchedRevenueName) {
            const currentReceived = revenuePaymentMap.get(matchedRevenueName) || 0;
            revenuePaymentMap.set(matchedRevenueName, currentReceived + invoice.paidAmount);
            console.log(`💰 Single Invoice ${invoice.number}: ${matchedRevenueName} + €${invoice.paidAmount}`);
          } else {
            console.log(`❌ No match found for invoice "${invoice.revenueItem}" in revenue items:`, revenueItems.map(i => i.name));
          }
        }
      } else {
        console.log(`⏭️ Skipping invoice ${invoice.number}: status=${invoice.status}, paidAmount=${invoice.paidAmount || 0}`);
      }
    });

    console.log('📊 Revenue payment map:', Object.fromEntries(revenuePaymentMap));

    // 更新revenue items的收款数据
    const updatedItems = revenueItems.map(item => {
      const receivedAmount = revenuePaymentMap.get(item.name) || 0;
      let totalAmount = 0;
      
      if (item.type === 'nrc') {
        totalAmount = item.totalAmount || item.amount || 0;
      } else {
        totalAmount = calculateMrcTotalRevenue(item);
      }
      
      const progress = totalAmount > 0 ? Math.min((receivedAmount / totalAmount) * 100, 100) : 0;
      let paymentStatus = 'pending';
      
      if (progress >= 100) {
        paymentStatus = 'paid';
      } else if (progress > 0) {
        paymentStatus = 'partial';
      }

      const updatedItem = {
        ...item,
        totalReceived: receivedAmount,
        collectionProgress: Math.round(progress),
        paymentStatus: paymentStatus
      };

      if (receivedAmount > 0) {
        console.log(`✅ Updated ${item.name}: €${receivedAmount} received (${Math.round(progress)}% of €${totalAmount})`);
      } else {
        console.log(`📝 ${item.name}: €0 received (total: €${totalAmount})`);
      }

      return updatedItem;
    });

    console.log(`🎯 Revenue sync completed: ${revenuePaymentMap.size} items with payments`);
    return updatedItems;
  }, [calculateMrcTotalRevenue]);

  const loadData = useCallback(async () => {
    if (!projectId) return;
    setLoading(true);
    
    try {
      // 🔧 修复：先同步收款进度，再重新加载数据
      console.log('🔄 Starting Revenue Management data load with invoice sync...');
      
      // 首先检查localStorage中是否有数据
      const storageKey = `finance_revenue_items_${projectId}`;
      const localData = localStorage.getItem(storageKey);
      
      let finalRevenueData: CombinedItem[] = [];
      
      if (localData) {
        const parsedData = JSON.parse(localData);
        console.log('📊 Found local revenue data:', parsedData);
        
        // 🔧 关键修复：先应用新的收款进度计算逻辑
        console.log('🔄 Triggering collection progress sync with new calculation logic...');
        syncAllRevenueItemProgresses(projectId);
        
        // 然后使用Invoice数据同步收款状态
        finalRevenueData = syncRevenueItemsWithInvoices(parsedData);
        
        // 立即保存修复后的数据
        localStorage.setItem(storageKey, JSON.stringify(finalRevenueData));
      } else {
        // 如果没有本地数据，尝试从API获取
        const [nrcData, mrcData] = await Promise.all([
          financeService.getNrcItems(projectId),
          financeService.getMrcItems(projectId),
        ]);
        
        // 为MRC项目计算统计期内的总收入
        const enhancedMrcData = mrcData.map(item => {
          const totalCycleRevenue = calculateMrcTotalRevenue(item);
          const totalCycleRevenueWithVat = totalCycleRevenue * (1 + (item.vatRate || 0) / 100);
          return {
            ...item,
            totalCycleRevenue,
            totalCycleRevenueWithVat
          };
        });
        
        const combined: CombinedItem[] = [
          ...nrcData.map(i => ({...i, type: 'nrc' as const})),
          ...enhancedMrcData.map(i => ({
            ...i, 
            type: 'mrc' as const, 
            currency: i.currency || 'EUR', 
            amount: i.unitPrice, 
            totalAmount: i.totalCycleRevenueWithVat || i.totalUnitPrice
          })),
        ];
        
        // 🆕 关键修复：使用Invoice数据同步收款状态
        finalRevenueData = syncRevenueItemsWithInvoices(combined);
        
        // 保存到localStorage作为备份
        localStorage.setItem(storageKey, JSON.stringify(finalRevenueData));
      }
      
      setRevenueItems(finalRevenueData);
      calculateFinancialSummary(finalRevenueData);
      
      // 🔧 调试信息：显示NRC-WEB的状态
      const nrcWebItem = finalRevenueData.find(item => 
        item.name === 'NRC-WEB' || item.name.includes('NRC-WEB')
      );
      if (nrcWebItem) {
        console.log('🎯 NRC-WEB Status after sync:', {
          name: nrcWebItem.name,
          totalAmount: nrcWebItem.totalAmount,
          totalReceived: nrcWebItem.totalReceived,
          collectionProgress: nrcWebItem.collectionProgress,
          paymentStatus: nrcWebItem.paymentStatus
        });
      }
      
      console.log('✅ Revenue Management data loaded with updated payment status');
      
    } catch (error) {
      console.error('Failed to load revenue data:', error);
      message.error('Failed to load revenue data.');
    } finally {
      setLoading(false);
    }
  }, [projectId, calculateFinancialSummary, syncRevenueItemsWithInvoices]);

  // Tech Maintenance issue has been fixed through proper form handling

  // 🔧 修复：使用useRef来避免无限循环，并减少事件监听器
  const loadDataRef = useRef(loadData);
  loadDataRef.current = loadData;

  useEffect(() => {
    // 🔧 关键修复：在初始加载前先应用修复
    const initializeWithFix = async () => {
             if (projectId) {
         console.log('🔧 Applying collection progress fix on component initialization...');
         
         // 直接应用修复逻辑
         try {
           // 获取当前的MRC数据
           const mrcKey = `mrc_items_${projectId}`;
           const mrcData = localStorage.getItem(mrcKey);
           
           if (mrcData) {
             const mrcItems = JSON.parse(mrcData);
             
             // 应用新的计算逻辑到每个MRC项目
             const updatedMrcItems = mrcItems.map((item: any) => {
               // 重新计算总金额
               let totalAmount = 0;
               const unitPrice = item.unitPrice || 50; // Hosting的unitPrice应该是50
               
               if (item.startDate && item.endDate) {
                 const startDate = new Date(item.startDate);
                 const endDate = new Date(item.endDate);
                 const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                                   (endDate.getMonth() - startDate.getMonth()) + 1;
                 totalAmount = unitPrice * monthsDiff;
                 
                 console.log(`📊 Fixed calculation for ${item.name}: ${unitPrice} × ${monthsDiff} months = €${totalAmount}`);
               } else {
                 totalAmount = item.totalCycleRevenue || unitPrice;
               }
               
               // 计算已收款金额
               const transactionsData = localStorage.getItem('finance_transactions');
               let receivedAmount = 0;
               
               if (transactionsData) {
                 const transactions = JSON.parse(transactionsData);
                 const itemTransactions = transactions.filter((tx: any) => 
                   tx.type === 'income' && 
                   tx.revenueItem && 
                   tx.revenueItem.toLowerCase().includes(item.name.toLowerCase()) &&
                   ['completed', 'paid'].includes(tx.status.toLowerCase())
                 );
                 
                 receivedAmount = itemTransactions.reduce((sum: number, tx: any) => sum + (tx.totalAmount || 0), 0);
               }
               
               // 计算新的进度
               const newProgress = totalAmount > 0 ? Math.round((receivedAmount / totalAmount) * 100) : 0;
               
               console.log(`🎯 ${item.name}: ${receivedAmount}/${totalAmount} = ${newProgress}% (was ${item.collectionProgress || 0}%)`);
               
               return {
                 ...item,
                 totalCycleRevenue: totalAmount,
                 collectionProgress: newProgress,
                 totalReceived: receivedAmount
               };
             });
             
             // 保存更新后的数据
             localStorage.setItem(mrcKey, JSON.stringify(updatedMrcItems));
             console.log('✅ MRC items updated with fixed calculation');
             
             // 触发数据重新加载
             setTimeout(() => loadDataRef.current(), 100);
           }
         } catch (error) {
           console.error('Error fixing collection progress:', error);
         }
       }
    };
    
    // 延迟500ms让组件先稳定，然后应用修复
    const initTimeout = setTimeout(initializeWithFix, 500);
    
    // 🔧 修复：减少事件监听器，避免无限循环
    let syncTimeout: NodeJS.Timeout;
    
    const handleDataUpdate = () => {
      // 防抖处理，避免频繁触发
      if (syncTimeout) clearTimeout(syncTimeout);
      syncTimeout = setTimeout(() => {
        console.log('📢 Data updated, refreshing Revenue Management...');
        loadDataRef.current();
      }, 1000); // 增加延迟到1秒
    };
    
    // 只监听必要的事件
    window.addEventListener('invoiceDataUpdated', handleDataUpdate);
    window.addEventListener('transactionDataUpdated', handleDataUpdate);
    
    return () => {
      clearTimeout(initTimeout);
      if (syncTimeout) clearTimeout(syncTimeout);
      window.removeEventListener('invoiceDataUpdated', handleDataUpdate);
      window.removeEventListener('transactionDataUpdated', handleDataUpdate);
    };
  }, [projectId]); // 只依赖projectId
  


  const syncCollectionProgress = useCallback(async () => {
    if (!projectId) return;
    message.info('Syncing...');
    setLoading(true);
    try {
      syncAllRevenueItemProgresses(projectId);
      await loadDataRef.current();
      message.success('Sync complete!');
    } catch (error) {
      message.error('Sync failed.');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  // 🔧 新增：强制修复Collection Progress计算
  const forceFixCollectionProgress = useCallback(() => {
    if (!projectId) return;
    
    console.log('🛠️ Force fixing collection progress with new calculation logic...');
    
    try {
      // 获取当前的MRC数据
      const mrcKey = `mrc_items_${projectId}`;
      const mrcData = localStorage.getItem(mrcKey);
      
      if (mrcData) {
        const mrcItems = JSON.parse(mrcData);
        
        // 应用新的计算逻辑到每个MRC项目
        const updatedMrcItems = mrcItems.map((item: any) => {
          // 重新计算总金额
          let totalAmount = 0;
          const unitPrice = item.unitPrice || 50; // Hosting的unitPrice应该是50
          
          if (item.startDate && item.endDate) {
            const startDate = new Date(item.startDate);
            const endDate = new Date(item.endDate);
            const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                              (endDate.getMonth() - startDate.getMonth()) + 1;
            totalAmount = unitPrice * monthsDiff;
            
            console.log(`📊 Fixed calculation for ${item.name}: ${unitPrice} × ${monthsDiff} months = €${totalAmount}`);
          } else {
            totalAmount = item.totalCycleRevenue || unitPrice;
          }
          
          // 计算已收款金额
          const transactionsData = localStorage.getItem('finance_transactions');
          let receivedAmount = 0;
          
          if (transactionsData) {
            const transactions = JSON.parse(transactionsData);
            const itemTransactions = transactions.filter((tx: any) => 
              tx.type === 'income' && 
              tx.revenueItem && 
              tx.revenueItem.toLowerCase().includes(item.name.toLowerCase()) &&
              ['completed', 'paid'].includes(tx.status.toLowerCase())
            );
            
            receivedAmount = itemTransactions.reduce((sum: number, tx: any) => sum + (tx.totalAmount || 0), 0);
          }
          
          // 计算新的进度
          const newProgress = totalAmount > 0 ? Math.round((receivedAmount / totalAmount) * 100) : 0;
          
          console.log(`🎯 ${item.name}: ${receivedAmount}/${totalAmount} = ${newProgress}% (was ${item.collectionProgress || 0}%)`);
          
          return {
            ...item,
            totalCycleRevenue: totalAmount,
            collectionProgress: newProgress,
            totalReceived: receivedAmount
          };
        });
        
        // 保存更新后的数据
        localStorage.setItem(mrcKey, JSON.stringify(updatedMrcItems));
        console.log('✅ MRC items updated with fixed calculation');
        
        // 触发数据重新加载
        loadDataRef.current();
        
        message.success('Collection Progress fixed successfully!');
      }
    } catch (error) {
      console.error('Error fixing collection progress:', error);
      message.error('Failed to fix collection progress.');
    }
  }, [projectId]);



  const showAddItemModal = () => {
    setEditingItem(null);
    form.resetFields();
    form.setFieldsValue({ type: 'nrc', currency: 'EUR', vatRate: 21 });
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingItem(null);
  };

  const handleSubmit = async () => {
    if (!projectId) return message.error('Project ID is missing.');
    try {
      // 🔧 FIX: 添加详细的验证调试信息
      console.log('🔍 Starting form validation...');
      const values = await form.validateFields();
      console.log('✅ Form validation passed:', values);
      
      const isEditing = !!editingItem;
      const id = isEditing ? editingItem.id : `new-${Date.now()}`;

      // 🔧 FIX: 正确处理MRC和NRC的amount字段
      const amount = values.type === 'mrc' ? (values.unitPrice || 0) : (values.amount || 0);
      const vatRate = values.vatRate || 0;
      const vatAmount = amount * (vatRate / 100);
      const totalAmount = amount + vatAmount;
      
      console.log(`💰 Calculated amounts - Type: ${values.type}, Amount: ${amount}, VAT: ${vatAmount}, Total: ${totalAmount}`);

      if (values.type === 'nrc') {
        const nrcPayload: Omit<NrcItem, 'id'> = {
          name: values.name, description: values.description, currency: values.currency,
          amount, vatRate, vatAmount: amount * vatRate / 100, totalAmount,
          notes: values.notes || '', paymentStatus: 'pending'
        };
        
        try {
          isEditing
            ? await financeService.updateNrcItem(projectId, id, nrcPayload)
            : await financeService.createNrcItem(projectId, nrcPayload);
          
          message.success(`Item ${isEditing ? 'updated' : 'added'}.`);
          setIsModalVisible(false);
          loadData();
        } catch (apiError) {
          console.error("API call failed for NRC, saving to localStorage:", apiError);
          
          // API失败时，先获取当前数据，然后更新localStorage
          const currentItems = revenueItems.slice();
          if (isEditing) {
            const index = currentItems.findIndex(item => item.id === id);
            if (index !== -1) {
              currentItems[index] = {
                ...currentItems[index],
                ...nrcPayload,
                id,
                type: 'nrc'
              } as CombinedItem;
            }
          } else {
            currentItems.push({
              ...nrcPayload,
              id,
              type: 'nrc'
            } as CombinedItem);
          }
          
          // 保存到localStorage
          const storageKey = `finance_revenue_items_${projectId}`;
          localStorage.setItem(storageKey, JSON.stringify(currentItems));
          setRevenueItems(currentItems);
          calculateFinancialSummary(currentItems);
          
          message.warning('API temporarily unavailable. Data saved locally and will sync when connection is restored.');
          setIsModalVisible(false);
          // 不要调用loadData，避免覆盖localStorage数据
        }
      } else {
        // 为MRC计算统计期内的总收入
        const startDate = values.startDate ? values.startDate.format('YYYY-MM-DD') : '';
        const endDate = values.endDate ? values.endDate.format('YYYY-MM-DD') : '';
        
        let totalCycleRevenue = 0;
        if (startDate && endDate) {
          const start = dayjs(startDate);
          const end = dayjs(endDate);
          let cycles = 0;
          
          switch (values.cycle) {
            case 'monthly':
              cycles = Math.max(0, end.diff(start, 'month') + 1);
              break;
            case 'quarterly':
              cycles = Math.max(0, Math.ceil(end.diff(start, 'month') / 3));
              break;
            case 'annually':
              cycles = Math.max(0, Math.ceil(end.diff(start, 'year')));
              break;
            default:
              cycles = 1;
          }
          totalCycleRevenue = amount * cycles;
        }
        
        const mrcPayload: Omit<MrcItem, 'id'> = {
          name: values.name, description: values.description, cycle: values.cycle,
          unitPrice: amount, vatRate, totalUnitPrice: totalAmount,
          startDate, endDate, totalCycleRevenue, received: 0, pending: totalCycleRevenue, 
          status: 'Active', paymentStatus: 'pending', currency: values.currency,
        };
        
        try {
          isEditing
            ? await financeService.updateMrcItem(projectId, id, mrcPayload)
            : await financeService.createMrcItem(projectId, mrcPayload);
          
          message.success(`Item ${isEditing ? 'updated' : 'added'}.`);
          setIsModalVisible(false);
          loadData();
        } catch (apiError) {
          console.error("API call failed, saving to localStorage:", apiError);
          
          // API失败时，先获取当前数据，然后更新localStorage
          const currentItems = revenueItems.slice();
          if (isEditing) {
            const index = currentItems.findIndex(item => item.id === id);
            if (index !== -1) {
              currentItems[index] = {
                ...currentItems[index],
                ...mrcPayload,
                id,
                type: 'mrc',
                amount,
                totalAmount: totalCycleRevenue * (1 + vatRate / 100),
                totalCycleRevenue,
                totalCycleRevenueWithVat: totalCycleRevenue * (1 + vatRate / 100)
              } as CombinedItem;
            }
          } else {
            currentItems.push({
              ...mrcPayload,
              id,
              type: 'mrc',
              amount,
              totalAmount: totalCycleRevenue * (1 + vatRate / 100),
              totalCycleRevenue,
              totalCycleRevenueWithVat: totalCycleRevenue * (1 + vatRate / 100)
            } as CombinedItem);
          }
          
          // 保存到localStorage
          const storageKey = `finance_revenue_items_${projectId}`;
          localStorage.setItem(storageKey, JSON.stringify(currentItems));
          setRevenueItems(currentItems);
          calculateFinancialSummary(currentItems);
          
          message.warning('API temporarily unavailable. Data saved locally and will sync when connection is restored.');
          setIsModalVisible(false);
          // 不要调用loadData，避免覆盖localStorage数据
        }
      }
      
      // 如果没有错误，说明API成功或者fallback成功
    } catch (error: any) {
      console.error("Submit failed:", error);
      
      // 🔧 FIX: 详细的错误处理和用户反馈
      if (error.errorFields && error.errorFields.length > 0) {
        // 表单验证错误
        console.log('❌ Form validation errors:', error.errorFields);
        const errorMessages = error.errorFields.map((field: any) => 
          `${field.name.join('.')}: ${field.errors.join(', ')}`
        ).join('\n');
        message.error(`Please fix the following errors:\n${errorMessages}`);
      } else if (error?.message?.includes('API')) {
        // API错误（已经在上面处理了）
        console.log('API error already handled');
      } else {
        // 其他未知错误
        console.error('Unknown error:', error);
        message.error('Failed to save. Please check all required fields.');
      }
    }
  };

  const handleEditItem = (item: CombinedItem) => {
    setEditingItem(item);
    
    // 🔧 FIX: 正确设置MRC和NRC的字段值
    const formValues: any = {
      ...item,
      startDate: item.type === 'mrc' && item.startDate ? dayjs(item.startDate) : null,
      endDate: item.type === 'mrc' && item.endDate ? dayjs(item.endDate) : null,
    };
    
    // 为MRC和NRC设置正确的amount字段
    if (item.type === 'mrc') {
      formValues.unitPrice = (item as any).unitPrice;
      // 不设置amount字段，因为MRC使用unitPrice
    } else {
      formValues.amount = item.amount;
      // 不设置unitPrice字段，因为NRC使用amount
    }
    
    form.setFieldsValue(formValues);
    setIsModalVisible(true);
  };

  const handleDeleteItem = async (item: CombinedItem) => {
    if (!projectId) return message.error('Project ID is missing.');
    try {
      // 🔧 FIX: 彻底删除 - 删除所有可能的数据源
      if (item.type === 'nrc') {
        await financeService.deleteNrcItem(projectId, item.id);
        
        // 也从合并的数据源中删除
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        if (combinedData) {
          const combinedItems = JSON.parse(combinedData);
          const updatedCombined = combinedItems.filter((i: any) => i.id !== item.id);
          localStorage.setItem(combinedKey, JSON.stringify(updatedCombined));
          console.log('✅ Also deleted from combined revenue data:', item.id);
        }
      } else {
        await financeService.deleteMrcItem(projectId, item.id);
        
        // 也从合并的数据源中删除
        const combinedKey = `finance_revenue_items_${projectId}`;
        const combinedData = localStorage.getItem(combinedKey);
        if (combinedData) {
          const combinedItems = JSON.parse(combinedData);
          const updatedCombined = combinedItems.filter((i: any) => i.id !== item.id);
          localStorage.setItem(combinedKey, JSON.stringify(updatedCombined));
          console.log('✅ Also deleted from combined revenue data:', item.id);
        }
      }
      
      // 🔧 FIX: 立即更新本地状态，而不是重新加载
      const updatedItems = revenueItems.filter(i => i.id !== item.id);
      setRevenueItems(updatedItems);
      calculateFinancialSummary(updatedItems);
      
      message.success('Item deleted.');
      
      // 🔧 FIX: 也强制刷新一次数据以确保一致性
      setTimeout(() => loadData(), 500);
      
    } catch (error) {
      console.error('Delete error:', error);
      message.error('Failed to delete item.');
    }
  };

  const renderCollectionProgress = (item: CombinedItem) => {
    const progress = item.collectionProgress || 0;
    const color = progress >= 100 ? '#52c41a' : progress > 0 ? '#1890ff' : '#d9d9d9';
  return (
      <Tooltip title={`Progress: ${progress.toFixed(0)}%`}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <div style={{ width: '70px', backgroundColor: '#f5f5f5', borderRadius: '2px', height: '10px' }}>
                <div style={{ width: `${progress}%`, height: '10px', backgroundColor: color, borderRadius: '2px' }} />
                  </div>
            <span style={{fontSize: '11px', color: '#888'}}>{progress.toFixed(0)}%</span>
                    </div>
      </Tooltip>
    );
  };



  const columns = [
    { title: 'No.', key: 'index', render: (_: any, __: any, index: number) => index + 1, width: 60 },
    { title: 'Revenue Item', dataIndex: 'name', key: 'name', ellipsis: true },
    { title: 'Type', dataIndex: 'type', key: 'type', width: 90, render: (type: string) => <Tag color={type === 'nrc' ? 'blue' : 'green'}>{type.toUpperCase()}</Tag> },
    { 
      title: 'Amount (Excl. VAT)', 
      key: 'amount', 
      render: (_: any, r: CombinedItem) => {
        if (r.type === 'mrc') {
          const totalCycleRevenue = calculateMrcTotalRevenue(r);
          return `${getCurrencySymbol(r.currency)}${totalCycleRevenue.toLocaleString()}`;
        }
        return `${getCurrencySymbol(r.currency)}${(r.amount || 0).toLocaleString()}`;
      }
    },
    { title: 'VAT', key: 'vat', width: 80, render: (_: any, r: CombinedItem) => `${r.vatRate || 0}%` },
    { 
      title: 'Total Amount (incl. VAT)', 
      key: 'totalAmount', 
      render: (_: any, r: CombinedItem) => {
        if (r.type === 'mrc') {
          const totalCycleRevenue = calculateMrcTotalRevenue(r);
          const totalWithVat = totalCycleRevenue * (1 + (r.vatRate || 0) / 100);
          return `${getCurrencySymbol(r.currency)}${totalWithVat.toLocaleString()}`;
        }
        return `${getCurrencySymbol(r.currency)}${(r.totalAmount || 0).toLocaleString()}`;
      }
    },
    { title: 'Collection Process', dataIndex: 'collectionProgress', key: 'collectionProgress', render: (_: any, r: CombinedItem) => renderCollectionProgress(r) },
    { title: 'Actions', key: 'actions', width: 120, render: (_: any, record: CombinedItem) => (
                              <Space size="small">
                                <Tooltip title="Edit">
                                  <Button 
                                    type="text" 
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => handleEditItem(record)}
                                    size="small"
                                    style={{ fontSize: '12px' }}
                                  />
                                </Tooltip>
                                  <Popconfirm
            title="Delete Revenue Item"
            description="Are you sure you want to delete this revenue item?"
            onConfirm={() => handleDeleteItem(record)}
                                    okText="Yes"
                                    cancelText="No"
                                  >
            <Tooltip title="Delete">
                                    <Button 
                                      type="text" 
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                                      danger
                                    size="small"
                                    style={{ fontSize: '12px' }}
                                  />
                                </Tooltip>
                                  </Popconfirm>
                              </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}><Card style={{ background: '#f5f5f5' }}><Text>Total Receivable</Text><Typography.Title level={4}>{getCurrencySymbol('EUR')}{financialSummary.totalValue.toLocaleString()}</Typography.Title></Card></Col>
        <Col span={6}><Card style={{ background: '#f5f5f5' }}><Text>Received</Text><Typography.Title level={4} style={{ color: '#52c41a' }}>{getCurrencySymbol('EUR')}{financialSummary.receivedValue.toLocaleString()}</Typography.Title></Card></Col>
        <Col span={6}><Card style={{ background: '#f5f5f5' }}><Text>Pending</Text><Typography.Title level={4} style={{ color: '#faad14' }}>{getCurrencySymbol('EUR')}{financialSummary.pendingValue.toLocaleString()}</Typography.Title></Card></Col>
        <Col span={6}><Card style={{ background: '#f5f5f5' }}><Text>Collection Ratio</Text><Typography.Title level={4} style={{ color: '#722ed1' }}>{financialSummary.receivedPercentage.toFixed(1)}%</Typography.Title></Card></Col>
      </Row>

      <Card title="Revenue Items Management" extra={
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={showAddItemModal}>Add Item</Button>
        </Space>
      }>
        <Table loading={loading} columns={columns} dataSource={revenueItems} rowKey="id" pagination={{ pageSize: 10 }} />
          </Card>

      <Modal 
        title={editingItem ? 'Edit Revenue Item' : 'Add Revenue Item'} 
        open={isModalVisible} 
        onCancel={handleCancel} 
        onOk={handleSubmit} 
        width={1000} 
        destroyOnClose
        styles={{ body: { padding: '16px' } }}
      >
        <Form 
          form={form} 
          layout="vertical" 
          name="revenueItemForm" 
          initialValues={{ type: 'nrc', currency: 'EUR', vatRate: 21 }}
        >
          {/* 基础信息区域 - 最重要的信息 */}
          <div style={{ 
            backgroundColor: '#fafafa', 
            padding: '16px', 
            borderRadius: '8px', 
            marginBottom: '16px',
            border: '1px solid #e8e8e8'
          }}>
            <h4 style={{ 
              margin: '0 0 12px 0', 
              color: '#1890ff', 
              fontSize: '14px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              📋 Basic Information
            </h4>
            <Row gutter={16}>
              <Col xs={24} sm={12}>
                <Form.Item 
                  name="name" 
                  label={<span>Revenue Item Name <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please enter revenue item name!' }]}
                >
                  <Input 
                    size="large"
                    style={{ height: '48px' }}
                    placeholder="Enter revenue item name..."
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={12}>
                <Form.Item 
                  name="type" 
                  label={<span>Item Type <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select item type!' }]}
                >
                  <Select 
                    size="large"
                    style={{ height: '48px' }}
                    placeholder="Select item type..."
                    options={[
                      { 
                        value: 'nrc', 
                        label: (
                          <div style={{ lineHeight: '1.2' }}>
                            <strong>NRC (Non-Recurring Charge)</strong>
                            <br />
                            <span style={{ fontSize: '11px', color: '#666' }}>One-time payment</span>
                          </div>
                        )
                      },
                      { 
                        value: 'mrc', 
                        label: (
                          <div style={{ lineHeight: '1.2' }}>
                            <strong>MRC (Monthly Recurring Charge)</strong>
                            <br />
                            <span style={{ fontSize: '11px', color: '#666' }}>Recurring payment</span>
                          </div>
                        )
                      }
                    ]}
                  />
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item name="description" label="Description">
              <TextArea 
                rows={3} 
                placeholder="Brief description of the revenue item..."
                style={{ resize: 'vertical' }}
              />
            </Form.Item>
          </div>

          {/* 财务信息区域 */}
          <div style={{ 
            backgroundColor: '#f6ffed', 
            padding: '16px', 
            borderRadius: '8px', 
            marginBottom: '16px',
            border: '1px solid #b7eb8f'
          }}>
            <h4 style={{ 
              margin: '0 0 12px 0', 
              color: '#52c41a', 
              fontSize: '14px',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              💰 Financial Details
            </h4>
            <Row gutter={16}>
              <Col xs={24} sm={8}>
                <Form.Item name="currency" label="Currency">
                  <Select 
                    size="large"
                    placeholder="Select currency..."
                    options={[
                      { value: 'EUR', label: 'EUR (€)' },
                      { value: 'USD', label: 'USD ($)' },
                      { value: 'GBP', label: 'GBP (£)' },
                      { value: 'CNY', label: 'CNY (¥)' },
                      { value: 'JPY', label: 'JPY (¥)' }
                    ]}
                  />
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={8}>
                <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type} noStyle>
                  {({ getFieldValue }) => {
                    const currentType = getFieldValue('type');
                    const fieldName = currentType === 'mrc' ? 'unitPrice' : 'amount';
                    const labelText = currentType === 'mrc' ? 'Unit Price (per Cycle)' : 'Amount (Excl. VAT)';
                    
                    return (
                      <Form.Item 
                        name={fieldName}
                        label={<span>{labelText} <span style={{ color: 'red' }}>*</span></span>}
                        rules={[{ required: true, message: `Please enter ${labelText.toLowerCase()}!` }]}
                      >
                        <InputNumber 
                          size="large"
                          style={{ width: '100%' }} 
                          placeholder="0.00"
                          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')} 
                          min={0}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
              </Col>
              
              <Col xs={24} sm={8}>
                <Form.Item name="vatRate" label="VAT Rate">
                  <InputNumber 
                    size="large"
                    style={{ width: '100%' }} 
                    min={0} 
                    max={100} 
                    addonAfter="%" 
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* MRC循环计划区域 - 条件显示 */}
          <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type} noStyle>
            {({ getFieldValue }) => 
              getFieldValue('type') === 'mrc' && (
                <div style={{ 
                  backgroundColor: '#fff7e6', 
                  padding: '16px', 
                  borderRadius: '8px', 
                  marginBottom: '16px',
                  border: '1px solid #ffd591'
                }}>
                  <h4 style={{ 
                    margin: '0 0 12px 0', 
                    color: '#fa8c16', 
                    fontSize: '14px',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px'
                  }}>
                    🔄 Recurring Schedule
                  </h4>
                  <Row gutter={16}>
                    <Col xs={24} sm={8}>
                      <Form.Item name="cycle" label="Billing Cycle">
                        <Select 
                          size="large"
                          placeholder="Select billing cycle..."
                          options={[
                            { value: 'monthly', label: 'Monthly' },
                            { value: 'quarterly', label: 'Quarterly' },
                            { value: 'annually', label: 'Annually' }
                          ]}
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col xs={24} sm={8}>
                      <Form.Item 
                        name="startDate" 
                        label={<span>Start Date <span style={{ color: 'red' }}>*</span></span>}
                        rules={[{ required: true, message: 'Please select start date!' }]}
                      >
                        <DatePicker 
                          size="large"
                          style={{ width: '100%' }} 
                          format="YYYY-MM-DD"
                        />
                      </Form.Item>
                    </Col>
                    
                    <Col xs={24} sm={8}>
                      <Form.Item 
                        name="endDate" 
                        label={<span>End Date <span style={{ color: 'red' }}>*</span></span>}
                        rules={[{ required: true, message: 'Please select end date!' }]}
                      >
                        <DatePicker 
                          size="large"
                          style={{ width: '100%' }} 
                          format="YYYY-MM-DD"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              )
            }
          </Form.Item>

          {/* Internal Notes - 去掉外层卡片，直接显示 */}
          <Form.Item name="notes" label="Internal Notes">
            <TextArea 
              rows={3} 
              placeholder="Internal notes for reference..."
              style={{ resize: 'vertical' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RevenueManagement;
