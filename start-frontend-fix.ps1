Write-Host "====================================" -ForegroundColor Cyan
Write-Host "    前端服务启动脚本 (修复版)" -ForegroundColor Cyan  
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

# 进入前端目录
Set-Location frontend

# 设置环境变量
$env:SKIP_PREFLIGHT_CHECK = "true"
$env:BROWSER = "none"
$env:FAST_REFRESH = "false"
$env:ESLINT_NO_DEV_ERRORS = "true"
$env:TSC_COMPILE_ON_ERROR = "true"
$env:GENERATE_SOURCEMAP = "false"

Write-Host "设置环境变量完成" -ForegroundColor Green

# 检查依赖
if (Test-Path "node_modules\react-scripts") {
    Write-Host "✓ react-scripts 已安装" -ForegroundColor Green
} else {
    Write-Host "✗ react-scripts 未安装，正在安装..." -ForegroundColor Yellow
    npm install react-scripts@5.0.1 --legacy-peer-deps --force
}

Write-Host ""
Write-Host "启动前端开发服务器..." -ForegroundColor Green
Write-Host "请等待编译完成..." -ForegroundColor Yellow
Write-Host ""

# 启动前端服务
& "node_modules\.bin\react-scripts.cmd" start 