/* 流转按钮区域 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.btn-workflow {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 12px !important;
  font-weight: 500;
  background-color: #f5f5f5;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-workflow:hover {
  background-color: #e6e6e6;
  border-color: #d9d9d9;
}

.btn-workflow .anticon {
  transition: transform 0.3s ease;
}

.btn-workflow:hover .anticon {
  transform: translateX(3px);
}

/* 橙色边框上传组件 */
/* .orange-border-upload.ant-upload-drag {
  border-color: #FF7A00 !important;
  border-width: 2px !important;
}

.orange-border-upload.ant-upload-drag:hover {
  border-color: #e66a00 !important;
} */

/* 上传区域图标强制放大 */
.ant-upload-drag-icon svg {
  font-size: 96px !important;
  width: 96px !important;
  height: 96px !important;
} 