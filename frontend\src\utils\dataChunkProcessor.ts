/**
 * 数据分片处理器
 * 用于高效处理大量数据
 */

// 处理配置
export interface ProcessorConfig {
  chunkSize: number;           // 每个分片的大小
  delay: number;               // 分片之间的延迟（毫秒）
  maxConcurrent: number;       // 最大并发处理数
  abortOnError: boolean;       // 出错时是否中止
  retryCount: number;          // 重试次数
  retryDelay: number;          // 重试延迟（毫秒）
}

// 默认配置
const DEFAULT_CONFIG: ProcessorConfig = {
  chunkSize: 100,
  delay: 0,
  maxConcurrent: 1,
  abortOnError: false,
  retryCount: 3,
  retryDelay: 1000
};

// 处理状态
export interface ProcessingStatus<T, R> {
  processed: number;           // 已处理数量
  total: number;               // 总数量
  results: R[];                // 处理结果
  errors: Array<{ item: T, error: Error, index: number }>; // 处理错误
  inProgress: boolean;         // 是否正在处理
  aborted: boolean;            // 是否已中止
  startTime: number;           // 开始时间
  endTime?: number;            // 结束时间
  currentChunk: number;        // 当前分片
  totalChunks: number;         // 总分片数
}

// 进度回调
export type ProgressCallback<T, R> = (status: ProcessingStatus<T, R>) => void;

/**
 * 数据分片处理器
 * 用于高效处理大量数据，避免阻塞UI线程
 */
export class DataChunkProcessor<T, R> {
  private config: ProcessorConfig;
  private abortController: AbortController;
  private status: ProcessingStatus<T, R>;

  /**
   * 构造函数
   * @param config 处理配置
   */
  constructor(config: Partial<ProcessorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.abortController = new AbortController();
    this.status = this.createInitialStatus();
  }

  /**
   * 创建初始状态
   */
  private createInitialStatus(): ProcessingStatus<T, R> {
    return {
      processed: 0,
      total: 0,
      results: [],
      errors: [],
      inProgress: false,
      aborted: false,
      startTime: 0,
      currentChunk: 0,
      totalChunks: 0
    };
  }

  /**
   * 重置状态
   */
  public reset(): void {
    this.abortController = new AbortController();
    this.status = this.createInitialStatus();
  }

  /**
   * 中止处理
   */
  public abort(): void {
    if (this.status.inProgress) {
      this.abortController.abort();
      this.status.aborted = true;
      this.status.inProgress = false;
      this.status.endTime = Date.now();
    }
  }

  /**
   * 处理数据
   * @param data 数据数组
   * @param processor 处理函数
   * @param onProgress 进度回调
   * @returns 处理结果
   */
  public async process(
    data: T[],
    processor: (item: T, index: number) => Promise<R>,
    onProgress?: ProgressCallback<T, R>
  ): Promise<R[]> {
    // 重置状态
    this.reset();

    // 如果数据为空，直接返回
    if (!data || data.length === 0) {
      return [];
    }

    // 初始化状态
    this.status.total = data.length;
    this.status.inProgress = true;
    this.status.startTime = Date.now();

    // 计算分片数
    const { chunkSize } = this.config;
    const chunks = this.chunkArray(data, chunkSize);
    this.status.totalChunks = chunks.length;

    // 通知初始进度
    if (onProgress) {
      onProgress({ ...this.status });
    }

    try {
      // 处理所有分片
      for (let i = 0; i < chunks.length; i++) {
        // 检查是否已中止
        if (this.abortController.signal.aborted) {
          break;
        }

        this.status.currentChunk = i + 1;

        // 处理当前分片
        const chunk = chunks[i];
        const startIndex = i * chunkSize;

        // 并发处理分片中的项
        const chunkResults = await this.processChunk(chunk, processor, startIndex);

        // 合并结果
        this.status.results.push(...chunkResults.filter((r): r is R => r !== null));

        // 通知进度
        if (onProgress) {
          onProgress({ ...this.status });
        }

        // 添加延迟，避免阻塞UI
        if (this.config.delay > 0 && i < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.delay));
        }
      }
    } finally {
      // 更新状态
      this.status.inProgress = false;
      this.status.endTime = Date.now();

      // 最终通知进度
      if (onProgress) {
        onProgress({ ...this.status });
      }
    }

    return this.status.results;
  }

  /**
   * 处理单个分片
   * @param chunk 分片数据
   * @param processor 处理函数
   * @param startIndex 起始索引
   * @returns 处理结果
   */
  private async processChunk(
    chunk: T[],
    processor: (item: T, index: number) => Promise<R>,
    startIndex: number
  ): Promise<Array<R | null>> {
    const results: Array<R | null> = new Array(chunk.length).fill(null);

    // 根据并发数选择处理方式
    if (this.config.maxConcurrent === 1) {
      // 串行处理
      for (let i = 0; i < chunk.length; i++) {
        // 检查是否已中止
        if (this.abortController.signal.aborted) {
          break;
        }

        const item = chunk[i];
        const index = startIndex + i;

        try {
          results[i] = await this.processItemWithRetry(item, index, processor);
          this.status.processed++;
        } catch (error) {
          this.handleError(item, error as Error, index);

          // 如果配置为出错时中止，则中止处理
          if (this.config.abortOnError) {
            this.abort();
            break;
          }
        }
      }
    } else {
      // 并行处理
      const promises = chunk.map(async (item, i) => {
        const index = startIndex + i;

        try {
          const result = await this.processItemWithRetry(item, index, processor);
          this.status.processed++;
          return { index: i, result };
        } catch (error) {
          this.handleError(item, error as Error, index);

          // 如果配置为出错时中止，则中止处理
          if (this.config.abortOnError) {
            this.abort();
          }

          return { index: i, error };
        }
      });

      // 等待所有并行处理完成
      const chunkResults = await Promise.all(promises);

      // 整理结果
      chunkResults.forEach(({ index, result, error }) => {
        if (!error && result !== undefined) {
          results[index] = result;
        }
      });
    }

    return results;
  }

  /**
   * 处理单个项（带重试）
   * @param item 数据项
   * @param index 索引
   * @param processor 处理函数
   * @returns 处理结果
   */
  private async processItemWithRetry(
    item: T,
    index: number,
    processor: (item: T, index: number) => Promise<R>
  ): Promise<R> {
    let lastError: Error | null = null;

    // 尝试处理，最多重试指定次数
    for (let attempt = 0; attempt <= this.config.retryCount; attempt++) {
      try {
        // 检查是否已中止
        if (this.abortController.signal.aborted) {
          throw new Error('Processing aborted');
        }

        // 处理项
        return await processor(item, index);
      } catch (error) {
        lastError = error as Error;

        // 如果还有重试次数，等待后重试
        if (attempt < this.config.retryCount) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    }

    // 所有重试都失败，抛出最后一个错误
    throw lastError;
  }

  /**
   * 处理错误
   * @param item 数据项
   * @param error 错误
   * @param index 索引
   */
  private handleError(item: T, error: Error, index: number): void {
    this.status.errors.push({ item, error, index });
    console.error(`Error processing item at index ${index}:`, error);
  }

  /**
   * 将数组分割成多个分片
   * @param array 数组
   * @param size 分片大小
   * @returns 分片数组
   */
  private chunkArray(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 获取当前状态
   * @returns 处理状态
   */
  public getStatus(): ProcessingStatus<T, R> {
    return { ...this.status };
  }

  /**
   * 获取处理进度（0-100）
   * @returns 进度百分比
   */
  public getProgress(): number {
    if (this.status.total === 0) return 0;
    return Math.round((this.status.processed / this.status.total) * 100);
  }

  /**
   * 获取处理时间（毫秒）
   * @returns 处理时间
   */
  public getProcessingTime(): number {
    const endTime = this.status.endTime || Date.now();
    return endTime - this.status.startTime;
  }
}

// 创建默认实例
const dataChunkProcessor = new DataChunkProcessor();

export default dataChunkProcessor;
