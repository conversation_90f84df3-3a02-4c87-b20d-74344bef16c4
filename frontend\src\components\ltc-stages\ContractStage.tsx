import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Row, 
  Col, 
  Button, 
  Upload, 
  Table, 
  Tag, 
  Modal, 
  message,
  Space,
  Statistic,
  Progress,
  Tabs,
  Tooltip
} from 'antd';
import { 
  CloudUploadOutlined, 
  FileTextOutlined, 
  EyeOutlined, 
  DownloadOutlined, 
  DeleteOutlined, 
  RightOutlined,
  ContainerOutlined,
  CheckCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface ContractStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

interface ContractDocument {
  id: string;
  name: string;
  type: 'contract' | 'appendix' | 'nda' | 'amendment';
  status: 'draft' | 'pending' | 'signed' | 'rejected';
  date: string;
  size: string;
  signedBy?: string;
  fileUrl?: string;
}

const ContractStage: React.FC<ContractStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('overview');
  const [saving, setSaving] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState<ContractDocument | null>(null);

  // Get storage key based on project ID
  const getStorageKey = useCallback(() => {
    const projectId = project?.id || stage?.projectId || 'default';
    return `contract_documents_${projectId}`;
  }, [project?.id, stage?.projectId]);

  // Load initial documents from localStorage
  const loadInitialDocuments = useCallback(() => {
    try {
      const savedDocuments = localStorage.getItem(getStorageKey());
      if (savedDocuments) {
        return JSON.parse(savedDocuments);
      }
    } catch (error) {
      console.error('Error loading contract documents from localStorage:', error);
    }
    return [];
  }, [getStorageKey]);

  const [documents, setDocuments] = useState<ContractDocument[]>(loadInitialDocuments());

  // Save documents to localStorage whenever documents change
  useEffect(() => {
    try {
      localStorage.setItem(getStorageKey(), JSON.stringify(documents));
    } catch (error) {
      console.error('Error saving contract documents to localStorage:', error);
    }
  }, [documents, getStorageKey]);

  // Load documents when project changes
  useEffect(() => {
    const newDocuments = loadInitialDocuments();
    setDocuments(newDocuments);
    console.log('Contract documents loaded for project:', project?.id || stage?.projectId, newDocuments.length, 'documents');
  }, [project?.id, stage?.projectId, loadInitialDocuments]);

  const [completionStatus, setCompletionStatus] = useState({
    documents: false,
    details: false,
    signatures: false,
    terms: false
  });

  // 计算整体完成度
  const overallCompletion = Object.values(completionStatus).filter(Boolean).length * 25;
  const signedDocuments = documents.filter(doc => doc.status === 'signed').length;
  const pendingDocuments = documents.filter(doc => doc.status === 'pending').length;

  useEffect(() => {
    if (stage?.data) {
      try {
        const stageData = JSON.parse(stage.data);
        form.setFieldsValue(stageData);
        
        // 检查完成状态
        setCompletionStatus({
          documents: documents.some(doc => doc.status === 'signed'),
          details: !!(stageData.contractNumber && stageData.contractType),
          signatures: !!(stageData.clientSignatory && stageData.companySignatory),
          terms: !!(stageData.contractValue && stageData.effectiveDate)
        });
      } catch (error) {
        console.error('Error parsing stage data:', error);
      }
    }
  }, [stage, form, documents]);

  // 保存数据
  const handleSave = async (values: any, section?: string) => {
    try {
      setSaving(true);
      
      const allData = {
        ...form.getFieldsValue(),
        ...values,
        documents,
        type: 'contract',
        status: overallCompletion === 100 ? 'completed' : 'in_progress',
        updatedAt: new Date().toISOString()
      };

      if (onSave) {
        onSave(allData);
      }

      // 更新完成状态
      if (section) {
        setCompletionStatus(prev => ({
          ...prev,
          [section]: true
        }));
      }

      message.success('Contract data saved successfully');
    } catch (error) {
      console.error('Save error:', error);
      message.error('Failed to save, please try again');
    } finally {
      setSaving(false);
    }
  };

  // 进入下一阶段
  const handleProceed = async () => {
    try {
      const values = form.getFieldsValue();
      await handleSave(values);
      
    setTimeout(() => {
        message.success('Contract completed! Proceeding to Execution stage...');
        if (onProceed) {
          onProceed('execution');
        }
      }, 1000);
    } catch (error) {
      message.error('Please complete all contract sections before proceeding');
    }
  };

  // 渲染状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      'signed': { color: 'success', text: 'Signed' },
      'pending': { color: 'processing', text: 'Pending' },
      'draft': { color: 'default', text: 'Draft' },
      'rejected': { color: 'error', text: 'Rejected' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 文档表格列
  const documentColumns = [
    {
      title: 'Document',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ContractDocument) => (
        <Space>
          <FileTextOutlined style={{ color: '#1890ff' }} />
          <div>
            <div><strong>{text}</strong></div>
            <div style={{ color: '#666' }}>
              {record.size} • {new Date(record.date).toLocaleDateString()}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeMap = {
          'contract': 'Contract',
          'appendix': 'Appendix',
          'nda': 'NDA',
          'amendment': 'Amendment'
        };
        return typeMap[type as keyof typeof typeMap] || type;
      }
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: 'Signed By',
      dataIndex: 'signedBy',
      key: 'signedBy',
      width: 150,
      render: (signedBy: string) => signedBy || '-'
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_: any, record: ContractDocument) => (
        <Space size="small">
          <Tooltip title="View">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                setCurrentDocument(record);
                setIsModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Download">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => {
                message.loading({ content: 'Preparing download...', key: 'download' });
                setTimeout(() => {
                  message.success({ content: `"${record.name}" downloaded successfully`, key: 'download' });
                }, 1500);
              }}
            />
          </Tooltip>
          {record.status === 'draft' && (
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // Custom upload request to ensure success
  const customUploadRequest = (options: any) => {
    const { onSuccess, onError, file } = options;
    
    // Simulate upload delay
    setTimeout(() => {
      try {
        // Create appropriate URL based on file type
        let fileUrl;
        if (file.type.startsWith('image/')) {
          // For images, create object URL to display the actual uploaded image
          fileUrl = URL.createObjectURL(file);
        } else if (file.type === 'application/pdf') {
          // For PDFs, use dummy PDF URL
          fileUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        } else {
          // For other files, use generic URL
          fileUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        }
        
        // Return a proper JSON response object
        const response = {
          success: true,
          data: {
            fileUrl: fileUrl,
            fileName: file.name,
            fileSize: file.size,
            uploadTime: new Date().toISOString()
          }
        };
        onSuccess(response);
      } catch (error) {
        onError(error);
      }
    }, 1000);
  };

  // Handle file upload
  const handleFileUpload = (info: any) => {
    // 处理多文件上传
    if (info.fileList && info.fileList.length > 0) {
      const uploadedFiles = info.fileList.filter((file: any) => file.status === 'done');
      
      if (uploadedFiles.length > 0) {
        const newDocuments = uploadedFiles.map((file: any, index: number) => {
          const response = file.response;
          
          if (response && response.success) {
            const { fileName, fileUrl, fileSize } = response.data;
            
            return {
              id: String(Date.now() + index),
              name: fileName,
              type: 'contract' as const,
              status: 'draft' as const,
              date: new Date().toISOString().split('T')[0],
              size: `${(fileSize / 1024 / 1024).toFixed(1)} MB`,
              fileUrl: fileUrl
            };
          }
          return null;
        }).filter(Boolean);

        if (newDocuments.length > 0) {
          // Add new documents to the list
          setDocuments(prev => [...newDocuments, ...prev]);
          
          // Log success without showing message
          console.log(`${newDocuments.length} contract document(s) uploaded successfully`);
        }
      }
      
      // Check for failed uploads
      const failedFiles = info.fileList.filter((file: any) => file.status === 'error');
      if (failedFiles.length > 0) {
        failedFiles.forEach((file: any) => {
          message.error(`${file.name} upload failed.`);
        });
      }
    }
  };

  return (
    <div className="ltc-stage-content">
      {/* 页面标题和进度概览 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 className="ltc-stage-title">
          Contract Management
        </h1>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              percent={overallCompletion}
              size={60}
              strokeColor="#52c41a"
            />
            <div className="ltc-progress-label">
              Complete
          </div>
          </div>
          <Button
            type="primary"
            icon={<RightOutlined />}
            onClick={handleProceed}
            disabled={overallCompletion < 75}
            size="large"
          >
            Proceed to Execution
          </Button>
        </div>
                  </div>

      {/* 状态卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Signed Documents"
              value={signedDocuments}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Pending Documents"
              value={pendingDocuments}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Total Documents"
              value={documents.length}
              prefix={<ContainerOutlined style={{ color: '#666' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Contract Value"
              value="€1.5M"
              prefix={<DollarOutlined style={{ color: '#fa8c16' }} />}
        />
      </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card className="ltc-card-content">
        <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
          {/* 概览 */}
          <TabPane 
            tab={
              <span>
                <ContainerOutlined />
                Overview
              </span>
            } 
            key="overview"
          >
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
              <Space>
                <Upload
                  name="files"
                  customRequest={customUploadRequest}
                  onChange={handleFileUpload}
                  beforeUpload={(file) => {
                    const isLt20M = file.size / 1024 / 1024 < 20;
                    if (!isLt20M) {
                      message.error('File must be smaller than 20MB!');
                      return false;
                    }
                    return true;
                  }}
                  multiple={true}
                  showUploadList={false}
                >
                  <Button type="primary" icon={<CloudUploadOutlined style={{ fontSize: '16px', color: '#FF7A00' }} />}>
                    Upload Document
                  </Button>
                </Upload>
                <Button icon={<ReloadOutlined />}>
                  Refresh
                </Button>
              </Space>
              <Space>
                <Select defaultValue="all" style={{ width: 140 }}>
                  <Option value="all">All Documents</Option>
                  <Option value="contract">Contract</Option>
                  <Option value="appendix">Appendix</Option>
                  <Option value="nda">NDA</Option>
                  <Option value="amendment">Amendment</Option>
                </Select>
                <Select defaultValue="all" style={{ width: 120 }}>
                  <Option value="all">All Status</Option>
                  <Option value="signed">Signed</Option>
                  <Option value="pending">Pending</Option>
                  <Option value="draft">Draft</Option>
                </Select>
              </Space>
            </div>
            
            <Table
              columns={documentColumns}
              dataSource={documents}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `${range[0]}-${range[1]} of ${total} documents`
              }}
              size="small"
            />
          </TabPane>

          {/* 合同详情 */}
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                Contract Details
                {completionStatus.details && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="details"
          >
      <Form
        form={form}
        layout="vertical"
              onFinish={(values) => handleSave(values, 'details')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="contractNumber" 
                    label="Contract Number"
                    rules={[{ required: true, message: 'Please enter contract number' }]}
                  >
                    <Input placeholder="e.g. CNT-2025-001" />
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="contractType" 
                    label="Contract Type"
                    rules={[{ required: true, message: 'Please select contract type' }]}
                  >
                    <Select placeholder="Select contract type">
                      <Option value="fixed">Fixed Price</Option>
                      <Option value="time">Time & Materials</Option>
                      <Option value="milestone">Milestone Based</Option>
                      <Option value="retainer">Retainer</Option>
                </Select>
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="contractPeriod" 
                    label="Contract Period"
                    rules={[{ required: true, message: 'Please select contract period' }]}
                  >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="contractValue" 
                    label="Contract Value (€)"
                    rules={[{ required: true, message: 'Please enter contract value' }]}
                  >
                    <Input placeholder="1500000" prefix="€" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="currency" label="Currency">
                    <Select placeholder="Select currency" defaultValue="EUR">
                      <Option value="EUR">EUR - Euro</Option>
                      <Option value="USD">USD - US Dollar</Option>
                      <Option value="GBP">GBP - British Pound</Option>
                      <Option value="CNY">CNY - Chinese Yuan</Option>
                      <Option value="JPY">JPY - Japanese Yen</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="paymentTerms" label="Payment Terms">
                    <Select placeholder="Select payment terms">
                      <Option value="30">Net 30 days</Option>
                      <Option value="45">Net 45 days</Option>
                      <Option value="60">Net 60 days</Option>
                      <Option value="milestone">Milestone based</Option>
                    </Select>
              </Form.Item>
            </Col>
                <Col xs={24}>
                  <Form.Item name="contractDescription" label="Contract Description">
                    <TextArea 
                      rows={4} 
                      placeholder="Brief description of the contract scope and objectives..."
                    />
              </Form.Item>
            </Col>
          </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Contract Details
                </Button>
              </div>
            </Form>
          </TabPane>

          {/* 签署信息 */}
          <TabPane 
            tab={
              <span>
                <UserOutlined />
                Signature Information
                {completionStatus.signatures && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="signatures"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'signatures')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="clientSignatory" 
                    label="Client Signatory"
                    rules={[{ required: true, message: 'Please enter client signatory' }]}
                  >
                    <Input placeholder="e.g. John Smith (CEO)" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="companySignatory" 
                    label="Company Signatory"
                    rules={[{ required: true, message: 'Please enter company signatory' }]}
                  >
                    <Input placeholder="e.g. Jane Doe (VP Sales)" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="clientWitness" label="Client Witness">
                    <Input placeholder="e.g. Michael Brown (Legal Counsel)" />
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="companyWitness" label="Company Witness">
                    <Input placeholder="e.g. Sarah Wilson (Legal Director)" />
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="signatureDate" label="Signature Date">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="effectiveDate" 
                    label="Effective Date"
                    rules={[{ required: true, message: 'Please select effective date' }]}
                  >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
                <Col xs={24}>
                  <Form.Item name="signatureNotes" label="Signature Notes">
                    <TextArea 
                      rows={4} 
                      placeholder="Any special notes about the signing process or conditions..."
                    />
                  </Form.Item>
                </Col>
          </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Signature Information
                </Button>
              </div>
            </Form>
          </TabPane>

          {/* 条款和条件 */}
          <TabPane 
            tab={
              <span>
                <CalendarOutlined />
                Terms & Conditions
                {completionStatus.terms && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="terms"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'terms')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item name="deliveryTerms" label="Delivery Terms">
                    <Select placeholder="Select delivery terms">
                      <Option value="incoterms_exw">EXW - Ex Works</Option>
                      <Option value="incoterms_fob">FOB - Free on Board</Option>
                      <Option value="incoterms_cif">CIF - Cost, Insurance & Freight</Option>
                      <Option value="digital_delivery">Digital Delivery</Option>
                      <Option value="on_site">On-site Delivery</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="warrantyPeriod" label="Warranty Period">
                    <Select placeholder="Select warranty period">
                      <Option value="6">6 months</Option>
                      <Option value="12">12 months</Option>
                      <Option value="24">24 months</Option>
                      <Option value="36">36 months</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="terminationClause" label="Termination Notice">
                    <Select placeholder="Select termination notice period">
                      <Option value="30">30 days</Option>
                      <Option value="60">60 days</Option>
                      <Option value="90">90 days</Option>
                      <Option value="120">120 days</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="governingLaw" label="Governing Law">
                    <Select placeholder="Select governing law">
                      <Option value="german">German Law</Option>
                      <Option value="english">English Law</Option>
                      <Option value="us_law">US Law</Option>
                      <Option value="international">International Law</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="specialConditions" label="Special Conditions">
                    <TextArea 
                      rows={6} 
                      placeholder="Any special conditions, restrictions, or unique terms for this contract..."
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="disputeResolution" label="Dispute Resolution">
                    <TextArea 
                      rows={4} 
                      placeholder="Procedures for handling disputes and conflicts..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Terms & Conditions
          </Button>
        </div>
      </Form>
          </TabPane>
        </Tabs>
      </Card>

      {/* 文档查看模态框 */}
      <Modal
        title={`Document: ${currentDocument?.name}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button key="download" icon={<DownloadOutlined />}>
            Download
          </Button>,
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            Close
          </Button>
        ]}
        width={800}
      >
        {currentDocument && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <strong>Type:</strong> {currentDocument.type}
              </Col>
              <Col span={12}>
                <strong>Status:</strong> {getStatusTag(currentDocument.status)}
              </Col>
              <Col span={12}>
                <strong>Size:</strong> {currentDocument.size}
              </Col>
              <Col span={12}>
                <strong>Date:</strong> {new Date(currentDocument.date).toLocaleDateString()}
              </Col>
              {currentDocument.signedBy && (
                <Col span={24}>
                  <strong>Signed By:</strong> {currentDocument.signedBy}
                </Col>
              )}
            </Row>
            <div style={{ 
              marginTop: '16px', 
              padding: '16px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              textAlign: 'center',
              color: '#666'
            }}>
              Document preview would be displayed here
            </div>
        </div>
        )}
      </Modal>
    </div>
  );
};

export default ContractStage;
