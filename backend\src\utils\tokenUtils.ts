import * as jwt from 'jsonwebtoken';

const SECRET_KEY = process.env.JWT_SECRET || 'your_secret_key';

export function generateInvitationToken(collaboratorId: string): string {
  const payload = {
    collaboratorId,
    type: 'invitation',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7天过期
  };

  return jwt.sign(payload, SECRET_KEY);
}

export function verifyInvitationToken(token: string): { collaboratorId: string } {
  try {
    const decoded = jwt.verify(token, SECRET_KEY) as any;
    if (decoded.type !== 'invitation') {
      throw new Error('Invalid token type');
    }
    return { collaboratorId: decoded.collaboratorId };
  } catch (error) {
    throw new Error('Invalid invitation token');
  }
}

export function generateShareLink(projectId: string, permissions: string[] = ['view']): string {
  const payload = {
    projectId,
    permissions,
    type: 'share_link',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天过期
  };

  const token = jwt.sign(payload, SECRET_KEY);
  return `${process.env.FRONTEND_URL || 'http://localhost:3000'}/shared/${token}`;
} 