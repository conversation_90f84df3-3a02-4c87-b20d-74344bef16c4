import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Space,
  Card,
  Typography,
  Row,
  Col,
  AutoComplete,
  Tag,
  message,
  Divider,
  Upload
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  CloudUploadOutlined,
  MailOutlined,
  UserAddOutlined
} from '@ant-design/icons';
import { TeamRole } from '../../types';
import teamService from '../../services/team.service';

interface InviteMemberProps {
  projectId: string;
  onMemberInvited: () => void;
}

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface InvitationForm {
  email: string;
  role: TeamRole;
}

const InviteMember: React.FC<InviteMemberProps> = ({
  projectId,
  onMemberInvited
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [bulkInvitations, setBulkInvitations] = useState<InvitationForm[]>([
    { email: '', role: TeamRole.MEMBER }
  ]);
  const [userSuggestions, setUserSuggestions] = useState<Array<{id: string, name: string, email: string}>>([]);
  const [inviteMode, setInviteMode] = useState<'single' | 'bulk' | 'csv'>('single');

  const roleOptions = [
    {
      value: TeamRole.ADMIN,
      label: 'Admin',
      description: 'Can manage project and team members'
    },
    {
      value: TeamRole.MEMBER,
      label: 'Member',
      description: 'Can edit project content'
    },
    {
      value: TeamRole.VIEWER,
      label: 'Viewer',
      description: 'View-only access'
    }
  ];

  const handleSingleInvite = async (values: any) => {
    setLoading(true);
    try {
      await teamService.inviteMember(projectId, values.email, values.role, values.message);
      form.resetFields();
      onMemberInvited();
      message.success(`Invitation sent to ${values.email}`);
    } catch (error) {
      message.error('Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkInvite = async () => {
    const validInvitations = bulkInvitations.filter(inv => inv.email.trim() !== '');
    
    if (validInvitations.length === 0) {
      message.warning('Please add at least one email address');
      return;
    }

    setLoading(true);
    try {
      await teamService.inviteMultipleMembers(projectId, validInvitations);
      setBulkInvitations([{ email: '', role: TeamRole.MEMBER }]);
      onMemberInvited();
      message.success(`${validInvitations.length} invitations sent successfully`);
    } catch (error) {
      message.error('Failed to send bulk invitations');
    } finally {
      setLoading(false);
    }
  };

  const addBulkInvitation = () => {
    setBulkInvitations([...bulkInvitations, { email: '', role: TeamRole.MEMBER }]);
  };

  const removeBulkInvitation = (index: number) => {
    const newInvitations = bulkInvitations.filter((_, i) => i !== index);
    setBulkInvitations(newInvitations);
  };

  const updateBulkInvitation = (index: number, field: keyof InvitationForm, value: any) => {
    const newInvitations = [...bulkInvitations];
    newInvitations[index] = { ...newInvitations[index], [field]: value };
    setBulkInvitations(newInvitations);
  };

  const searchUsers = async (query: string) => {
    if (query.length >= 2) {
      try {
        const users = await teamService.searchUsers(query);
        setUserSuggestions(users);
      } catch (error) {
        console.error('Failed to search users:', error);
      }
    }
  };

  const handleCSVUpload = (file: any) => {
    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('File must be smaller than 20MB!');
      return false;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csv = e.target?.result as string;
        const lines = csv.split('\n').filter(line => line.trim());
        const invitations: InvitationForm[] = [];

        lines.forEach((line, index) => {
          if (index === 0) return; // Skip header
          const [email, role] = line.split(',').map(item => item.trim());
          if (email && Object.values(TeamRole).includes(role as TeamRole)) {
            invitations.push({ email, role: role as TeamRole });
          }
        });

        setBulkInvitations(invitations);
        message.success(`Loaded ${invitations.length} invitations from CSV`);
      } catch (error) {
        message.error('Failed to parse CSV file');
      }
    };
    reader.readAsText(file);
    return false; // Prevent upload
  };

  const renderSingleInvite = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSingleInvite}
    >
      <Form.Item
        name="email"
        label="Email Address"
        rules={[
          { required: true, message: 'Please enter email address' },
          { type: 'email', message: 'Please enter a valid email address' }
        ]}
      >
        <AutoComplete
          placeholder="Enter email address"
          onSearch={searchUsers}
          options={userSuggestions.map(user => ({
            value: user.email,
            label: (
              <div>
                <div>{user.name}</div>
                <div style={{ color: '#8c8c8c', fontSize: '12px' }}>{user.email}</div>
              </div>
            )
          }))}
        />
      </Form.Item>

      <Form.Item
        name="role"
        label="Role"
        rules={[{ required: true, message: 'Please select a role' }]}
        initialValue={TeamRole.MEMBER}
      >
        <Select placeholder="Select role">
          {roleOptions.map(option => (
            <Option key={option.value} value={option.value}>
              <div>
                <div>{option.label}</div>
                <div style={{ color: '#8c8c8c', fontSize: '12px' }}>{option.description}</div>
              </div>
            </Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="message"
        label="Personal Message (Optional)"
      >
        <TextArea
          rows={3}
          placeholder="Add a personal message to the invitation..."
          maxLength={200}
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          icon={<MailOutlined />}
          block
        >
          Send Invitation
        </Button>
      </Form.Item>
    </Form>
  );

  const renderBulkInvite = () => (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="dashed"
          onClick={addBulkInvitation}
          icon={<PlusOutlined />}
          style={{ marginRight: 8 }}
        >
          Add Email
        </Button>
        
        <Upload
          accept=".csv"
          beforeUpload={handleCSVUpload}
          showUploadList={false}
        >
          <Button icon={<CloudUploadOutlined style={{ fontSize: '16px', color: '#FF7A00' }} />}>
            Import CSV
          </Button>
        </Upload>
        
        <div style={{ marginTop: 8, color: '#8c8c8c', fontSize: '12px' }}>
          CSV format: email,role (one per line, Max: 20MB)
        </div>
      </div>

      <div style={{ maxHeight: 400, overflowY: 'auto' }}>
        {bulkInvitations.map((invitation, index) => (
          <Card key={index} size="small" style={{ marginBottom: 8 }}>
            <Row gutter={8} align="middle">
              <Col span={12}>
                <AutoComplete
                  placeholder="Email address"
                  value={invitation.email}
                  onChange={(value) => updateBulkInvitation(index, 'email', value)}
                  onSearch={searchUsers}
                  options={userSuggestions.map(user => ({
                    value: user.email,
                    label: `${user.name} (${user.email})`
                  }))}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={8}>
                <Select
                  value={invitation.role}
                  onChange={(value) => updateBulkInvitation(index, 'role', value)}
                  style={{ width: '100%' }}
                  size="small"
                >
                  {roleOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={4}>
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => removeBulkInvitation(index)}
                  disabled={bulkInvitations.length === 1}
                />
              </Col>
            </Row>
          </Card>
        ))}
      </div>

      <Divider />
      
      <Button
        type="primary"
        onClick={handleBulkInvite}
        loading={loading}
        icon={<UserAddOutlined />}
        block
      >
        Send {bulkInvitations.filter(inv => inv.email.trim()).length} Invitations
      </Button>
    </div>
  );

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>Invite Team Members</Title>
        <Paragraph type="secondary">
          Invite new members to collaborate on your project. They will receive an email invitation
          with instructions to join.
        </Paragraph>
        
        <Space style={{ marginBottom: 16 }}>
          <Tag.CheckableTag
            checked={inviteMode === 'single'}
            onChange={() => setInviteMode('single')}
          >
            Single Invite
          </Tag.CheckableTag>
          <Tag.CheckableTag
            checked={inviteMode === 'bulk'}
            onChange={() => setInviteMode('bulk')}
          >
            Bulk Invite
          </Tag.CheckableTag>
        </Space>
      </div>

      {inviteMode === 'single' ? renderSingleInvite() : renderBulkInvite()}

      <Divider />
      
      <div>
        <Title level={5}>Role Permissions</Title>
        {roleOptions.map(option => (
          <div key={option.value} style={{ marginBottom: 8 }}>
            <Text strong>{option.label}:</Text>
            <Text type="secondary" style={{ marginLeft: 8 }}>
              {option.description}
            </Text>
          </div>
        ))}
      </div>
    </div>
  );
};

export default InviteMember; 