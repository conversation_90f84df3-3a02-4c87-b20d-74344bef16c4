import { dal } from './dataAccessLayer';
import TierMappingService from './tierMappingService';

// 数据变更事件接口
export interface DataChangeEvent {
  id: string;
  type: 'CLIENT_UPDATED' | 'PROJECT_UPDATED' | 'FINANCE_UPDATED' | 'CLIENT_CREATED' | 'PROJECT_CREATED' | 'PROJECT_DELETED';
  entityId: string;
  entityType: 'client' | 'project' | 'finance';
  changes: Record<string, any>;
  previousData?: Record<string, any>;
  newData?: Record<string, any>;
  timestamp: string;
  source: string;
  userId?: string;
}

// 事件处理器接口
export interface EventHandler {
  eventType: string;
  handler: (event: DataChangeEvent) => Promise<void>;
  priority: number; // 优先级，数字越小优先级越高
}

// 同步规则配置
export const SYNC_RULES = {
  // 客户信息变更时
  CLIENT_UPDATED: [
    {
      target: 'PROJECT',
      action: 'UPDATE_CLIENT_SNAPSHOT',
      fields: ['name', 'tier', 'country', 'contactPerson'],
      description: '更新项目中的客户信息快照'
    }
  ],
  
  // 客户创建时
  CLIENT_CREATED: [
    {
      target: 'PROJECT',
      action: 'LINK_EXISTING_PROJECTS',
      fields: ['name'],
      description: '关联现有同名项目到新客户'
    }
  ],
  
  // 项目信息变更时
  PROJECT_UPDATED: [
    {
      target: 'CLIENT',
      action: 'RECALCULATE_METRICS',
      fields: ['revenue', 'status', 'client'],
      description: '重新计算客户指标'
    },
    {
      target: 'FINANCE',
      action: 'UPDATE_PROJECT_INFO',
      fields: ['name', 'client'],
      description: '更新财务模块中的项目信息'
    }
  ],
  
  // 项目创建时
  PROJECT_CREATED: [
    {
      target: 'CLIENT',
      action: 'RECALCULATE_METRICS',
      fields: ['revenue', 'client'],
      description: '更新客户统计指标'
    }
  ],
  
  // 项目删除时
  PROJECT_DELETED: [
    {
      target: 'CLIENT',
      action: 'RECALCULATE_METRICS',
      fields: ['client'],
      description: '重新计算客户指标'
    }
  ],
  
  // 财务信息变更时
  FINANCE_UPDATED: [
    {
      target: 'PROJECT',
      action: 'UPDATE_REVENUE_SNAPSHOT',
      fields: ['summary.totalRevenue', 'summary.totalNrc', 'summary.totalMrc'],
      description: '更新项目收入快照'
    },
    {
      target: 'CLIENT',
      action: 'RECALCULATE_METRICS',
      fields: ['summary.totalRevenue'],
      description: '重新计算客户收入指标'
    }
  ]
};

// 数据同步引擎
export class DataSyncEngine {
  private static instance: DataSyncEngine;
  private eventHandlers: EventHandler[] = [];
  private eventQueue: DataChangeEvent[] = [];
  private isProcessing = false;
  private eventHistory: DataChangeEvent[] = [];
  private maxHistorySize = 1000;

  private constructor() {
    this.setupDefaultHandlers();
  }

  // 单例模式
  public static getInstance(): DataSyncEngine {
    if (!DataSyncEngine.instance) {
      DataSyncEngine.instance = new DataSyncEngine();
    }
    return DataSyncEngine.instance;
  }

  // 注册事件处理器
  public registerHandler(eventType: string, handler: (event: DataChangeEvent) => Promise<void>, priority = 10) {
    this.eventHandlers.push({ 
      eventType, 
      handler, 
      priority 
    });
    
    // 按优先级排序
    this.eventHandlers.sort((a, b) => a.priority - b.priority);
    
    console.log(`[DataSyncEngine] Registered handler for ${eventType} with priority ${priority}`);
  }

  // 发送事件
  public async emitEvent(event: DataChangeEvent): Promise<void> {
    console.log(`[DataSyncEngine] Emitting event:`, event);
    
    // 添加到事件历史
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // 添加到队列
    this.eventQueue.push(event);
    
    // 如果没在处理中，开始处理队列
    if (!this.isProcessing) {
      await this.processEventQueue();
    }
  }

  // 处理事件队列
  private async processEventQueue(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    try {
      while (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift()!;
        await this.processEvent(event);
      }
    } catch (error) {
      console.error('[DataSyncEngine] Error processing event queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // 处理单个事件
  private async processEvent(event: DataChangeEvent): Promise<void> {
    const handlers = this.eventHandlers.filter(h => h.eventType === event.type);
    
    if (handlers.length === 0) {
      console.warn(`[DataSyncEngine] No handlers found for event type: ${event.type}`);
      return;
    }

    console.log(`[DataSyncEngine] Processing event ${event.type} with ${handlers.length} handlers`);

    // 并行执行所有处理器
    const results = await Promise.allSettled(
      handlers.map(h => this.executeHandler(h, event))
    );

    // 检查执行结果
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.error(`[DataSyncEngine] Handler ${index} failed for event ${event.type}:`, result.reason);
      }
    });
  }

  // 执行单个处理器
  private async executeHandler(handler: EventHandler, event: DataChangeEvent): Promise<void> {
    try {
      const startTime = Date.now();
      await handler.handler(event);
      const duration = Date.now() - startTime;
      
      if (duration > 1000) {
        console.warn(`[DataSyncEngine] Handler took ${duration}ms for event ${event.type}`);
      }
    } catch (error) {
      console.error(`[DataSyncEngine] Handler execution failed:`, error);
      throw error;
    }
  }

  // 创建事件
  public createEvent(
    type: DataChangeEvent['type'],
    entityId: string,
    entityType: DataChangeEvent['entityType'],
    changes: Record<string, any>,
    source: string,
    previousData?: Record<string, any>,
    newData?: Record<string, any>
  ): DataChangeEvent {
    return {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      entityId,
      entityType,
      changes,
      previousData,
      newData,
      timestamp: new Date().toISOString(),
      source,
      userId: 'current_user' // 在实际应用中应该从认证系统获取
    };
  }

  // 设置默认事件处理器
  private setupDefaultHandlers(): void {
    // 客户更新处理器
    this.registerHandler('CLIENT_UPDATED', async (event) => {
      await this.handleClientUpdated(event);
    }, 1);

    // 项目更新处理器
    this.registerHandler('PROJECT_UPDATED', async (event) => {
      await this.handleProjectUpdated(event);
    }, 1);

    // 项目创建处理器
    this.registerHandler('PROJECT_CREATED', async (event) => {
      await this.handleProjectCreated(event);
    }, 1);

    // 项目删除处理器
    this.registerHandler('PROJECT_DELETED', async (event) => {
      await this.handleProjectDeleted(event);
    }, 1);

    // 财务更新处理器
    this.registerHandler('FINANCE_UPDATED', async (event) => {
      await this.handleFinanceUpdated(event);
    }, 1);

    console.log('[DataSyncEngine] Default handlers registered');
  }

  // 处理客户更新事件
  private async handleClientUpdated(event: DataChangeEvent): Promise<void> {
    try {
      const { changes, entityId } = event;
      
      // 动态导入避免循环依赖
      const projectService = (await import('./new-project.service')).default;
      
      // 获取所有关联项目 - 使用现有的client字段
      const allProjects = await projectService.getAllProjects();
      const relatedProjects = allProjects.filter(p => p.client === changes.name);
      
      console.log(`[DataSyncEngine] Found ${relatedProjects.length} projects to update for client ${entityId}`);
      
      // 更新项目中的客户信息
      for (const project of relatedProjects) {
        const updateData: any = {};
        
        if (changes.name && changes.name !== project.client) {
          updateData.client = changes.name;
        }
        
        if (changes.country && changes.country !== project.country) {
          updateData.country = changes.country;
        }
        
        if (changes.contactPerson && changes.contactPerson !== project.owner) {
          updateData.owner = changes.contactPerson;
        }
        
        if (Object.keys(updateData).length > 0) {
          await projectService.updateProject(project.id, updateData);
          console.log(`[DataSyncEngine] Updated project ${project.id} with client changes`);
        }
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error handling client updated event:', error);
      throw error;
    }
  }

  // 处理项目更新事件
  private async handleProjectUpdated(event: DataChangeEvent): Promise<void> {
    try {
      const { changes } = event;
      
      // 如果客户相关信息发生变化，更新客户指标
      if (changes.revenue !== undefined || changes.status !== undefined || changes.client !== undefined) {
        await this.recalculateClientMetrics(changes.client);
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error handling project updated event:', error);
      throw error;
    }
  }

  // 处理项目创建事件
  private async handleProjectCreated(event: DataChangeEvent): Promise<void> {
    try {
      const { newData } = event;
      
      if (newData?.client) {
        await this.recalculateClientMetrics(newData.client);
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error handling project created event:', error);
      throw error;
    }
  }

  // 处理项目删除事件
  private async handleProjectDeleted(event: DataChangeEvent): Promise<void> {
    try {
      const { previousData } = event;
      
      if (previousData?.client) {
        await this.recalculateClientMetrics(previousData.client);
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error handling project deleted event:', error);
      throw error;
    }
  }

  // 处理财务更新事件
  private async handleFinanceUpdated(event: DataChangeEvent): Promise<void> {
    try {
      const { changes, entityId } = event;
      
      // 如果总收入发生变化，更新项目和客户信息
      if (changes['summary.totalRevenue'] !== undefined) {
        const projectService = (await import('./new-project.service')).default;
        
        // 更新项目收入快照
        const project = await projectService.getProjectById(entityId);
        if (project) {
          await projectService.updateProject(entityId, {
            revenue: changes['summary.totalRevenue']
          });
          
          // 重新计算客户指标
          await this.recalculateClientMetrics(project.client);
        }
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error handling finance updated event:', error);
      throw error;
    }
  }

  // 重新计算客户指标
  private async recalculateClientMetrics(clientIdentifier: string): Promise<void> {
    if (!clientIdentifier) return;
    
    try {
      const clientService = (await import('./client.service')).default;
      const projectService = (await import('./new-project.service')).default;
      
      // 获取所有项目
      const allProjects = await projectService.getAllProjects();
      
      // 根据客户名称过滤项目
      const clientProjects = allProjects.filter(p => 
        p.client?.toLowerCase() === clientIdentifier?.toLowerCase()
      );
      
      // 计算指标
      const totalRevenue = clientProjects.reduce((sum, p) => sum + (p.revenue || 0), 0);
      const totalProjects = clientProjects.length;
      
      // 查找客户并更新指标
      const allClients = await clientService.getAllClients();
      const client = allClients.find(c => 
        c.name?.toLowerCase() === clientIdentifier?.toLowerCase()
      );
      
      if (client) {
        // 使用现有字段结构更新
        await clientService.updateClient(client.id, { 
          totalRevenue,
          totalProjects
        });
        
        console.log(`[DataSyncEngine] Updated client metrics for ${client.name}: revenue=${totalRevenue}, projects=${totalProjects}`);
      }
      
    } catch (error) {
      console.error('[DataSyncEngine] Error recalculating client metrics:', error);
      throw error;
    }
  }

  // 获取事件历史
  public getEventHistory(limit = 50): DataChangeEvent[] {
    return this.eventHistory.slice(-limit);
  }

  // 清空事件历史
  public clearEventHistory(): void {
    this.eventHistory = [];
    console.log('[DataSyncEngine] Event history cleared');
  }

  // 获取同步统计
  public getSyncStats(): {
    totalEvents: number;
    queueSize: number;
    isProcessing: boolean;
    handlerCount: number;
  } {
    return {
      totalEvents: this.eventHistory.length,
      queueSize: this.eventQueue.length,
      isProcessing: this.isProcessing,
      handlerCount: this.eventHandlers.length
    };
  }
}

// 导出单例实例
export const dataSyncEngine = DataSyncEngine.getInstance(); 