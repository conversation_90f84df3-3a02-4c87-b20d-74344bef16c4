"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const typeorm_1 = require("typeorm");
const ProjectStage_1 = require("../entities/ProjectStage");
const Project_1 = require("../entities/Project");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const validation_middleware_1 = require("../middlewares/validation.middleware");
const project_stage_dto_1 = require("../dtos/project-stage.dto");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authMiddleware);
router.get('/project/:projectId', (0, validation_middleware_1.validateParams)(project_stage_dto_1.ProjectIdParamDto), async (req, res) => {
    try {
        const { projectId } = req.params;
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne({ where: { id: projectId } });
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        const stageRepository = (0, typeorm_1.getRepository)(ProjectStage_1.ProjectStage);
        const stages = await stageRepository.find({
            where: { project: project },
            order: { createdAt: 'ASC' }
        });
        res.json(stages);
    }
    catch (error) {
        res.status(500).json({ message: '获取项目阶段失败', error });
    }
});
router.get('/:id', (0, validation_middleware_1.validateParams)(project_stage_dto_1.ProjectStageIdParamDto), async (req, res) => {
    try {
        const stageRepository = (0, typeorm_1.getRepository)(ProjectStage_1.ProjectStage);
        const stage = await stageRepository.findOne({
            where: { id: req.params.id },
            relations: ['project']
        });
        if (!stage) {
            return res.status(404).json({ message: '项目阶段不存在' });
        }
        res.json(stage);
    }
    catch (error) {
        res.status(500).json({ message: '获取项目阶段失败', error });
    }
});
router.post('/', (0, validation_middleware_1.validateBody)(project_stage_dto_1.CreateProjectStageDto), async (req, res) => {
    try {
        const { projectId, ...stageData } = req.body;
        const projectRepository = (0, typeorm_1.getRepository)(Project_1.Project);
        const project = await projectRepository.findOne({ where: { id: projectId } });
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        const stageRepository = (0, typeorm_1.getRepository)(ProjectStage_1.ProjectStage);
        const stage = stageRepository.create({
            ...stageData,
            project
        });
        const result = await stageRepository.save(stage);
        res.status(201).json(result);
    }
    catch (error) {
        res.status(500).json({ message: '创建项目阶段失败', error });
    }
});
router.put('/:id', (0, validation_middleware_1.validateParams)(project_stage_dto_1.ProjectStageIdParamDto), (0, validation_middleware_1.validateBody)(project_stage_dto_1.UpdateProjectStageDto), async (req, res) => {
    try {
        const stageRepository = (0, typeorm_1.getRepository)(ProjectStage_1.ProjectStage);
        const stage = await stageRepository.findOne({ where: { id: req.params.id } });
        if (!stage) {
            return res.status(404).json({ message: '项目阶段不存在' });
        }
        stageRepository.merge(stage, req.body);
        const result = await stageRepository.save(stage);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ message: '更新项目阶段失败', error });
    }
});
router.delete('/:id', (0, validation_middleware_1.validateParams)(project_stage_dto_1.ProjectStageIdParamDto), async (req, res) => {
    try {
        const stageRepository = (0, typeorm_1.getRepository)(ProjectStage_1.ProjectStage);
        const stage = await stageRepository.findOne({ where: { id: req.params.id } });
        if (!stage) {
            return res.status(404).json({ message: '项目阶段不存在' });
        }
        await stageRepository.remove(stage);
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: '删除项目阶段失败', error });
    }
});
exports.default = router;
//# sourceMappingURL=project-stage.routes.js.map