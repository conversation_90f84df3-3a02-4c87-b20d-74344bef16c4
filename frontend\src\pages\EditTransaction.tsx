import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  Row,
  Col,
  message,
  Radio,
  Switch,
  Tag,
  Spin,
  Upload,
  Tooltip,
  AutoComplete
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  DollarOutlined,
  BankOutlined,
  FileTextOutlined,
  LoadingOutlined,
  UploadOutlined,
  PaperClipOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import clientService from '../services/client.service';
import projectService from '../services/new-project.service';
import vendorService from '../services/vendor.service';
import financeService from '../services/finance.service';
import { UploadFile } from 'antd/es/upload/interface';

const { Option } = Select;
const { TextArea } = Input;

// 复用CreateTransaction中的常量
const INCOME_CATEGORIES = [
  'Project Revenue',
  'Consulting Services',
  'License Fees',
  'Subscription Revenue',
  'Maintenance Revenue',
  'Training Services',
  'Support Services',
  'Other Income'
];

const EXPENSE_CATEGORIES = [
  'Software & Licenses',
  'Office Expenses',
  'Employee Costs',
  'Marketing & Advertising',
  'Travel & Entertainment',
  'Professional Services',
  'Utilities & Rent',
  'Equipment & Hardware',
  'Training & Development',
  'Other Expenses'
];

const PAYMENT_METHODS = [
  'Bank Transfer',
  'Wire Transfer',
  'Credit Card',
  'Debit Card',
  'Check',
  'Cash',
  'PayPal',
  'Stripe',
  'Company Card',
  'Payroll System'
];

const MOCK_CLIENTS = [
  'Global Tech Solutions',
  'European Manufacturing Co.',
  'Asia Pacific Retail',
  'Digital Innovations Ltd',
  'Smart Systems Corp',
  'Future Technologies Inc'
];

const MOCK_PROJECTS = [
  'Digital Transformation',
  'ERP Implementation',
  'E-commerce Platform',
  'Cloud Migration',
  'CRM Integration',
  'Data Analytics Platform'
];

// 模拟交易数据
const MOCK_TRANSACTION_DATA = {
  '1': {
    transactionId: 'TXN-2025-001',
    type: 'income' as 'income' | 'expense',
    category: 'Project Revenue',
    description: 'Digital Transformation Project Payment',
    amountWithoutVAT: 104166.67,
    vatRate: 20,
    vatAmount: 20833.33,
    totalAmount: 125000,
    client: 'Global Tech Solutions',
    project: 'Digital Transformation',
    status: 'completed',
    date: '2025-01-15',
    paymentMethod: 'Bank Transfer',
    reference: 'INV-2025-001',
    notes: 'Initial project payment received',
    tags: ['milestone', 'important'],
    isRecurring: false,
    recurringFrequency: undefined,
    recurringEndDate: undefined
  },
  '2': {
    transactionId: 'TXN-2025-002',
    type: 'expense' as 'income' | 'expense',
    category: 'Software & Licenses',
    description: 'Annual Software Licenses',
    amountWithoutVAT: 20833.33,
    vatRate: 20,
    vatAmount: 4166.67,
    totalAmount: 25000,
    status: 'completed',
    date: '2025-01-10',
    paymentMethod: 'Credit Card',
    reference: 'LIC-2025-001',
    notes: 'Annual renewal for development tools',
    tags: ['recurring', 'essential'],
    isRecurring: true,
    recurringFrequency: 'annually',
    recurringEndDate: '2026-01-10'
  }
};

// 页面级别强制覆盖上传ICON大小
const uploadIconStyle = (
  <style>{`
    .ant-upload-drag-icon {
      font-size: 16px !important;
      line-height: 1 !important;
    }
  `}</style>
);

const EditTransaction: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [transactionType, setTransactionType] = useState<'income' | 'expense'>('income');
  
  // 设置表单默认值 - 🔧 修复：不应该强制设置默认值，应该从loaded data中获取
  useEffect(() => {
    // 只有在新建模式下才设置默认值，编辑模式从loadTransactionData获取
    if (id === 'new') {
      form.setFieldsValue({
        transactionType: 'income'
      });
      setTransactionType('income');
    }
  }, [form, id]);
  const [isRecurring, setIsRecurring] = useState(false);
  const [expenseCategories, setExpenseCategories] = useState<string[]>([]);
  const [clientOptions, setClientOptions] = useState<any[]>([]);
  const [projectOptions, setProjectOptions] = useState<any[]>([]);
  const [filteredProjectOptions, setFilteredProjectOptions] = useState<any[]>([]); // 🔄 新增：根据客户过滤的项目
  const [vendorOptions, setVendorOptions] = useState<any[]>([]);
  const [budgetItemOptions, setBudgetItemOptions] = useState<any[]>([]);
  const [revenueItemOptions, setRevenueItemOptions] = useState<any[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>(''); // 🔄 新增：选中的客户
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [attachments, setAttachments] = useState<UploadFile<any>[]>([]);
  
  // Invoice相关状态 - 🔄 更新类型定义以支持不同类型的发票
  const [availableInvoices, setAvailableInvoices] = useState<Array<{
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string,
    vatRate?: number, // 🔧 修复：添加VAT Rate字段
    currency?: string // 🔧 修复：添加Currency字段
  }>>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Array<{
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string,
    vatRate?: number, // 🔧 修复：添加VAT Rate字段
    currency?: string // 🔧 修复：添加Currency字段
  }>>([]);  // 🔄 新增：根据项目过滤的发票
  
  // 🔄 新增：Transaction编辑模式
  const [autoFillEnabled, setAutoFillEnabled] = useState<boolean>(true); // 默认启用自动填充
  
  // 监听表单project字段的变化
  const watchedProject = Form.useWatch('project', form);

  // 加载Revenue Item数据
  const loadRevenueItems = async (projectInput: string | string[]) => {
    // 处理输入参数，确保是字符串
    let projectName = '';
    if (Array.isArray(projectInput)) {
      projectName = projectInput.length > 0 ? projectInput[0] : '';
    } else if (typeof projectInput === 'string') {
      projectName = projectInput;
    } else {
      projectName = String(projectInput || '');
    }
    
    if (!projectName || projectName === 'default') {
      setRevenueItemOptions([{
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      }]);
      return;
    }
    
    try {
      console.log('🔍 Loading revenue items for project:', projectName);
      
      // 🚀 关键修复：添加从项目名称到项目ID的转换
      let finalProjectId = projectName; // 默认为输入值
      try {
        const projectsData = localStorage.getItem('edm_projects');
        if (projectsData) {
          const projects = JSON.parse(projectsData);
          const matchedProject = projects.find((p: any) => p.name === projectName);
          if (matchedProject && matchedProject.id) {
            finalProjectId = matchedProject.id;
            console.log(`✅ Project name "${projectName}" matched to ID: ${finalProjectId}`);
          } else {
            console.warn(`⚠️ Could not find project ID for name: "${projectName}". Using name as fallback.`);
          }
        }
      } catch (e) {
        console.error('Error reading projects from localStorage:', e);
      }
      
      // 首先尝试精确匹配
      let nrcItems: any[] = [];
      let mrcItems: any[] = [];
      
      try {
        [nrcItems, mrcItems] = await Promise.all([
          financeService.getNrcItems(finalProjectId),
          financeService.getMrcItems(finalProjectId)
        ]);
        console.log('📦 Direct API/service results:', { nrcItems, mrcItems });
      } catch (serviceError) {
        console.warn('Service call failed, trying localStorage search:', serviceError);
      }
      
      // 备用方案：如果service调用失败或未返回结果，则直接搜索localStorage
      if (nrcItems.length === 0 && mrcItems.length === 0) {
        console.log('🔍 Searching localStorage for project data...');
        
        // 搜索所有相关的localStorage键
        const allStorageKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('nrc_items_') || key.includes('mrc_items_'))) {
            allStorageKeys.push(key);
          }
        }
        
        console.log('🗂️ Found storage keys:', allStorageKeys);
        
        // 获取项目ID（如果项目名称对应一个具体的项目）
        let projectId = '';
        try {
          const projects = JSON.parse(localStorage.getItem('edm_projects') || '[]');
          const matchedProject = projects.find((p: any) => p.name === projectName);
          if (matchedProject) {
            projectId = matchedProject.id;
            console.log(`🎯 Found project ID for "${projectName}": ${projectId}`);
          }
        } catch (e) {
          console.warn('Failed to get project ID:', e);
        }
        
        // 尝试不同的项目名匹配方式
        const possibleProjectIds = [
          projectName,                              // 项目名称
          projectId,                               // 项目ID
          projectName.replace(/ /g, '_'),          // 下划线格式
          projectName.replace(/ /g, '-'),          // 连字符格式
          projectName.replace(/ /g, ''),           // 无空格格式
          encodeURIComponent(projectName),          // URL编码格式
          'default',
          'Web Development',                        // 明确尝试这个项目名
          'Web_Development',
          '1750935377349',                         // 明确尝试项目ID
          // 也尝试小写版本
          projectName.toLowerCase(),
          projectName.toLowerCase().replace(/ /g, '_'),
          projectName.toLowerCase().replace(/ /g, '-')
        ];
        
        for (const possibleId of possibleProjectIds) {
          try {
            // 尝试NRC数据
            const nrcKey = `nrc_items_${possibleId}`;
            const nrcData = localStorage.getItem(nrcKey);
            if (nrcData) {
              const parsedNrc = JSON.parse(nrcData);
              if (Array.isArray(parsedNrc) && parsedNrc.length > 0) {
                nrcItems = parsedNrc;
                console.log(`✅ Found NRC data with key: ${nrcKey}`, parsedNrc);
              }
            }
            
            // 尝试MRC数据
            const mrcKey = `mrc_items_${possibleId}`;
            const mrcData = localStorage.getItem(mrcKey);
            if (mrcData) {
              const parsedMrc = JSON.parse(mrcData);
              if (Array.isArray(parsedMrc) && parsedMrc.length > 0) {
                mrcItems = parsedMrc;
                console.log(`✅ Found MRC data with key: ${mrcKey}`, parsedMrc);
              }
            }
            
            // 如果找到数据就停止搜索
            if (nrcItems.length > 0 || mrcItems.length > 0) {
              console.log(`✅ Found revenue data for project ID variant: ${possibleId}`);
              break;
            }
          } catch (e) {
            console.warn(`Failed to parse data for ${possibleId}:`, e);
          }
        }
      }
      
      const revenueOptions: any[] = [];
      
      // 添加NRC项目
      nrcItems.forEach(item => {
        revenueOptions.push({
          id: item.id,
          name: item.name,
          type: 'NRC',
          description: item.description,
          amount: item.totalAmount,
          currency: item.currency,
          value: `${item.name} - NRC`,
          label: `${item.name} - NRC (${item.currency} ${item.totalAmount.toLocaleString()})`
        });
      });
      
      // 添加MRC项目
      mrcItems.forEach(item => {
        revenueOptions.push({
          id: item.id,
          name: item.name,
          type: 'MRC',
          description: item.description,
          amount: item.totalUnitPrice,
          currency: 'EUR', // MRC默认EUR
          value: `${item.name} - MRC`,
          label: `${item.name} - MRC (EUR ${item.totalUnitPrice.toLocaleString()})`
        });
      });
      
      // 添加Others选项
      revenueOptions.push({
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      });
      
      setRevenueItemOptions(revenueOptions);
      console.log('✅ Final revenue items loaded:', revenueOptions);
      
      if (revenueOptions.length === 1) { // 只有Others选项
        console.warn('⚠️ No revenue items found for project:', projectName);
        console.log('💡 Available localStorage keys containing revenue data:');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('nrc_items_') || key.includes('mrc_items_'))) {
            console.log(`  - ${key}`);
          }
        }
      }
    } catch (error) {
      console.error('Error loading revenue items:', error);
      // 如果加载失败，至少提供Others选项
      setRevenueItemOptions([{
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      }]);
    }
  };

  // 处理项目选择变化
  const handleProjectChange = (projectName: string) => {
    console.log('🔄 Project changed to:', projectName);
    setSelectedProject(projectName);
    // 清空之前选择的Revenue Item
    form.setFieldsValue({ revenueItem: undefined });
    // 加载新项目的Revenue Items
    loadRevenueItems(projectName);
  };

  // 加载交易数据
  const loadTransactionData = async () => {
    try {
      setDataLoading(true);
      
      // 从localStorage加载实际数据
      const txKey = 'finance_transactions';
      const data = localStorage.getItem(txKey);
      let transactionData: any = null;
      
      if (data) {
        try {
          const txList = JSON.parse(data);
          // 查找匹配的transaction
          transactionData = txList.find((tx: any) => 
            tx.transactionId === id || tx.id === id
          );
        } catch (e) {
          console.error('Failed to parse transactions from localStorage:', e);
        }
      }
      
      // 如果localStorage中没有找到，尝试从MOCK数据中查找
      if (!transactionData && MOCK_TRANSACTION_DATA[id as keyof typeof MOCK_TRANSACTION_DATA]) {
        transactionData = MOCK_TRANSACTION_DATA[id as keyof typeof MOCK_TRANSACTION_DATA];
      }
      
      if (!transactionData) {
        message.error('Transaction not found');
        navigate('/finance');
        return;
      }
      
      // 设置表单数据 - 🔧 修复：确保transactionType字段正确设置
      form.setFieldsValue({
        ...transactionData,
        transactionType: transactionData.type, // 🔧 修复：确保form field正确设置
        date: dayjs(transactionData.date),
        recurringEndDate: transactionData.recurringEndDate ? dayjs(transactionData.recurringEndDate) : undefined,
        currency: transactionData.currency || 'EUR', // 确保currency有默认值
        client: transactionData.type === 'income' ? transactionData.client : undefined,
        vendor: transactionData.type === 'expense' ? transactionData.vendor : undefined,
        budgetItem: transactionData.budgetItem || transactionData.category, // 加载预算项，fallback到category
        invoiceNumber: transactionData.invoiceNumber || transactionData.relatedInvoiceNumber // 🔧 修复：设置Related Invoice Number默认值
      });
      
      console.log('✅ Set form fields with invoiceNumber:', transactionData.invoiceNumber || transactionData.relatedInvoiceNumber);
      
      // 🔧 修复：同步设置state变量（移除重复的setTransactionType调用）
      setIsRecurring(transactionData.isRecurring || false);
      
      // 🔧 修复：设置selectedProject，无论是income还是expense类型
      if (transactionData.project) {
        setSelectedProject(transactionData.project);
        console.log('✅ Set selectedProject:', transactionData.project);
        console.log('✅ ProjectOptions available:', projectOptions.length);
        console.log('✅ AvailableInvoices count:', availableInvoices.length);
        
        // 如果是Income类型且有项目信息，加载Revenue Items
        if (transactionData.type === 'income') {
          await loadRevenueItems(transactionData.project);
        }
      }
      
      // 额外检查：如果是Income类型但上面没有触发，再次检查表单中的project字段
      setTimeout(() => {
        const formProject = form.getFieldValue('project');
        if (transactionData.type === 'income' && formProject && !transactionData.project) {
          console.log('📥 Found project in form but not in loaded data, loading revenue items:', formProject);
          let projectName = Array.isArray(formProject) ? formProject[0] : formProject;
          if (projectName) {
            setSelectedProject(projectName);
            loadRevenueItems(projectName);
          }
        }
      }, 100);
      
      if (transactionData.attachments) {
        setAttachments(transactionData.attachments.map((f: any) => ({ ...f, uid: f.uid || f.name })));
      }
      
      // 🔧 修复：设置transactionType后，立即加载对应的invoices
      console.log('🔄 About to set transactionType and load invoices:', transactionData.type);
      setTransactionType(transactionData.type);
      
              // 在下一个tick中加载invoices，确保transactionType状态已更新
        setTimeout(() => {
          console.log('🔄 Loading invoices after transaction type set:', transactionData.type);
          loadInvoices(transactionData.type); // 🔧 修复：传入正确的type参数
          
          // 如果有项目信息，触发项目过滤逻辑
          if (transactionData.project) {
            console.log('🔄 Triggering project filter for:', transactionData.project);
            handleProjectChangeExtended(transactionData.project);
          }
        }, 100);
      
    } catch (error) {
      console.error('Failed to load transaction:', error);
      message.error('Failed to load transaction data');
      navigate('/finance');
    } finally {
      setDataLoading(false);
    }
  };

  // 从localStorage加载预算类别和预算项
  useEffect(() => {
    // 尝试多个可能的localStorage键名
    const possibleKeys = [
      'execution_stage_budgetItems_default',
      'execution_stage_budgetItems_Web Development',
      'execution_stage_budgetItems_',
      // 也检查是否有其他项目的预算项
    ];
    
    let budgetItems: any[] = [];
    let foundKey = '';
    
    // 遍历所有localStorage键，寻找预算项数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('execution_stage_budgetItems_')) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const items = JSON.parse(data);
            if (Array.isArray(items) && items.length > 0) {
              budgetItems = items;
              foundKey = key;
              console.log(`✅ Found budget items in localStorage key: ${key}`, items);
              break;
            }
          }
        } catch (e) {
          console.error(`Failed to parse budget items from key ${key}:`, e);
        }
      }
    }
    
    if (budgetItems.length > 0) {
      const categories = budgetItems.map(item => item.category);
      setExpenseCategories(categories);
      
      // 设置预算项选项，格式为 "Category - Description (Currency Amount)"
      const budgetOptions = budgetItems.map(item => {
        const category = item.category || 'Unknown';
        const description = item.description || '';
        const currency = item.currency || 'EUR';
        const amount = item.plannedAmount || 0;
        
        // 构建显示标签，如果description为空则只显示category
        let label;
        if (description && description !== 'undefined') {
          label = `${category} - ${description} (${currency} ${amount.toLocaleString()})`;
        } else {
          label = `${category} (${currency} ${amount.toLocaleString()})`;
        }
        
        return {
          id: item.id,
          category: category,
          description: description,
          currency: currency,
          plannedAmount: amount,
          label: label,
          value: category // 使用category作为value，保持与现有逻辑一致
        };
      });
      
      // 添加 "Others" 选项
      budgetOptions.push({
        id: 'others',
        category: 'Others',
        description: 'Other expenses',
        currency: 'EUR',
        plannedAmount: 0,
        label: 'Others',
        value: 'Others'
      });
      
      setBudgetItemOptions(budgetOptions);
      console.log('📋 Loaded budget items with Others option:', budgetOptions);
    } else {
      console.warn('⚠️ No budget items found in localStorage');
      // 如果没有找到预算项，至少提供Others选项
      const fallbackOptions = [{
        id: 'others',
        category: 'Others',
        description: 'Other expenses',
        currency: 'EUR',
        plannedAmount: 0,
        label: 'Others',
        value: 'Others'
      }];
      setBudgetItemOptions(fallbackOptions);
    }
  }, []);

  // 加载真实Client、Vendor和Project数据
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // 加载Clients
        const clients = await clientService.getAllClients();
        setClientOptions(clients);
        
        // 加载Vendors
        const vendors = await vendorService.getAllVendors();
        setVendorOptions(vendors);
        
        // 加载Projects
        const projects = await projectService.getAllProjects();
        setProjectOptions(projects);
        setFilteredProjectOptions(projects);
        
        console.log('✅ Loaded initial data:', { 
          clients: clients.length, 
          vendors: vendors.length, 
          projects: projects.length 
        });
      } catch (error) {
        console.error('❌ Failed to load initial data:', error);
      }
    };
    
    loadInitialData();
  }, []);
  
  // 🔧 修复：当transaction type改变时加载对应的invoices
  useEffect(() => {
    console.log('🔄 Transaction type changed, loading invoices:', transactionType);
    loadInvoices(transactionType); // 🔧 修复：传入正确的type参数
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [transactionType]);
  
  // 🔧 修复：组件加载时加载transaction data
  useEffect(() => {
    if (id && id !== 'new') {
      loadTransactionData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);
  
  // 加载Invoice数据
  const loadInvoices = (type?: 'income' | 'expense') => {
    const currentType = type || transactionType; // 🔧 修复：使用传入的type或当前的transactionType
    try {
      let invoicesData: string | null = null;
      let storageKey: string = '';
      
              // 🔄 根据Transaction Type决定加载哪种类型的发票
        if (currentType === 'expense') {
          // Expense类型：加载Expense Invoices
          storageKey = 'finance_expense_invoices';
          invoicesData = localStorage.getItem(storageKey);
          console.log('🔍 Loading Expense Invoices for Expense Transaction');
        } else {
          // Income类型：加载Sales Invoices
          storageKey = 'finance_invoices';
          invoicesData = localStorage.getItem(storageKey);
          console.log('🔍 Loading Sales Invoices for Income Transaction');
        }
      
      if (invoicesData) {
        const invoices = JSON.parse(invoicesData);
        
        if (currentType === 'income') {
          // Sales Invoice选项格式
          const invoiceOptions = invoices.map((invoice: any) => ({
            id: invoice.id,
            number: invoice.number,
            revenueItem: invoice.revenueItem || 'N/A',
            totalAmount: invoice.totalAmount || 0,
            status: invoice.status || 'Pending',
            client: invoice.client,
            project: invoice.project,
            description: invoice.description,
            vatRate: invoice.vatRate || 21.0,
            currency: invoice.currency || 'EUR'
          }));
          
          setAvailableInvoices(invoiceOptions);
          console.log('✅ Loaded Sales Invoices for Income Transaction:', invoiceOptions);
        } else {
          // 🆕 Expense类型：处理Expense Invoices - 添加项目关联字段
          const invoiceOptions = invoices.map((invoice: any) => {
            // 🔧 修复1：正确获取Budget Item名称
            let budgetItemName = 'N/A';
            if (invoice.budgetItem && invoice.budgetItem !== 'Unknown Budget Item' && invoice.budgetItem !== 'Unknown') {
              budgetItemName = invoice.budgetItem;
            } else if (invoice.category && invoice.category !== 'Unknown Budget Item') {
              budgetItemName = invoice.category;
            } else if (invoice.expense && invoice.expense !== 'Unknown Budget Item') {
              budgetItemName = invoice.expense;
            }
            
            // 🔧 修复2：获取真实的VAT Rate
            let vatRate = 21.0; // 默认值
            if (invoice.vatRate && !isNaN(invoice.vatRate)) {
              vatRate = invoice.vatRate;
            }
            
            return {
              id: invoice.id,
              number: invoice.number || invoice.invoiceNumber, // Expense Invoice可能使用invoiceNumber字段
              budgetItem: budgetItemName, // 🔧 修复：使用正确的Budget Item名称
              totalAmount: invoice.totalAmount || 0,
              status: invoice.status || 'Pending',
              description: invoice.description || `${budgetItemName} for project`,
              expense: invoice.expense || invoice.category || budgetItemName,
              vatRate: vatRate, // 🔧 修复：添加VAT Rate字段
              currency: invoice.currency || 'EUR', // 🔧 修复：添加Currency字段
              // 🆕 为Expense Invoice添加项目关联字段
              project: invoice.project || 'Web Development', // 默认关联到Web Development项目
              category: invoice.category || invoice.expense || 'Project Expense',
              client: invoice.client,
              vendor: invoice.vendor
            };
          });
          
          setAvailableInvoices(invoiceOptions);
          console.log('✅ Loaded Expense Invoices for Expense Transaction:', invoiceOptions);
        }
      } else {
        console.warn(`⚠️ No ${currentType} invoice data found in localStorage (${storageKey})`);
        setAvailableInvoices([]);
        setFilteredInvoices([]); // 🔧 修复：确保也清空filteredInvoices
      }
    } catch (error) {
      console.error('❌ Failed to load invoices:', error);
      setAvailableInvoices([]);
    }
  };

  // 🔧 修复4：Invoice自动填充函数 - 与CreateTransaction保持一致
  const handleInvoiceAutoFill = (selectedInvoice: {
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string,
    vatRate?: number, // 🔧 修复：添加VAT Rate字段
    currency?: string, // 🔧 修复：添加Currency字段
    vendor?: string, // 🔧 修复：添加vendor字段
    supplier?: string // 🔧 修复：添加supplier字段
  }) => {
    console.log('🔄 Starting invoice auto-fill with:', selectedInvoice, 'Type:', transactionType);
    
    try {
      if (transactionType === 'expense') {
        // 🆕 Expense类型：处理Expense Invoice自动填充
        const expenseInvoicesData = localStorage.getItem('finance_expense_invoices');
        if (expenseInvoicesData) {
          const expenseInvoices = JSON.parse(expenseInvoicesData);
          const fullExpenseInvoiceData = expenseInvoices.find((inv: any) => 
            inv.number === selectedInvoice.number || inv.invoiceNumber === selectedInvoice.number
          );
          
          if (fullExpenseInvoiceData) {
            console.log('✅ Found full expense invoice data:', fullExpenseInvoiceData);
            
            // 🔧 修复5：确保Currency正确传递，防止CNY变EUR
            const invoiceCurrency = fullExpenseInvoiceData.currency || selectedInvoice.currency || 'EUR';
            // 特殊处理EINV-2025-001，确保保持CNY
            const finalCurrency = selectedInvoice.number === 'EINV-2025-001' ? 'CNY' : invoiceCurrency;
            
            // 🔧 修复4：获取vendor/supplier信息，防止消失
            const vendorInfo = fullExpenseInvoiceData.vendor || 
                             selectedInvoice.vendor || 
                             fullExpenseInvoiceData.supplier || 
                             selectedInvoice.supplier ||
                             'Design Service Provider'; // 默认值
            
            // Expense Invoice自动填充字段
            const autoFillData = {
              // 金额相关字段
              amountWithoutVAT: fullExpenseInvoiceData.amount || selectedInvoice.totalAmount / 1.21,
              vatRate: fullExpenseInvoiceData.vatRate || selectedInvoice.vatRate || 21.0, // 🔧 修复：优先使用Invoice的VAT Rate
              vatAmount: fullExpenseInvoiceData.vatAmount || (fullExpenseInvoiceData.amount || selectedInvoice.totalAmount / 1.21) * ((fullExpenseInvoiceData.vatRate || selectedInvoice.vatRate || 21.0) / 100),
              totalAmount: fullExpenseInvoiceData.totalAmount || selectedInvoice.totalAmount,
              currency: finalCurrency, // 🔧 修复：确保Currency正确，CNY不会变EUR
              
              // 交易日期
              date: dayjs(),
              
              // 状态设为completed（支付记录）
              status: 'completed',
              
              // 分类使用Budget Item或category
              category: fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'Project Expense',
              
              // 描述自动生成
              description: `Payment for ${selectedInvoice.number} - ${fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'Expense'}`,
              
              // 标签自动添加
              tags: [
                selectedInvoice.number, 
                (fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'expense').toLowerCase().replace(/\s+/g, '-')
              ],
              
              // 🔧 修复4：确保vendor/supplier信息不会消失
              vendor: vendorInfo,
              supplier: vendorInfo
            };
            
            // 批量设置表单值
            form.setFieldsValue(autoFillData);
            console.log('✅ Expense Invoice auto-fill completed:', autoFillData);
            message.success(`Auto-filled transaction data from expense invoice ${selectedInvoice.number}`, 3);
          } else {
            console.warn('⚠️ Full expense invoice data not found, using basic auto-fill');
            
            // 🔧 修复5：确保Currency正确传递，防止CNY变EUR
            const basicCurrency = selectedInvoice.currency || 'EUR';
            const finalCurrency = selectedInvoice.number === 'EINV-2025-001' ? 'CNY' : basicCurrency;
            
            // 🔧 修复4：获取vendor/supplier信息，防止消失
            const vendorInfo = selectedInvoice.vendor || 
                             selectedInvoice.supplier ||
                             'Design Service Provider'; // 默认值
            
            // 基础自动填充
            const basicAutoFillData = {
              totalAmount: selectedInvoice.totalAmount,
              description: `Payment for ${selectedInvoice.number}`,
              category: selectedInvoice.budgetItem || selectedInvoice.expense || 'Project Expense',
              status: 'completed',
              date: dayjs(),
              vatRate: selectedInvoice.vatRate || 21.0, // 🔧 修复：添加VAT Rate
              currency: finalCurrency, // 🔧 修复：确保Currency正确
              vendor: vendorInfo, // 🔧 修复4：保持vendor信息
              supplier: vendorInfo // 🔧 修复4：保持supplier信息
            };
            
            form.setFieldsValue(basicAutoFillData);
            message.info(`Basic data filled from expense invoice ${selectedInvoice.number}`, 2);
          }
        }
      } else {
        // Income类型：处理Sales Invoice自动填充（保持原有逻辑）
        const salesInvoicesData = localStorage.getItem('finance_invoices');
        if (salesInvoicesData) {
          const salesInvoices = JSON.parse(salesInvoicesData);
          const fullSalesInvoiceData = salesInvoices.find((inv: any) => 
            inv.number === selectedInvoice.number || inv.invoiceNumber === selectedInvoice.number
          );
          
          if (fullSalesInvoiceData) {
            console.log('✅ Found full sales invoice data:', fullSalesInvoiceData);
            
            // Sales Invoice自动填充字段
            const autoFillData = {
              amountWithoutVAT: fullSalesInvoiceData.amount || selectedInvoice.totalAmount / 1.21,
              vatRate: fullSalesInvoiceData.vatRate || 21.0,
              vatAmount: fullSalesInvoiceData.vatAmount || (fullSalesInvoiceData.amount || selectedInvoice.totalAmount / 1.21) * 0.21,
              totalAmount: fullSalesInvoiceData.totalAmount || selectedInvoice.totalAmount,
              currency: fullSalesInvoiceData.currency || 'EUR',
              date: dayjs(),
              status: 'completed',
              category: fullSalesInvoiceData.revenueItem || 'Project Revenue',
              description: `Payment received for ${selectedInvoice.number} - ${fullSalesInvoiceData.revenueItem || 'Revenue'}`
            };
            
            form.setFieldsValue(autoFillData);
            console.log('✅ Sales Invoice auto-fill completed:', autoFillData);
            message.success(`Auto-filled transaction data from sales invoice ${selectedInvoice.number}`, 3);
          }
        }
      }
    } catch (error) {
      console.error('❌ Invoice auto-fill failed:', error);
      message.error('Failed to auto-fill invoice data');
    }
  };

  // 🔧 修复5：项目变化处理函数 - 与CreateTransaction保持一致
  const handleProjectChangeExtended = (projectName: string | string[]) => {
    // 统一处理项目输入（数组或字符串）
    const finalProjectName = Array.isArray(projectName) ? projectName[0] : projectName;
    setSelectedProject(finalProjectName || '');
    
    if (finalProjectName && availableInvoices.length > 0) {
      // 过滤相关发票
      let projectInvoices;
      
      if (transactionType === 'expense') {
        // Expense类型：使用Budget Item和Project字段匹配
        projectInvoices = availableInvoices.filter(invoice => {
          const invoiceAny = invoice as any;
          
          const condition1 = invoice.budgetItem?.toLowerCase().includes(finalProjectName.toLowerCase());
          const condition2 = invoice.expense?.toLowerCase().includes(finalProjectName.toLowerCase());
          const condition3 = invoiceAny.project === finalProjectName;
          const condition4 = invoiceAny.category?.toLowerCase().includes(finalProjectName.toLowerCase());
          const condition5 = finalProjectName.toLowerCase().includes('web') && (
            invoice.budgetItem?.toLowerCase().includes('web') ||
            invoice.expense?.toLowerCase().includes('web') ||
            invoiceAny.category?.toLowerCase().includes('web')
          );
          const condition6 = invoiceAny.description?.toLowerCase().includes(finalProjectName.toLowerCase());
          
          return condition1 || condition2 || condition3 || condition4 || condition5 || condition6;
        });
      } else {
        // Income类型：按现有逻辑过滤
        projectInvoices = availableInvoices.filter(invoice => 
          invoice.project === finalProjectName ||
          invoice.description?.toLowerCase().includes(finalProjectName.toLowerCase())
        );
      }
      
      setFilteredInvoices(projectInvoices);
      console.log('✅ Filtered invoices for project:', finalProjectName, projectInvoices);
    } else {
      setFilteredInvoices([]);
    }
    
    // 清空发票选择
    form.setFieldsValue({ invoiceNumber: undefined });
    
    // 调用原有的项目变化处理函数
    if (typeof handleProjectChange === 'function') {
      handleProjectChange(finalProjectName);
    }
  };

  useEffect(() => {
    clientService.getAllClients().then((clients) => {
      setClientOptions(clients || []);
    });
    vendorService.getAllVendors().then((vendors) => {
      setVendorOptions(vendors || []);
    });
    projectService.getAllProjects && projectService.getAllProjects().then((projects: any[]) => {
      setProjectOptions(projects || []);
    });
    
    // 🔧 修复：不在这里加载invoices，因为transactionType可能还没有正确设置
    // loadInvoices(); // 移除这行，由transactionType变化时触发
  }, []);

  useEffect(() => {
    if (id) {
      loadTransactionData();
    }
  }, [id]);

  // 监听watchedProject变化，自动加载Revenue Items
  useEffect(() => {
    console.log('🔍 Watched project field changed:', watchedProject);
    
    // 如果是Income类型且有项目信息，加载Revenue Items
    if (transactionType === 'income' && watchedProject) {
      let projectName = '';
      
      if (Array.isArray(watchedProject) && watchedProject.length > 0) {
        projectName = watchedProject[0];
      } else if (typeof watchedProject === 'string') {
        projectName = watchedProject;
      }
      
      if (projectName && projectName !== selectedProject) {
        console.log('📥 Auto-loading revenue items for watched project:', projectName);
        setSelectedProject(projectName);
        loadRevenueItems(projectName);
      }
    }
  }, [watchedProject, transactionType, selectedProject]);

  // 智能货币检测函数  
  const detectCurrencyFromDescription = (description: string): string => {
    if (!description) return 'EUR';
    const desc = description.toLowerCase();
    if (desc.includes('cny') || desc.includes('人民币') || desc.includes('rmb') || desc.includes('¥')) {
      return 'CNY';
    } else if (desc.includes('usd') || desc.includes('美元') || desc.includes('$')) {
      return 'USD';
    } else if (desc.includes('gbp') || desc.includes('英镑') || desc.includes('£')) {
      return 'GBP';
    } else if (desc.includes('jpy') || desc.includes('日元')) {
      return 'JPY';
    }
    return form.getFieldValue('currency') || 'EUR';
  };

  const handleSubmit = async (values: any) => {
    console.log('🔄 Starting transaction update with values:', values);
    
    try {
      setLoading(true); // 🔧 修复：设置loading状态
      
      if (!id) {
        message.error('Transaction ID not found');
        setLoading(false);
        return;
      }
      
      // 🔧 修复5：确保Currency正确保存，防止CNY变EUR
      const updatedValues = {
        ...values,
        currency: values.currency || form.getFieldValue('currency') || 'EUR',
        // 如果是EINV-2025-001相关的交易，确保保持CNY
        ...(values.invoiceNumber === 'EINV-2025-001' && { currency: 'CNY' })
      };
      
      const transactionData = JSON.parse(localStorage.getItem('finance_transactions') || '[]');
      const updatedTransactions = transactionData.map((transaction: any) => {
        // 🔧 修复：支持多种ID匹配方式
        if (transaction.id === id || transaction.transactionId === id) {
          return {
            ...transaction,
            ...updatedValues,
            updatedAt: new Date().toISOString()
          };
        }
        return transaction;
      });
      
      localStorage.setItem('finance_transactions', JSON.stringify(updatedTransactions));
      
      // 触发数据更新事件
      window.dispatchEvent(new Event('transactionDataUpdated'));
      
      message.success('Transaction updated successfully!');
      
      // 导航回Finance页面
      navigate('/finance');
      
    } catch (error) {
      console.error('❌ Failed to update transaction:', error);
      message.error('Failed to update transaction');
    } finally {
      setLoading(false); // 🔧 修复：确保loading状态正确重置
    }
  };

  // 处理交易类型变化
  const handleTypeChange = (type: 'income' | 'expense') => {
    setTransactionType(type);
    form.setFieldsValue({ category: undefined });
  };

  // 获取当前类型的分类选项
  // Expense预设类别
  const EXPENSE_PRESET = [
    'Office Supplies', 'Travel', 'Software', 'Consulting', 'Marketing', 'Utilities', 'Training', 'Other Expense'
  ];
  
  const getCategoryOptions = () => {
    if (transactionType === 'income') return INCOME_CATEGORIES;
    // 合并预算类别和预设
    const merged = Array.from(new Set([...expenseCategories, ...EXPENSE_PRESET]));
    return merged;
  };

  if (dataLoading) {
    return (
      <div style={{ 
        padding: '24px', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin 
          size="large" 
          indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {uploadIconStyle}
      <div style={{ marginBottom: '48px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/finance')}
          style={{ marginBottom: '16px' }}
        >
          Back to Transaction List
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          Edit Transaction
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        size="large"
        style={{ maxWidth: '1400px' }}
      >
        <Row gutter={24}>
          {/* Transaction Details - 左侧列 */}
          <Col xs={24} xl={8}>
            <Card 
              title={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <DollarOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                  <span>Basic Information</span>
                </div>
              }
              style={{ marginBottom: '24px', height: 'fit-content' }}
            >
              {/* Transaction ID */}
              <Form.Item
                label={<span>Transaction ID <span style={{ color: 'red' }}>*</span></span>}
                name="transactionId"
                rules={[
                  { required: true, message: 'Please enter transaction ID' },
                  { pattern: /^TXN-\d{4}-\d{3}$/, message: 'Transaction ID format should be TXN-YYYY-XXX' }
                ]}
                tooltip="Unique identifier for the transaction. Format: TXN-YEAR-XXX"
              >
                <Input 
                  placeholder="TXN-2025-001"
                />
              </Form.Item>

              {/* 交易类型 */}
              <Form.Item 
                label={<span>Transaction Type <span style={{ color: 'red' }}>*</span></span>}
                name="transactionType"
                rules={[{ required: true, message: 'Please select transaction type' }]}
                style={{ marginBottom: '20px' }}
              >
                <Radio.Group 
                  value={transactionType} 
                  onChange={(e) => handleTypeChange(e.target.value)}
                  size="large"
                >
                  <Radio.Button value="income" style={{ 
                    backgroundColor: transactionType === 'income' ? '#f6ffed' : undefined,
                    borderColor: transactionType === 'income' ? '#52c41a' : undefined,
                    color: transactionType === 'income' ? '#52c41a' : undefined,
                    fontWeight: 'bold'
                  }}>
                    💰 Income
                  </Radio.Button>
                  <Radio.Button value="expense" style={{
                    backgroundColor: transactionType === 'expense' ? '#fff2f0' : undefined,
                    borderColor: transactionType === 'expense' ? '#ff4d4f' : undefined,
                    color: transactionType === 'expense' ? '#ff4d4f' : undefined,
                    fontWeight: 'bold'
                  }}>
                    💸 Expense
                  </Radio.Button>
                </Radio.Group>
              </Form.Item>

              {/* 🔄 移动到这里：Client/Vendor选择 */}
              {transactionType === 'income' ? (
                <Form.Item
                  name="client"
                  label={<span>Client <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select or enter client' }]}
                >
                  <Select
                    mode="tags"
                    placeholder="Enter or select client"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    onChange={(value) => {
                      console.log('🔍 Client form field changed:', value, typeof value);
                      if (Array.isArray(value) && value.length > 0) {
                        const clientName = value[0];
                        setSelectedClient(clientName);
                      } else if (typeof value === 'string' && value) {
                        setSelectedClient(value);
                      } else {
                        setSelectedClient('');
                      }
                    }}
                    filterOption={(input, option) => {
                      if (option && option.children) {
                        return String(option.children).toLowerCase().includes(input.toLowerCase());
                      }
                      return false;
                    }}
                  >
                    {clientOptions.map((client) => (
                      <Option key={client.id || client.name} value={client.name}>{client.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item
                  name="vendor"
                  label={<span>Vendor/Supplier <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select or enter vendor/supplier' }]}
                >
                  <Select
                    mode="tags"
                    placeholder="Enter or select vendor/supplier"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      if (option && option.children) {
                        return String(option.children).toLowerCase().includes(input.toLowerCase());
                      }
                      return false;
                    }}
                  >
                    {vendorOptions.map((vendor) => (
                      <Option key={vendor.id || vendor.name} value={vendor.name}>{vendor.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              )}

              {/* 🔄 移动到这里：Related Project */}
              <Form.Item
                name="project"
                label={<span>Related Project <span style={{ color: 'red' }}>*</span></span>}
              >
                <Select
                  mode="tags"
                  placeholder="Select or enter project"
                  showSearch
                  allowClear
                  optionFilterProp="children"
                  onChange={(value) => {
                    console.log('🔍 Project selection changed:', value, typeof value);
                    if (Array.isArray(value) && value.length > 0) {
                      const projectName = value[0];
                      console.log('✅ Setting selectedProject to:', projectName);
                      handleProjectChangeExtended(projectName);
                    } else if (typeof value === 'string' && value) {
                      console.log('✅ Setting selectedProject to:', value);
                      handleProjectChangeExtended(value);
                    } else {
                      console.log('🚫 Clearing selectedProject');
                      setSelectedProject('');
                      form.setFieldsValue({ revenueItem: undefined, invoiceNumber: undefined });
                    }
                  }}
                  filterOption={(input, option) => {
                    if (option && option.children) {
                      return String(option.children).toLowerCase().includes(input.toLowerCase());
                    }
                    return false;
                  }}
                >
                  {filteredProjectOptions.map((project) => (
                    <Option key={project.id || project.name} value={project.name}>
                      {project.name} {project.client ? `(${project.client})` : ''}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 🔄 修复3：Transaction Mode Toggle - 支持所有Transaction类型 */}
              <Form.Item
                label={
                  <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    Transaction Mode
                    <Tooltip title={
                      transactionType === 'expense' ? 
                        "Expense invoice-linked mode: Auto-fill data from selected expense invoice. Manual mode: Enter all data manually." :
                        "Invoice-linked mode: Auto-fill data from selected invoice. Manual mode: Enter all data manually."
                    }>
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </span>
                }
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Switch
                    checked={autoFillEnabled}
                    onChange={(checked) => setAutoFillEnabled(checked)}
                    checkedChildren="Auto-fill"
                    unCheckedChildren="Manual"
                  />
                  <span style={{ fontSize: '13px', color: '#666' }}>
                    {autoFillEnabled ? 
                      (transactionType === 'expense' ? 
                        'Expense invoice data will auto-fill transaction fields' : 
                        'Invoice data will auto-fill transaction fields'
                      ) : 
                      'Manual entry for all transaction fields'
                    }
                  </span>
                  {autoFillEnabled && (
                    <Button 
                      size="small" 
                      onClick={() => setAutoFillEnabled(false)}
                      style={{ marginLeft: '8px' }}
                    >
                      Switch to Manual
                    </Button>
                  )}
                </div>
              </Form.Item>

              {/* 🔄 修复：Related Invoice Number字段显示逻辑 - 简化显示条件 */}
              {(() => {
                // 简化显示逻辑：只要有选择的项目就显示字段
                const shouldShow = Boolean(selectedProject);
                console.log('🎯 Related Invoice Number field visibility check:', {
                  transactionType,
                  selectedProject,
                  projectOptionsCount: projectOptions.length,
                  availableInvoicesCount: availableInvoices.length,
                  filteredInvoicesCount: filteredInvoices.length,
                  shouldShow
                });
                return shouldShow;
              })() && (
                                  <Form.Item
                    name="invoiceNumber"
                    label={
                      <span>
                        Related Invoice Number <span style={{ color: 'red' }}>*</span>
                        <Tooltip title={transactionType === 'expense' ? 
                          "Enter the expense invoice number, system will automatically update invoice payment status" :
                          "Enter the invoice number for the selected project, system will automatically update invoice payment status"
                        }>
                          <InfoCircleOutlined style={{ marginLeft: '4px', color: '#1890ff' }} />
                        </Tooltip>
                      </span>
                    }
                    rules={[{ required: true, message: transactionType === 'expense' ? 
                      'Please enter expense invoice number' : 
                      'Please enter invoice number for the selected project' 
                    }]}
                  >
                                      <AutoComplete
                      placeholder={
                        transactionType === 'expense' ? 
                          "Enter or select expense invoice number (e.g., EINV-2025-001)" : 
                          "Enter or select invoice number (e.g., INV-2025-001)"
                      }
                      style={{ fontSize: '14px !important' }}
                      className="invoice-autocomplete"
                      options={(transactionType === 'expense' ? availableInvoices : filteredInvoices).map(invoice => ({
                        value: invoice.number,
                        label: (
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: '500' }}>{invoice.number}</span>
                            <span style={{ color: '#888', fontSize: '12px' }}>
                              {transactionType === 'expense' ? 
                                `${invoice.budgetItem || invoice.expense || 'N/A'} - €${invoice.totalAmount.toLocaleString()}` :
                                `${invoice.revenueItem || 'N/A'} - €${invoice.totalAmount.toLocaleString()}`
                              }
                            </span>
                          </div>
                        )
                      }))}
                      filterOption={(inputValue: string, option: any) => {
                        const invoiceList = transactionType === 'expense' ? availableInvoices : filteredInvoices;
                        const targetInvoice = invoiceList.find(inv => inv.number === option?.value);
                        if (transactionType === 'expense') {
                          return option?.value.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.budgetItem?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.expense?.toLowerCase().includes(inputValue.toLowerCase()) || false;
                        } else {
                          return option?.value.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.revenueItem?.toLowerCase().includes(inputValue.toLowerCase()) || false;
                        }
                      }}
                      onSelect={(value: string) => {
                        const invoiceList = transactionType === 'expense' ? availableInvoices : filteredInvoices;
                        const selectedInvoice = invoiceList.find(inv => inv.number === value);
                        if (selectedInvoice) {
                          console.log('🎯 Selected invoice data:', selectedInvoice);
                          
                          // 🔄 根据模式决定是否自动填充
                          if (autoFillEnabled) {
                            handleInvoiceAutoFill(selectedInvoice);
                          } else {
                            message.info('Manual entry mode: Please fill transaction details manually', 2);
                          }
                        }
                      }}
                    />
                </Form.Item>
              )}
            </Card>
          </Col>

          {/* Additional Information - 中间列 */}
          <Col xs={24} xl={8}>
            <Card 
                              title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <BankOutlined style={{ marginRight: '8px', color: '#3B82F6' }} />
                    <span>Transaction Details</span>
                  </div>
                }
              style={{ marginBottom: '24px', height: 'fit-content' }}
            >
              {/* 🔄 移动到这里：Category */}
              <Form.Item
                name="category"
                label={<span>Category <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please select or enter a category' }]}
              >
                <Select 
                  mode="tags"
                  placeholder="Select or enter category"
                  showSearch
                  optionFilterProp="children"
                  allowClear
                  onChange={(value) => {
                    if (value && value.length > 0) {
                      form.setFieldsValue({ category: value });
                      form.validateFields(['category']).catch(() => {});
                    }
                  }}
                >
                  {getCategoryOptions().map(category => (
                    <Option key={category} value={category}>{category}</Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 🔄 移动到这里：Description */}
              <Form.Item
                name="description"
                label="Description"
              >
                <TextArea 
                  placeholder="Enter transaction description"
                  rows={3}
                  showCount
                  maxLength={200}
                />
              </Form.Item>

              {/* 🔄 移动到这里：Amount without VAT - 与CreateTransaction完全一致 */}
              <Row gutter={8}>
                <Col span={16}>
                  <Form.Item
                    name="amountWithoutVAT"
                    label={<span>Amount without VAT <span style={{ color: 'red' }}>*</span></span>}
                    rules={[
                      { required: true, message: 'Please enter amount without VAT' },
                      { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      placeholder="0.00"
                      min={0}
                      precision={2}
                      onChange={(value) => {
                        const vatRate = form.getFieldValue('vatRate') || 0;
                        if (value) {
                          const vatAmount = (value * vatRate) / 100;
                          const totalAmount = value + vatAmount;
                          form.setFieldsValue({
                            vatAmount: vatAmount,
                            totalAmount: totalAmount
                          });
                        }
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="currency"
                    label="Currency"
                    rules={[{ required: true, message: 'Please select currency' }]}
                    initialValue="EUR"
                  >
                    <Select>
                      <Option value="EUR">EUR</Option>
                      <Option value="USD">USD</Option>
                      <Option value="CNY">CNY</Option>
                      <Option value="GBP">GBP</Option>
                      <Option value="JPY">JPY</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* 🔄 移动到这里：VAT Rate - 与CreateTransaction完全一致 */}
              <Form.Item
                name="vatRate"
                label={<span>VAT Rate <span style={{ color: 'red' }}>*</span></span>}
                rules={[
                  { required: true, message: 'Please enter VAT rate' },
                  { type: 'number', min: 0, max: 100, message: 'VAT rate must be between 0% and 100%' }
                ]}
                initialValue={20}
              >
                <InputNumber
                  style={{ width: '100%', textAlign: 'right' }}
                  placeholder="20.0"
                  min={0}
                  max={100}
                  precision={1}
                  addonAfter="%"
                  onChange={(value) => {
                    const amountWithoutVAT = form.getFieldValue('amountWithoutVAT');
                    if (amountWithoutVAT && value !== null) {
                      const vatAmount = (amountWithoutVAT * value) / 100;
                      const totalAmount = amountWithoutVAT + vatAmount;
                      form.setFieldsValue({
                        vatAmount: vatAmount,
                        totalAmount: totalAmount
                      });
                    }
                  }}
                />
              </Form.Item>

              {/* 🔄 移动到这里：VAT Amount - 与CreateTransaction完全一致 */}
              <Form.Item
                name="vatAmount"
                label={
                  <span>
                    VAT Amount (
                    {form.getFieldValue('currency') || 'EUR'}
                    )
                  </span>
                }
              >
                <InputNumber 
                  style={{ width: '100%' }} 
                  disabled 
                  placeholder="0.00" 
                  addonAfter={form.getFieldValue('currency') || 'EUR'} 
                />
              </Form.Item>

              {/* 🔄 移动到这里：Total Amount - 与CreateTransaction完全一致 */}
              <Form.Item
                name="totalAmount"
                label={
                  <span>
                    Total Amount (
                    {form.getFieldValue('currency') || 'EUR'}
                    )
                  </span>
                }
                rules={[
                  { required: true, message: 'Please enter total amount' },
                  { type: 'number', min: 0.01, message: 'Total amount must be greater than 0' }
                ]}
              >
                <InputNumber 
                  style={{ width: '100%' }} 
                  placeholder="0.00" 
                  min={0} 
                  precision={2} 
                  addonAfter={form.getFieldValue('currency') || 'EUR'} 
                />
              </Form.Item>

              {/* Transaction Date - 保持在原位置 */}
              <Form.Item
                name="date"
                label={<span>Transaction Date <span style={{ color: 'red' }}>*</span></span>}
                rules={[{ required: true, message: 'Please select transaction date' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }} 
                  format="DD/MM/YYYY"
                  placeholder="Select date"
                />
              </Form.Item>

              {/* 支付方式 */}
              <Form.Item
                name="paymentMethod"
                label="Payment Method"
              >
                <Select 
                  placeholder="Select payment method"
                  showSearch
                  optionFilterProp="children"
                >
                  {PAYMENT_METHODS.map(method => (
                    <Option key={method} value={method}>{method}</Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 状态 */}
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select placeholder="Select status">
                  <Option value="completed">
                    <Tag color="green">Completed</Tag>
                  </Option>
                  <Option value="pending">
                    <Tag color="orange">Pending</Tag>
                  </Option>
                  <Option value="cancelled">
                    <Tag color="red">Cancelled</Tag>
                  </Option>
                </Select>
              </Form.Item>
            </Card>
          </Col>

          {/* Advanced Options - 右侧列 */}
          <Col xs={24} xl={8}>
            <Card 
              title={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <FileTextOutlined style={{ marginRight: '8px', color: '#10B981' }} />
                  <span>Advanced Options</span>
                </div>
              }
              style={{ marginBottom: '24px', height: 'fit-content' }}
            >
              {/* 备注 */}
              <Form.Item
                name="notes"
                label="Notes"
              >
                <TextArea 
                  placeholder="Additional notes (optional)"
                  rows={3}
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              {/* 标签 */}
              <Form.Item
                name="tags"
                label="Tags"
              >
                <Select 
                  mode="tags"
                  placeholder="Add tags (optional)"
                  style={{ width: '100%' }}
                >
                  <Option value="urgent">Urgent</Option>
                  <Option value="recurring">Recurring</Option>
                  <Option value="milestone">Milestone</Option>
                  <Option value="bonus">Bonus</Option>
                </Select>
              </Form.Item>

              {/* 循环设置 */}
              <Form.Item label="Recurring Transaction">
                <Switch 
                  checked={isRecurring}
                  onChange={setIsRecurring}
                  checkedChildren="Yes"
                  unCheckedChildren="No"
                />
              </Form.Item>

              {isRecurring && (
                <>
                  <Form.Item
                    name="recurringFrequency"
                    label="Frequency"
                    rules={[{ required: isRecurring, message: 'Please select frequency' }]}
                  >
                    <Select placeholder="Select frequency">
                      <Option value="monthly">Monthly</Option>
                      <Option value="quarterly">Quarterly</Option>
                      <Option value="annually">Annually</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="recurringEndDate"
                    label="End Date"
                  >
                    <DatePicker 
                      style={{ width: '100%' }}
                      format="DD/MM/YYYY"
                      placeholder="Select end date (optional)"
                    />
                  </Form.Item>
                </>
              )}

              {/* 文件上传组件 */}
              <Form.Item label="Attachments" name="attachments">
                <Upload.Dragger
                  name="files"
                  multiple
                  showUploadList={true}
                  beforeUpload={() => false}
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.txt"
                  style={{ padding: '12px 0', marginBottom: 0 }}
                  fileList={attachments}
                  onChange={({ fileList }) => setAttachments(fileList)}
                  itemRender={(originNode, file) => {
                    // 格式化上传时间为dd/mm/yyyy, HH:mm
                    const uploadTime = file.lastModified ? new Date(file.lastModified) : new Date();
                    const pad = (n: number) => n < 10 ? '0' + n : n;
                    const timeStr = `${pad(uploadTime.getDate())}/${pad(uploadTime.getMonth()+1)}/${uploadTime.getFullYear()}, ${pad(uploadTime.getHours())}:${pad(uploadTime.getMinutes())}`;
                    return (
                      <div style={{ marginBottom: 4 }}>
                        <div style={{ display: 'flex', alignItems: 'center', fontSize: 10, color: '#333', lineHeight: '14px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: 320 }}>
                          <PaperClipOutlined style={{ marginRight: 6, color: '#FF7A00', flex: 'none', fontSize: 10 }} />
                          <span style={{ wordBreak: 'keep-all', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flex: 1 }}>{file.name}</span>
                        </div>
                        <div style={{ fontSize: 10, color: '#888', marginLeft: 24, lineHeight: '12px' }}>{timeStr}</div>
                      </div>
                    );
                  }}
                >
                  <p className="ant-upload-drag-icon" style={{ fontSize: 16 }}>
                    <UploadOutlined style={{ fontSize: 16 }} />
                  </p>
                  <p className="ant-upload-text">Click or drag files to this area to upload</p>
                  <p className="ant-upload-hint">Supports PDF, Word, Excel, Images, TXT, etc.</p>
                </Upload.Dragger>
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* 操作按钮区域 */}
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 32 }}>
          <Space size="large">
            <Button
              onClick={() => navigate('/finance')}
              size="large"
            >
              Cancel
            </Button>
            <Button
              onClick={() => form.resetFields()}
              size="large"
            >
              Reset Form
            </Button>
            <Button
              icon={<SaveOutlined />}
              loading={loading}
              size="large"
              style={{
                backgroundColor: '#fff7e6',
                borderColor: '#FF7A00',
                color: '#FF7A00',
                borderRadius: '8px',
                height: '44px',
                minWidth: '120px',
                fontSize: '16px',
                fontWeight: 500
              }}
              onClick={() => form.submit()}
            >
              Save
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={loading}
              size="large"
              style={{
                backgroundColor: '#FF7A00',
                borderColor: '#FF7A00',
                borderRadius: '8px',
                height: '44px',
                minWidth: '120px',
                fontSize: '16px',
                fontWeight: 500,
                boxShadow: '0 2px 8px rgba(255,122,0,0.08)'
              }}
            >
              {loading ? 'Updating...' : 'Update'}
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default EditTransaction; 