import { getRepository } from 'typeorm';
import { PermissionTemplate, TemplateType } from '../entities/PermissionTemplate';
import { ProjectCollaborator, CollaboratorRole } from '../entities/ProjectCollaborator';
import { User } from '../entities/User';
import { Project } from '../entities/Project';
import { BadRequestError, NotFoundError, ForbiddenError } from '../utils/errors';

interface TimeRestriction {
  startDate?: Date;
  endDate?: Date;
  daysOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  timeRanges?: { start: string; end: string }[];
}

interface AdvancedPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canInvite: boolean;
  canManageStages: boolean;
  canUploadDocuments: boolean;
  canViewFinancials: boolean;
  canComment: boolean;
  canMention: boolean;
  moduleAccess: string[];
  stageAccess?: string[];
  timeRestrictions?: TimeRestriction;
  ipRestrictions?: string[];
  downloadRestrictions?: {
    allowDownload: boolean;
    fileTypes?: string[];
    maxFileSize?: number;
  };
}

export class AdvancedPermissionService {
  private templateRepository = getRepository(PermissionTemplate);
  private collaboratorRepository = getRepository(ProjectCollaborator);
  private userRepository = getRepository(User);
  private projectRepository = getRepository(Project);

  /**
   * 创建权限模板
   */
  async createTemplate(
    name: string,
    description: string,
    permissions: AdvancedPermissions,
    type: TemplateType = TemplateType.CUSTOM,
    createdById?: string
  ): Promise<PermissionTemplate> {
    // 检查模板名称是否已存在
    const existingTemplate = await this.templateRepository.findOne({ where: { name } });
    if (existingTemplate) {
      throw new BadRequestError('权限模板名称已存在');
    }

    const template = this.templateRepository.create({
      name,
      description,
      type,
      permissions,
      createdBy: createdById ? { id: createdById } : undefined
    });

    return await this.templateRepository.save(template);
  }

  /**
   * 获取所有权限模板
   */
  async getAllTemplates(): Promise<PermissionTemplate[]> {
    return await this.templateRepository.find({
      where: { isActive: true },
      relations: ['createdBy'],
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 根据类型获取权限模板
   */
  async getTemplatesByType(type: TemplateType): Promise<PermissionTemplate[]> {
    return await this.templateRepository.find({
      where: { type, isActive: true },
      relations: ['createdBy'],
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * 应用权限模板到协作者
   */
  async applyTemplateToCollaborator(
    templateId: string,
    collaboratorId: string,
    appliedById: string
  ): Promise<ProjectCollaborator> {
    const template = await this.templateRepository.findOne(templateId);
    if (!template || !template.isActive) {
      throw new NotFoundError('权限模板不存在或已禁用');
    }

    const collaborator = await this.collaboratorRepository.findOne(collaboratorId, {
      relations: ['project', 'user']
    });
    if (!collaborator) {
      throw new NotFoundError('协作者不存在');
    }

    // 验证应用者权限
    await this.validateManagePermission(collaborator.project.id, appliedById);

    // 应用模板权限
    collaborator.permissions = template.permissions;
    
    return await this.collaboratorRepository.save(collaborator);
  }

  /**
   * 批量应用权限模板
   */
  async batchApplyTemplate(
    templateId: string,
    collaboratorIds: string[],
    appliedById: string
  ): Promise<ProjectCollaborator[]> {
    const template = await this.templateRepository.findOne(templateId);
    if (!template || !template.isActive) {
      throw new NotFoundError('权限模板不存在或已禁用');
    }

    const collaborators = await this.collaboratorRepository.findByIds(collaboratorIds, {
      relations: ['project', 'user']
    });

    const results: ProjectCollaborator[] = [];

    for (const collaborator of collaborators) {
      try {
        // 验证每个项目的管理权限
        await this.validateManagePermission(collaborator.project.id, appliedById);
        
        collaborator.permissions = template.permissions;
        const saved = await this.collaboratorRepository.save(collaborator);
        results.push(saved);
      } catch (error) {
        // 记录失败的项目但继续处理其他项目
        console.warn(`Failed to apply template to collaborator ${collaborator.id}:`, error);
      }
    }

    return results;
  }

  /**
   * 创建时间限制权限
   */
  async createTimeRestrictedPermission(
    projectId: string,
    userId: string,
    permissions: Omit<AdvancedPermissions, 'timeRestrictions'>,
    timeRestrictions: TimeRestriction,
    createdById: string
  ): Promise<ProjectCollaborator> {
    await this.validateManagePermission(projectId, createdById);

    // 查找现有协作者或创建新的
    let collaborator = await this.collaboratorRepository.findOne({
      where: { project: { id: projectId }, user: { id: userId } }
    });

    if (!collaborator) {
      // 创建新的临时协作者
      collaborator = this.collaboratorRepository.create({
        project: { id: projectId },
        user: { id: userId },
        role: CollaboratorRole.GUEST_EDITOR,
        status: 'accepted',
        permissions: {
          ...permissions,
          timeRestrictions
        }
      });
    } else {
      // 更新现有协作者
      collaborator.permissions = {
        ...permissions,
        timeRestrictions
      };
    }

    return await this.collaboratorRepository.save(collaborator);
  }

  /**
   * 检查时间限制权限
   */
  checkTimeRestrictions(timeRestrictions: TimeRestriction, currentTime: Date = new Date()): boolean {
    if (!timeRestrictions) return true;

    // 检查日期范围
    if (timeRestrictions.startDate && currentTime < timeRestrictions.startDate) {
      return false;
    }
    if (timeRestrictions.endDate && currentTime > timeRestrictions.endDate) {
      return false;
    }

    // 检查星期限制
    if (timeRestrictions.daysOfWeek && timeRestrictions.daysOfWeek.length > 0) {
      const currentDay = currentTime.getDay();
      if (!timeRestrictions.daysOfWeek.includes(currentDay)) {
        return false;
      }
    }

    // 检查时间段限制
    if (timeRestrictions.timeRanges && timeRestrictions.timeRanges.length > 0) {
      const currentTimeStr = currentTime.toTimeString().slice(0, 5); // HH:mm format
      const isInRange = timeRestrictions.timeRanges.some(range => {
        return currentTimeStr >= range.start && currentTimeStr <= range.end;
      });
      if (!isInRange) {
        return false;
      }
    }

    return true;
  }

  /**
   * 检查IP限制
   */
  checkIpRestrictions(ipRestrictions: string[], clientIp: string): boolean {
    if (!ipRestrictions || ipRestrictions.length === 0) return true;

    return ipRestrictions.some(allowedIp => {
      // 支持CIDR格式或简单的IP匹配
      if (allowedIp.includes('/')) {
        // CIDR 匹配逻辑 (简化实现)
        const [network, prefixLength] = allowedIp.split('/');
        // 这里应该实现完整的CIDR匹配逻辑
        return clientIp.startsWith(network.split('.').slice(0, parseInt(prefixLength) / 8).join('.'));
      } else {
        // 精确匹配或通配符匹配
        return allowedIp === clientIp || allowedIp === '*';
      }
    });
  }

  /**
   * 验证用户访问权限（包含高级限制）
   */
  async validateAdvancedPermissions(
    projectId: string,
    userId: string,
    permission: string,
    clientIp?: string,
    currentTime: Date = new Date()
  ): Promise<{ allowed: boolean; reason?: string }> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { project: { id: projectId }, user: { id: userId }, status: 'accepted' }
    });

    if (!collaborator) {
      return { allowed: false, reason: '不是项目协作者' };
    }

    const permissions = collaborator.permissions;

    // 检查基本权限
    const hasBasicPermission = this.checkBasicPermission(permissions, permission);
    if (!hasBasicPermission) {
      return { allowed: false, reason: '没有基本权限' };
    }

    // 检查时间限制
    if (permissions.timeRestrictions) {
      const timeAllowed = this.checkTimeRestrictions(permissions.timeRestrictions, currentTime);
      if (!timeAllowed) {
        return { allowed: false, reason: '当前时间不允许访问' };
      }
    }

    // 检查IP限制
    if (permissions.ipRestrictions && clientIp) {
      const ipAllowed = this.checkIpRestrictions(permissions.ipRestrictions, clientIp);
      if (!ipAllowed) {
        return { allowed: false, reason: 'IP地址不在允许范围内' };
      }
    }

    return { allowed: true };
  }

  /**
   * 批量更新协作者权限
   */
  async batchUpdatePermissions(
    updates: Array<{
      collaboratorId: string;
      permissions: AdvancedPermissions;
    }>,
    updatedById: string
  ): Promise<ProjectCollaborator[]> {
    const results: ProjectCollaborator[] = [];

    for (const update of updates) {
      try {
        const collaborator = await this.collaboratorRepository.findOne(update.collaboratorId, {
          relations: ['project']
        });

        if (collaborator) {
          await this.validateManagePermission(collaborator.project.id, updatedById);
          collaborator.permissions = update.permissions;
          const saved = await this.collaboratorRepository.save(collaborator);
          results.push(saved);
        }
      } catch (error) {
        console.warn(`Failed to update collaborator ${update.collaboratorId}:`, error);
      }
    }

    return results;
  }

  // 私有方法

  private async validateManagePermission(projectId: string, userId: string): Promise<void> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { project: { id: projectId }, user: { id: userId } }
    });

    if (!collaborator || ![CollaboratorRole.OWNER, CollaboratorRole.ADMIN].includes(collaborator.role)) {
      throw new ForbiddenError('没有管理权限');
    }
  }

  private checkBasicPermission(permissions: AdvancedPermissions, permission: string): boolean {
    switch (permission) {
      case 'edit':
        return permissions.canEdit;
      case 'delete':
        return permissions.canDelete;
      case 'invite':
        return permissions.canInvite;
      case 'manageStages':
        return permissions.canManageStages;
      case 'uploadDocuments':
        return permissions.canUploadDocuments;
      case 'viewFinancials':
        return permissions.canViewFinancials;
      case 'comment':
        return permissions.canComment;
      case 'mention':
        return permissions.canMention;
      default:
        return false;
    }
  }
} 