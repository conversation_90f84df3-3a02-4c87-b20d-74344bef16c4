"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const typeorm_1 = require("typeorm");
const User_1 = require("../entities/User");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const google_auth_service_1 = require("../services/google-auth.service");
const apple_auth_service_1 = require("../services/apple-auth.service");
const facebook_auth_service_1 = require("../services/facebook-auth.service");
const router = (0, express_1.Router)();
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
        const user = await userRepository.findOne({ where: { email } });
        if (!user) {
            return res.status(401).json({ message: 'Invalid email or password' });
        }
        if (!user.checkPassword || !user.checkPassword(password)) {
            return res.status(401).json({ message: 'Invalid email or password' });
        }
        const token = jsonwebtoken_1.default.sign({ userId: user.id, email: user.email }, process.env.JWT_SECRET || 'your_jwt_secret_key', { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        const userResponse = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
        res.json({ user: userResponse, token });
        return;
    }
    catch (error) {
        res.status(500).json({ message: 'Error logging in', error });
        return;
    }
});
router.post('/google', async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        const googleUserInfo = await (0, google_auth_service_1.verifyGoogleToken)(token);
        if (!googleUserInfo.verified) {
            return res.status(401).json({ message: 'Email not verified with Google' });
        }
        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
        let user = await userRepository.findOne({ where: { googleId: googleUserInfo.googleId } });
        if (!user) {
            user = await userRepository.findOne({ where: { email: googleUserInfo.email } });
            if (user) {
                user.googleId = googleUserInfo.googleId;
                await userRepository.save(user);
            }
            else {
                user = userRepository.create({
                    username: googleUserInfo.username,
                    email: googleUserInfo.email,
                    googleId: googleUserInfo.googleId,
                    password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
                });
                if (user && typeof user.hashPassword === 'function') {
                    user.hashPassword();
                }
                await userRepository.save(user);
            }
        }
        if (!user) {
            return res.status(500).json({ message: 'Failed to create or find user' });
        }
        const jwtToken = jsonwebtoken_1.default.sign({ userId: user.id, email: user.email }, process.env.JWT_SECRET || 'your_jwt_secret_key', { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        const userResponse = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
        res.json({ user: userResponse, token: jwtToken });
        return;
    }
    catch (error) {
        console.error('Google login error:', error);
        res.status(500).json({ message: 'Error during Google login', error: error.message });
        return;
    }
});
router.post('/apple', async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        const appleUserInfo = await (0, apple_auth_service_1.verifyAppleToken)(token);
        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
        let user = await userRepository.findOne({ where: { appleId: appleUserInfo.appleId } });
        if (!user && appleUserInfo.email) {
            user = await userRepository.findOne({ where: { email: appleUserInfo.email } });
            if (user) {
                user.appleId = appleUserInfo.appleId;
                await userRepository.save(user);
            }
            else {
                user = userRepository.create({
                    username: appleUserInfo.username,
                    email: appleUserInfo.email,
                    appleId: appleUserInfo.appleId,
                    password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
                });
                if (user && typeof user.hashPassword === 'function') {
                    user.hashPassword();
                }
                await userRepository.save(user);
            }
        }
        if (!user) {
            return res.status(500).json({ message: 'Failed to create or find user' });
        }
        const jwtToken = jsonwebtoken_1.default.sign({ userId: user.id, email: user.email }, process.env.JWT_SECRET || 'your_jwt_secret_key', { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        const userResponse = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
        res.json({ user: userResponse, token: jwtToken });
        return;
    }
    catch (error) {
        console.error('Apple login error:', error);
        res.status(500).json({ message: 'Error during Apple login', error: error.message });
        return;
    }
});
router.post('/facebook', async (req, res) => {
    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        const facebookUserInfo = await (0, facebook_auth_service_1.verifyFacebookToken)(token);
        const userRepository = (0, typeorm_1.getRepository)(User_1.User);
        let user = await userRepository.findOne({ where: { facebookId: facebookUserInfo.facebookId } });
        if (!user && facebookUserInfo.email) {
            user = await userRepository.findOne({ where: { email: facebookUserInfo.email } });
            if (user) {
                user.facebookId = facebookUserInfo.facebookId;
                await userRepository.save(user);
            }
            else {
                user = userRepository.create({
                    username: facebookUserInfo.username,
                    email: facebookUserInfo.email,
                    facebookId: facebookUserInfo.facebookId,
                    password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)
                });
                if (user && typeof user.hashPassword === 'function') {
                    user.hashPassword();
                }
                await userRepository.save(user);
            }
        }
        if (!user) {
            return res.status(500).json({ message: 'Failed to create or find user' });
        }
        const jwtToken = jsonwebtoken_1.default.sign({ userId: user.id, email: user.email }, process.env.JWT_SECRET || 'your_jwt_secret_key', { expiresIn: process.env.JWT_EXPIRES_IN || '24h' });
        const userResponse = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
        res.json({ user: userResponse, token: jwtToken });
        return;
    }
    catch (error) {
        console.error('Facebook login error:', error);
        res.status(500).json({ message: 'Error during Facebook login', error: error.message });
        return;
    }
});
exports.default = router;
//# sourceMappingURL=auth.routes.js.map