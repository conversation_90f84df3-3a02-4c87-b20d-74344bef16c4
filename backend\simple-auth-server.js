const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const sqlite3 = require('sqlite3').verbose();

// 创建Express应用
const app = express();

// 中间件
app.use(cors({
  origin: '*', // 允许所有来源
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'x-request-id',
    'X-Requested-With',
    'x-csrf-token'
  ]
}));
app.use(express.json());

// 创建内存数据库
const db = new sqlite3.Database(':memory:', (err) => {
  if (err) {
    console.error('创建内存数据库失败:', err.message);
    process.exit(1);
  }
  console.log('成功创建内存数据库');

  // 创建用户表
  db.run(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      role TEXT NOT NULL,
      createdAt TEXT NOT NULL,
      updatedAt TEXT NOT NULL
    )
  `, function(err) {
    if (err) {
      console.error('创建用户表失败:', err.message);
      return;
    }
    console.log('用户表创建成功');

    // 插入管理员用户
    const adminId = '703732e7-5236-4b5d-a46f-40bc6afa801e';
    const adminUsername = 'admin';
    const adminEmail = '<EMAIL>';
    const adminPassword = bcrypt.hashSync('admin123', 10);
    const adminRole = 'admin';
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19);

    db.run(`
      INSERT OR REPLACE INTO users (id, username, email, password, role, createdAt, updatedAt)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [adminId, adminUsername, adminEmail, adminPassword, adminRole, now, now], function(err) {
      if (err) {
        console.error('插入管理员用户失败:', err.message);
        return;
      }
      console.log('管理员用户创建成功');
    });
  });
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: '服务器正常运行' });
});

// 登录端点
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    console.log('登录请求:', { email, password });

    // 查询用户
    db.get("SELECT * FROM users WHERE email = ?", [email], async (err, user) => {
      if (err) {
        console.error('查询用户失败:', err.message);
        return res.status(500).json({ message: '服务器错误' });
      }

      if (!user) {
        console.log('用户不存在:', email);
        return res.status(401).json({ message: '邮箱或密码错误' });
      }

      console.log('找到用户:', user);

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        console.log('密码不匹配');
        return res.status(401).json({ message: '邮箱或密码错误' });
      }

      console.log('密码验证成功');

      // 生成JWT令牌
      const token = jwt.sign(
        { userId: user.id, email: user.email },
        'your_jwt_secret_key',
        { expiresIn: '24h' }
      );

      // 创建用户响应对象（不包含密码）
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      };

      console.log('登录成功，返回用户信息:', userResponse);
      res.json({ user: userResponse, token });
    });
  } catch (error) {
    console.error('登录处理错误:', error);
    res.status(500).json({ message: '服务器错误', error: error.message });
  }
});

// 添加一个测试路由
app.get('/api/test', (req, res) => {
  console.log('收到测试请求');
  res.json({ message: '测试成功' });
});

// 启动服务器
const PORT = 5002;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`简单认证服务器运行在端口 ${PORT}`);
  console.log(`访问 http://localhost:${PORT}/api/health 测试服务器是否正常运行`);
  console.log(`登录URL: http://localhost:${PORT}/api/auth/login`);
  console.log('服务器监听所有网络接口');
});
