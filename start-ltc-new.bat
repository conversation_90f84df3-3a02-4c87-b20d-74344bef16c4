@echo off
chcp 65001 >nul
echo ===================================
echo    LTC项目管理系统启动脚本
echo ===================================
echo.

:: 检查Node.js和npm
echo 检查Node.js和npm版本...
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到npm，请先安装npm
    pause
    exit /b 1
)

echo Node.js版本:
node --version
echo npm版本:
npm --version
echo.

:: 停止现有的Node.js进程
echo 正在停止现有的Node.js进程...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

:: 检查端口占用情况
echo 检查端口占用情况...
netstat -ano | findstr :3000 >nul
if not errorlevel 1 (
    echo 警告: 端口3000仍被占用，尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

netstat -ano | findstr :5002 >nul
if not errorlevel 1 (
    echo 警告: 端口5002仍被占用，尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5002') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

echo.
echo ===================================
echo    开始启动服务
echo ===================================

:: 启动后端服务
echo 1. 启动后端服务 (端口 5002)...
start "LTC Backend" cmd /c "cd /d %~dp0backend && npm run dev"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 5 >nul

:: 检查后端是否启动成功
:check_backend
netstat -ano | findstr :5002 >nul
if errorlevel 1 (
    echo 等待后端服务启动中...
    timeout /t 2 >nul
    goto check_backend
)

echo ✓ 后端服务启动成功!

:: 启动前端服务
echo.
echo 2. 启动前端服务 (端口 3000)...
start "LTC Frontend" cmd /c "cd /d %~dp0frontend && npm start"

:: 等待前端启动
echo 等待前端服务启动...
timeout /t 10 >nul

:: 检查前端是否启动成功
:check_frontend
netstat -ano | findstr :3000 >nul
if errorlevel 1 (
    echo 等待前端服务启动中...
    timeout /t 3 >nul
    goto check_frontend
)

echo ✓ 前端服务启动成功!

echo.
echo ===================================
echo    启动完成!
echo ===================================
echo.
echo 服务状态:
echo ✓ 后端服务: http://localhost:5002
echo ✓ 前端服务: http://localhost:3000
echo.
echo 登录信息:
echo   邮箱: <EMAIL>
echo   密码: admin123
echo.
echo 正在打开浏览器...

:: 打开浏览器
timeout /t 2 >nul
start http://localhost:3000

echo.
echo 项目已启动完成！
echo 要停止服务，请运行 stop-ltc-new.bat
echo.
pause 