import React from 'react';
import { CheckOutlined } from '@ant-design/icons';
import { ProjectStage } from '../../types';

interface FinanceStepIndicatorProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  financeStages: { key: string; title: string }[];
}

const FinanceStepIndicator: React.FC<FinanceStepIndicatorProps> = ({
  currentStep,
  setCurrentStep,
  financeStages
}) => {
  return (
    <div className="ltc-steps finance-steps">
      {financeStages.map((step, index) => (
        <div
          key={index}
          className={`ltc-step ${currentStep === index ? 'active' : ''} ${index < currentStep ? 'completed' : ''}`}
          onClick={() => setCurrentStep(index)}
        >
          <div className="ltc-step-circle finance-step-circle">
            {index < currentStep ? (
              <CheckOutlined style={{ fontSize: '16px' }} />
            ) : (
              index + 1
            )}
          </div>
          <div className="ltc-step-label">{step.title}</div>
          {index < financeStages.length - 1 && (
            <div className="ltc-step-connector finance-step-connector"></div>
          )}
        </div>
      ))}
    </div>
  );
};

export default FinanceStepIndicator;
