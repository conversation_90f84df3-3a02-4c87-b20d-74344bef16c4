import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Card, 
  Tag, 
  message,
  Popconfirm,
  Tooltip,
  Drawer,
  Checkbox,
  DatePicker,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  DownloadOutlined,
  MenuOutlined,
  MailOutlined,
  PhoneOutlined,
  BankOutlined,
  ShopOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import vendorService from '../services/vendor.service';
import { Vendor } from '../types';
import '../styles/table-row-spacing.css';
import { enableTableColumnResize } from '../utils/tableResizer';
import type { Dayjs } from 'dayjs';

const { Option } = Select;

const VendorManagement: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [countryFilter, setCountryFilter] = useState('all');
  const [creditRatingFilter, setCreditRatingFilter] = useState('all');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [columnSettingsVisible, setColumnSettingsVisible] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<{[key: string]: boolean}>({
    vendorId: true,
    name: true,
    category: true,
    industry: true,
    country: true,
    contactPerson: true,
    contact: true,
    paymentTerms: true,
    creditRating: true,
    status: true,
    totalSpend: true,
    lastOrderDate: true,
    actions: true
  });
  const [stats, setStats] = useState({
    totalVendors: 0,
    activeVendors: 0,
    totalSpend: 0,
    averageSpend: 0
  });

  // 获取供应商列表
  const fetchVendors = async () => {
    try {
      setLoading(true);
      const data = await vendorService.getAllVendors();
      setVendors(data);
      
      // 获取统计数据
      const statsData = await vendorService.getVendorStats();
      setStats(statsData);
    } catch (error) {
      console.error('Failed to fetch vendors:', error);
      message.error('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVendors();
  }, []);

  // 初始化表格列宽调整功能
  useEffect(() => {
    const timer = setTimeout(() => {
      enableTableColumnResize('.vendor-list-container .ant-table');
    }, 200);
    
    return () => clearTimeout(timer);
  }, [vendors]);

  // 过滤数据
  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = !searchText || 
      vendor.name.toLowerCase().includes(searchText.toLowerCase()) ||
      vendor.vendorId.toLowerCase().includes(searchText.toLowerCase()) ||
      vendor.contactPerson.toLowerCase().includes(searchText.toLowerCase());

    const matchesStatus = statusFilter === 'all' || vendor.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || vendor.category === categoryFilter;
    const matchesIndustry = industryFilter === 'all' || vendor.industry === industryFilter;
    const matchesCountry = countryFilter === 'all' || vendor.country === countryFilter;
    const matchesCreditRating = creditRatingFilter === 'all' || vendor.creditRating === creditRatingFilter;

    // 日期范围过滤
    let matchesDateRange = true;
    if (dateRange && dateRange[0] && dateRange[1]) {
      const vendorDate = new Date(vendor.lastOrderDate || vendor.createdAt);
      const startDate = dateRange[0].toDate();
      const endDate = dateRange[1].toDate();
      endDate.setHours(23, 59, 59, 999);
      matchesDateRange = vendorDate >= startDate && vendorDate <= endDate;
    }

    return matchesSearch && matchesStatus && matchesCategory && matchesIndustry && 
           matchesCountry && matchesCreditRating && matchesDateRange;
  });

  // 删除供应商
  const handleDelete = async (id: string) => {
    try {
      await vendorService.deleteVendor(id);
      message.success('Vendor deleted successfully');
      fetchVendors();
    } catch (error) {
      console.error('Failed to delete vendor:', error);
      message.error('Failed to delete vendor');
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'active': { color: 'green', text: 'Active' },
      'inactive': { color: 'red', text: 'Inactive' },
      'potential': { color: 'blue', text: 'Potential' },
      'blacklisted': { color: 'volcano', text: 'Blacklisted' },
      'suspended': { color: 'orange', text: 'Suspended' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color} style={{ fontSize: '12px' }}>{config.text}</Tag>;
  };

  // 渲染类别标签
  const renderCategoryTag = (category: string) => {
    const categoryColors = { 
      'product': 'purple', 
      'service': 'gold', 
      'material': 'blue', 
      'software': 'green',
      'consulting': 'magenta',
      'logistics': 'cyan',
      'other': 'default'
    };
    return <Tag color={categoryColors[category as keyof typeof categoryColors] || 'default'} style={{ fontSize: '12px' }}>
      {category.charAt(0).toUpperCase() + category.slice(1)}
    </Tag>;
  };

  // 渲染信用评级标签
  const renderCreditRatingTag = (rating: string) => {
    const ratingColors = {
      'AAA': 'green',
      'AA': 'green', 
      'A': 'blue',
      'BBB': 'orange',
      'BB': 'orange',
      'B': 'red',
      'CCC': 'red',
      'CC': 'volcano',
      'C': 'volcano',
      'D': 'magenta'
    };
    return <Tag color={ratingColors[rating as keyof typeof ratingColors] || 'default'} style={{ fontSize: '12px' }}>
      {rating}
    </Tag>;
  };

  // 渲染付款条件
  const renderPaymentTerms = (terms: string) => {
    const termLabels = {
      'net_15': 'Net 15',
      'net_30': 'Net 30',
      'net_45': 'Net 45',
      'net_60': 'Net 60',
      'net_90': 'Net 90',
      'prepaid': 'Prepaid',
      'cod': 'COD'
    };
    return <span style={{ fontSize: '12px' }}>
      {termLabels[terms as keyof typeof termLabels] || terms}
    </span>;
  };

  // 导出数据功能
  const handleExport = () => {
    const csvContent = [
      // 表头
      ['Vendor ID', 'Name', 'Category', 'Industry', 'Country', 'Contact Person', 'Email', 'Phone', 'Payment Terms', 'Credit Rating', 'Status', 'Total Spend', 'Last Order Date'].join(','),
      // 数据行
      ...filteredVendors.map(vendor => [
        vendor.vendorId,
        `"${vendor.name.replace(/"/g, '""')}"`,
        vendor.category,
        `"${vendor.industry}"`,
        `"${vendor.country}"`,
        `"${vendor.contactPerson.replace(/"/g, '""')}"`,
        vendor.email,
        vendor.phone || '-',
        vendor.paymentTerms,
        vendor.creditRating,
        vendor.status,
        `€${vendor.totalSpend.toLocaleString()}`,
        vendor.lastOrderDate ? new Date(vendor.lastOrderDate).toLocaleDateString() : '-'
      ].join(','))
    ].join('\n');

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `vendors_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success('Export successful');
  };

  // 获取唯一的行业列表
  const uniqueIndustries = Array.from(new Set(vendors.map(v => v.industry).filter(Boolean)));
  
  // 获取唯一的国家列表
  const uniqueCountries = Array.from(new Set(vendors.map(v => v.country).filter(Boolean)));

  // 表格列定义
  const allColumns = [
    {
      title: 'Vendor ID',
      dataIndex: 'vendorId',
      key: 'vendorId',
      width: 130,
      sorter: (a: Vendor, b: Vendor) => a.vendorId.localeCompare(b.vendorId),
      render: (vendorId: string) => (
        <span style={{ fontSize: '12px', fontWeight: 500 }}>{vendorId}</span>
      ),
    },
    {
      title: 'Vendor Name',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      sorter: (a: Vendor, b: Vendor) => a.name.localeCompare(b.name),
      render: (name: string, record: Vendor) => (
        <div style={{ fontSize: '12px' }}>
          <div style={{ fontWeight: 500, marginBottom: '2px' }}>{name}</div>
          {record.website && (
            <div style={{ color: '#666', fontSize: '11px' }}>
              <GlobalOutlined style={{ marginRight: '4px' }} />
              <a 
                href={record.website.startsWith('http') ? record.website : `https://${record.website}`}
                target="_blank"
                rel="noopener noreferrer"
                style={{ 
                  color: '#1890ff', 
                  textDecoration: 'none',
                  fontSize: '11px'
                }}
                onMouseEnter={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'underline'}
                onMouseLeave={(e) => (e.target as HTMLAnchorElement).style.textDecoration = 'none'}
                onClick={(e) => e.stopPropagation()}
              >
                {record.website.replace(/^https?:\/\//, '')}
              </a>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      sorter: (a: Vendor, b: Vendor) => a.category.localeCompare(b.category),
      render: renderCategoryTag
    },
    {
      title: 'Industry',
      dataIndex: 'industry',
      key: 'industry',
      width: 140,
      sorter: (a: Vendor, b: Vendor) => a.industry.localeCompare(b.industry),
      render: (industry: string) => (
        <span style={{ fontSize: '12px' }}>{industry}</span>
      ),
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      width: 120,
      sorter: (a: Vendor, b: Vendor) => a.country.localeCompare(b.country),
      render: (country: string) => (
        <span style={{ fontSize: '12px' }}>{country}</span>
      ),
    },
    {
      title: 'Contact Person',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      width: 140,
      sorter: (a: Vendor, b: Vendor) => a.contactPerson.localeCompare(b.contactPerson),
      render: (contactPerson: string) => (
        <span style={{ fontSize: '12px' }}>{contactPerson}</span>
      ),
    },
    {
      title: 'Contact Info',
      key: 'contact',
      width: 200,
      render: (_: any, record: Vendor) => (
        <div style={{ fontSize: '12px' }}>
          <div style={{ marginBottom: '2px' }}>
            <MailOutlined style={{ color: '#1890ff', marginRight: '4px' }} />
            {record.email}
          </div>
          {record.phone && (
            <div>
              <PhoneOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
              {record.phone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Payment Terms',
      dataIndex: 'paymentTerms',
      key: 'paymentTerms',
      width: 120,
      sorter: (a: Vendor, b: Vendor) => a.paymentTerms.localeCompare(b.paymentTerms),
      render: renderPaymentTerms
    },
    {
      title: 'Credit Rating',
      dataIndex: 'creditRating',
      key: 'creditRating',
      width: 110,
      sorter: (a: Vendor, b: Vendor) => a.creditRating.localeCompare(b.creditRating),
      render: renderCreditRatingTag
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      sorter: (a: Vendor, b: Vendor) => a.status.localeCompare(b.status),
      render: renderStatusTag
    },
    {
      title: 'Total Spend',
      dataIndex: 'totalSpend',
      key: 'totalSpend',
      width: 120,
      sorter: (a: Vendor, b: Vendor) => a.totalSpend - b.totalSpend,
      render: (totalSpend: number) => (
        <span style={{ fontSize: '12px', fontWeight: 500, color: totalSpend > 0 ? '#cf1322' : '#666' }}>
          €{totalSpend.toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Last Order Date',
      dataIndex: 'lastOrderDate',
      key: 'lastOrderDate',
      width: 130,
      sorter: (a: Vendor, b: Vendor) => {
        const dateA = a.lastOrderDate ? new Date(a.lastOrderDate).getTime() : 0;
        const dateB = b.lastOrderDate ? new Date(b.lastOrderDate).getTime() : 0;
        return dateA - dateB;
      },
      render: (date: string) => (
        <span style={{ fontSize: '12px' }}>
          {date ? new Date(date).toLocaleDateString() : '-'}
        </span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: Vendor) => (
        <Space size="small">
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined style={{ fontSize: '12px' }} />}
              onClick={() => navigate(`/vendor/edit/${record.id}`)}
              size="small"
              style={{ fontSize: '12px' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Vendor"
            description="Are you sure you want to delete this vendor?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                danger
                size="small"
                style={{ fontSize: '12px' }}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 过滤显示的列
  const columns = allColumns.filter(column => visibleColumns[column.key as string]);

  // 列设置功能
  const handleColumnVisibilityChange = (columnKey: string, checked: boolean) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: checked
    }));
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和新建按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 style={{
          fontSize: '16px',
          fontWeight: 600,
          margin: 0,
          color: 'var(--text-primary)'
        }}>
          Vendor Management
        </h1>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/vendor/create')}
          size="large"
        >
          New Vendor
        </Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Vendors"
              value={stats.totalVendors}
              prefix={<ShopOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Vendors"
              value={stats.activeVendors}
              prefix={<ShopOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Spend"
              value={stats.totalSpend}
              prefix={<span style={{ fontSize: '0.7em' }}>€</span>}
              valueStyle={{ color: '#cf1322', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Average Spend"
              value={Math.round(stats.averageSpend)}
              prefix={<span style={{ fontSize: '0.7em' }}>€</span>}
              valueStyle={{ color: '#fa8c16', fontSize: '1.2em' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选区域 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space wrap size="middle">
          <Input
            placeholder="Search vendors..."
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />

          <DatePicker.RangePicker
            style={{ width: 250 }}
            value={dateRange}
            onChange={setDateRange}
            placeholder={['Start Date', 'End Date']}
            allowClear
          />
          
          <Select
            style={{ width: 140 }}
            value={statusFilter}
            onChange={setStatusFilter}
            placeholder="All Status"
          >
            <Option value="all">All Status</Option>
            <Option value="active">Active</Option>
            <Option value="inactive">Inactive</Option>
            <Option value="potential">Potential</Option>
            <Option value="blacklisted">Blacklisted</Option>
            <Option value="suspended">Suspended</Option>
          </Select>

          <Select
            style={{ width: 140 }}
            value={categoryFilter}
            onChange={setCategoryFilter}
            placeholder="All Categories"
          >
            <Option value="all">All Categories</Option>
            <Option value="product">Product</Option>
            <Option value="service">Service</Option>
            <Option value="material">Material</Option>
            <Option value="software">Software</Option>
            <Option value="consulting">Consulting</Option>
            <Option value="logistics">Logistics</Option>
            <Option value="other">Other</Option>
          </Select>

          <Select
            style={{ width: 140 }}
            value={industryFilter}
            onChange={setIndustryFilter}
            placeholder="All Industries"
          >
            <Option value="all">All Industries</Option>
            {uniqueIndustries.map(industry => (
              <Option key={industry} value={industry}>{industry}</Option>
            ))}
          </Select>

          <Select
            style={{ width: 140 }}
            value={countryFilter}
            onChange={setCountryFilter}
            placeholder="All Countries"
          >
            <Option value="all">All Countries</Option>
            {uniqueCountries.map(country => (
              <Option key={country} value={country}>{country}</Option>
            ))}
          </Select>

          <Select
            style={{ width: 140 }}
            value={creditRatingFilter}
            onChange={setCreditRatingFilter}
            placeholder="All Ratings"
          >
            <Option value="all">All Ratings</Option>
            <Option value="AAA">AAA</Option>
            <Option value="AA">AA</Option>
            <Option value="A">A</Option>
            <Option value="BBB">BBB</Option>
            <Option value="BB">BB</Option>
            <Option value="B">B</Option>
            <Option value="CCC">CCC</Option>
            <Option value="CC">CC</Option>
            <Option value="C">C</Option>
            <Option value="D">D</Option>
          </Select>

          <Button onClick={() => {
            setSearchText('');
            setStatusFilter('all');
            setCategoryFilter('all');
            setIndustryFilter('all');
            setCountryFilter('all');
            setCreditRatingFilter('all');
            setDateRange(null);
          }}>
            Clear Filters
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card 
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span style={{ fontSize: '16px', fontWeight: 600 }}>Vendors</span>
            
            {/* 功能快捷键 */}
            <Space size="small">
              <Tooltip title="Refresh">
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchVendors()}
                  loading={loading}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Export">
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  size="small"
                />
              </Tooltip>

              <Tooltip title="Column Settings">
                <Button
                  icon={<MenuOutlined />}
                  onClick={() => setColumnSettingsVisible(true)}
                  size="small"
                />
              </Tooltip>
            </Space>
          </div>
        }
      >
        <div className="vendor-list-container">
          <Table
            columns={columns}
            dataSource={filteredVendors}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `${range[0]}-${range[1]} of ${total} vendors`,
              defaultPageSize: 20,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
            scroll={{ x: 1600 }}
            size="middle"
            bordered
          />
        </div>
      </Card>

      {/* 列设置抽屉 */}
      <Drawer
        title="Column Settings"
        placement="right"
        onClose={() => setColumnSettingsVisible(false)}
        open={columnSettingsVisible}
        width={300}
      >
        <div style={{ marginBottom: '16px' }}>
          <strong>Select columns to display:</strong>
        </div>
        <Space direction="vertical" style={{ width: '100%' }}>
          {allColumns.map(column => (
            <Checkbox
              key={column.key}
              checked={visibleColumns[column.key as string]}
              onChange={(e) => handleColumnVisibilityChange(column.key as string, e.target.checked)}
            >
              {column.title}
            </Checkbox>
          ))}
        </Space>
      </Drawer>
    </div>
  );
};

export default VendorManagement; 