"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyFacebookTokenDetails = exports.verifyFacebookToken = void 0;
const axios_1 = __importDefault(require("axios"));
async function verifyFacebookToken(accessToken) {
    try {
        const response = await axios_1.default.get(`https://graph.facebook.com/me?fields=id,name,email&access_token=${accessToken}`);
        const userData = response.data;
        if (!userData || !userData.id) {
            throw new Error('Invalid Facebook token');
        }
        return {
            facebookId: userData.id,
            email: userData.email,
            username: userData.name || (userData.email ? userData.email.split('@')[0] : `fb_user_${userData.id}`),
            verified: true
        };
    }
    catch (error) {
        console.error('Error verifying Facebook token:', error);
        throw new Error('Invalid Facebook token');
    }
}
exports.verifyFacebookToken = verifyFacebookToken;
async function verifyFacebookTokenDetails(accessToken) {
    try {
        const appId = process.env.FACEBOOK_APP_ID;
        const appSecret = process.env.FACEBOOK_APP_SECRET;
        const response = await axios_1.default.get(`https://graph.facebook.com/debug_token?input_token=${accessToken}&access_token=${appId}|${appSecret}`);
        const data = response.data.data;
        if (!data.is_valid) {
            throw new Error('Invalid Facebook token');
        }
        if (data.app_id !== appId) {
            throw new Error('Token was not issued for this app');
        }
        return await verifyFacebookToken(accessToken);
    }
    catch (error) {
        console.error('Error verifying Facebook token details:', error);
        throw new Error('Invalid Facebook token');
    }
}
exports.verifyFacebookTokenDetails = verifyFacebookTokenDetails;
//# sourceMappingURL=facebook-auth.service.js.map