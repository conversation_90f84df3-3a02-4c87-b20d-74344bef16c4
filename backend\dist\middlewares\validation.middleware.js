"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateQuery = exports.validateParams = exports.validateBody = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
function validateBody(type, skipMissingProperties = false) {
    return async (req, res, next) => {
        try {
            const instance = (0, class_transformer_1.plainToClass)(type, req.body);
            const errors = await (0, class_validator_1.validate)(instance, {
                skipMissingProperties,
                whitelist: true,
                forbidNonWhitelisted: true
            });
            if (errors.length > 0) {
                const formattedErrors = errors.reduce((acc, error) => {
                    const property = error.property;
                    const constraints = error.constraints || {};
                    acc[property] = Object.values(constraints);
                    return acc;
                }, {});
                return res.status(400).json({
                    message: '数据验证失败',
                    errors: formattedErrors
                });
            }
            req.body = instance;
            next();
        }
        catch (error) {
            console.error('验证中间件错误:', error);
            return res.status(500).json({
                message: '验证过程中发生错误',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    };
}
exports.validateBody = validateBody;
function validateParams(type) {
    return async (req, res, next) => {
        try {
            const instance = (0, class_transformer_1.plainToClass)(type, req.params);
            const errors = await (0, class_validator_1.validate)(instance);
            if (errors.length > 0) {
                const formattedErrors = errors.reduce((acc, error) => {
                    const property = error.property;
                    const constraints = error.constraints || {};
                    acc[property] = Object.values(constraints);
                    return acc;
                }, {});
                return res.status(400).json({
                    message: '参数验证失败',
                    errors: formattedErrors
                });
            }
            next();
        }
        catch (error) {
            console.error('验证中间件错误:', error);
            return res.status(500).json({
                message: '验证过程中发生错误',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    };
}
exports.validateParams = validateParams;
function validateQuery(type) {
    return async (req, res, next) => {
        try {
            const instance = (0, class_transformer_1.plainToClass)(type, req.query);
            const errors = await (0, class_validator_1.validate)(instance, { skipMissingProperties: true });
            if (errors.length > 0) {
                const formattedErrors = errors.reduce((acc, error) => {
                    const property = error.property;
                    const constraints = error.constraints || {};
                    acc[property] = Object.values(constraints);
                    return acc;
                }, {});
                return res.status(400).json({
                    message: '查询参数验证失败',
                    errors: formattedErrors
                });
            }
            next();
        }
        catch (error) {
            console.error('验证中间件错误:', error);
            return res.status(500).json({
                message: '验证过程中发生错误',
                error: error instanceof Error ? error.message : '未知错误'
            });
        }
    };
}
exports.validateQuery = validateQuery;
//# sourceMappingURL=validation.middleware.js.map