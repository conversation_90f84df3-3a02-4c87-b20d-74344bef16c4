/* 增强型表单样式 */
.enhanced-form-container {
  margin-bottom: 24px;
}

/* 表单分组卡片 */
.form-section-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.form-section-card .form-section-header {
  background-color: #f8fafc;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section-card .form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2023;
  margin: 0;
}

.form-section-card .form-section-content {
  padding: 16px;
}

/* 必填字段标记 */
.required-field-label::before {
  content: '*';
  color: #ef4444;
  margin-right: 4px;
}

/* 输入框增强 */
.enhanced-input {
  position: relative;
}

.enhanced-input .ant-input,
.enhanced-input .ant-input-number,
.enhanced-input .ant-select-selector,
.enhanced-input .ant-picker {
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.enhanced-input .ant-input:hover,
.enhanced-input .ant-input-number:hover,
.enhanced-input .ant-select-selector:hover,
.enhanced-input .ant-picker:hover {
  border-color: #a0aec0;
}

.enhanced-input .ant-input:focus,
.enhanced-input .ant-input-number:focus,
.enhanced-input .ant-select-selector:focus,
.enhanced-input .ant-picker:focus,
.enhanced-input .ant-input-focused,
.enhanced-input .ant-input-number-focused,
.enhanced-input .ant-select-focused .ant-select-selector,
.enhanced-input .ant-picker-focused {
  border-color: #3f4287;
  box-shadow: 0 0 0 2px rgba(63, 66, 135, 0.2);
}

/* 输入框前缀/后缀 */
.input-prefix {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  z-index: 1;
}

.input-suffix {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  z-index: 1;
}

.input-with-prefix {
  padding-left: 28px !important;
}

.input-with-suffix {
  padding-right: 28px !important;
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.form-actions .ant-btn {
  min-width: 100px;
}

/* 表单字段说明 */
.field-help-text {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
}

/* 表单验证反馈 */
.ant-form-item-has-error .enhanced-input .ant-input,
.ant-form-item-has-error .enhanced-input .ant-input-number,
.ant-form-item-has-error .enhanced-input .ant-select-selector,
.ant-form-item-has-error .enhanced-input .ant-picker {
  border-color: #ef4444;
}

.ant-form-item-has-error .enhanced-input .ant-input:focus,
.ant-form-item-has-error .enhanced-input .ant-input-number:focus,
.ant-form-item-has-error .enhanced-input .ant-select-selector:focus,
.ant-form-item-has-error .enhanced-input .ant-picker:focus {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-section-card .form-section-content {
    padding: 12px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions .ant-btn {
    width: 100%;
  }
}
