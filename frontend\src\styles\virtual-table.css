/* 虚拟表格样式 - 优化表头显示 */
.virtual-table-container {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.virtual-table-header {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  position: sticky;
  top: 0;
  z-index: 10;
}

.virtual-table-row {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
  height: 48px;
  cursor: pointer;
}

.virtual-table-row:hover {
  background-color: #f5f5f5;
}

.virtual-table-row:last-child {
  border-bottom: none;
}

.virtual-table-cell {
  padding: 8px 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex-shrink: 0;
  font-size: 14px;
  line-height: 1.4;
  display: flex;
  align-items: center;
  border-right: 1px solid transparent;
  position: relative;
}

.virtual-table-cell:last-child {
  border-right: none;
}

.virtual-table-header .virtual-table-cell {
  padding: 12px 16px;
  font-weight: 600;
  background-color: #fafafa;
  color: rgba(0, 0, 0, 0.85);
  border-right: 1px solid #f0f0f0;
  text-align: left;
  font-size: 14px;
  line-height: 1.4;
}

.virtual-table-header .virtual-table-cell:last-child {
  border-right: none;
}

/* 设置固定列宽，防止文字变形 */
.virtual-table-cell[style*="width: 120"] { /* ID, Country, Stage, Create Date, Owner */
  min-width: 120px;
  max-width: 120px;
}

.virtual-table-cell[style*="width: 200"] { /* Name */
  min-width: 200px;
  max-width: 200px;
}

.virtual-table-cell[style*="width: 150"] { /* Client */
  min-width: 150px;
  max-width: 150px;
}

.virtual-table-cell[style*="width: 100"] { /* Tier, Probability, Actions */
  min-width: 100px;
  max-width: 100px;
}

.virtual-table-cell[style*="width: 80"] { /* Level */
  min-width: 80px;
  max-width: 80px;
}

/* 文本对齐 */
.virtual-table-cell[style*="text-align: right"] {
  justify-content: flex-end;
  text-align: right;
}

.virtual-table-cell[style*="text-align: center"] {
  justify-content: center;
  text-align: center;
}

/* 表头特殊样式 */
.virtual-table-header .virtual-table-row {
  background-color: #fafafa;
  border-bottom: 2px solid #f0f0f0;
  height: 44px;
}

.virtual-table-header .virtual-table-row:hover {
  background-color: #fafafa;
}

/* 分页信息样式 */
.pagination-info {
  padding: 12px 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

/* 虚拟列表样式优化 */
.virtual-list {
  position: relative;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  will-change: transform;
  background-color: #fff;
}

.virtual-list-inner {
  position: relative;
}

.virtual-list-item {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
}

.virtual-list-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}

.virtual-list-loading p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.virtual-list-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.virtual-list-empty p {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.45);
}

/* 链接样式 */
.virtual-table-cell a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.virtual-table-cell a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 按钮样式 */
.virtual-table-cell .ant-btn {
  height: 28px;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.2;
  border-radius: 4px;
  margin: 0 2px;
}

.virtual-table-cell .ant-btn-text {
  padding: 0 4px;
}

/* 性能优化面板样式 */
.performance-panel {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-top: 16px;
}

.performance-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.performance-metric-label {
  color: rgba(0, 0, 0, 0.65);
  font-weight: 500;
}

.performance-metric-value {
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.performance-timestamp {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 8px;
  text-align: right;
  font-style: italic;
}

/* 处理状态样式 */
.processing-status {
  margin-top: 16px;
  padding: 16px;
  background-color: #f0f8ff;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
}

.processing-status-info {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 1.4;
}

/* 滚动条样式 */
.virtual-table-container::-webkit-scrollbar,
.virtual-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.virtual-table-container::-webkit-scrollbar-track,
.virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-table-container::-webkit-scrollbar-thumb,
.virtual-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.virtual-table-container::-webkit-scrollbar-thumb:hover,
.virtual-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .virtual-table-cell {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .virtual-table-header .virtual-table-cell {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .virtual-table-row {
    height: 44px;
  }
  
  .virtual-table-header .virtual-table-row {
    height: 40px;
  }
}

@media (max-width: 1200px) {
  .virtual-table-cell {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .virtual-table-header .virtual-table-cell {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .virtual-table-row {
    height: 40px;
  }
  
  .virtual-table-header .virtual-table-row {
    height: 36px;
  }
}

@media (max-width: 768px) {
  .virtual-table-cell {
    padding: 4px 6px;
    font-size: 10px;
  }
  
  .virtual-table-header .virtual-table-cell {
    padding: 6px;
    font-size: 10px;
  }
  
  .virtual-table-row {
    height: 36px;
  }
  
  .virtual-table-header .virtual-table-row {
    height: 32px;
  }
}
