import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Button, message, Typography, Breadcrumb } from 'antd';
import { HomeOutlined, FolderOutlined } from '@ant-design/icons';
import { Project } from '../types';
import projectService from '../services/new-project.service';

const { Title, Text } = Typography;

const ProjectSpace: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchProject = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const projectData = await projectService.getProjectById(id);
        setProject(projectData);
      } catch (error) {
        console.error('Failed to fetch project:', error);
        message.error('加载项目信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [id]);

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <div>正在加载项目空间...</div>
      </div>
    );
  }

  if (!project) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <div>项目未找到</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Breadcrumb style={{ marginBottom: '24px' }}>
        <Breadcrumb.Item>
          <Button 
            type="link" 
            icon={<HomeOutlined />} 
            onClick={() => navigate('/opportunity')}
            style={{ padding: 0 }}
          >
            机会列表
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Button 
            type="link"
            onClick={() => navigate(`/opportunity/${id}`)}
            style={{ padding: 0 }}
          >
            {project.name}
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>项目空间</Breadcrumb.Item>
      </Breadcrumb>

      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
          <FolderOutlined style={{ color: '#FF7A00' }} />
          项目空间 - {project.name}
        </Title>
        <Text type="secondary" style={{ fontSize: '16px' }}>
          项目文件协作空间
        </Text>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <FolderOutlined style={{ fontSize: 64, color: '#FF7A00', marginBottom: 16 }} />
          <Title level={3}>项目空间功能开发中</Title>
          <Text type="secondary">
            项目空间功能正在开发中，敬请期待！
          </Text>
          <div style={{ marginTop: 24 }}>
            <Button 
              type="primary" 
              onClick={() => navigate(`/opportunity/${id}`)}
            >
              返回项目详情
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProjectSpace; 