import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import jwt from 'jsonwebtoken';
import { getRepository } from 'typeorm';
import { User } from '../entities/User';
import { ProjectCollaborator } from '../entities/ProjectCollaborator';

interface AuthenticatedSocket {
  id: string;
  userId: string;
  user: User;
  join: (room: string) => void;
  leave: (room: string) => void;
  emit: (event: string, data: any) => void;
  to: (room: string) => any;
  broadcast: any;
}

interface OnlineUser {
  userId: string;
  username: string;
  avatar?: string;
  lastSeen: Date;
}

interface ProjectRoom {
  projectId: string;
  onlineUsers: Map<string, OnlineUser>;
  activities: any[];
}

export class WebSocketService {
  private io: SocketIOServer;
  private userRepository = getRepository(User);
  private collaboratorRepository = getRepository(ProjectCollaborator);
  private projectRooms = new Map<string, ProjectRoom>();

  constructor(server: HttpServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // 认证中间件
    this.io.use(async (socket: any, next) => {
      try {
        const token = socket.handshake.auth.token;
        if (!token) {
          return next(new Error('Authentication error'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_secret_key') as any;
        const user = await this.userRepository.findOne(decoded.userId);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`用户 ${socket.user.username} 已连接`);

      // 加入项目房间
      socket.on('join-project', async (projectId: string) => {
        try {
          // 验证用户是否有项目访问权限
          const hasAccess = await this.checkProjectAccess(socket.userId, projectId);
          if (!hasAccess) {
            socket.emit('error', { message: '没有项目访问权限' });
            return;
          }

          // 加入房间
          socket.join(`project:${projectId}`);
          
          // 更新在线用户列表
          this.updateOnlineUsers(projectId, socket.user, 'join');
          
          // 通知其他用户
          socket.to(`project:${projectId}`).emit('user-joined', {
            userId: socket.userId,
            username: socket.user.username,
            avatar: socket.user.avatar
          });

          // 发送当前在线用户列表
          const onlineUsers = this.getOnlineUsers(projectId);
          socket.emit('online-users', onlineUsers);

          console.log(`用户 ${socket.user.username} 加入项目 ${projectId}`);
        } catch (error) {
          socket.emit('error', { message: '加入项目失败' });
        }
      });

      // 离开项目房间
      socket.on('leave-project', (projectId: string) => {
        socket.leave(`project:${projectId}`);
        this.updateOnlineUsers(projectId, socket.user, 'leave');
        
        socket.to(`project:${projectId}`).emit('user-left', {
          userId: socket.userId,
          username: socket.user.username
        });

        console.log(`用户 ${socket.user.username} 离开项目 ${projectId}`);
      });

      // 实时活动广播
      socket.on('project-activity', (data: {
        projectId: string;
        type: string;
        title: string;
        description?: string;
        metadata?: any;
      }) => {
        // 广播给项目内所有用户
        socket.to(`project:${data.projectId}`).emit('activity-update', {
          ...data,
          user: {
            id: socket.userId,
            username: socket.user.username,
            avatar: socket.user.avatar
          },
          timestamp: new Date()
        });

        // 存储到项目房间活动记录
        this.addProjectActivity(data.projectId, {
          ...data,
          userId: socket.userId,
          user: socket.user,
          timestamp: new Date()
        });
      });

      // 实时编辑状态
      socket.on('editing-status', (data: {
        projectId: string;
        section: string; // 'basic_info', 'stages', 'documents' etc.
        isEditing: boolean;
      }) => {
        socket.to(`project:${data.projectId}`).emit('user-editing', {
          userId: socket.userId,
          username: socket.user.username,
          section: data.section,
          isEditing: data.isEditing
        });
      });

      // 实时评论
      socket.on('new-comment', (data: {
        projectId: string;
        stageId?: string;
        content: string;
        mentions?: string[]; // 提及的用户ID
      }) => {
        const comment = {
          id: Date.now().toString(),
          content: data.content,
          author: {
            id: socket.userId,
            username: socket.user.username,
            avatar: socket.user.avatar
          },
          timestamp: new Date(),
          stageId: data.stageId
        };

        // 广播评论
        this.io.to(`project:${data.projectId}`).emit('comment-added', comment);

        // 发送提及通知
        if (data.mentions?.length) {
          this.sendMentionNotifications(data.projectId, data.mentions, comment);
        }
      });

      // 权限变更通知
      socket.on('permission-updated', (data: {
        projectId: string;
        targetUserId: string;
        newRole: string;
        permissions: any;
      }) => {
        // 通知被更改权限的用户
        this.io.to(`user:${data.targetUserId}`).emit('permission-changed', {
          projectId: data.projectId,
          newRole: data.newRole,
          permissions: data.permissions,
          updatedBy: socket.user.username
        });

        // 通知项目内其他用户
        socket.to(`project:${data.projectId}`).emit('collaborator-updated', data);
      });

      // 断线处理
      socket.on('disconnect', () => {
        // 从所有项目房间移除用户
        this.handleUserDisconnect(socket.userId, socket.user);
        console.log(`用户 ${socket.user.username} 已断开连接`);
      });
    });
  }

  // 检查项目访问权限
  private async checkProjectAccess(userId: string, projectId: string): Promise<boolean> {
    const collaborator = await this.collaboratorRepository.findOne({
      where: { 
        project: { id: projectId }, 
        user: { id: userId },
        status: 'accepted'
      }
    });
    return !!collaborator;
  }

  // 更新在线用户列表
  private updateOnlineUsers(projectId: string, user: User, action: 'join' | 'leave') {
    if (!this.projectRooms.has(projectId)) {
      this.projectRooms.set(projectId, {
        projectId,
        onlineUsers: new Map(),
        activities: []
      });
    }

    const room = this.projectRooms.get(projectId)!;
    
    if (action === 'join') {
      room.onlineUsers.set(user.id, {
        userId: user.id,
        username: user.username,
        avatar: user.avatar,
        lastSeen: new Date()
      });
    } else {
      room.onlineUsers.delete(user.id);
    }

    // 广播在线用户更新
    this.io.to(`project:${projectId}`).emit('online-users-updated', 
      Array.from(room.onlineUsers.values())
    );
  }

  // 获取在线用户列表
  private getOnlineUsers(projectId: string): OnlineUser[] {
    const room = this.projectRooms.get(projectId);
    return room ? Array.from(room.onlineUsers.values()) : [];
  }

  // 添加项目活动
  private addProjectActivity(projectId: string, activity: any) {
    if (!this.projectRooms.has(projectId)) {
      this.projectRooms.set(projectId, {
        projectId,
        onlineUsers: new Map(),
        activities: []
      });
    }

    const room = this.projectRooms.get(projectId)!;
    room.activities.unshift(activity);
    
    // 只保留最近100条活动
    if (room.activities.length > 100) {
      room.activities = room.activities.slice(0, 100);
    }
  }

  // 发送提及通知
  private sendMentionNotifications(projectId: string, mentionedUserIds: string[], comment: any) {
    mentionedUserIds.forEach(userId => {
      this.io.to(`user:${userId}`).emit('mentioned', {
        projectId,
        comment,
        type: 'mention'
      });
    });
  }

  // 处理用户断开连接
  private handleUserDisconnect(userId: string, user: User) {
    // 从所有项目房间移除用户
    for (const [projectId, room] of this.projectRooms) {
      if (room.onlineUsers.has(userId)) {
        room.onlineUsers.delete(userId);
        this.io.to(`project:${projectId}`).emit('user-left', {
          userId,
          username: user.username
        });
        this.io.to(`project:${projectId}`).emit('online-users-updated', 
          Array.from(room.onlineUsers.values())
        );
      }
    }
  }

  // 外部方法：广播项目活动
  public broadcastProjectActivity(projectId: string, activity: any) {
    this.io.to(`project:${projectId}`).emit('activity-update', activity);
    this.addProjectActivity(projectId, activity);
  }

  // 外部方法：发送通知
  public sendNotification(userId: string, notification: any) {
    this.io.to(`user:${userId}`).emit('notification', notification);
  }

  // 外部方法：广播权限变更
  public broadcastPermissionChange(projectId: string, data: any) {
    this.io.to(`project:${projectId}`).emit('permission-changed', data);
  }
} 