/* 专门修复表格头部文字变形问题 */

/* 全局表格头部修复 */
.ant-table-thead > tr > th {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON><PERSON>, sans-serif !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  letter-spacing: 0.02em !important;
  word-spacing: normal !important;
  text-transform: none !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  vertical-align: middle !important;
  padding: 12px 16px !important;
  background-color: #fafafa !important;
  border-bottom: 1px solid #f0f0f0 !important;
  color: rgba(0, 0, 0, 0.85) !important;
  text-align: left !important;
  box-sizing: border-box !important;
}

/* 修复sticky表头问题 */
.ant-table-thead > tr {
  position: relative !important;
  z-index: 3 !important;
  margin: 0 !important;
}

.ant-table-header {
  position: relative !important;
  z-index: 3 !important;
  background-color: #fff !important;
  margin: 0 !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
}

/* 确保表头在数据行之上 */
.ant-table-sticky-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 4 !important;
  background-color: #fafafa !important;
  margin: 0 !important;
}

/* 强制消除表头和表体之间的间隙 */
.ant-table-header-column {
  margin: 0 !important;
  padding: 0 !important;
}

.ant-table-header table {
  margin: 0 !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

.ant-table-body table {
  margin: 0 !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

/* 确保表体在表头之下并消除间隙 */
.ant-table-tbody {
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ant-table-tbody > tr {
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
}

.ant-table-tbody > tr > td {
  margin: 0 !important;
  border-top: none !important;
}

/* 消除表头和表体之间的间隙 */
.ant-table-thead {
  margin: 0 !important;
  padding: 0 !important;
}

.ant-table-thead + .ant-table-tbody {
  margin-top: 0 !important;
  border-top: none !important;
}

/* 强制消除任何可能的间隙 */
.ant-table-header + .ant-table-body {
  margin-top: 0 !important;
  border-top: none !important;
}

.ant-table-body {
  margin-top: 0 !important;
  padding-top: 0 !important;
  border-top: none !important;
}

/* 确保表格整体没有多余的间距 */
.ant-table {
  margin: 0 !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

.ant-table-content {
  margin: 0 !important;
  padding: 0 !important;
}

.ant-table table {
  margin: 0 !important;
  padding: 0 !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

/* 项目列表容器特殊处理 */
.project-list-container .ant-table-wrapper {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-container {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-header {
  margin: 0 !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  border-bottom: none !important;
}

.project-list-container .ant-table-body {
  margin: 0 !important;
  padding: 0 !important;
  margin-top: 0 !important;
  border-top: none !important;
  padding-top: 0 !important;
}

.project-list-container .ant-table-header::after,
.project-list-container .ant-table-body::before {
  display: none !important;
}

/* 确保表头文字不被压缩 */
.ant-table-thead > tr > th .ant-table-header-column {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  width: 100% !important;
  min-height: 20px !important;
  position: relative !important;
  z-index: 5 !important;
  margin: 0 !important;
}

.ant-table-thead > tr > th .ant-table-column-title {
  flex: 1 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  color: rgba(0, 0, 0, 0.85) !important;
  position: relative !important;
  z-index: 5 !important;
  margin: 0 !important;
}

/* 修复排序图标 */
.ant-table-thead > tr > th .ant-table-column-sorter {
  margin-left: 4px !important;
  flex-shrink: 0 !important;
  position: relative !important;
  z-index: 5 !important;
}

/* 修复表格容器层级 */
.ant-table-container {
  position: relative !important;
  z-index: 1 !important;
  overflow: visible !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ant-table {
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
}

/* 项目列表特定的表头修复 */
.project-list-container {
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 消除项目列表表格中的间隙 */
.project-list-container .ant-table-header {
  margin-bottom: 0 !important;
}

.project-list-container .ant-table-body {
  margin-top: 0 !important;
  border-top: none !important;
}

.project-list-container .ant-table-thead > tr > th {
  border-bottom: 1px solid #f0f0f0 !important;
  margin-bottom: 0 !important;
}

.project-list-container .ant-table-tbody > tr:first-child > td {
  border-top: none !important;
  margin-top: 0 !important;
}

/* 强制设置第一行无边距 */
.project-list-container .ant-table-tbody > tr:first-child {
  margin-top: 0 !important;
  border-top: none !important;
}

.project-list-container .ant-table-tbody > tr:first-child::before {
  display: none !important;
}

/* 消除可能的伪元素间隙 */
.project-list-container .ant-table-thead::after,
.project-list-container .ant-table-tbody::before {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(1) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(2) {
  width: 200px !important;
  min-width: 200px !important;
  max-width: 200px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(3) {
  width: 150px !important;
  min-width: 150px !important;
  max-width: 150px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(4) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(5) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(6) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(7) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  text-align: right !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(8) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(9) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  text-align: right !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(10) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: center !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(11) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(12) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
}

.project-list-container .ant-table-thead > tr > th:nth-child(13) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  text-align: center !important;
}

/* 确保表格容器有足够的宽度 */
.project-list-container .ant-table-container {
  min-width: 1400px !important;
  overflow-x: auto !important;
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table {
  min-width: 1400px !important;
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
}

/* 修复表体单元格对应的宽度 */
.project-list-container .ant-table-tbody > tr > td:nth-child(1) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(2) {
  width: 200px !important;
  min-width: 200px !important;
  max-width: 200px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(3) {
  width: 150px !important;
  min-width: 150px !important;
  max-width: 150px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(4) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(5) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(6) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(7) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  text-align: right !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(8) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(9) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  text-align: right !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(10) {
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
  text-align: center !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(11) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(12) {
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr > td:nth-child(13) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  text-align: center !important;
  margin: 0 !important;
}

/* 防止元素重叠 */
.project-list-container .ant-card {
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
}

.project-list-container .ant-card-body {
  position: relative !important;
  z-index: 1 !important;
  overflow: visible !important;
  margin: 0 !important;
}

/* 浏览器兼容性修复 */
@supports (-webkit-text-stroke: 1px) {
  .ant-table-thead > tr > th .ant-table-column-title {
    -webkit-text-stroke: 0.1px transparent !important;
  }
}

@supports (font-feature-settings: "kern") {
  .ant-table-thead > tr > th .ant-table-column-title {
    font-feature-settings: "kern" 1 !important;
  }
}

/* 防止字体渲染问题 */
.ant-table-thead > tr > th .ant-table-column-title {
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;
}

/* 响应式调整 */
@media (max-width: 1600px) {
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    font-size: 12px !important;
    padding: 8px 12px !important;
  }
}

@media (max-width: 1400px) {
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    font-size: 11px !important;
    padding: 6px 8px !important;
  }
} 