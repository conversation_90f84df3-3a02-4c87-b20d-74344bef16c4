
/* 流转按钮区域 */
.workflow-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.submit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
}

.submit-btn .anticon {
  transition: transform 0.3s ease;
}

.submit-btn:hover .anticon {
  transform: translateX(3px);
}

.basic-info-container {
  min-height: 100vh;
  background-color: #F9FAFB;
}

.basic-info-container input,
.basic-info-container textarea {
  pointer-events: auto !important;
  cursor: text !important;
  background-color: white !important;
  color: rgba(0, 0, 0, 0.88) !important;
  opacity: 1 !important;
}
