import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Avatar,
  Tag,
  Button,
  Input,
  Select,
  Table,
  Space,
  Tooltip,
  message,
  Divider,
  Typography,
  Row,
  Col,
  Popconfirm,
  Alert,
  Form
} from 'antd';
import {
  UserAddOutlined,
  ShareAltOutlined,
  EditOutlined,
  DeleteOutlined,
  MailOutlined,
  TeamOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface Collaborator {
  id: string;
  user?: {
    id: string;
    username: string;
    email: string;
    avatar?: string;
  };
  invitedEmail: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer' | 'guest_editor' | 'guest_viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  permissions: {
    canEdit: boolean;
    canDelete: boolean;
    canInvite: boolean;
    canManageStages: boolean;
    canUploadDocuments: boolean;
    canViewFinancials: boolean;
    moduleAccess: string[];
  };
  createdAt: string;
  lastAccessedAt?: string;
}

interface ProjectCollaborationProps {
  projectId: string;
  projectName: string;
  visible: boolean;
  onClose: () => void;
  onCollaboratorsChange?: (collaborators: Collaborator[]) => void;
}

const ProjectCollaboration: React.FC<ProjectCollaborationProps> = ({
  projectId,
  projectName,
  visible,
  onClose,
  onCollaboratorsChange
}) => {
  const [loading, setLoading] = useState(false);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [inviteVisible, setInviteVisible] = useState(false);
  const [form] = Form.useForm();

  // 角色配置
  const roleConfig = {
    owner: { label: '项目拥有者', color: 'purple', description: '完全控制项目的所有方面' },
    admin: { label: '管理员', color: 'red', description: '除删除项目外的所有权限' },
    editor: { label: '编辑者', color: 'blue', description: '可以编辑项目内容和上传文档' },
    viewer: { label: '查看者', color: 'green', description: '只能查看项目内容' },
    guest_editor: { label: '临时编辑者', color: 'orange', description: '有期限的编辑权限' },
    guest_viewer: { label: '临时查看者', color: 'gray', description: '有期限的查看权限' }
  };

  const fetchCollaborators = useCallback(async () => {
    try {
      setLoading(true);
      // TODO: 调用API获取协作者列表
      // const response = await collaborationService.getProjectCollaborators(projectId);
      // setCollaborators(response);
      
      // 模拟数据
      const mockData: Collaborator[] = [
        {
          id: '1',
          user: {
            id: '1',
            username: 'admin',
            email: '<EMAIL>',
            avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'
          },
          invitedEmail: '<EMAIL>',
          role: 'owner',
          status: 'accepted',
          permissions: {
            canEdit: true,
            canDelete: true,
            canInvite: true,
            canManageStages: true,
            canUploadDocuments: true,
            canViewFinancials: true,
            moduleAccess: ['all']
          },
          createdAt: '2024-01-01T00:00:00Z',
          lastAccessedAt: '2024-01-15T10:30:00Z'
        }
      ];
      setCollaborators(mockData);
      onCollaboratorsChange?.(mockData);
    } catch (error) {
      message.error('获取协作者列表失败');
    } finally {
      setLoading(false);
    }
  }, [onCollaboratorsChange]); // 移除 projectId 依赖

  useEffect(() => {
    if (visible) {
      fetchCollaborators();
    }
  }, [visible, fetchCollaborators]);

  const handleInvite = async (values: any) => {
    try {
      setLoading(true);
      // TODO: 调用API邀请协作者
      // await collaborationService.inviteCollaborator(projectId, values.email, values.role, values.message);
      message.success('邀请发送成功');
      setInviteVisible(false);
      form.resetFields();
      fetchCollaborators();
    } catch (error) {
      message.error('邀请发送失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRoleChange = async (collaboratorId: string, newRole: string) => {
    try {
      // TODO: 调用API更新角色
      // await collaborationService.updateCollaboratorPermissions(projectId, collaboratorId, newRole);
      message.success('权限更新成功');
      fetchCollaborators();
    } catch (error) {
      message.error('权限更新失败');
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    try {
      // TODO: 调用API移除协作者
      // await collaborationService.removeCollaborator(projectId, collaboratorId);
      message.success('协作者移除成功');
      fetchCollaborators();
    } catch (error) {
      message.error('移除协作者失败');
    }
  };

  const handleGenerateShareLink = async () => {
    try {
      // TODO: 生成分享链接
      const shareLink = `${window.location.origin}/shared/${projectId}`;
      await navigator.clipboard.writeText(shareLink);
      message.success('分享链接已复制到剪贴板');
    } catch (error) {
      message.error('生成分享链接失败');
    }
  };

  const columns = [
    {
      title: '成员',
      key: 'member',
      render: (record: Collaborator) => (
        <Space>
          <Avatar 
            src={record.user?.avatar} 
            icon={<UserAddOutlined />}
            size="small"
          />
          <div>
            <div>{record.user?.username || record.invitedEmail}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.invitedEmail}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: '角色',
      key: 'role',
      render: (record: Collaborator) => (
        <Select
          value={record.role}
          size="small"
          style={{ width: 140 }}
          onChange={(value) => handleRoleChange(record.id, value)}
          disabled={record.role === 'owner'}
        >
          {Object.entries(roleConfig).map(([key, config]) => (
            <Option key={key} value={key}>
              <Tag color={config.color}>{config.label}</Tag>
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Collaborator) => {
        const statusConfig = {
          pending: { color: 'orange', text: '待接受' },
          accepted: { color: 'green', text: '已接受' },
          declined: { color: 'red', text: '已拒绝' },
          expired: { color: 'gray', text: '已过期' }
        };
        const config = statusConfig[record.status];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '最后活动',
      key: 'lastAccessed',
      render: (record: Collaborator) => (
        <Text type="secondary">
          {record.lastAccessedAt 
            ? new Date(record.lastAccessedAt).toLocaleDateString()
            : '从未访问'
          }
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Collaborator) => (
        <Space>
          <Tooltip title="编辑权限">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              disabled={record.role === 'owner'}
            />
          </Tooltip>
          <Popconfirm
            title="确定要移除此协作者吗？"
            onConfirm={() => handleRemoveCollaborator(record.id)}
            disabled={record.role === 'owner'}
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
              disabled={record.role === 'owner'}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <>
      <Modal
        title={
          <Space>
            <TeamOutlined />
            共享 "{projectName}"
          </Space>
        }
        open={visible}
        onCancel={onClose}
        width={800}
        footer={null}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="协作管理"
            description="管理项目成员权限，控制谁可以查看、编辑和管理此项目。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Button
                type="primary"
                icon={<MailOutlined />}
                onClick={() => setInviteVisible(true)}
                block
              >
                邀请成员
              </Button>
            </Col>
            <Col span={12}>
              <Button
                icon={<ShareAltOutlined />}
                onClick={handleGenerateShareLink}
                block
              >
                生成分享链接
              </Button>
            </Col>
          </Row>
        </div>

        <Divider />

        <Title level={5}>项目成员 ({collaborators.length})</Title>
        
        <Table
          columns={columns}
          dataSource={collaborators}
          rowKey="id"
          pagination={false}
          loading={loading}
          size="small"
        />
      </Modal>

      {/* 邀请成员模态框 */}
      <Modal
        title="邀请成员"
        open={inviteVisible}
        onCancel={() => setInviteVisible(false)}
        onOk={() => form.submit()}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleInvite}
        >
          <Form.Item
            name="email"
            label="邮箱地址"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="输入要邀请的用户邮箱" />
          </Form.Item>

          <Form.Item
            name="role"
            label="权限角色"
            rules={[{ required: true, message: '请选择权限角色' }]}
          >
            <Select placeholder="选择权限角色">
              {Object.entries(roleConfig).filter(([key]) => key !== 'owner').map(([key, config]) => (
                <Option key={key} value={key}>
                  <div>
                    <Tag color={config.color}>{config.label}</Tag>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {config.description}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="message"
            label="邀请消息"
          >
            <TextArea 
              rows={3} 
              placeholder="可选：添加邀请消息"
              maxLength={200}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ProjectCollaboration; 