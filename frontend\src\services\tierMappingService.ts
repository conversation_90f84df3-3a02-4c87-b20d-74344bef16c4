/**
 * Tier映射服务 - 统一处理客户和项目之间的tier类型转换
 * 解决历史数据不一致问题
 */

export type StandardTier = 'S' | 'V' | 'B' | 'A';
export type LegacyTier = 'SVIP' | 'VIP' | 'BA' | 'C';

export class TierMappingService {
  // 标准tier定义
  private static readonly TIER_MAPPINGS = {
    // 新标准格式 -> 标准格式 (直接映射)
    'S': 'S',
    'V': 'V', 
    'B': 'B',
    'A': 'A',
    
    // 历史格式 -> 标准格式 (转换映射)
    'SVIP': 'S',
    'VIP': 'V',
    'BA': 'B',
    'C': 'A'
  } as const;

  private static readonly TIER_DESCRIPTIONS = {
    'S': { label: 'Strategic', value: 'S', priority: 1, color: '#722ed1' },
    'V': { label: 'VIP', value: 'V', priority: 2, color: '#1890ff' },
    'B': { label: 'Business', value: 'B', priority: 3, color: '#52c41a' },
    'A': { label: 'Account', value: 'A', priority: 4, color: '#fa8c16' }
  } as const;

  /**
   * 将任何tier格式转换为标准格式
   */
  static normalizeToStandard(tier: string | StandardTier | LegacyTier): StandardTier {
    const normalized = tier?.toString().toUpperCase();
    
    if (normalized in this.TIER_MAPPINGS) {
      return this.TIER_MAPPINGS[normalized as keyof typeof this.TIER_MAPPINGS];
    }
    
    // 兜底策略：无法识别的tier默认为A
    console.warn(`Unknown tier value: ${tier}, defaulting to 'A'`);
    return 'A';
  }

  /**
   * 批量转换tier数组
   */
  static normalizeArray(tiers: (string | StandardTier | LegacyTier)[]): StandardTier[] {
    return tiers.map(tier => this.normalizeToStandard(tier));
  }

  /**
   * 获取tier的显示信息
   */
  static getTierInfo(tier: string | StandardTier | LegacyTier) {
    const standardTier = this.normalizeToStandard(tier);
    return this.TIER_DESCRIPTIONS[standardTier];
  }

  /**
   * 判断tier1是否比tier2等级更高
   */
  static isHigherTier(tier1: string | StandardTier | LegacyTier, tier2: string | StandardTier | LegacyTier): boolean {
    const info1 = this.getTierInfo(tier1);
    const info2 = this.getTierInfo(tier2);
    return info1.priority < info2.priority; // 优先级数字越小等级越高
  }

  /**
   * 获取所有标准tier列表
   */
  static getAllStandardTiers(): StandardTier[] {
    return ['S', 'V', 'B', 'A'];
  }

  /**
   * 获取tier选项用于下拉选择
   */
  static getTierOptions() {
    return this.getAllStandardTiers().map(tier => ({
      value: tier,
      label: this.TIER_DESCRIPTIONS[tier].label,
      color: this.TIER_DESCRIPTIONS[tier].color
    }));
  }

  /**
   * 数据迁移：转换历史客户数据
   */
  static migrateClientData(clients: any[]): any[] {
    return clients.map(client => ({
      ...client,
      tier: this.normalizeToStandard(client.tier),
      // 添加迁移标记
      _migrated: true,
      _originalTier: client.tier
    }));
  }

  /**
   * 数据迁移：转换历史项目数据
   */
  static migrateProjectData(projects: any[]): any[] {
    return projects.map(project => ({
      ...project,
      tier: this.normalizeToStandard(project.tier),
      _migrated: true,
      _originalTier: project.tier
    }));
  }

  /**
   * 验证tier值是否有效
   */
  static isValidTier(tier: string): tier is StandardTier {
    return this.getAllStandardTiers().includes(tier as StandardTier);
  }
}

export default TierMappingService; 