{"version": 3, "file": "collaboration.service.js", "sourceRoot": "", "sources": ["../../src/services/collaboration.service.ts"], "names": [], "mappings": ";;;AAAA,qCAAwC;AACxC,yEAA0G;AAC1G,iDAA8C;AAC9C,2CAAwC;AACxC,iEAA4E;AAC5E,4CAAiF;AACjF,wDAA4D;AAC5D,oDAA8D;AAE9D,MAAa,oBAAoB;IAAjC;QACU,2BAAsB,GAAG,IAAA,uBAAa,EAAC,yCAAmB,CAAC,CAAC;QAC5D,sBAAiB,GAAG,IAAA,uBAAa,EAAC,iBAAO,CAAC,CAAC;QAC3C,mBAAc,GAAG,IAAA,uBAAa,EAAC,WAAI,CAAC,CAAC;QACrC,uBAAkB,GAAG,IAAA,uBAAa,EAAC,iCAAe,CAAC,CAAC;IA4U9D,CAAC;IAvUC,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,aAAqB,EACrB,KAAa,EACb,IAAsB,EACtB,OAAgB,EAChB,gBAAwB,CAAC;QAGzB,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAE/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,sBAAa,CAAC,OAAO,CAAC,CAAC;SAClC;QAGD,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YACrE,KAAK,EAAE;gBACL,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE;gBAC/C,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE;aACpD;SACF,CAAC,CAAC;QAEH,IAAI,oBAAoB,EAAE;YACxB,MAAM,IAAI,wBAAe,CAAC,YAAY,CAAC,CAAC;SACzC;QAGD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7E,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,CAAC;QAEvD,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;YACtD,OAAO;YACP,IAAI,EAAE,YAAY;YAClB,YAAY,EAAE,KAAK;YACnB,IAAI;YACJ,MAAM,EAAE,sCAAgB,CAAC,OAAO;YAChC,SAAS;YACT,SAAS,EAAE,aAAa;YACxB,iBAAiB,EAAE,OAAO;YAC1B,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;SAC9C,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAG/E,MAAM,eAAe,GAAG,IAAA,oCAAuB,EAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QACtE,MAAM,IAAA,kCAAmB,EAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;QAGzE,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,8BAAY,CAAC,kBAAkB,EACjF,MAAM,KAAK,OAAO,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAEnD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,MAAe;QACnD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAE9D,IAAI,YAAY,CAAC,MAAM,KAAK,sCAAgB,CAAC,OAAO,EAAE;YACpD,MAAM,IAAI,wBAAe,CAAC,SAAS,CAAC,CAAC;SACtC;QAED,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE;YACjE,YAAY,CAAC,MAAM,GAAG,sCAAgB,CAAC,OAAO,CAAC;YAC/C,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrD,MAAM,IAAI,wBAAe,CAAC,OAAO,CAAC,CAAC;SACpC;QAGD,IAAI,MAAM,EAAE;YACV,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,YAAY,EAAE;gBACpD,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;aAC1B;SACF;QAED,YAAY,CAAC,MAAM,GAAG,sCAAgB,CAAC,QAAQ,CAAC;QAChD,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAG/E,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,IAAI,IAAI,EAAE,8BAAY,CAAC,kBAAkB,EAChG,GAAG,YAAY,CAAC,YAAY,UAAU,CAAC,CAAC;QAE1C,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACnC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAE9D,YAAY,CAAC,MAAM,GAAG,sCAAgB,CAAC,QAAQ,CAAC;QAChD,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAGrD,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,8BAAY,CAAC,oBAAoB,EACxF,GAAG,YAAY,CAAC,YAAY,UAAU,CAAC,CAAC;IAC5C,CAAC;IAKD,KAAK,CAAC,6BAA6B,CACjC,SAAiB,EACjB,cAAsB,EACtB,aAAqB,EACrB,OAAyB,EACzB,iBAAuB;QAEvB,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAE9D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7E,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE;YAC1D,MAAM,IAAI,sBAAa,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,MAAM,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC;QAClC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;QAC5B,YAAY,CAAC,WAAW,GAAG,iBAAiB,IAAI,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAEpF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAG/E,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,8BAAY,CAAC,kBAAkB,EACjF,OAAO,YAAY,CAAC,YAAY,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QAEpF,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,cAAsB,EACtB,aAAqB;QAErB,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAE9D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7E,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE;YAC1D,MAAM,IAAI,sBAAa,CAAC,QAAQ,CAAC,CAAC;SACnC;QAED,MAAM,WAAW,GAAG,YAAY,CAAC,YAAY,CAAC;QAC9C,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAGvD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,EAAE,8BAAY,CAAC,oBAAoB,EACnF,UAAU,WAAW,EAAE,CAAC,CAAC;IAC7B,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAAC,SAAiB;QAC7C,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC5C,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE;YACrC,SAAS,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;YAC9B,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,MAAc,EAAE,UAAkB;QACzE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;YAC3D,SAAS,EAAE,CAAC,MAAM,CAAC;SACpB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,sCAAgB,CAAC,QAAQ,EAAE;YACtE,OAAO,KAAK,CAAC;SACd;QAGD,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QAC7C,QAAQ,UAAU,EAAE;YAClB,KAAK,MAAM;gBACT,OAAO,WAAW,CAAC,OAAO,CAAC;YAC7B,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,KAAK,cAAc;gBACjB,OAAO,WAAW,CAAC,eAAe,CAAC;YACrC,KAAK,iBAAiB;gBACpB,OAAO,WAAW,CAAC,kBAAkB,CAAC;YACxC,KAAK,gBAAgB;gBACnB,OAAO,WAAW,CAAC,iBAAiB,CAAC;YACvC;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC3D,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,sCAAgB,CAAC,QAAQ,EAAE;YAClE,SAAS,EAAE,CAAC,SAAS,CAAC;SACvB,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAIO,KAAK,CAAC,yBAAyB,CAAC,SAAiB,EAAE,MAAc;QACvE,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC9E,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,uBAAc,CAAC,QAAQ,CAAC,CAAC;SACpC;IACH,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAAiB,EAAE,MAAc;QACtE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,sCAAgB,CAAC,KAAK,EAAE,sCAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;YAClG,MAAM,IAAI,uBAAc,CAAC,QAAQ,CAAC,CAAC;SACpC;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAa;QAGhD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACtD,CAAC;IAEO,qBAAqB,CAAC,IAAsB;QAClD,MAAM,aAAa,GAAG;YACpB,CAAC,sCAAgB,CAAC,KAAK,CAAC,EAAE;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,eAAe,EAAE,IAAI;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,YAAY,EAAE,CAAC,KAAK,CAAC;aACtB;YACD,CAAC,sCAAgB,CAAC,KAAK,CAAC,EAAE;gBACxB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,IAAI;gBACf,eAAe,EAAE,IAAI;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,YAAY,EAAE,CAAC,KAAK,CAAC;aACtB;YACD,CAAC,sCAAgB,CAAC,MAAM,CAAC,EAAE;gBACzB,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,IAAI;gBACrB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC;aAC/C;YACD,CAAC,sCAAgB,CAAC,MAAM,CAAC,EAAE;gBACzB,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,KAAK;gBACtB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC,OAAO,CAAC;aACxB;YACD,CAAC,sCAAgB,CAAC,YAAY,CAAC,EAAE;gBAC/B,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,KAAK;gBACtB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;aACrC;YACD,CAAC,sCAAgB,CAAC,YAAY,CAAC,EAAE;gBAC/B,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,KAAK;gBAChB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,KAAK;gBACtB,kBAAkB,EAAE,KAAK;gBACzB,iBAAiB,EAAE,KAAK;gBACxB,YAAY,EAAE,CAAC,OAAO,CAAC;aACxB;SACF,CAAC;QAEF,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,SAAiB,EACjB,MAAqB,EACrB,IAAkB,EAClB,KAAa,EACb,QAAc;QAEd,IAAI,CAAC,MAAM;YAAE,OAAO;QAEpB,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;YAC1B,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;YACpB,IAAI;YACJ,KAAK;YACL,QAAQ;SACT,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;CACF;AAhVD,oDAgVC"}