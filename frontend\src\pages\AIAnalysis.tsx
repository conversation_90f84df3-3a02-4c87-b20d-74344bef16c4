import React, { useState, useEffect } from 'react';
import { 
  Card, Row, Col, Button, Table, Tag, Space, Typography, Divider, 
  Modal, Form, Input, Select, message, Spin, Progress 
} from 'antd';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  FileTextOutlined,
  RobotOutlined,
  SaveOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface ExtractedData {
  field: string;
  value: string;
  confidence: number;
  module: string;
  suggestion: string;
  status: 'pending' | 'approved' | 'rejected' | 'modified';
}

const AIAnalysis: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const [extractedData, setExtractedData] = useState<ExtractedData[]>([]);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ExtractedData | null>(null);
  const [form] = Form.useForm();
  const [analysisProgress, setAnalysisProgress] = useState(0);

  // Simulate AI analysis progress
  useEffect(() => {
    const fileName = location.state?.fileName || 'document.pdf';
    
    // Simulate analysis progress
    const progressInterval = setInterval(() => {
      setAnalysisProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          setLoading(false);
          return 100;
        }
        return prev + 10;
      });
    }, 200);

    // Simulate extracted data
    setTimeout(() => {
      setExtractedData([
        {
          field: 'Client Name',
          value: 'Global Tech Solutions Ltd',
          confidence: 95,
          module: 'Client Management',
          suggestion: 'Create new client record',
          status: 'pending'
        },
        {
          field: 'Project Name',
          value: 'Digital Transformation Initiative',
          confidence: 88,
          module: 'Lead to Cash',
          suggestion: 'Create new opportunity',
          status: 'pending'
        },
        {
          field: 'Contract Amount',
          value: '€125,000',
          confidence: 92,
          module: 'Finance Management',
          suggestion: 'Create transaction record',
          status: 'pending'
        },
        {
          field: 'Contract Date',
          value: '2024-06-15',
          confidence: 85,
          module: 'Finance Management',
          suggestion: 'Set as transaction date',
          status: 'pending'
        },
        {
          field: 'Payment Terms',
          value: '30 days NET',
          confidence: 78,
          module: 'Finance Management',
          suggestion: 'Add to contract terms',
          status: 'pending'
        },
        {
          field: 'Contact Person',
          value: 'John Smith, CTO',
          confidence: 90,
          module: 'Client Management',
          suggestion: 'Add as primary contact',
          status: 'pending'
        }
      ]);
    }, 2500);

    return () => clearInterval(progressInterval);
  }, [location.state]);

  // Handle confirm data
  const handleConfirm = (record: ExtractedData) => {
    setSelectedRecord(record);
    form.setFieldsValue({
      field: record.field,
      value: record.value,
      module: record.module
    });
    setConfirmModalVisible(true);
  };

  // Handle data confirmation submission
  const handleConfirmSubmit = () => {
    const values = form.getFieldsValue();
    
    setExtractedData(prev => prev.map(item => 
      item === selectedRecord 
        ? { ...item, ...values, status: 'approved' }
        : item
    ));
    
    message.success('Data confirmed and saved to system!');
    setConfirmModalVisible(false);
    setSelectedRecord(null);
  };

  // Handle reject data
  const handleReject = (record: ExtractedData) => {
    setExtractedData(prev => prev.map(item => 
      item === record 
        ? { ...item, status: 'rejected' }
        : item
    ));
    message.info('Data rejected');
  };

  // Bulk confirm all data
  const handleBulkConfirm = () => {
    Modal.confirm({
      title: 'Confirm All Extracted Data',
      content: 'Are you sure you want to confirm and save all pending data to the system?',
      onOk: () => {
        setExtractedData(prev => prev.map(item => 
          item.status === 'pending' 
            ? { ...item, status: 'approved' }
            : item
        ));
        message.success('All data confirmed and saved to system!');
      }
    });
  };

  const columns = [
    {
      title: 'Field',
      dataIndex: 'field',
      key: 'field',
      width: 150,
    },
    {
      title: 'Extracted Value',
      dataIndex: 'value',
      key: 'value',
      width: 200,
      render: (text: string) => <Text strong>{text}</Text>
    },
    {
      title: 'Confidence',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 120,
      render: (confidence: number) => (
        <div>
          <Progress 
            percent={confidence} 
            size="small" 
            status={confidence >= 90 ? 'success' : confidence >= 70 ? 'active' : 'exception'}
            showInfo={false}
          />
          <Text style={{ 
            fontSize: '12px', 
            color: confidence >= 90 ? '#10B981' : confidence >= 70 ? '#FF7A00' : '#EF4444' 
          }}>
            {confidence}%
          </Text>
        </div>
      )
    },
    {
      title: 'Target Module',
      dataIndex: 'module',
      key: 'module',
      width: 150,
      render: (module: string) => {
        const color = module.includes('Client') ? '#3B82F6' : 
                     module.includes('Finance') ? '#10B981' : 
                     module.includes('Lead') ? '#FF7A00' : 'default';
        return <Tag color={color}>{module}</Tag>;
      }
    },
    {
      title: 'AI Suggestion',
      dataIndex: 'suggestion',
      key: 'suggestion',
      width: 180,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusConfig = {
          pending: { color: '#F59E0B', text: 'Pending' },
          approved: { color: '#10B981', text: 'Approved' },
          rejected: { color: '#EF4444', text: 'Rejected' },
          modified: { color: '#3B82F6', text: 'Modified' }
        };
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_: any, record: ExtractedData) => (
        <Space size="small">
          {record.status === 'pending' && (
            <>
              <Button 
                type="primary" 
                size="small" 
                icon={<CheckOutlined />}
                onClick={() => handleConfirm(record)}
                style={{ 
                  backgroundColor: '#10B981',
                  borderColor: '#10B981'
                }}
              >
                Confirm
              </Button>
              <Button 
                danger 
                size="small" 
                icon={<CloseOutlined />}
                onClick={() => handleReject(record)}
                style={{ 
                  backgroundColor: '#EF4444',
                  borderColor: '#EF4444'
                }}
              >
                Reject
              </Button>
            </>
          )}
          {record.status === 'approved' && (
            <Button 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => handleConfirm(record)}
              style={{ 
                color: '#FF7A00',
                borderColor: '#FF7A00'
              }}
            >
              Edit
            </Button>
          )}
          {record.status === 'rejected' && (
            <Button 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => handleConfirm(record)}
              style={{ 
                color: '#3B82F6',
                borderColor: '#3B82F6'
              }}
            >
              Review
            </Button>
          )}
        </Space>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '48px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/ai')}
            style={{ marginBottom: '16px' }}
          >
            Back to AI Assistant
          </Button>
        </div>

        <Card style={{ textAlign: 'center', padding: '48px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '24px' }}>
            <RobotOutlined style={{ fontSize: '48px', color: '#FF7A00', marginBottom: '16px' }} />
            <Title level={3}>AI is analyzing your document...</Title>
            <Progress 
              percent={analysisProgress} 
              status="active"
              style={{ maxWidth: '400px', margin: '16px auto' }}
              strokeColor="#FF7A00"
            />
            <Text type="secondary">
              {analysisProgress < 30 ? 'Reading document structure...' :
               analysisProgress < 60 ? 'Extracting key information...' :
               analysisProgress < 90 ? 'Matching with system modules...' :
               'Finalizing analysis results...'}
            </Text>
          </div>
        </Card>
      </div>
    );
  }

  const pendingCount = extractedData.filter(item => item.status === 'pending').length;
  const approvedCount = extractedData.filter(item => item.status === 'approved').length;

  return (
    <div style={{ padding: '24px' }}>
      {/* Page title */}
      <div style={{ marginBottom: '48px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/ai')}
          style={{ marginBottom: '16px' }}
        >
          Back to AI Assistant
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          <FileTextOutlined style={{ marginRight: '8px', color: '#10B981' }} />
          Document Analysis Results
        </div>
      </div>

      {/* Analysis statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#FF7A00', margin: 0 }}>{extractedData.length}</Title>
              <Text type="secondary">Fields Extracted</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#F59E0B', margin: 0 }}>{pendingCount}</Title>
              <Text type="secondary">Pending Review</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#10B981', margin: 0 }}>{approvedCount}</Title>
              <Text type="secondary">Approved</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <Title level={2} style={{ color: '#10B981', margin: 0 }}>87%</Title>
              <Text type="secondary">Avg Confidence</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Bulk operations */}
      {pendingCount > 0 && (
        <Card style={{ marginBottom: '24px', background: '#f6ffed', border: '1px solid #b7eb8f' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong style={{ color: '#10B981' }}>
                🤖 {pendingCount} fields are waiting for your confirmation
              </Text>
              <br />
              <Text type="secondary">
                Review the extracted data and confirm to save to corresponding system modules
              </Text>
            </div>
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={handleBulkConfirm}
              size="large"
              style={{ 
                backgroundColor: '#10B981',
                borderColor: '#10B981'
              }}
            >
              Confirm All ({pendingCount})
            </Button>
          </div>
        </Card>
      )}

      {/* Extracted data table */}
      <Card title="Extracted Data" style={{ marginBottom: '24px' }}>
        <Table
          columns={columns}
          dataSource={extractedData}
          rowKey="field"
          pagination={false}
          size="middle"
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Confirmation modal */}
      <Modal
        title="Confirm Extracted Data"
        open={confirmModalVisible}
        onOk={handleConfirmSubmit}
        onCancel={() => setConfirmModalVisible(false)}
        width={600}
        okText="Confirm"
        cancelText="Cancel"
        okButtonProps={{
          style: { 
            backgroundColor: '#10B981',
            borderColor: '#10B981'
          }
        }}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="field"
            label="Field Name"
            rules={[{ required: true, message: 'Please enter field name' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="value"
            label="Value"
            rules={[{ required: true, message: 'Please enter value' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="module"
            label="Target Module"
            rules={[{ required: true, message: 'Please select target module' }]}
          >
            <Select>
              <Option value="Client Management">Client Management</Option>
              <Option value="Lead to Cash">Lead to Cash</Option>
              <Option value="Finance Management">Finance Management</Option>
              <Option value="System">System</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AIAnalysis; 