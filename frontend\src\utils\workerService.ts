/**
 * 工作线程服务
 * 用于在后台线程中处理计算密集型任务
 */

// 任务类型
export enum TaskType {
  DATA_PROCESSING = 'data_processing',
  CALCULATION = 'calculation',
  FILTERING = 'filtering',
  SORTING = 'sorting',
  EXPORT = 'export',
  IMPORT = 'import',
  CUSTOM = 'custom'
}

// 任务状态
export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 任务接口
export interface Task<T = any, R = any> {
  id: string;                  // 任务ID
  type: TaskType;              // 任务类型
  data: T;                     // 任务数据
  status: TaskStatus;          // 任务状态
  progress: number;            // 任务进度（0-100）
  result?: R;                  // 任务结果
  error?: Error;               // 任务错误
  startTime?: number;          // 开始时间
  endTime?: number;            // 结束时间
  priority: number;            // 任务优先级（越高越优先）
}

// 任务处理器接口
export interface TaskHandler<T = any, R = any> {
  (data: T, updateProgress: (progress: number) => void): Promise<R>;
}

// 工作线程配置
export interface WorkerServiceConfig {
  maxConcurrent: number;       // 最大并发任务数
  taskTimeout: number;         // 任务超时时间（毫秒）
  retryCount: number;          // 重试次数
  retryDelay: number;          // 重试延迟（毫秒）
  logLevel: 'none' | 'error' | 'warn' | 'info' | 'debug'; // 日志级别
}

// 默认配置
const DEFAULT_CONFIG: WorkerServiceConfig = {
  maxConcurrent: 2,
  taskTimeout: 30000,
  retryCount: 1,
  retryDelay: 1000,
  logLevel: 'error'
};

/**
 * 工作线程服务
 * 用于管理和执行后台任务
 */
export class WorkerService {
  private config: WorkerServiceConfig;
  private taskQueue: Task[] = [];
  private runningTasks: Map<string, Task> = new Map();
  private taskHandlers: Map<TaskType, TaskHandler> = new Map();
  private taskPromises: Map<string, { resolve: (result: any) => void, reject: (error: Error) => void }> = new Map();
  private taskTimeouts: Map<string, number> = new Map();
  private isProcessing: boolean = false;
  private listeners: Map<string, Set<(task: Task) => void>> = new Map();
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: Partial<WorkerServiceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // 初始化监听器集合
    this.listeners.set('taskAdded', new Set());
    this.listeners.set('taskStarted', new Set());
    this.listeners.set('taskCompleted', new Set());
    this.listeners.set('taskFailed', new Set());
    this.listeners.set('taskCancelled', new Set());
    this.listeners.set('taskProgressUpdated', new Set());
  }
  
  /**
   * 注册任务处理器
   * @param type 任务类型
   * @param handler 处理器函数
   */
  public registerTaskHandler<T, R>(type: TaskType, handler: TaskHandler<T, R>): void {
    this.taskHandlers.set(type, handler as TaskHandler);
    this.log('debug', `Registered handler for task type: ${type}`);
  }
  
  /**
   * 添加任务
   * @param type 任务类型
   * @param data 任务数据
   * @param priority 任务优先级
   * @returns 任务ID
   */
  public addTask<T, R>(type: TaskType, data: T, priority = 0): Promise<R> {
    // 检查是否有对应的处理器
    if (!this.taskHandlers.has(type)) {
      throw new Error(`No handler registered for task type: ${type}`);
    }
    
    // 创建任务
    const taskId = this.generateTaskId();
    const task: Task<T, R> = {
      id: taskId,
      type,
      data,
      status: TaskStatus.PENDING,
      progress: 0,
      priority
    };
    
    // 添加到队列
    this.taskQueue.push(task);
    
    // 按优先级排序
    this.taskQueue.sort((a, b) => b.priority - a.priority);
    
    // 触发事件
    this.emitEvent('taskAdded', task);
    
    // 开始处理队列
    this.processQueue();
    
    // 返回Promise
    return new Promise<R>((resolve, reject) => {
      this.taskPromises.set(taskId, { resolve, reject });
    });
  }
  
  /**
   * 取消任务
   * @param taskId 任务ID
   * @returns 是否成功取消
   */
  public cancelTask(taskId: string): boolean {
    // 检查是否在队列中
    const queueIndex = this.taskQueue.findIndex(task => task.id === taskId);
    if (queueIndex >= 0) {
      const task = this.taskQueue[queueIndex];
      task.status = TaskStatus.CANCELLED;
      
      // 从队列中移除
      this.taskQueue.splice(queueIndex, 1);
      
      // 拒绝Promise
      const promise = this.taskPromises.get(taskId);
      if (promise) {
        promise.reject(new Error('Task cancelled'));
        this.taskPromises.delete(taskId);
      }
      
      // 触发事件
      this.emitEvent('taskCancelled', task);
      
      return true;
    }
    
    // 检查是否正在运行
    if (this.runningTasks.has(taskId)) {
      const task = this.runningTasks.get(taskId)!;
      task.status = TaskStatus.CANCELLED;
      
      // 清除超时
      if (this.taskTimeouts.has(taskId)) {
        clearTimeout(this.taskTimeouts.get(taskId));
        this.taskTimeouts.delete(taskId);
      }
      
      // 从运行中任务移除
      this.runningTasks.delete(taskId);
      
      // 拒绝Promise
      const promise = this.taskPromises.get(taskId);
      if (promise) {
        promise.reject(new Error('Task cancelled'));
        this.taskPromises.delete(taskId);
      }
      
      // 触发事件
      this.emitEvent('taskCancelled', task);
      
      // 处理队列
      this.processQueue();
      
      return true;
    }
    
    return false;
  }
  
  /**
   * 获取任务状态
   * @param taskId 任务ID
   * @returns 任务状态
   */
  public getTaskStatus(taskId: string): Task | null {
    // 检查是否在队列中
    const queueTask = this.taskQueue.find(task => task.id === taskId);
    if (queueTask) {
      return { ...queueTask };
    }
    
    // 检查是否正在运行
    if (this.runningTasks.has(taskId)) {
      return { ...this.runningTasks.get(taskId)! };
    }
    
    return null;
  }
  
  /**
   * 获取所有任务
   * @returns 所有任务
   */
  public getAllTasks(): Task[] {
    const tasks: Task[] = [];
    
    // 添加队列中的任务
    tasks.push(...this.taskQueue.map(task => ({ ...task })));
    
    // 添加运行中的任务
    this.runningTasks.forEach(task => {
      tasks.push({ ...task });
    });
    
    return tasks;
  }
  
  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public addEventListener(
    event: 'taskAdded' | 'taskStarted' | 'taskCompleted' | 'taskFailed' | 'taskCancelled' | 'taskProgressUpdated',
    listener: (task: Task) => void
  ): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.add(listener);
    }
  }
  
  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  public removeEventListener(
    event: 'taskAdded' | 'taskStarted' | 'taskCompleted' | 'taskFailed' | 'taskCancelled' | 'taskProgressUpdated',
    listener: (task: Task) => void
  ): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.delete(listener);
    }
  }
  
  /**
   * 处理任务队列
   */
  private processQueue(): void {
    // 如果已经在处理，直接返回
    if (this.isProcessing) {
      return;
    }
    
    this.isProcessing = true;
    
    // 检查是否有可以执行的任务
    while (
      this.taskQueue.length > 0 &&
      this.runningTasks.size < this.config.maxConcurrent
    ) {
      // 获取下一个任务
      const task = this.taskQueue.shift()!;
      
      // 如果任务已取消，跳过
      if (task.status === TaskStatus.CANCELLED) {
        continue;
      }
      
      // 执行任务
      this.executeTask(task);
    }
    
    this.isProcessing = false;
  }
  
  /**
   * 执行任务
   * @param task 任务
   */
  private async executeTask(task: Task): Promise<void> {
    // 更新任务状态
    task.status = TaskStatus.RUNNING;
    task.startTime = Date.now();
    
    // 添加到运行中任务
    this.runningTasks.set(task.id, task);
    
    // 触发事件
    this.emitEvent('taskStarted', task);
    
    // 设置超时
    this.setTaskTimeout(task);
    
    // 获取处理器
    const handler = this.taskHandlers.get(task.type);
    if (!handler) {
      this.handleTaskError(task, new Error(`No handler for task type: ${task.type}`));
      return;
    }
    
    // 创建进度更新函数
    const updateProgress = (progress: number) => {
      task.progress = Math.max(0, Math.min(100, progress));
      this.emitEvent('taskProgressUpdated', task);
    };
    
    try {
      // 执行处理器
      const result = await handler(task.data, updateProgress);
      
      // 清除超时
      if (this.taskTimeouts.has(task.id)) {
        clearTimeout(this.taskTimeouts.get(task.id));
        this.taskTimeouts.delete(task.id);
      }
      
      // 更新任务状态
      task.status = TaskStatus.COMPLETED;
      task.progress = 100;
      task.result = result;
      task.endTime = Date.now();
      
      // 从运行中任务移除
      this.runningTasks.delete(task.id);
      
      // 解决Promise
      const promise = this.taskPromises.get(task.id);
      if (promise) {
        promise.resolve(result);
        this.taskPromises.delete(task.id);
      }
      
      // 触发事件
      this.emitEvent('taskCompleted', task);
      
      // 处理队列
      this.processQueue();
    } catch (error) {
      this.handleTaskError(task, error as Error);
    }
  }
  
  /**
   * 处理任务错误
   * @param task 任务
   * @param error 错误
   */
  private handleTaskError(task: Task, error: Error): void {
    // 清除超时
    if (this.taskTimeouts.has(task.id)) {
      clearTimeout(this.taskTimeouts.get(task.id));
      this.taskTimeouts.delete(task.id);
    }
    
    // 更新任务状态
    task.status = TaskStatus.FAILED;
    task.error = error;
    task.endTime = Date.now();
    
    // 从运行中任务移除
    this.runningTasks.delete(task.id);
    
    // 拒绝Promise
    const promise = this.taskPromises.get(task.id);
    if (promise) {
      promise.reject(error);
      this.taskPromises.delete(task.id);
    }
    
    // 触发事件
    this.emitEvent('taskFailed', task);
    
    // 记录错误
    this.log('error', `Task ${task.id} failed: ${error.message}`);
    
    // 处理队列
    this.processQueue();
  }
  
  /**
   * 设置任务超时
   * @param task 任务
   */
  private setTaskTimeout(task: Task): void {
    const timeoutId = window.setTimeout(() => {
      this.handleTaskError(task, new Error(`Task timed out after ${this.config.taskTimeout}ms`));
    }, this.config.taskTimeout);
    
    this.taskTimeouts.set(task.id, timeoutId);
  }
  
  /**
   * 触发事件
   * @param event 事件名称
   * @param task 任务
   */
  private emitEvent(
    event: 'taskAdded' | 'taskStarted' | 'taskCompleted' | 'taskFailed' | 'taskCancelled' | 'taskProgressUpdated',
    task: Task
  ): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener({ ...task });
        } catch (error) {
          this.log('error', `Error in ${event} listener: ${(error as Error).message}`);
        }
      });
    }
  }
  
  /**
   * 生成任务ID
   * @returns 任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }
  
  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   */
  private log(level: 'none' | 'error' | 'warn' | 'info' | 'debug', message: string): void {
    const levels = { none: 0, error: 1, warn: 2, info: 3, debug: 4 };
    
    if (levels[level] <= levels[this.config.logLevel]) {
      switch (level) {
        case 'error':
          console.error(`[WorkerService] ${message}`);
          break;
        case 'warn':
          console.warn(`[WorkerService] ${message}`);
          break;
        case 'info':
          console.info(`[WorkerService] ${message}`);
          break;
        case 'debug':
          console.debug(`[WorkerService] ${message}`);
          break;
      }
    }
  }
}

// 创建默认实例
const workerService = new WorkerService();

export default workerService;
