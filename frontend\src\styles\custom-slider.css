/* 自定义滑块样式 */

/* 滑块容器 */
.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

/* 滑块轨道 */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: #e5e7eb; /* 默认轨道颜色 */
  border-radius: 3px;
  outline: none;
}

/* 滑块填充部分 - 使用背景渐变实现 */
input[type="range"]::-webkit-slider-runnable-track {
  background: linear-gradient(
    to right,
    rgba(255, 122, 0, 0.3) 0%, /* 橙色，透明度30% */
    rgba(255, 122, 0, 0.3) var(--value-percent, 50%),
    #e5e7eb var(--value-percent, 50%)
  );
  height: 6px;
  border-radius: 3px;
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 6px;
  border-radius: 3px;
}

/* 滑块填充部分 - Firefox */
input[type="range"]::-moz-range-progress {
  background-color: rgba(255, 122, 0, 0.3); /* 橙色，透明度30% */
  height: 6px;
  border-radius: 3px;
}

/* 滑块手柄 - 缩小到14px */
input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #FF7A00; /* 橙色 */
  cursor: pointer;
  margin-top: -4px; /* 调整垂直位置 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #FF7A00; /* 橙色 */
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 滑块手柄悬停效果 */
input[type="range"]:hover::-webkit-slider-thumb {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

input[type="range"]:hover::-moz-range-thumb {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

/* 滑块手柄激活效果 */
input[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

input[type="range"]:active::-moz-range-thumb {
  transform: scale(1.15);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 滑块值显示 - 调整为12px */
.slider-value {
  font-size: 12px !important;
  font-weight: 600;
  color: #1F2023;
  min-width: 35px;
  transition: transform 0.2s ease;
}

/* 禁用状态 */
input[type="range"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #9CA3AF;
  cursor: not-allowed;
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #9CA3AF;
  cursor: not-allowed;
}

/* 自定义搜索下拉框样式 */
.ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border-color: #d9d9d9;
  transition: all 0.3s;
}

.ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector:hover {
  border-color: #FF7A00;
}

.ant-select-show-search.ant-select-focused:not(.ant-select-customize-input) .ant-select-selector {
  border-color: #FF7A00;
  box-shadow: 0 0 0 2px rgba(255, 122, 0, 0.2);
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgba(255, 122, 0, 0.1);
}

.ant-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: rgba(255, 122, 0, 0.05);
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-content {
  color: #FF7A00;
  font-weight: 600;
}

/* 搜索框中的搜索图标 */
.ant-select-arrow .anticon-search {
  color: rgba(255, 122, 0, 0.5);
}

/* 搜索结果高亮 */
.ant-select-dropdown .ant-select-item-option-content mark {
  background-color: rgba(255, 122, 0, 0.2);
  padding: 0 2px;
  border-radius: 2px;
  color: #FF7A00;
  font-weight: 600;
}
