const bcrypt = require('bcryptjs');
const sqlite3 = require('sqlite3').verbose();

// 连接到SQLite数据库
const db = new sqlite3.Database('./ltc_project.sqlite', (err) => {
  if (err) {
    console.error('连接数据库失败:', err.message);
    process.exit(1);
  }
  console.log('成功连接到SQLite数据库');
});

// 重置管理员账户
const resetAdmin = async () => {
  // 检查是否存在管理员账户
  db.get("SELECT * FROM users WHERE email = '<EMAIL>'", [], (err, user) => {
    if (err) {
      console.error('查询失败:', err.message);
      db.close();
      process.exit(1);
    }
    
    // 生成新的密码哈希
    const password = 'admin123';
    const hashedPassword = bcrypt.hashSync(password, 8);
    
    if (user) {
      console.log('找到管理员用户，正在重置密码...');
      
      // 更新管理员密码
      db.run(`UPDATE users SET password = ? WHERE email = '<EMAIL>'`, 
        [hashedPassword], 
        function(err) {
          if (err) {
            console.error('更新管理员密码失败:', err.message);
            db.close();
            process.exit(1);
          }
          
          console.log('超级管理员密码重置成功!');
          console.log('用户名: admin');
          console.log('邮箱: <EMAIL>');
          console.log('密码: admin123');
          console.log('请使用以上信息登录系统');
          
          db.close();
          process.exit(0);
        }
      );
    } else {
      console.log('管理员用户不存在，将创建新的管理员用户');
      
      // 创建超级管理员用户
      const adminUser = {
        id: generateUUID(),
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin'
      };
      
      // 插入管理员用户
      db.run(`INSERT INTO users (id, username, email, password, role) 
              VALUES (?, ?, ?, ?, ?)`, 
        [adminUser.id, adminUser.username, adminUser.email, adminUser.password, adminUser.role], 
        function(err) {
          if (err) {
            console.error('创建超级管理员失败:', err.message);
            db.close();
            process.exit(1);
          }
          
          console.log('超级管理员创建成功!');
          console.log('用户名:', adminUser.username);
          console.log('邮箱:', adminUser.email);
          console.log('密码: admin123');
          console.log('请使用以上信息登录系统');
          
          db.close();
          process.exit(0);
        }
      );
    }
  });
};

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 执行重置
resetAdmin();
