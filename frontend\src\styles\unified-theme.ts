/**
 * 统一主题配置
 * 根据用户偏好设置，使用橙色作为主色调，透明度为30%
 */

// 主题接口
export interface ThemeConfig {
  colors: {
    // 主色调
    primary: string;
    primaryLight: string;
    secondary: string;
    
    // 状态颜色
    success: string;
    warning: string;
    danger: string;
    info: string;
    
    // 文本颜色
    textPrimary: string;
    textSecondary: string;
    textLight: string;
    
    // 背景颜色
    background: string;
    cardBackground: string;
    
    // 边框颜色
    border: string;
    borderFocus: string;
    
    // 灰度
    grayLight: string;
    gray: string;
    grayDark: string;
    
    // 悬停效果
    hover: string;
    
    // 财务指标颜色
    revenueColor: string;
    directCostColor: string;
    grossProfitColor: string;
    indirectCostColor: string;
    netProfitColor: string;
  };
  
  // 圆角
  borderRadius: {
    small: string;
    medium: string;
    large: string;
    circle: string;
  };
  
  // 阴影
  shadows: {
    small: string;
    medium: string;
    large: string;
  };
  
  // 间距
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
  };
  
  // 字体
  typography: {
    fontFamily: string;
    fontSizes: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
      xxl: string;
      xxxl: string;
    };
    fontWeights: {
      light: number;
      regular: number;
      medium: number;
      semibold: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  
  // 过渡效果
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
  
  // 布局
  layout: {
    maxWidth: string;
    sidebarWidth: string;
    sidebarCollapsedWidth: string;
    headerHeight: string;
    footerHeight: string;
    contentPadding: string;
  };
  
  // z-index层级
  zIndex: {
    dropdown: number;
    sticky: number;
    fixed: number;
    modal: number;
    popover: number;
    tooltip: number;
  };
}

// 浅色主题配置
export const lightTheme: ThemeConfig = {
  colors: {
    // 主色调 - 橙色，透明度30%
    primary: '#FF7A00',
    primaryLight: 'rgba(255, 122, 0, 0.3)',
    secondary: '#10B981',
    
    // 状态颜色
    success: '#10B981',
    warning: '#F59E0B',
    danger: '#EF4444',
    info: '#3B82F6',
    
    // 文本颜色
    textPrimary: '#1F2023',
    textSecondary: '#4B5563',
    textLight: '#9CA3AF',
    
    // 背景颜色
    background: '#F9FAFB',
    cardBackground: '#FFFFFF',
    
    // 边框颜色
    border: '#E5E7EB',
    borderFocus: '#FF7A00',
    
    // 灰度
    grayLight: '#F3F4F6',
    gray: '#9CA3AF',
    grayDark: '#4B5563',
    
    // 悬停效果
    hover: 'rgba(255, 122, 0, 0.1)',
    
    // 财务指标颜色 - 透明度30%
    revenueColor: 'rgba(255, 122, 0, 0.3)',
    directCostColor: 'rgba(148, 163, 184, 0.3)',
    grossProfitColor: 'rgba(100, 116, 139, 0.3)',
    indirectCostColor: 'rgba(203, 213, 225, 0.3)',
    netProfitColor: 'rgba(71, 85, 105, 0.3)',
  },
  
  // 圆角
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    circle: '50%',
  },
  
  // 阴影
  shadows: {
    small: '0 1px 2px rgba(0, 0, 0, 0.05)',
    medium: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
    large: '0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05)',
  },
  
  // 间距
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  // 字体
  typography: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Microsoft YaHei', sans-serif",
    fontSizes: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px',
      xxxl: '30px',
    },
    fontWeights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },
  
  // 过渡效果
  transitions: {
    fast: '0.15s ease-in-out',
    normal: '0.25s ease-in-out',
    slow: '0.35s ease-in-out',
  },
  
  // 布局
  layout: {
    maxWidth: '1200px',
    sidebarWidth: '240px',
    sidebarCollapsedWidth: '80px',
    headerHeight: '64px',
    footerHeight: '48px',
    contentPadding: '24px',
  },
  
  // z-index层级
  zIndex: {
    dropdown: 100,
    sticky: 200,
    fixed: 300,
    modal: 400,
    popover: 500,
    tooltip: 600,
  },
};

// 深色主题配置
export const darkTheme: ThemeConfig = {
  colors: {
    // 主色调 - 橙色，透明度30%
    primary: '#FF7A00',
    primaryLight: 'rgba(255, 122, 0, 0.3)',
    secondary: '#34D399',
    
    // 状态颜色
    success: '#34D399',
    warning: '#FBBF24',
    danger: '#F87171',
    info: '#60A5FA',
    
    // 文本颜色
    textPrimary: '#F9FAFB',
    textSecondary: '#D1D5DB',
    textLight: '#6B7280',
    
    // 背景颜色
    background: '#111827',
    cardBackground: '#1F2937',
    
    // 边框颜色
    border: '#374151',
    borderFocus: '#FF7A00',
    
    // 灰度
    grayLight: '#1F2937',
    gray: '#6B7280',
    grayDark: '#D1D5DB',
    
    // 悬停效果
    hover: 'rgba(255, 122, 0, 0.2)',
    
    // 财务指标颜色 - 透明度30%
    revenueColor: 'rgba(255, 122, 0, 0.3)',
    directCostColor: 'rgba(148, 163, 184, 0.3)',
    grossProfitColor: 'rgba(100, 116, 139, 0.3)',
    indirectCostColor: 'rgba(203, 213, 225, 0.3)',
    netProfitColor: 'rgba(71, 85, 105, 0.3)',
  },
  
  // 圆角
  borderRadius: {
    small: '4px',
    medium: '8px',
    large: '12px',
    circle: '50%',
  },
  
  // 阴影
  shadows: {
    small: '0 1px 2px rgba(0, 0, 0, 0.2)',
    medium: '0 4px 6px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.3)',
    large: '0 10px 15px rgba(0, 0, 0, 0.2), 0 4px 6px rgba(0, 0, 0, 0.2)',
  },
  
  // 间距
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  
  // 字体
  typography: {
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Microsoft YaHei', sans-serif",
    fontSizes: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px',
      xxxl: '30px',
    },
    fontWeights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.8,
    },
  },
  
  // 过渡效果
  transitions: {
    fast: '0.15s ease-in-out',
    normal: '0.25s ease-in-out',
    slow: '0.35s ease-in-out',
  },
  
  // 布局
  layout: {
    maxWidth: '1200px',
    sidebarWidth: '240px',
    sidebarCollapsedWidth: '80px',
    headerHeight: '64px',
    footerHeight: '48px',
    contentPadding: '24px',
  },
  
  // z-index层级
  zIndex: {
    dropdown: 100,
    sticky: 200,
    fixed: 300,
    modal: 400,
    popover: 500,
    tooltip: 600,
  },
};

// 导出默认主题
export default lightTheme;
