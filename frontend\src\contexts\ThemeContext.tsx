import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { lightTheme, darkTheme, ThemeConfig } from '../styles/unified-theme';
import { ConfigProvider, theme as antdTheme } from 'antd';

// 主题上下文接口
interface ThemeContextType {
  theme: ThemeConfig;
  isDarkMode: boolean;
  toggleTheme: () => void;
  setDarkMode: (isDark: boolean) => void;
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDarkMode: false,
  toggleTheme: () => {},
  setDarkMode: () => {},
});

// 主题提供者属性
interface ThemeProviderProps {
  children: ReactNode;
}

// 主题提供者组件
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // 检查本地存储中的主题设置
  const getInitialTheme = (): boolean => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme === 'dark';
  };

  // 状态
  const [isDarkMode, setIsDarkMode] = useState<boolean>(getInitialTheme());
  const [theme, setTheme] = useState<ThemeConfig>(getInitialTheme() ? darkTheme : lightTheme);

  // 切换主题
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  // 设置深色模式
  const setDarkMode = (isDark: boolean) => {
    setIsDarkMode(isDark);
  };

  // 当深色模式变化时更新主题
  useEffect(() => {
    setTheme(isDarkMode ? darkTheme : lightTheme);
    
    // 更新文档类
    if (isDarkMode) {
      document.body.classList.add('dark-theme');
      localStorage.setItem('theme', 'dark');
    } else {
      document.body.classList.remove('dark-theme');
      localStorage.setItem('theme', 'light');
    }
    
    // 更新CSS变量
    updateCSSVariables(isDarkMode ? darkTheme : lightTheme);
  }, [isDarkMode]);

  // 更新CSS变量
  const updateCSSVariables = (theme: ThemeConfig) => {
    const root = document.documentElement;
    
    // 颜色
    root.style.setProperty('--primary', theme.colors.primary);
    root.style.setProperty('--primary-light', theme.colors.primaryLight);
    root.style.setProperty('--secondary', theme.colors.secondary);
    root.style.setProperty('--success', theme.colors.success);
    root.style.setProperty('--warning', theme.colors.warning);
    root.style.setProperty('--danger', theme.colors.danger);
    root.style.setProperty('--info', theme.colors.info);
    
    root.style.setProperty('--text-primary', theme.colors.textPrimary);
    root.style.setProperty('--text-secondary', theme.colors.textSecondary);
    root.style.setProperty('--text-light', theme.colors.textLight);
    
    root.style.setProperty('--background', theme.colors.background);
    root.style.setProperty('--card-background', theme.colors.cardBackground);
    
    root.style.setProperty('--border', theme.colors.border);
    root.style.setProperty('--border-focus', theme.colors.borderFocus);
    
    root.style.setProperty('--gray-light', theme.colors.grayLight);
    root.style.setProperty('--gray', theme.colors.gray);
    root.style.setProperty('--gray-dark', theme.colors.grayDark);
    
    root.style.setProperty('--hover', theme.colors.hover);
    
    // 财务指标颜色
    root.style.setProperty('--revenue-color', theme.colors.revenueColor);
    root.style.setProperty('--direct-cost-color', theme.colors.directCostColor);
    root.style.setProperty('--gross-profit-color', theme.colors.grossProfitColor);
    root.style.setProperty('--indirect-cost-color', theme.colors.indirectCostColor);
    root.style.setProperty('--net-profit-color', theme.colors.netProfitColor);
    
    // 圆角
    root.style.setProperty('--radius-sm', theme.borderRadius.small);
    root.style.setProperty('--radius-md', theme.borderRadius.medium);
    root.style.setProperty('--radius-lg', theme.borderRadius.large);
    
    // 阴影
    root.style.setProperty('--shadow-sm', theme.shadows.small);
    root.style.setProperty('--shadow-md', theme.shadows.medium);
    root.style.setProperty('--shadow-lg', theme.shadows.large);
    
    // 间距
    root.style.setProperty('--spacing-xs', theme.spacing.xs);
    root.style.setProperty('--spacing-sm', theme.spacing.sm);
    root.style.setProperty('--spacing-md', theme.spacing.md);
    root.style.setProperty('--spacing-lg', theme.spacing.lg);
    root.style.setProperty('--spacing-xl', theme.spacing.xl);
    root.style.setProperty('--spacing-xxl', theme.spacing.xxl);
    
    // 过渡
    root.style.setProperty('--transition-fast', theme.transitions.fast);
    root.style.setProperty('--transition-normal', theme.transitions.normal);
    root.style.setProperty('--transition-slow', theme.transitions.slow);
    
    // 布局
    root.style.setProperty('--layout-max-width', theme.layout.maxWidth);
    root.style.setProperty('--sidebar-width', theme.layout.sidebarWidth);
    root.style.setProperty('--sidebar-collapsed-width', theme.layout.sidebarCollapsedWidth);
    root.style.setProperty('--header-height', theme.layout.headerHeight);
    root.style.setProperty('--footer-height', theme.layout.footerHeight);
    root.style.setProperty('--content-padding', theme.layout.contentPadding);
  };

  // Ant Design 主题配置
  const { defaultAlgorithm, darkAlgorithm } = antdTheme;

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme, setDarkMode }}>
      <ConfigProvider
        theme={{
          algorithm: isDarkMode ? darkAlgorithm : defaultAlgorithm,
          token: {
            colorPrimary: theme.colors.primary,
            colorSuccess: theme.colors.success,
            colorWarning: theme.colors.warning,
            colorError: theme.colors.danger,
            colorInfo: theme.colors.info,
            borderRadius: parseInt(theme.borderRadius.medium),
            fontFamily: theme.typography.fontFamily,
            fontSize: parseInt(theme.typography.fontSizes.md),
            colorBgContainer: theme.colors.cardBackground,
            colorBgElevated: theme.colors.cardBackground,
            colorBorder: theme.colors.border,
            colorText: theme.colors.textPrimary,
            colorTextSecondary: theme.colors.textSecondary,
          },
          components: {
            Button: {
              paddingInline: 16,
              borderRadius: parseInt(theme.borderRadius.small),
            },
            Card: {
              borderRadius: parseInt(theme.borderRadius.medium),
            },
            Table: {
              borderRadius: parseInt(theme.borderRadius.medium),
            },
            Input: {
              borderRadius: parseInt(theme.borderRadius.small),
            },
            Select: {
              borderRadius: parseInt(theme.borderRadius.small),
            },
            Modal: {
              borderRadius: parseInt(theme.borderRadius.medium),
            },
            Drawer: {
              borderRadius: parseInt(theme.borderRadius.medium),
            },
          },
        }}
      >
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

// 使用主题的钩子
export const useTheme = () => useContext(ThemeContext);

export default ThemeContext;
