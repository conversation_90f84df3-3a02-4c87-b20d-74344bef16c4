
> backend@1.0.0 dev
> ts-node-dev --respawn --transpile-only src/index.ts

[INFO] 14:40:17 ts-node-dev ver. 1.1.8 (using ts-node ver. 9.1.1, typescript ver. 4.9.5)
Database connected successfully
Error: listen EADDRINUSE: address already in use :::5000
    at Server.setupListenHandle [as _listen2] (node:net:1937:16)
    at listenInCluster (node:net:1994:12)
    at Server.listen (node:net:2099:7)
    at Function.listen (E:\AI\LTC\v0.53\backend\node_modules\express\lib\application.js:635:24)
    at E:\AI\LTC\v0.53\backend\src\index.ts:37:9
[ERROR] 14:40:27 Error: listen EADDRINUSE: address already in use :::5000
