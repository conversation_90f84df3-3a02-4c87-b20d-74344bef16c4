import React, { useState, useEffect } from 'react';
import { Layout, Menu, Tooltip, Dropdown } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  TeamOutlined,
  FileTextOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  BankOutlined,
  DollarOutlined,
  ApartmentOutlined,
  RocketOutlined,
  ShopOutlined,
  ToolOutlined,
  UserOutlined,
  RobotOutlined,
  MessageOutlined,
  ExportOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import '../styles/sidebar.css';

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从localStorage读取菜单状态偏好，默认收起
  const [collapsed, setCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved !== null ? JSON.parse(saved) : true; // 默认收起
  });

  // 保存菜单状态到localStorage
  useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(collapsed));
  }, [collapsed]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 处理右键菜单点击
  const handleContextMenu = (event: React.MouseEvent, path: string) => {
    event.preventDefault();
    event.stopPropagation();
    
    // 创建上下文菜单
    const contextMenuItems = [
      {
        key: 'current',
        label: 'Open in Current Window',
        icon: '→',
        onClick: () => navigate(path),
      },
      {
        key: 'new-tab',
        label: 'Open in New Tab',
        icon: '⧉',
        onClick: () => window.open(`${window.location.origin}${path}`, '_blank'),
      },
      {
        key: 'new-window',
        label: 'Open in New Window',
        icon: '⬀',
        onClick: () => window.open(`${window.location.origin}${path}`, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes'),
      },
    ];

    // 显示上下文菜单
    const contextMenu = document.createElement('div');
    contextMenu.className = 'context-menu';
    contextMenu.style.top = `${event.clientY}px`;
    contextMenu.style.left = `${event.clientX}px`;

    contextMenuItems.forEach(item => {
      const menuItem = document.createElement('div');
      menuItem.className = 'context-menu-item';
      menuItem.innerHTML = `
        <span class="menu-icon">${item.icon}</span>
        <span class="menu-text">${item.label}</span>
      `;
      
      menuItem.addEventListener('click', () => {
        item.onClick();
        if (document.body.contains(contextMenu)) {
          document.body.removeChild(contextMenu);
        }
      });
      
      contextMenu.appendChild(menuItem);
    });

    document.body.appendChild(contextMenu);

    // 确保菜单在视窗内
    const rect = contextMenu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      contextMenu.style.left = `${event.clientX - rect.width}px`;
    }
    if (rect.bottom > window.innerHeight) {
      contextMenu.style.top = `${event.clientY - rect.height}px`;
    }

    // 点击其他地方关闭菜单
    const closeContextMenu = (e: MouseEvent) => {
      if (!contextMenu.contains(e.target as Node)) {
        if (document.body.contains(contextMenu)) {
          document.body.removeChild(contextMenu);
        }
        document.removeEventListener('click', closeContextMenu);
        document.removeEventListener('contextmenu', closeContextMenu);
      }
    };

    setTimeout(() => {
      document.addEventListener('click', closeContextMenu);
      document.addEventListener('contextmenu', closeContextMenu);
    }, 0);
  };

  // 创建增强的菜单项，支持右键点击
  const createMenuItem = (item: any) => ({
    ...item,
    label: collapsed ? (
      <Tooltip 
        title={`${item.label} (Right-click for more options)`} 
        placement="right"
        className="sidebar-tooltip"
      >
        <span
          onContextMenu={(e) => handleContextMenu(e, item.key)}
          style={{ display: 'block', width: '100%' }}
        >
          {item.label}
        </span>
      </Tooltip>
    ) : (
      <span
        onContextMenu={(e) => handleContextMenu(e, item.key)}
        title="Right-click for more options"
        style={{ display: 'block', width: '100%' }}
      >
        {item.label}
      </span>
    ),
  });

  // 第一组菜单项 - Dashboard
  const menuGroup1 = [
    createMenuItem({
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: 'Dashboard',
    }),
  ];

  // 第二组菜单项 - 核心业务模块
  const menuGroup2 = [
    createMenuItem({
      key: '/opportunity',
      icon: <RocketOutlined />,
      label: 'Lead to Cash',
    }),
    createMenuItem({
      key: '/client',
      icon: <UserOutlined />,
      label: 'Client',
    }),
    createMenuItem({
      key: '/finance',
      icon: <DollarOutlined className="finance-icon" />,
      label: 'Finance',
    }),
    createMenuItem({
      key: '/vendor',
      icon: <ShopOutlined />,
      label: 'Vendor',
    }),
  ];

  // 第三组菜单项 - 辅助功能
  const menuGroup3 = [
    createMenuItem({
      key: '/messages',
      icon: <MessageOutlined />,
      label: 'Messages',
    }),
    createMenuItem({
      key: '/ai',
      icon: <RobotOutlined />,
      label: 'AI Assistant',
    }),
    createMenuItem({
      key: '/system',
      icon: <ToolOutlined />,
      label: 'System',
    }),
  ];

  return (
    <Sider
      width={180}
      collapsible
      collapsed={collapsed}
      onCollapse={toggleCollapsed}
      trigger={null}
      className="sidebar"
    >
      {/* Logo */}
      <div 
        className={`sidebar-logo ${collapsed ? 'sidebar-logo-collapsed' : ''}`}
        onClick={() => navigate('/dashboard')}
        style={{ cursor: 'pointer' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', marginLeft: collapsed ? 0 : '12px' }}>
          <AppstoreOutlined
            className={`sidebar-logo-icon ${collapsed ? 'sidebar-logo-icon-collapsed' : ''}`}
          />
          {!collapsed && (
            <h1 className="sidebar-logo-text">
              SmartLTC
            </h1>
          )}
        </div>
      </div>

      {/* 折叠按钮 */}
      <div
        className="sidebar-trigger"
        onClick={toggleCollapsed}
      >
        {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
      </div>

      {/* 菜单 */}
      <div className={`sidebar-menu ${collapsed ? 'sidebar-collapsed' : ''}`}>
        {/* 第一组 - Dashboard */}
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuGroup1}
          onClick={({ key }) => navigate(key)}
        />
        
        {/* 第一个分割线 */}
        <div className="sidebar-divider"></div>
        
        {/* 第二组 - 核心业务模块 */}
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
          defaultOpenKeys={['/opportunity', '/finance']}
          items={menuGroup2}
        onClick={({ key }) => {
          // 处理导航逻辑
          if (key === '/projects') {
            navigate('/projects');
          } else if (key === '/leads') {
            navigate('/leads');
          } else if (key === '/customers') {
            navigate('/customers');
          } else {
            navigate(key);
          }
        }}
      />
        
        {/* 第二个分割线 */}
        <div className="sidebar-divider"></div>
        
        {/* 第三组 - 辅助功能 */}
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          defaultOpenKeys={['/ai']}
          items={menuGroup3}
          onClick={({ key }) => navigate(key)}
        />
      </div>
    </Sider>
  );
};

export default Sidebar;
