import { useEffect, useRef, useState, useCallback } from 'react';
import { message } from 'antd';

// 暂时使用自定义类型定义来避免兼容性问题
declare global {
  interface Window {
    io: any;
  }
}

interface SocketInterface {
  on: (event: string, callback: (...args: any[]) => void) => void;
  emit: (event: string, data?: any) => void;
  disconnect: () => void;
  connected: boolean;
}

interface OnlineUser {
  userId: string;
  username: string;
  avatar?: string;
  lastSeen: Date;
}

interface Activity {
  id: string;
  type: string;
  title: string;
  description?: string;
  user: {
    id: string;
    username: string;
    avatar?: string;
  };
  timestamp: Date;
  metadata?: any;
}

interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar?: string;
  };
  timestamp: Date;
  stageId?: string;
}

interface EditingUser {
  userId: string;
  username: string;
  section: string;
  isEditing: boolean;
}

interface UseWebSocketOptions {
  projectId?: string;
  onActivity?: (activity: Activity) => void;
  onComment?: (comment: Comment) => void;
  onUserJoined?: (user: OnlineUser) => void;
  onUserLeft?: (user: { userId: string; username: string }) => void;
  onOnlineUsersUpdate?: (users: OnlineUser[]) => void;
  onUserEditing?: (editingUser: EditingUser) => void;
  onPermissionChanged?: (data: any) => void;
  onMentioned?: (data: any) => void;
}

export const useWebSocket = (options: UseWebSocketOptions = {}) => {
  const socketRef = useRef<SocketInterface | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [editingUsers, setEditingUsers] = useState<EditingUser[]>([]);

  const {
    projectId,
    onActivity,
    onComment,
    onUserJoined,
    onUserLeft,
    onOnlineUsersUpdate,
    onUserEditing,
    onPermissionChanged,
    onMentioned
  } = options;

  // 连接WebSocket
  const connect = useCallback(() => {
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        console.warn('No auth token found, skipping WebSocket connection');
        return;
      }

             // 动态导入socket.io-client以避免编译时类型错误
       import('socket.io-client').then((socketIO) => {
         const io = socketIO.default || socketIO;
        const socket = io(process.env.REACT_APP_WS_URL || 'http://localhost:5002', {
          auth: { token },
          transports: ['websocket']
        });

        socketRef.current = socket as any;

        // 连接事件
        socket.on('connect', () => {
          setIsConnected(true);
          console.log('WebSocket connected');
        });

        socket.on('disconnect', () => {
          setIsConnected(false);
          console.log('WebSocket disconnected');
        });

        socket.on('connect_error', (error: any) => {
          console.error('WebSocket connection error:', error);
          message.error('实时连接失败，某些功能可能不可用');
        });

        // 在线用户事件
        socket.on('user-joined', (user: OnlineUser) => {
          setOnlineUsers(prev => [...prev.filter(u => u.userId !== user.userId), user]);
          onUserJoined?.(user);
          message.info(`${user.username} 加入了项目`);
        });

        socket.on('user-left', (user: { userId: string; username: string }) => {
          setOnlineUsers(prev => prev.filter(u => u.userId !== user.userId));
          onUserLeft?.(user);
        });

        socket.on('online-users', (users: OnlineUser[]) => {
          setOnlineUsers(users);
          onOnlineUsersUpdate?.(users);
        });

        socket.on('online-users-updated', (users: OnlineUser[]) => {
          setOnlineUsers(users);
          onOnlineUsersUpdate?.(users);
        });

        // 活动事件
        socket.on('activity-update', (activity: Activity) => {
          setActivities(prev => [activity, ...prev.slice(0, 49)]); // 保留最近50条
          onActivity?.(activity);
        });

        // 评论事件
        socket.on('comment-added', (comment: Comment) => {
          onComment?.(comment);
        });

        // 编辑状态事件
        socket.on('user-editing', (editingUser: EditingUser) => {
          setEditingUsers(prev => {
            const filtered = prev.filter(u => u.userId !== editingUser.userId || u.section !== editingUser.section);
            return editingUser.isEditing ? [...filtered, editingUser] : filtered;
          });
          onUserEditing?.(editingUser);
        });

        // 权限变更事件
        socket.on('permission-changed', (data: any) => {
          message.info(`您的项目权限已更新为: ${data.newRole}`);
          onPermissionChanged?.(data);
        });

        socket.on('collaborator-updated', (data: any) => {
          // 协作者权限更新通知
        });

        // 提及事件
        socket.on('mentioned', (data: any) => {
          message.info(`${data.comment.author.username} 在项目中提及了您`);
          onMentioned?.(data);
        });

      }).catch((error) => {
        console.error('Failed to load socket.io-client:', error);
        message.warning('实时协作功能暂时不可用');
      });
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      message.warning('实时协作功能暂时不可用');
    }
  }, [onActivity, onComment, onUserJoined, onUserLeft, onOnlineUsersUpdate, onUserEditing, onPermissionChanged, onMentioned]);

  // 断开连接
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, []);

  // 加入项目房间
  const joinProject = useCallback((projectId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join-project', projectId);
    }
  }, []);

  // 离开项目房间
  const leaveProject = useCallback((projectId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('leave-project', projectId);
    }
  }, []);

  // 发送活动
  const sendActivity = useCallback((data: {
    projectId: string;
    type: string;
    title: string;
    description?: string;
    metadata?: any;
  }) => {
    if (socketRef.current) {
      socketRef.current.emit('project-activity', data);
    }
  }, []);

  // 发送编辑状态
  const sendEditingStatus = useCallback((data: {
    projectId: string;
    section: string;
    isEditing: boolean;
  }) => {
    if (socketRef.current) {
      socketRef.current.emit('editing-status', data);
    }
  }, []);

  // 发送评论
  const sendComment = useCallback((data: {
    projectId: string;
    stageId?: string;
    content: string;
    mentions?: string[];
  }) => {
    if (socketRef.current) {
      socketRef.current.emit('new-comment', data);
    }
  }, []);

  // 发送权限更新
  const sendPermissionUpdate = useCallback((data: {
    projectId: string;
    targetUserId: string;
    newRole: string;
    permissions: any;
  }) => {
    if (socketRef.current) {
      socketRef.current.emit('permission-updated', data);
    }
  }, []);

  // 初始化连接
  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // 自动加入/离开项目
  useEffect(() => {
    if (projectId && isConnected) {
      joinProject(projectId);
      return () => {
        leaveProject(projectId);
      };
    }
  }, [projectId, isConnected, joinProject, leaveProject]);

  return {
    isConnected,
    onlineUsers,
    activities,
    editingUsers,
    connect,
    disconnect,
    joinProject,
    leaveProject,
    sendActivity,
    sendEditingStatus,
    sendComment,
    sendPermissionUpdate
  };
}; 