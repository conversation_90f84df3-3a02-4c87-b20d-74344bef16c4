@echo off
echo ===================================
echo GitHub 初始化脚本
echo ===================================
echo.

REM 检查是否已安装Git
git --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未检测到Git安装。请先安装Git: https://git-scm.com/downloads
    exit /b 1
)

echo 正在初始化Git仓库...
git init
if %ERRORLEVEL% NEQ 0 (
    echo 错误: Git初始化失败
    exit /b 1
)

echo 正在添加文件到暂存区...
git add .
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 添加文件失败
    exit /b 1
)

echo.
set /p COMMIT_MSG=请输入初始提交信息 (默认: "初始化项目"): 
if "%COMMIT_MSG%"=="" set COMMIT_MSG=初始化项目

echo 正在提交更改...
git commit -m "%COMMIT_MSG%"
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 提交更改失败
    exit /b 1
)

echo.
set /p GITHUB_USERNAME=请输入你的GitHub用户名: 
if "%GITHUB_USERNAME%"=="" (
    echo 错误: GitHub用户名不能为空
    exit /b 1
)

set /p REPO_NAME=请输入GitHub仓库名 (默认: "ltc-project"): 
if "%REPO_NAME%"=="" set REPO_NAME=ltc-project

echo.
echo 正在添加远程仓库...
git remote add origin https://github.com/%GITHUB_USERNAME%/%REPO_NAME%.git
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 添加远程仓库失败
    exit /b 1
)

echo.
echo ===================================
echo 初始化完成！
echo ===================================
echo.
echo 请确保你已在GitHub上创建了名为 %REPO_NAME% 的仓库
echo 然后运行以下命令推送代码到GitHub:
echo git push -u origin master
echo.
echo 更多详细信息请参考 GITHUB_SETUP_GUIDE.md
echo.
pause