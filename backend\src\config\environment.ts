/**
 * 环境配置
 * 用于区分开发环境和生产环境
 */

// 环境类型
export enum Environment {
  DEVELOPMENT = 'development',
  PRODUCTION = 'production',
  TEST = 'test'
}

// 当前环境
export const NODE_ENV = process.env.NODE_ENV || Environment.DEVELOPMENT;

// 是否为开发环境
export const isDevelopment = NODE_ENV === Environment.DEVELOPMENT;

// 是否为生产环境
export const isProduction = NODE_ENV === Environment.PRODUCTION;

// 是否为测试环境
export const isTest = NODE_ENV === Environment.TEST;

// 安全配置
export const securityConfig = {
  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your_jwt_secret_key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  
  // CORS配置
  cors: {
    origin: isProduction 
      ? process.env.CORS_ORIGIN || 'https://your-production-domain.com'
      : 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'x-request-id',
      'X-Requested-With',
      'x-csrf-token'
    ]
  },
  
  // 密码哈希配置
  password: {
    saltRounds: 10
  },
  
  // 开发模式下的安全绕过
  bypassAuth: isDevelopment,
  
  // 请求限制配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: isProduction ? 100 : 1000, // 生产环境限制更严格
    message: '请求过于频繁，请稍后再试'
  }
};

// 服务器配置
export const serverConfig = {
  port: process.env.PORT || 5002,
  host: process.env.HOST || 'localhost'
};

// 数据库配置
export const databaseConfig = {
  type: process.env.DB_TYPE || 'sqlite',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  database: process.env.DB_DATABASE || 'ltc_project',
  synchronize: !isProduction, // 生产环境不自动同步数据库结构
  logging: isDevelopment
};

// 日志配置
export const loggingConfig = {
  level: isProduction ? 'info' : 'debug',
  format: isProduction ? 'json' : 'pretty',
  enabled: true
};

// 导出默认配置
export default {
  environment: NODE_ENV,
  isDevelopment,
  isProduction,
  isTest,
  security: securityConfig,
  server: serverConfig,
  database: databaseConfig,
  logging: loggingConfig
};
