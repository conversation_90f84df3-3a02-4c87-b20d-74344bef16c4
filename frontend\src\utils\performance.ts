export class Performance {
  private static readonly METRICS_KEY = 'performance_metrics';
  private static readonly MAX_METRICS = 100;

  static measure(name: string, startTime: number) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.saveMetric(name, duration);
    return duration;
  }

  private static saveMetric(name: string, duration: number) {
    try {
      const metrics = this.loadMetrics();
      metrics.push({
        name,
        duration,
        timestamp: Date.now()
      });

      // 只保留最近的指标
      if (metrics.length > this.MAX_METRICS) {
        metrics.shift();
      }

      localStorage.setItem(this.METRICS_KEY, JSON.stringify(metrics));
    } catch (error) {
      console.error('保存性能指标失败:', error);
    }
  }

  private static loadMetrics(): Array<{ name: string; duration: number; timestamp: number }> {
    try {
      const metrics = localStorage.getItem(this.METRICS_KEY);
      return metrics ? JSON.parse(metrics) : [];
    } catch (error) {
      console.error('加载性能指标失败:', error);
      return [];
    }
  }

  static getMetrics() {
    return this.loadMetrics();
  }

  static clearMetrics() {
    localStorage.removeItem(this.METRICS_KEY);
  }

  static getAverageDuration(name: string): number {
    const metrics = this.loadMetrics();
    const filteredMetrics = metrics.filter(m => m.name === name);
    if (filteredMetrics.length === 0) return 0;

    const total = filteredMetrics.reduce((sum, m) => sum + m.duration, 0);
    return total / filteredMetrics.length;
  }
} 