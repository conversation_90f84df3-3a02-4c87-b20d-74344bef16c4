# LTC 项目管理系统

这是一个基于 React + Node.js + PostgreSQL 的 LTC（Lead to Cash）项目管理系统，用于跟踪项目从线索到现金的完整流程。

## 功能特点

- 项目管理：创建、查看、更新和删除项目
- 项目跟踪：跟踪项目从线索到现金的完整流程（基本信息、提案、合同、执行、验收、财务、结项、售后）
- 用户认证：用户注册和登录
- 权限控制：基于角色的访问控制
- 数据统计：项目状态和进度统计

## 技术栈

### 前端
- React.js
- TypeScript
- Ant Design
- React Router
- Axios

### 后端
- Node.js
- Express.js
- TypeScript
- TypeORM
- PostgreSQL
- JWT 认证

## 开发环境设置

### 前提条件
- Node.js (v14+)
- PostgreSQL (v12+)
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd ltc-project
```

2. 安装后端依赖
```bash
cd backend
npm install
```

3. 配置后端环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，设置数据库连接信息和JWT密钥
```

4. 启动后端服务器
```bash
npm run dev
```

5. 安装前端依赖
```bash
cd ../frontend
npm install
```

6. 启动前端开发服务器
```bash
npm start
```

## 项目结构

```
ltc-project/
├── backend/                # Node.js 后端项目
│   ├── src/
│   │   ├── config/         # 配置文件
│   │   ├── controllers/    # 控制器
│   │   ├── entities/       # 数据库实体模型
│   │   ├── middlewares/    # 中间件
│   │   ├── routes/         # 路由
│   │   ├── services/       # 服务
│   │   └── index.ts        # 入口文件
│   ├── .env                # 环境变量
│   └── package.json        # 依赖配置
│
├── frontend/               # React 前端项目
│   ├── public/             # 静态资源
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/          # 页面
│   │   ├── services/       # API 服务
│   │   ├── store/          # 状态管理
│   │   ├── types/          # 类型定义
│   │   ├── utils/          # 工具函数
│   │   └── App.tsx         # 主应用组件
│   ├── .env                # 环境变量
│   └── package.json        # 依赖配置
│
└── README.md               # 项目说明
```

## API 文档

### 认证 API
- POST /api/auth/register - 用户注册
- POST /api/auth/login - 用户登录

### 项目 API
- GET /api/projects - 获取所有项目
- GET /api/projects/:id - 获取单个项目
- POST /api/projects - 创建新项目
- PUT /api/projects/:id - 更新项目
- DELETE /api/projects/:id - 删除项目

### 项目阶段 API
- GET /api/project-stages/project/:projectId - 获取项目的所有阶段
- GET /api/project-stages/:id - 获取单个阶段
- POST /api/project-stages - 创建新阶段
- PUT /api/project-stages/:id - 更新阶段
- DELETE /api/project-stages/:id - 删除阶段

## 使用指南

1. 注册/登录系统
2. 在仪表盘查看项目统计信息
3. 在项目管理页面创建、查看、编辑和删除项目
4. 在项目详情页面管理项目的各个阶段

## 快速启动

使用提供的启动脚本可以快速启动整个系统：

```bash
# 在项目根目录下运行
.\start-ltc.bat
```

启动脚本提供了多种后端服务启动选项：

1. **简单服务器**：适用于测试登录和基本功能
2. **完整后端服务**：使用npm run dev启动完整的开发环境
3. **PM2管理的简单服务器**：使用PM2进程管理工具提高稳定性
4. **PM2管理的健壮服务器**：使用增强版服务器，具有错误处理、日志记录和自动恢复功能

## 后端服务稳定性问题及解决方案

### 常见问题

后端服务可能会因以下原因出现中断：

1. 未捕获的异常导致进程崩溃
2. 内存泄漏导致内存使用过高
3. 连接管理问题
4. 系统资源限制

### 解决方案

1. **使用PM2进程管理**

   PM2是一个Node.js应用的进程管理工具，可以保持应用持续运行，并在崩溃时自动重启。

   ```bash
   # 安装PM2
   npm install -g pm2

   # 启动应用
   pm2 start robust-server.js --name ltc-robust

   # 查看状态
   pm2 status

   # 查看日志
   pm2 logs ltc-robust
   ```

2. **健壮服务器**

   项目中提供了一个增强版的服务器实现（robust-server.js），具有以下特性：

   - 全局错误处理
   - 未捕获异常处理
   - 内存监控和自动重启
   - 详细日志记录
   - 优雅关闭

3. **日志记录**

   健壮服务器会在`backend/logs`目录下生成详细的日志文件：

   - `access-YYYY-MM-DD.log`：访问日志
   - `error-YYYY-MM-DD.log`：错误日志

## PM2常用命令

```bash
# 启动应用
pm2 start <script> --name <app_name>

# 查看所有应用状态
pm2 status

# 查看应用日志
pm2 logs <app_name>

# 重启应用
pm2 restart <app_name>

# 停止应用
pm2 stop <app_name>

# 删除应用
pm2 delete <app_name>

# 设置开机自启
pm2 startup

# 保存当前应用列表
pm2 save
```

## 许可证

MIT
