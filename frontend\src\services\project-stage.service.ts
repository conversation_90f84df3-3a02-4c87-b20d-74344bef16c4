import api from './api';
import { ProjectStage } from '../types';

// 本地存储键
const STORAGE_KEY = 'project_stages';

class ProjectStageService {
  private projectStages: ProjectStage[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载数据
  private loadFromStorage() {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (data) {
        this.projectStages = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading project stages from localStorage:', error);
      this.projectStages = [];
    }
  }

  // 保存到localStorage
  private saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.projectStages));
    } catch (error) {
      console.error('Error saving project stages to localStorage:', error);
    }
  }

  async getStagesByProjectId(projectId: string): Promise<ProjectStage[]> {
    try {
      const response = await api.get<ProjectStage[]>(`/project-stages/project/${projectId}`);
      return response;
    } catch (error) {
      console.error('Failed to fetch project stages from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      return this.projectStages.filter(stage => stage.projectId === projectId);
    }
  }

  async getStageById(id: string): Promise<ProjectStage> {
    try {
      const response = await api.get<ProjectStage>(`/project-stages/${id}`);
      return response;
    } catch (error) {
      console.error('Failed to fetch project stage from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      const stage = this.projectStages.find(s => s.id === id);
      if (stage) {
        return stage;
      }
      throw new Error('Project stage not found');
    }
  }

  async createStage(stage: Partial<ProjectStage> & { projectId: string }): Promise<ProjectStage> {
    try {
      const response = await api.post<ProjectStage>('/project-stages', stage);
      return response;
    } catch (error) {
      console.error('Failed to create project stage via API:', error);
      // 本地创建项目阶段
      const newStage: ProjectStage = {
        id: `stage_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId: stage.projectId,
        name: stage.name || 'New Stage',
        type: stage.type || 'basic_info',
        status: stage.status || 'not_started',
        startDate: stage.startDate,
        endDate: stage.endDate,
        notes: stage.notes,
        data: stage.data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      this.projectStages.push(newStage);
      this.saveToStorage();
      
      return newStage;
    }
  }

  async updateStage(id: string, stage: Partial<ProjectStage>): Promise<ProjectStage> {
    try {
      const response = await api.put<ProjectStage>(`/project-stages/${id}`, stage);
      return response;
    } catch (error) {
      console.error('Failed to update project stage via API:', error);
      // 本地更新项目阶段
      const stageIndex = this.projectStages.findIndex(s => s.id === id);
      if (stageIndex !== -1) {
        this.projectStages[stageIndex] = {
          ...this.projectStages[stageIndex],
          ...stage,
          updatedAt: new Date().toISOString()
        };
        this.saveToStorage();
        return this.projectStages[stageIndex];
      }
      throw new Error('Project stage not found');
    }
  }

  async deleteStage(id: string): Promise<void> {
    try {
      await api.delete(`/project-stages/${id}`);
    } catch (error) {
      console.error('Failed to delete project stage via API:', error);
      // 本地删除项目阶段
      this.projectStages = this.projectStages.filter(s => s.id !== id);
      this.saveToStorage();
    }
  }
}

export default new ProjectStageService();
