{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "esnext.asynciterable"], "skipLibCheck": true, "sourceMap": true, "outDir": "./dist", "moduleResolution": "node", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "baseUrl": ".", "typeRoots": ["./node_modules/@types", "./src/types"], "strict": false}, "exclude": ["node_modules"], "include": ["./src/**/*.ts", "./src/**/*.d.ts"]}