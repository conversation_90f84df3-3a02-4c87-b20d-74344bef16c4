import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  InputNumber,
  Select, 
  Row, 
  Col, 
  Button, 
  message, 
  Typography,
  Divider,
  Slider,
  Switch,
  Tag,
  Alert
} from 'antd';
import { 
  AudioOutlined, 
  SaveOutlined,
  RightOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';
import './BasicInfo.css';

const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface BasicInfoProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
  id?: string;
}

const BasicInfo: React.FC<BasicInfoProps> = ({ project, stage, onSave, onProceed, id }) => {
  const [clientForm] = Form.useForm();
  const [opportunityForm] = Form.useForm();
  const [competitorForm] = Form.useForm();
  const [otherForm] = Form.useForm();

  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [activeModule, setActiveModule] = useState('client');
  const [winProbabilityValue, setWinProbabilityValue] = useState(75);
  const [hasCompetitors, setHasCompetitors] = useState(false);
  const [competitorTags, setCompetitorTags] = useState<string[]>([]);

  // 项目数据自动映射
  useEffect(() => {
    console.log('=== BasicInfo useEffect triggered ===');
    console.log('Stage:', stage);
    console.log('Project:', project);
    console.log('ID:', id);
    console.log('Active Module:', activeModule);

    // 🔧 首先强制添加CSS样式启用所有输入框
    const styleId = 'basic-info-force-enable-style';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* 强制启用Basic Info页面的所有输入框 */
        #basic-info-form .ant-input,
        #basic-info-form .ant-input-number-input,
        #basic-info-form .ant-select-selector,
        #basic-info-form textarea,
        #basic-info-form .form-input {
          pointer-events: auto !important;
          cursor: text !important;
          background-color: white !important;
          color: rgba(0, 0, 0, 0.88) !important;
          border-color: #d9d9d9 !important;
          opacity: 1 !important;
        }
        
        #basic-info-form .ant-input[disabled],
        #basic-info-form .ant-input-number-input[disabled],
        #basic-info-form .form-input[disabled],
        #basic-info-form textarea[disabled] {
          pointer-events: auto !important;
          cursor: text !important;
          background-color: white !important;
          color: rgba(0, 0, 0, 0.88) !important;
          opacity: 1 !important;
        }
        
        #basic-info-form .ant-input:hover,
        #basic-info-form .ant-input:focus,
        #basic-info-form textarea:hover,
        #basic-info-form textarea:focus {
          border-color: #4096ff !important;
          box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1) !important;
        }
      `;
      document.head.appendChild(style);
      console.log('✅ Force enable CSS styles added to document head');
    }

    // 数据优先级：阶段数据 > 项目数据 > 空值
    let formData = {};

    // 1. 首先从项目数据自动映射基础信息
    if (project) {
      console.log('Auto-mapping project data to form fields...');
      
      // 客户信息映射
      const clientData = {
        clientName: project.client,
        clientTier: project.tier === 'S' ? 'svip' : 
                   project.tier === 'V' ? 'vip' : 
                   project.tier === 'B' ? 'ba' : 
                   project.tier === 'A' ? 'a' : 
                   project.tier || 'a',
        address: '', // 地址信息需要用户手动填写
        industry: project.category === 'Software' ? 'technology' :
                 project.category === 'Service' ? 'finance' :
                 project.category === 'Product' ? 'manufacturing' : 'technology',
        keyStakeholder: '', // 关键利益相关者需要用户手动填写
        primaryContact: project.owner,
        contactPhone: '', // 联系电话需要用户手动填写
        email: '' // 邮箱需要用户手动填写
      };

                    // 机会信息映射
      const opportunityData = {
        opportunityName: project.name,
        totalRevenue: project.revenue || 0, // 使用项目的 revenue 字段
        currency: 'EUR', // 默认欧元
        expectedCloseDate: null, // 预期关闭日期需要用户手动填写
        currentStage: project.stage,
        competitivePosition: '', // 竞争地位需要用户手动填写
        keyRequirements: '', // 关键需求需要用户手动填写
        decisionCriteria: '' // 决策标准需要用户手动填写
      };

      formData = { ...clientData, ...opportunityData };
      console.log('Auto-mapped form data:', formData);

      // 设置表单值
      console.log('Setting client form values:', clientData);
      clientForm.setFieldsValue(clientData);
      opportunityForm.setFieldsValue(opportunityData);
      
      // 确保表单字段可编辑 - 强制启用
      setTimeout(() => {
        console.log('=== FORCE ENABLING FORM FIELDS ===');
        const allInputs = document.querySelectorAll('input[disabled], textarea[disabled]');
        console.log('Found disabled inputs:', allInputs.length);
        allInputs.forEach((input: any) => {
          input.disabled = false;
          input.readOnly = false;
          input.style.pointerEvents = 'auto';
          input.style.cursor = 'text';
          console.log('Enabled input:', input);
        });
        
        // 强制重新渲染表单
        clientForm.validateFields().catch(() => {});
        console.log('Client form fields should be editable now');
      }, 200);

      // 设置概率滑块的值
      if (typeof project.probability === 'string') {
        const probabilityValue = project.probability === 'A' ? 90 :
                                project.probability === 'B' ? 75 :
                                project.probability === 'C' ? 55 :
                                project.probability === 'D' ? 30 : 75;
        setWinProbabilityValue(probabilityValue);
      } else if (typeof project.probability === 'number') {
        setWinProbabilityValue(project.probability);
      }

      console.log('✅ Successfully auto-filled forms with project data');
    }

    // 2. 优先从localStorage加载最新数据
    if (id) {
      try {
        const stageStorageKey = `project_stages_${id}`;
        const localStagesData = localStorage.getItem(stageStorageKey);
        if (localStagesData) {
          const parsedLocalStages = JSON.parse(localStagesData);
          const basicInfoStage = parsedLocalStages.find((stage: any) => stage.type === 'basic_info');
          
          if (basicInfoStage && basicInfoStage.data) {
            console.log('Loading data from localStorage basicInfoStage:', basicInfoStage);
            const localStageData = typeof basicInfoStage.data === 'string' ? JSON.parse(basicInfoStage.data) : basicInfoStage.data;
            console.log('Parsed localStorage stage data:', localStageData);

            // 设置所有模块的表单数据
            clientForm.setFieldsValue({
              clientName: localStageData.clientName,
              clientTier: localStageData.clientTier,
              address: localStageData.address,
              industry: localStageData.industry,
              keyStakeholder: localStageData.keyStakeholder,
              primaryContact: localStageData.primaryContact,
              contactPhone: localStageData.contactPhone,
              email: localStageData.email
            });
            
            opportunityForm.setFieldsValue({
              opportunityName: localStageData.opportunityName,
              totalRevenue: localStageData.totalRevenue,
              currency: localStageData.currency,
              expectedCloseDate: localStageData.expectedCloseDate,
              currentStage: localStageData.currentStage,
              competitivePosition: localStageData.competitivePosition,
              keyRequirements: localStageData.keyRequirements,
              decisionCriteria: localStageData.decisionCriteria
            });
            
            competitorForm.setFieldsValue({
              competitorNames: localStageData.competitorNames,
              competitorNotes: localStageData.competitorNotes
            });
            
            otherForm.setFieldsValue({
              source: localStageData.source,
              tags: localStageData.tags,
              clientAnalysis: localStageData.clientAnalysis
            });

            // 恢复状态
            if (localStageData.winProbability !== undefined) {
              setWinProbabilityValue(localStageData.winProbability);
            }
            if (localStageData.hasCompetitors !== undefined) {
              setHasCompetitors(localStageData.hasCompetitors);
            }
            if (localStageData.competitorNames && Array.isArray(localStageData.competitorNames)) {
              setCompetitorTags(localStageData.competitorNames);
            }

            console.log('✅ Successfully loaded data from localStorage');
            return; // 如果从localStorage加载成功，不再处理其他数据源
          }
        }
      } catch (localStorageError) {
        console.warn('Failed to load from localStorage:', localStorageError);
      }
    }

    // 3. 如果有阶段数据，则覆盖项目数据（阶段数据优先级更高）
    if (stage?.data) {
      try {
        console.log('Loading existing stage data...');
        const stageData = JSON.parse(stage.data);
        console.log('Parsed stage data:', stageData);
        
        // 合并数据：阶段数据覆盖自动映射的数据
        const mergedData = { ...formData, ...stageData };
        
        // 根据模块设置对应的表单
        if (stageData.module === 'client' || !stageData.module) {
          clientForm.setFieldsValue(mergedData);
        } else if (stageData.module === 'opportunity') {
          opportunityForm.setFieldsValue(mergedData);
          if (mergedData.winProbability) {
            setWinProbabilityValue(mergedData.winProbability);
          }
        } else if (stageData.module === 'competitor') {
          competitorForm.setFieldsValue(mergedData);
          // 恢复竞争对手相关状态
          if (mergedData.hasCompetitors !== undefined) {
            setHasCompetitors(mergedData.hasCompetitors);
          }
          if (mergedData.competitorNames && Array.isArray(mergedData.competitorNames)) {
            setCompetitorTags(mergedData.competitorNames);
          }
        } else if (stageData.module === 'other') {
          otherForm.setFieldsValue(mergedData);
        }

        console.log('✅ Successfully loaded existing stage data');
      } catch (error) {
        console.error('❌ Error parsing stage data:', error);
      }
    }

    // 3. 如果既没有阶段数据也没有项目数据，显示提示信息
    if (!project && !stage?.data) {
      console.log('ℹ️ No existing data found, forms will be empty');
    }

  }, [stage, project, clientForm, opportunityForm, competitorForm, otherForm, id, activeModule]);

  const handleSubmit = async (values: any) => {
    try {
      // 收集所有模块的数据
      const clientData = clientForm.getFieldsValue();
      const opportunityData = opportunityForm.getFieldsValue();
      const competitorData = competitorForm.getFieldsValue();
      const otherData = otherForm.getFieldsValue();

      const dataToSave = {
        ...clientData,
        ...opportunityData,
        ...competitorData,
        ...otherData,
        ...values, // 当前提交的数据覆盖其他
        type: 'basic_info',
        module: activeModule,
        status: 'completed',
        updatedAt: new Date().toISOString(),
        // 包含概率滑块的值
        winProbability: winProbabilityValue,
        // 包含竞争对手相关状态
        hasCompetitors: hasCompetitors,
        competitorNames: competitorTags
      };

      console.log('BasicInfo handleSubmit - dataToSave:', dataToSave);
      console.log('BasicInfo handleSubmit - activeModule:', activeModule);
      
      // 确保数据保存到localStorage
      if (id) {
        try {
          const stageStorageKey = `project_stages_${id}`;
          const existingStages = JSON.parse(localStorage.getItem(stageStorageKey) || '[]');
          
          // 查找或创建basic_info阶段
          let updatedStages = [...existingStages];
          const basicInfoStageIndex = updatedStages.findIndex(stage => stage.type === 'basic_info');
          
          const stageData = {
            id: basicInfoStageIndex >= 0 ? updatedStages[basicInfoStageIndex].id : `local_basic_info_${Date.now()}`,
            projectId: id,
            name: 'Basic Information',
            type: 'basic_info',
            status: 'completed',
            data: JSON.stringify(dataToSave),
            createdAt: basicInfoStageIndex >= 0 ? updatedStages[basicInfoStageIndex].createdAt : new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          if (basicInfoStageIndex >= 0) {
            updatedStages[basicInfoStageIndex] = stageData;
          } else {
            updatedStages.push(stageData);
          }
          
          localStorage.setItem(stageStorageKey, JSON.stringify(updatedStages));
          console.log('BasicInfo data saved to localStorage:', stageStorageKey, stageData);
          
          // 也保存到项目级别的localStorage以确保数据一致性
          const allProjects = JSON.parse(localStorage.getItem('projects') || '[]');
          const updatedProjects = allProjects.map((proj: any) => {
            if (proj.id === id) {
              return {
                ...proj,
                ...(dataToSave.clientName && { client: dataToSave.clientName }),
                ...(dataToSave.totalRevenue && { revenue: dataToSave.totalRevenue }),
                ...(dataToSave.clientTier && { tier: dataToSave.clientTier }),
                ...(dataToSave.country && { country: dataToSave.country }),
                updatedAt: new Date().toISOString()
              };
            }
            return proj;
          });
          localStorage.setItem('projects', JSON.stringify(updatedProjects));
          console.log('BasicInfo project data synchronized in localStorage');
        } catch (storageError) {
          console.error('Failed to save BasicInfo data to localStorage:', storageError);
        }
      }
      
      if (onSave) {
        await onSave(dataToSave);
        console.log('BasicInfo data passed to onSave callback');
      }
      
      message.success({
        content: 'Information saved successfully!',
        key: 'stage-save-success',
        duration: 2,
      });
    } catch (error) {
      console.error('Error saving BasicInfo data:', error);
      message.error('Failed to save information');
    }
  };

  const handleProceed = () => {
    if (onProceed) {
      onProceed('proposal'); // 下一个阶段是Proposal
    }
  };

  const handleVoiceButtonClick = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      setTranscript('Recording started, please describe your project...');
      message.info('Recording started, please describe your project...');
      
      // 模拟AI语音识别和自动填充
      setTimeout(() => {
        setIsRecording(false);
        const mockTranscript = "This is a cloud migration project for Acme Corporation, estimated revenue €1.5M, 80% success probability, 6-month timeline.";
        setTranscript(mockTranscript);

        // AI自动填充表单
        clientForm.setFieldsValue({
          clientName: 'Acme Corporation',
          clientTier: 'vip',
          industry: 'technology',
          primaryContact: 'Jane Doe (CTO)',
          email: '<EMAIL>',
          contactPhone: '+****************',
        });

        opportunityForm.setFieldsValue({
          opportunityName: 'Cloud Migration Project',
          totalRevenue: 1500000,
          currency: 'EUR',
          currentStage: 'Discovery',
          keyRequirements: 'Cloud migration with AWS/Azure, microservices architecture',
          decisionCriteria: 'Cost effectiveness, scalability, reliability'
        });

        setWinProbabilityValue(80);

        message.success('AI successfully extracted key information and filled the form');
      }, 2000);
    } else {
      setTranscript('Voice recording stopped.');
    }
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#F9FAFB', 
      minHeight: '100vh' 
    }}>
      {/* AI Voice Collection Card */}
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
          border: '1px solid #E5E7EB',
          marginBottom: '16px'
        }}
        bodyStyle={{ padding: '16px' }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Button
            type="primary"
            shape="circle"
            icon={<AudioOutlined />}
            size="large"
            onClick={handleVoiceButtonClick}
            danger={isRecording}
            style={{
              width: '40px',
              height: '40px',
              fontSize: '18px'
            }}
          />
          <div style={{ flex: 1 }}>
            <Title level={5} style={{ margin: 0, marginBottom: '4px', fontSize: '15px' }}>
              🎤 AI Voice Information Collection
            </Title>
            <Text type="secondary" style={{ fontSize: '14px' }}>
              Click the microphone to record your project. Key information will be extracted automatically by AI.
            </Text>
            {transcript && (
              <div style={{ 
                marginTop: '12px', 
                padding: '12px', 
                backgroundColor: '#F3F4F6', 
                borderRadius: '6px',
                fontSize: '13px'
              }}>
                {transcript}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Module Navigation */}
      <Card 
        style={{ 
          borderRadius: '8px',
          boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
          border: '1px solid #E5E7EB',
          marginBottom: '16px'
        }}
        bodyStyle={{ padding: '12px' }}
      >
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          {[
            { key: 'client', label: 'Client Information', color: '#3B82F6' },
            { key: 'opportunity', label: 'Opportunity Information', color: '#FF7A00' },
            { key: 'competitor', label: 'Competitor Information', color: '#10B981' },
            { key: 'other', label: 'Other Information', color: '#6B7280' }
          ].map(item => (
            <Button
              key={item.key}
              type={activeModule === item.key ? 'primary' : 'default'}
              onClick={() => {
                console.log(`Switching to module: ${item.key}`);
                setActiveModule(item.key);
                
                // 如果切换到client模块，确保表单可编辑
                if (item.key === 'client') {
                  console.log('Enabling client form for editing');
                  // 强制重置表单状态
                  setTimeout(() => {
                    const formFields = clientForm.getFieldsValue();
                    console.log('Current client form values:', formFields);
                  }, 50);
                }
              }}
              style={{
                borderRadius: '6px',
                height: '32px',
                fontSize: '13px',
                ...(activeModule === item.key ? { backgroundColor: item.color, borderColor: item.color } : {})
              }}
            >
              {item.label}
            </Button>
          ))}
        </div>
      </Card>

      {/* Client Information Module */}
      {activeModule === 'client' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>👤 Client Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form 
            form={clientForm} 
            layout="vertical" 
            onFinish={handleSubmit}
            disabled={false}
            id="basic-info-form"
            style={{
              pointerEvents: 'auto'
            }}
            onFieldsChange={(changedFields) => {
              console.log('Form fields changed:', changedFields);
            }}
          >
            {/* 调试信息区域 */}
            <div style={{ 
              background: '#f0f0f0', 
              padding: '8px 12px', 
              borderRadius: '4px', 
              marginBottom: '16px',
              fontSize: '12px',
              border: '1px solid #d9d9d9'
            }}>
              <strong>Debug Info:</strong> Form State = Active | 
              Disabled = false | 
              ReadOnly = false | 
              Active Module = {activeModule}
              <Button 
                size="small" 
                type="link" 
                onClick={() => {
                  console.log('=== SUPER MANUAL FIELD ENABLE ===');
                  
                  // 1. 强制启用所有input元素
                  const inputs = document.querySelectorAll('#basic-info-form input, #basic-info-form textarea');
                  console.log('Found inputs:', inputs.length);
                  
                  inputs.forEach((input: any, index) => {
                    console.log(`Enabling input ${index}:`, input.name || input.placeholder);
                    
                    // 移除所有可能的禁用属性
                    input.disabled = false;
                    input.readOnly = false;
                    input.removeAttribute('disabled');
                    input.removeAttribute('readonly');
                    input.removeAttribute('aria-disabled');
                    
                    // 强制设置样式
                    input.style.pointerEvents = 'auto';
                    input.style.cursor = 'text';
                    input.style.backgroundColor = 'white';
                    input.style.color = 'rgba(0, 0, 0, 0.88)';
                    input.style.opacity = '1';
                    input.style.border = '1px solid #d9d9d9';
                    
                    // 添加事件监听器
                    input.addEventListener('focus', function() {
                      this.style.borderColor = '#4096ff';
                      this.style.outline = 'none';
                      console.log('Input focused:', this.name || this.placeholder);
                    });
                    
                    input.addEventListener('input', function() {
                      console.log('Input changed:', this.name, this.value);
                    });
                  });
                  
                  // 2. 强制重新渲染表单
                  clientForm.validateFields().catch(() => {});
                  
                  // 3. 显示结果
                  alert(`✅ 强制启用了 ${inputs.length} 个输入字段！现在应该可以编辑了。`);
                }}
              >
                🔧 Super Manual Enable
              </Button>
            </div>
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="clientName" label="Client Name">
                  <Input 
                    placeholder="e.g. Acme Corporation" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text',
                      backgroundColor: 'white',
                      color: 'rgba(0, 0, 0, 0.88)'
                    }}
                    onChange={(e) => console.log('Client Name changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                    autoComplete="off"
                    key="client-name-force-editable"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="clientTier" label="Client Tier">
                  <Select placeholder="Select client tier" style={{ borderRadius: '6px' }}>
                    <Option value="SVIP">SVIP - Strategic VIP</Option>
                    <Option value="VIP">VIP - Very Important</Option>
                    <Option value="BA">BA - Business Account</Option>
                    <Option value="A">A - Standard</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="address" label="Address">
                  <Input 
                    placeholder="e.g. 123 Main Street, San Francisco, CA 94105" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text',
                      backgroundColor: 'white',
                      color: 'rgba(0, 0, 0, 0.88)'
                    }}
                    onChange={(e) => console.log('Address changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                    autoComplete="off"
                    key="address-force-editable"
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="industry" label="Industry">
                  <Select placeholder="Select industry" style={{ borderRadius: '6px' }}>
                    <Option value="Technology">Technology</Option>
                    <Option value="Financial Services">Financial Services</Option>
                    <Option value="Healthcare">Healthcare</Option>
                    <Option value="Manufacturing">Manufacturing</Option>
                    <Option value="Retail">Retail</Option>
                    <Option value="Education">Education</Option>
                    <Option value="Other">Other</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="keyStakeholder" label="Key Stakeholder">
                  <Input 
                    placeholder="e.g. John Smith (CEO)" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Key Stakeholder changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="primaryContact" label="Primary Contact">
                  <Input 
                    placeholder="e.g. Jane Doe (CTO)" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Primary Contact changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="email" label="Email">
                  <Input 
                    placeholder="e.g. <EMAIL>" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Email changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="contactPhone" label="Contact Phone">
                  <Input 
                    placeholder="e.g. +****************" 
                    style={{ 
                      borderRadius: '6px',
                      pointerEvents: 'auto',
                      cursor: 'text'
                    }}
                    onChange={(e) => console.log('Contact Phone changed:', e.target.value)}
                    disabled={false}
                    readOnly={false}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button 
                onClick={() => {
                  console.log('=== CLEAR FORM AND FORCE ENABLE FIELDS ===');
                  
                  // 1. 清空表单
                  clientForm.resetFields();
                  
                  // 2. 强制启用所有表单字段
                  setTimeout(() => {
                    const allInputs = document.querySelectorAll('#basic-info-form input, #basic-info-form textarea, #basic-info-form .ant-select');
                    console.log('Found form elements:', allInputs.length);
                    
                    allInputs.forEach((element: any) => {
                      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.disabled = false;
                        element.readOnly = false;
                        element.style.pointerEvents = 'auto';
                        element.style.cursor = 'text';
                        element.style.backgroundColor = 'white';
                        console.log('Enabled element:', element);
                      }
                    });
                    
                    // 强制重新渲染
                    clientForm.validateFields().catch(() => {});
                    console.log('✅ All form fields should be editable now!');
                  }, 100);
                }}
                style={{ marginRight: '8px' }}
              >
                Clear Form & Enable Editing
              </Button>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Client Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Opportunity Information Module */}
      {activeModule === 'opportunity' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>💼 Opportunity Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form form={opportunityForm} layout="vertical" onFinish={handleSubmit}>
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="opportunityName" label="Opportunity Name">
                  <Input placeholder="e.g. Digital Transformation Project" style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="totalRevenue" label="Total Revenue">
                  <InputNumber 
                    placeholder="100000" 
                    style={{ borderRadius: '6px', width: '100%' }}
                    addonBefore="€"
                    min={0}
                    precision={0}
                    formatter={(value) => {
                      if (!value) return '';
                      return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    }}
                    parser={(value: any) => {
                      if (!value) return 0;
                      return value.replace(/[€\s,]/g, '');
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="winProbability" label={`Win Probability: ${winProbabilityValue}%`}>
                  <Slider
                    min={0}
                    max={100}
                    value={winProbabilityValue}
                    onChange={setWinProbabilityValue}
                    marks={{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="currentStage" label="Current Stage">
                  <Select placeholder="Select stage" style={{ borderRadius: '6px' }}>
                    <Option value="qualification">Qualification</Option>
                    <Option value="proposal">Proposal</Option>
                    <Option value="negotiation">Negotiation</Option>
                    <Option value="closed_won">Closed Won</Option>
                    <Option value="closed_lost">Closed Lost</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item name="background" label="Background">
                  <TextArea rows={4} placeholder="Describe the opportunity background..." style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button 
                onClick={() => {
                  console.log('Clearing opportunity form...');
                  opportunityForm.resetFields();
                  setWinProbabilityValue(50);
                  console.log('Opportunity form cleared.');
                }}
                style={{ marginRight: '8px' }}
              >
                Clear Form
              </Button>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Opportunity Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Competitor Information Module */}
      {activeModule === 'competitor' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>🏆 Competitor Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form form={competitorForm} layout="vertical" onFinish={handleSubmit}>
            <Row gutter={[16, 8]}>
              {/* Competitor Existence Toggle */}
              <Col xs={24}>
                <Form.Item label="Do you have competitors?" style={{ marginBottom: '20px' }}>
                  <Switch
                    checked={hasCompetitors}
                    onChange={(checked) => {
                      setHasCompetitors(checked);
                      if (!checked) {
                        // Clear competitor data when "No" is selected
                        setCompetitorTags([]);
                        competitorForm.setFieldsValue({
                          competitorNames: [],
                          competitorNotes: ''
                        });
                      }
                    }}
                    checkedChildren="Yes"
                    unCheckedChildren="No"
                    style={{
                      backgroundColor: hasCompetitors ? '#52c41a' : '#d9d9d9'
                    }}
                  />
                  <span style={{ marginLeft: '12px', color: '#666', fontSize: '14px' }}>
                    {hasCompetitors ? 'Yes, we have competitors' : 'No, no direct competitors'}
                  </span>
                </Form.Item>
              </Col>

              {/* Competitor Names - Only show if hasCompetitors is true */}
              {hasCompetitors && (
                <Col xs={24}>
                  <Form.Item name="competitorNames" label="Competitor Names">
                    <Select
                      mode="tags"
                      placeholder="Enter competitor names (press Enter to add each one)"
                      style={{ borderRadius: '6px' }}
                      value={competitorTags}
                      onChange={setCompetitorTags}
                      tokenSeparators={[',']}
                      maxTagCount={10}
                      maxTagTextLength={50}
                    >
                      {/* Pre-defined common competitors */}
                      <Select.Option value="Microsoft">Microsoft</Select.Option>
                      <Select.Option value="Google">Google</Select.Option>
                      <Select.Option value="Amazon">Amazon</Select.Option>
                      <Select.Option value="IBM">IBM</Select.Option>
                      <Select.Option value="Oracle">Oracle</Select.Option>
                      <Select.Option value="SAP">SAP</Select.Option>
                      <Select.Option value="Salesforce">Salesforce</Select.Option>
                      <Select.Option value="Adobe">Adobe</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              )}

              {/* Competitor Analysis */}
              <Col xs={24}>
                <Form.Item 
                  name="competitorNotes" 
                  label="Competitor Analysis"
                  extra={hasCompetitors ? (
                    <Alert
                      message="SWOT Analysis Framework"
                      description={
                        <div style={{ marginTop: '8px' }}>
                          <div style={{ marginBottom: '8px' }}>
                            <strong>Use this framework to analyze your competitors:</strong>
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="green">Strengths</Tag> What advantages do they have? (e.g., market share, technology, brand recognition)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="red">Weaknesses</Tag> What are their limitations? (e.g., pricing, customer service, outdated technology)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="blue">Opportunities</Tag> What market gaps can you exploit? (e.g., unserved segments, emerging technologies)
                          </div>
                          <div style={{ marginBottom: '6px' }}>
                            <Tag color="orange">Threats</Tag> What competitive risks exist? (e.g., new entrants, price wars, market consolidation)
                          </div>
                          <div style={{ marginTop: '8px', fontStyle: 'italic', color: '#666' }}>
                            <strong>Example:</strong> "Competitor A has strong brand recognition (Strength) but higher pricing (Weakness). 
                            They're expanding into mobile solutions (Threat) but lack AI capabilities (Opportunity for us)."
                          </div>
                        </div>
                      }
                      type="info"
                      showIcon
                      style={{ marginBottom: '12px' }}
                    />
                  ) : null}
                >
                  <TextArea
                    rows={hasCompetitors ? 8 : 4}
                    placeholder={
                      hasCompetitors 
                        ? "Analyze the competitive landscape using the SWOT framework above. Include key competitors, their strengths, weaknesses, market positioning, and competitive threats..."
                        : "Since there are no direct competitors, describe the unique market position and any potential indirect competition or substitutes..."
                    }
                    style={{ borderRadius: '6px' }}
                    disabled={!hasCompetitors}
                  />
                </Form.Item>
              </Col>

              {/* No Competitors Information */}
              {!hasCompetitors && (
                <Col xs={24}>
                  <Alert
                    message="No Direct Competitors"
                    description={
                      <div>
                        <p>You've indicated that there are no direct competitors. Consider analyzing:</p>
                        <ul style={{ marginLeft: '20px', marginBottom: '8px' }}>
                          <li>Indirect competitors or substitute products/services</li>
                          <li>Potential future competitors entering the market</li>
                          <li>Your unique value proposition and market positioning</li>
                          <li>Barriers to entry that protect your market position</li>
                        </ul>
                      </div>
                    }
                    type="success"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                </Col>
              )}
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button 
                onClick={() => {
                  console.log('Clearing competitor form...');
                  competitorForm.resetFields();
                  setHasCompetitors(false);
                  setCompetitorTags([]);
                  console.log('Competitor form cleared.');
                }}
                style={{ marginRight: '8px' }}
              >
                Clear Form
              </Button>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Competitor Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Other Information Module */}
      {activeModule === 'other' && (
        <Card 
          title={
            <span style={{ fontSize: '15px', fontWeight: '500', color: '#1F2023' }}>📋 Other Information</span>
          }
          style={{ 
            borderRadius: '8px',
            boxShadow: '0 1px 2px rgba(0,0,0,0.05)',
            border: '1px solid #E5E7EB',
            marginBottom: '16px'
          }}
          bodyStyle={{ padding: '20px' }}
        >
          <Form form={otherForm} layout="vertical" onFinish={handleSubmit}>
            <Row gutter={[16, 8]}>
              <Col xs={24} md={12}>
                <Form.Item name="source" label="Lead Source">
                  <Select placeholder="Select lead source" style={{ borderRadius: '6px' }}>
                    <Option value="website">Website</Option>
                    <Option value="referral">Referral</Option>
                    <Option value="cold_call">Cold Call</Option>
                    <Option value="trade_show">Trade Show</Option>
                    <Option value="social_media">Social Media</Option>
                    <Option value="other">Other</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item name="tags" label="Tags">
                  <Input placeholder="e.g. high-priority, enterprise, strategic" style={{ borderRadius: '6px' }} />
                </Form.Item>
              </Col>
              <Col xs={24}>
                <Form.Item name="clientAnalysis" label="Client Analysis">
                  <TextArea
                    rows={6}
                    placeholder="Additional analysis about the client, market position, decision-making process..."
                    style={{ borderRadius: '6px' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Divider />
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button 
                onClick={() => {
                  console.log('Clearing other form...');
                  otherForm.resetFields();
                  console.log('Other form cleared.');
                }}
                style={{ marginRight: '8px' }}
              >
                Clear Form
              </Button>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                  Save Other Information
                </Button>
              </div>
            </div>
          </Form>
        </Card>
      )}

      {/* Save & Proceed 按钮 */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>
    </div>
  );
};

export default BasicInfo; 