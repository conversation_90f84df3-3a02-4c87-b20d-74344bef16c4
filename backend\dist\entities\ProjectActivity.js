"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectActivity = exports.ActivityType = void 0;
const typeorm_1 = require("typeorm");
const Project_1 = require("./Project");
const User_1 = require("./User");
var ActivityType;
(function (ActivityType) {
    ActivityType["PROJECT_CREATED"] = "project_created";
    ActivityType["PROJECT_UPDATED"] = "project_updated";
    ActivityType["PROJECT_SHARED"] = "project_shared";
    ActivityType["COLLABORATOR_ADDED"] = "collaborator_added";
    ActivityType["COLLABORATOR_REMOVED"] = "collaborator_removed";
    ActivityType["PERMISSION_CHANGED"] = "permission_changed";
    ActivityType["STAGE_UPDATED"] = "stage_updated";
    ActivityType["DOCUMENT_UPLOADED"] = "document_uploaded";
    ActivityType["DOCUMENT_DOWNLOADED"] = "document_downloaded";
    ActivityType["COMMENT_ADDED"] = "comment_added";
    ActivityType["STATUS_CHANGED"] = "status_changed";
})(ActivityType = exports.ActivityType || (exports.ActivityType = {}));
let ProjectActivity = class ProjectActivity {
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProjectActivity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Project_1.Project, { onDelete: 'CASCADE' }),
    __metadata("design:type", Project_1.Project)
], ProjectActivity.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, { nullable: true, onDelete: 'SET NULL' }),
    __metadata("design:type", User_1.User)
], ProjectActivity.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar'
    }),
    __metadata("design:type", String)
], ProjectActivity.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProjectActivity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectActivity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProjectActivity.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProjectActivity.prototype, "createdAt", void 0);
ProjectActivity = __decorate([
    (0, typeorm_1.Entity)('project_activities')
], ProjectActivity);
exports.ProjectActivity = ProjectActivity;
//# sourceMappingURL=ProjectActivity.js.map