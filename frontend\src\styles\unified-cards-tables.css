/* 统一卡片和表格样式 */

/* ===== 卡片组件 ===== */

/* 基础卡片 */
.card {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  margin-bottom: var(--spacing-md);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* 卡片头部 */
.card-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 卡片标题 */
.card-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 卡片副标题 */
.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

/* 卡片内容 */
.card-content {
  padding: var(--spacing-md);
}

/* 卡片底部 */
.card-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 卡片操作区 */
.card-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 卡片网格 */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

/* 响应式卡片网格 */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* 卡片变体 */
.card-primary {
  border-top: 3px solid var(--primary);
}

.card-success {
  border-top: 3px solid var(--success);
}

.card-warning {
  border-top: 3px solid var(--warning);
}

.card-danger {
  border-top: 3px solid var(--danger);
}

.card-info {
  border-top: 3px solid var(--info);
}

/* 统计卡片 */
.stat-card {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.stat-card__title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-card__value {
  font-size: var(--font-size-xxl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-card__trend {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  margin-top: auto;
}

.stat-card__trend--up {
  color: var(--success);
}

.stat-card__trend--down {
  color: var(--danger);
}

/* 项目卡片 */
.project-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.project-card__header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border);
}

.project-card__title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-card__subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.project-card__content {
  padding: var(--spacing-md);
  flex: 1;
}

.project-card__meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.project-card__meta-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.project-card__meta-icon {
  margin-right: var(--spacing-xs);
}

.project-card__description {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-card__footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-card__status {
  display: flex;
  align-items: center;
}

.project-card__actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* ===== 表格组件 ===== */

/* 表格容器 */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  background-color: var(--card-background);
  border: 1px solid var(--border);
}

/* 表格 */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* 表头 */
.table thead th {
  background-color: var(--gray-light);
  color: var(--text-secondary);
  font-weight: 600;
  text-align: left;
  padding: 12px 16px;
  font-size: var(--font-size-sm);
  border-bottom: 1px solid var(--border);
}

/* 表格单元格 */
.table tbody td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

/* 表格行悬停效果 */
.table tbody tr:hover td {
  background-color: var(--hover);
}

/* 表格行选中效果 */
.table tbody tr.selected td {
  background-color: var(--primary-light);
}

/* 表格数字列居中 */
.table .numeric {
  text-align: center;
}

/* 表格空状态 */
.table-empty {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

/* 表格加载状态 */
.table-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 表格分页 */
.table-pagination {
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-top: 1px solid var(--border);
}

/* 表格工具栏 */
.table-toolbar {
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border);
  background-color: var(--card-background);
}

.table-toolbar__left {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.table-toolbar__right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 表格过滤器 */
.table-filters {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--border);
  background-color: var(--gray-light);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  align-items: center;
}

/* 表格操作列 */
.table-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 表格展开行 */
.table-expanded-row {
  padding: var(--spacing-md);
  background-color: var(--gray-light);
}

/* 表格可排序列 */
.table-sortable {
  cursor: pointer;
  user-select: none;
}

.table-sortable:hover {
  background-color: var(--hover);
}

.table-sort-icon {
  margin-left: var(--spacing-xs);
}

/* 表格可选择行 */
.table-selectable .table-checkbox {
  padding-right: 0;
}

/* 表格可拖动列 */
.table-draggable-handle {
  cursor: move;
  color: var(--text-light);
}

/* 表格可编辑单元格 */
.table-editable-cell {
  position: relative;
  padding: 5px;
  cursor: pointer;
}

.table-editable-cell:hover {
  background-color: var(--hover);
}

.table-editable-cell-input {
  margin: -5px;
  padding: 5px;
  width: calc(100% + 10px);
}

/* 表格可展开图标 */
.table-expandable-icon {
  cursor: pointer;
  color: var(--text-secondary);
}

/* 表格树形结构 */
.table-tree-indent {
  display: inline-block;
  width: 16px;
}

.table-tree-icon {
  margin-right: var(--spacing-xs);
  cursor: pointer;
}
