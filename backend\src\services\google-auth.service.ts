import { OAuth2Client } from 'google-auth-library';

// 创建 Google OAuth 客户端
// 注意：在生产环境中，应该从环境变量中获取客户端 ID
const client = new OAuth2Client(
  process.env.GOOGLE_CLIENT_ID || '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com'
);

/**
 * 验证 Google 令牌并获取用户信息
 * @param token Google 提供的访问令牌
 * @returns 用户信息对象
 */
export async function verifyGoogleToken(token: string) {
  try {
    // 使用令牌获取用户信息
    const response = await fetch(`https://www.googleapis.com/oauth2/v3/userinfo?access_token=${token}`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch user info from Google');
    }
    
    const userInfo = await response.json();
    
    return {
      googleId: userInfo.sub,
      email: userInfo.email,
      username: userInfo.name || userInfo.email.split('@')[0],
      verified: userInfo.email_verified
    };
  } catch (error) {
    console.error('Error verifying Google token:', error);
    throw new Error('Invalid Google token');
  }
}

/**
 * 验证 Google ID 令牌
 * 这是另一种验证方式，使用 ID 令牌而不是访问令牌
 * @param idToken Google 提供的 ID 令牌
 * @returns 用户信息对象
 */
export async function verifyGoogleIdToken(idToken: string) {
  try {
    const ticket = await client.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID || '1234567890-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
    });
    
    const payload = ticket.getPayload();
    
    if (!payload) {
      throw new Error('Empty payload from Google token verification');
    }
    
    return {
      googleId: payload.sub,
      email: payload.email,
      username: payload.name || payload.email?.split('@')[0] || '',
      verified: payload.email_verified
    };
  } catch (error) {
    console.error('Error verifying Google ID token:', error);
    throw new Error('Invalid Google ID token');
  }
}
