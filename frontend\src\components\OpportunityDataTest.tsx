import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Space, message, Typography } from 'antd';
import projectService from '../services/new-project.service';
import emergencyDataManager from '../services/emergencyDataManager';
import { Project } from '../types';

const { Text, Paragraph } = Typography;

const OpportunityDataTest: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebugInfo = (info: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugInfo(prev => [...prev, `[${timestamp}] ${info}`]);
    console.log(`[OpportunityDataTest] ${info}`);
  };

  const loadProjects = async () => {
    try {
      addDebugInfo('开始加载项目...');
      const data = await projectService.getAllProjects(true);
      setProjects(data);
      addDebugInfo(`加载完成，项目数量: ${data.length}`);
    } catch (error) {
      addDebugInfo(`加载失败: ${error}`);
    }
  };

  const createTestProject = async () => {
    try {
      addDebugInfo('开始创建测试项目...');
      
      const testProject: Partial<Project> = {
        name: `测试项目 - ${new Date().toLocaleTimeString()}`,
        client: '测试客户',
        country: 'Netherlands',
        tier: 'A' as const,
        category: 'Technology',
        revenue: 1200,
        stage: 'Lead',
        status: 'planning',
        probability: 'A',
        owner: '测试用户',
        description: '这是一个测试项目'
      };

      const newProject = await projectService.createProject(testProject);
      addDebugInfo(`项目创建成功，ID: ${newProject.id}`);
      
      // 重新加载项目列表
      await loadProjects();
      
      message.success('测试项目创建成功！');
    } catch (error) {
      addDebugInfo(`创建失败: ${error}`);
      message.error('创建失败');
    }
  };

  const checkLocalStorage = () => {
    addDebugInfo('=== 检查 localStorage ===');
    
    // 检查 emergencyDataManager 存储
    const edmProjects = localStorage.getItem('edm_projects');
    addDebugInfo(`edm_projects: ${edmProjects ? JSON.parse(edmProjects).length + ' 个项目' : '未找到'}`);
    
    // 检查其他可能的存储键
    const otherKeys = ['projects', 'ltc_projects', 'dal_projects'];
    otherKeys.forEach(key => {
      const data = localStorage.getItem(key);
      addDebugInfo(`${key}: ${data ? JSON.parse(data).length + ' 个项目' : '未找到'}`);
    });

    // 检查 emergencyDataManager 状态
    const status = emergencyDataManager.getStatus();
    addDebugInfo(`EmergencyDataManager 状态: 项目${status.projectsCount}个, 缓存${status.cacheSize}个`);
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  useEffect(() => {
    addDebugInfo('组件初始化');
    loadProjects();
    checkLocalStorage();
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card title="🧪 Opportunity 数据测试工具" style={{ marginBottom: '24px' }}>
        <Space wrap>
          <Button type="primary" onClick={createTestProject}>
            创建测试项目
          </Button>
          <Button onClick={loadProjects}>
            重新加载项目
          </Button>
          <Button onClick={checkLocalStorage}>
            检查存储状态
          </Button>
          <Button onClick={clearDebugInfo}>
            清除调试信息
          </Button>
        </Space>
      </Card>

      <Card title="📊 当前项目列表" style={{ marginBottom: '24px' }}>
        <Text strong>项目数量: {projects.length}</Text>
        <div style={{ marginTop: '12px' }}>
          {projects.map((project, index) => (
            <div key={project.id} style={{ marginBottom: '8px', padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
              <Text strong>{index + 1}. {project.name}</Text>
              <br />
              <Text type="secondary">
                ID: {project.id} | ProjectID: {project.projectId} | Client: {project.client}
              </Text>
            </div>
          ))}
        </div>
      </Card>

      <Card title="🐛 调试信息">
        <div style={{ height: '300px', overflow: 'auto', background: '#f8f8f8', padding: '12px', borderRadius: '4px' }}>
          {debugInfo.map((info, index) => (
            <div key={index} style={{ marginBottom: '4px', fontSize: '12px', fontFamily: 'monospace' }}>
              {info}
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default OpportunityDataTest; 