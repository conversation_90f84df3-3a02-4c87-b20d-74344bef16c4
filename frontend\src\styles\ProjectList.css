.project-list-page {
  padding: 24px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow: hidden;
}

.project-list-page .project-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.project-list-page .project-list-header h2 {
  margin: 0;
}

.project-list-page .project-list-header .ant-btn.ant-btn-icon-only {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-list-page .project-list-header .ant-btn .anticon {
  font-size: 16px;
}

.project-list-page .project-list-header .ant-badge .ant-badge-dot {
  background-color: #1890ff;
  box-shadow: 0 0 0 2px #fff;
}

.project-list-page .error-alert {
  margin-bottom: 16px;
}

.project-list-page .loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.project-list-page .virtualized-list {
  flex: 1;
  overflow: hidden;
}
