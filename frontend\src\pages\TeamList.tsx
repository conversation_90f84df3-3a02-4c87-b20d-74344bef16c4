import React, { useEffect, useState } from 'react';
import { Card, Table, Space, Input, Select, Button, Tag, message, Modal, Form, Avatar } from 'antd';
import { SearchOutlined, PlusOutlined, DeleteOutlined, EditOutlined, UserOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import teamMemberService from '../services/team-member.service';
import { TeamMember } from '../types';

const { Option } = Select;
const { confirm } = Modal;

const TeamList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const fetchTeamMembers = async () => {
    try {
      setLoading(true);
      const data = await teamMemberService.getAllTeamMembers();
      setTeamMembers(data);
    } catch (error) {
      console.error('Error fetching team members:', error);
      message.error('获取团队成员列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = (id: string) => {
    confirm({
      title: '确认删除',
      content: '您确定要删除这个团队成员吗？此操作不可逆。',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await teamMemberService.deleteTeamMember(id);
          message.success('团队成员已删除');
          fetchTeamMembers();
        } catch (error) {
          console.error('Error deleting team member:', error);
          message.error('删除团队成员失败');
        }
      },
    });
  };

  const showModal = (member?: TeamMember) => {
    if (member) {
      setEditingMember(member);
      form.setFieldsValue(member);
    } else {
      setEditingMember(null);
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingMember) {
        // 更新现有成员
        await teamMemberService.updateTeamMember(editingMember.id, values);
        message.success('团队成员已更新');
      } else {
        // 创建新成员
        await teamMemberService.createTeamMember(values);
        message.success('团队成员已创建');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      fetchTeamMembers();
    } catch (error) {
      console.error('Error saving team member:', error);
      message.error('保存团队成员失败');
    }
  };

  // 过滤团队成员
  const filteredTeamMembers = teamMembers.filter(member => {
    const matchesSearch = 
      member.name.toLowerCase().includes(searchText.toLowerCase()) ||
      member.email.toLowerCase().includes(searchText.toLowerCase()) ||
      (member.position || '').toLowerCase().includes(searchText.toLowerCase()) ||
      (member.department || '').toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || member.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TeamMember) => (
        <Space>
          <Avatar icon={<UserOutlined />} src={record.avatar} />
          <a onClick={() => navigate(`/team/${record.id}`)}>{text}</a>
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === 'active' ? 'green' : 'red';
        const text = status === 'active' ? '在职' : '离职';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TeamMember) => (
        <Space size="middle">
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => showModal(record)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Space style={{ marginBottom: 16 }}>
          <Input
            placeholder="搜索团队成员"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 200 }}
          />
          <Select 
            defaultValue="all" 
            style={{ width: 120 }}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value)}
          >
            <Option value="all">所有状态</Option>
            <Option value="active">在职</Option>
            <Option value="inactive">离职</Option>
          </Select>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => showModal()}
          >
            添加团队成员
          </Button>
        </Space>
        <Table 
          columns={columns} 
          dataSource={filteredTeamMembers} 
          rowKey="id"
          loading={loading}
        />
      </Card>

      <Modal
        title={editingMember ? '编辑团队成员' : '添加团队成员'}
        visible={isModalVisible}
        onCancel={handleCancel}
        onOk={handleSubmit}
        okText={editingMember ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="position"
            label="职位"
            rules={[{ required: true, message: '请输入职位' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="department"
            label="部门"
            rules={[{ required: true, message: '请输入部门' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="phone"
            label="电话"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              <Option value="active">在职</Option>
              <Option value="inactive">离职</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="bio"
            label="简介"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TeamList;
