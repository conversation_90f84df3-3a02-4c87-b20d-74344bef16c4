"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const typeorm_1 = require("typeorm");
const TeamMember_1 = require("../entities/TeamMember");
const auth_middleware_1 = require("../middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.use(auth_middleware_1.authMiddleware);
router.get('/', async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMembers = await teamMemberRepository.find();
        res.json(teamMembers);
    }
    catch (error) {
        res.status(500).json({ message: '获取团队成员列表失败', error });
    }
});
router.get('/:id', async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        res.json(teamMember);
    }
    catch (error) {
        res.status(500).json({ message: '获取团队成员失败', error });
    }
});
router.post('/', (0, auth_middleware_1.checkRole)(['admin', 'manager']), async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const existingMember = await teamMemberRepository.findOne({ where: { email: req.body.email } });
        if (existingMember) {
            return res.status(400).json({ message: '该邮箱已被使用' });
        }
        const teamMember = teamMemberRepository.create(req.body);
        const result = await teamMemberRepository.save(teamMember);
        res.status(201).json(result);
    }
    catch (error) {
        res.status(500).json({ message: '创建团队成员失败', error });
    }
});
router.put('/:id', (0, auth_middleware_1.checkRole)(['admin', 'manager']), async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id);
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        if (req.body.email && req.body.email !== teamMember.email) {
            const existingMember = await teamMemberRepository.findOne({ where: { email: req.body.email } });
            if (existingMember) {
                return res.status(400).json({ message: '该邮箱已被使用' });
            }
        }
        teamMemberRepository.merge(teamMember, req.body);
        const result = await teamMemberRepository.save(teamMember);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ message: '更新团队成员失败', error });
    }
});
router.delete('/:id', (0, auth_middleware_1.checkRole)(['admin']), async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id);
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        await teamMemberRepository.remove(teamMember);
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: '删除团队成员失败', error });
    }
});
router.get('/:id/projects', async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        res.json(teamMember.projects);
    }
    catch (error) {
        res.status(500).json({ message: '获取团队成员项目失败', error });
    }
});
router.post('/:id/projects/:projectId', (0, auth_middleware_1.checkRole)(['admin', 'manager']), async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        const projectRepository = (0, typeorm_1.getRepository)('Project');
        const project = await projectRepository.findOne(req.params.projectId);
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        if (teamMember.projects.some(p => p.id === req.params.projectId)) {
            return res.status(400).json({ message: '团队成员已在该项目中' });
        }
        teamMember.projects.push(project);
        await teamMemberRepository.save(teamMember);
        res.status(201).json({ message: '团队成员已添加到项目' });
    }
    catch (error) {
        res.status(500).json({ message: '添加团队成员到项目失败', error });
    }
});
router.delete('/:id/projects/:projectId', (0, auth_middleware_1.checkRole)(['admin', 'manager']), async (req, res) => {
    try {
        const teamMemberRepository = (0, typeorm_1.getRepository)(TeamMember_1.TeamMember);
        const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
        if (!teamMember) {
            return res.status(404).json({ message: '团队成员不存在' });
        }
        teamMember.projects = teamMember.projects.filter(p => p.id !== req.params.projectId);
        await teamMemberRepository.save(teamMember);
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ message: '从项目中移除团队成员失败', error });
    }
});
exports.default = router;
//# sourceMappingURL=team-member.routes.js.map