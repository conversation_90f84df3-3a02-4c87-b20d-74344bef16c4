# LTC项目管理系统启动脚本 (PowerShell版本)
# 设置执行策略和编码
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "===================================" -ForegroundColor Green
Write-Host "    LTC项目管理系统启动脚本" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""

# 检查Node.js和npm
Write-Host "检查Node.js和npm版本..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version 2>$null
    $npmVersion = npm --version 2>$null
    
    if (-not $nodeVersion) {
        Write-Host "错误: 未找到Node.js，请先安装Node.js" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
    
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Green
    Write-Host "npm版本: $npmVersion" -ForegroundColor Green
    Write-Host ""
} catch {
    Write-Host "错误: 检查Node.js版本时出错 - $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 停止现有的Node.js进程
Write-Host "正在停止现有的Node.js进程..." -ForegroundColor Yellow
try {
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
} catch {
    Write-Host "停止进程时出现警告，继续执行..." -ForegroundColor Yellow
}

# 检查并释放端口
Write-Host "检查端口占用情况..." -ForegroundColor Yellow

# 检查端口3000
$port3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
if ($port3000) {
    Write-Host "警告: 端口3000被占用，尝试释放..." -ForegroundColor Yellow
    foreach ($conn in $port3000) {
        try {
            Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Host "无法停止进程 $($conn.OwningProcess)" -ForegroundColor Yellow
        }
    }
    Start-Sleep -Seconds 2
}

# 检查端口5002
$port5002 = Get-NetTCPConnection -LocalPort 5002 -ErrorAction SilentlyContinue
if ($port5002) {
    Write-Host "警告: 端口5002被占用，尝试释放..." -ForegroundColor Yellow
    foreach ($conn in $port5002) {
        try {
            Stop-Process -Id $conn.OwningProcess -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Host "无法停止进程 $($conn.OwningProcess)" -ForegroundColor Yellow
        }
    }
    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "===================================" -ForegroundColor Green
Write-Host "    开始启动服务" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# 获取脚本所在目录
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# 启动后端服务
Write-Host "1. 启动后端服务 (端口 5002)..." -ForegroundColor Cyan
$backendDir = Join-Path $scriptDir "backend"
Start-Process -FilePath "cmd" -ArgumentList "/c", "cd /d `"$backendDir`" && npm run dev" -WindowStyle Normal

# 等待后端启动
Write-Host "等待后端服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# 检查后端是否启动成功
$maxRetries = 10
$retryCount = 0
do {
    $backendRunning = Get-NetTCPConnection -LocalPort 5002 -ErrorAction SilentlyContinue
    if ($backendRunning) {
        Write-Host "✓ 后端服务启动成功!" -ForegroundColor Green
        break
    }
    Write-Host "等待后端服务启动中..." -ForegroundColor Yellow
    Start-Sleep -Seconds 2
    $retryCount++
} while ($retryCount -lt $maxRetries)

if (-not $backendRunning) {
    Write-Host "警告: 后端服务可能未正常启动" -ForegroundColor Yellow
}

# 启动前端服务
Write-Host ""
Write-Host "2. 启动前端服务 (端口 3000)..." -ForegroundColor Cyan
$frontendDir = Join-Path $scriptDir "frontend"
Start-Process -FilePath "cmd" -ArgumentList "/c", "cd /d `"$frontendDir`" && npm start" -WindowStyle Normal

# 等待前端启动
Write-Host "等待前端服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查前端是否启动成功
$maxRetries = 15
$retryCount = 0
do {
    $frontendRunning = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
    if ($frontendRunning) {
        Write-Host "✓ 前端服务启动成功!" -ForegroundColor Green
        break
    }
    Write-Host "等待前端服务启动中..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    $retryCount++
} while ($retryCount -lt $maxRetries)

if (-not $frontendRunning) {
    Write-Host "警告: 前端服务可能未正常启动" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "===================================" -ForegroundColor Green
Write-Host "    启动完成!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""
Write-Host "服务状态:" -ForegroundColor White
Write-Host "✓ 后端服务: http://localhost:5002" -ForegroundColor Green
Write-Host "✓ 前端服务: http://localhost:3000" -ForegroundColor Green
Write-Host ""
Write-Host "登录信息:" -ForegroundColor White
Write-Host "  邮箱: <EMAIL>" -ForegroundColor Cyan
Write-Host "  密码: admin123" -ForegroundColor Cyan
Write-Host ""
Write-Host "正在打开浏览器..." -ForegroundColor Yellow

# 打开浏览器
Start-Sleep -Seconds 2
Start-Process "http://localhost:3000"

Write-Host ""
Write-Host "项目已启动完成！" -ForegroundColor Green
Write-Host "要停止服务，请运行 stop-ltc-new.ps1" -ForegroundColor White
Write-Host ""
Read-Host "按任意键继续" 