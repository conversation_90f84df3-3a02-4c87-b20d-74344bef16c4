.project-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;

    a {
      color: inherit;
      text-decoration: none;

      &:hover {
        color: var(--primary);
      }
    }
  }

  &__id {
    font-size: 12px;
    color: var(--text-secondary);
  }

  &__client {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;
  }

  &__details {
    flex: 1;
    margin-bottom: 16px;
  }

  &__detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;

    .label {
      color: var(--text-secondary);
    }

    .value {
      color: var(--text-primary);
      font-weight: 500;
    }
  }

  &__footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
  }

  &__owner {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__owner-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 500;
  }

  &__owner-name {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

.status-tag {
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;

  &--blue {
    background-color: var(--blue-1);
    color: var(--blue-6);
  }

  &--green {
    background-color: var(--green-1);
    color: var(--green-6);
  }

  &--purple {
    background-color: var(--purple-1);
    color: var(--purple-6);
  }

  &--orange {
    background-color: var(--orange-1);
    color: var(--orange-6);
  }
}

.delete-button {
  &:hover {
    background-color: var(--red-1);
  }
} 