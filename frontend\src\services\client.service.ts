import { Client } from '../types';

// 本地存储键
const STORAGE_KEY = 'clients';

// 模拟客户数据 - 新系统从空白状态开始
const mockClients: Client[] = [];

class ClientService {
  private clients: Client[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载数据
  private loadFromStorage() {
    const data = localStorage.getItem(STORAGE_KEY);
    if (data) {
      try {
        this.clients = JSON.parse(data);
      } catch (e) {
        this.clients = [];
      }
    } else {
      this.clients = [...mockClients];
    }
  }

  // 保存到localStorage
  private saveToStorage() {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(this.clients));
  }

  // 获取所有客户
  async getAllClients(): Promise<Client[]> {
    this.loadFromStorage();
    return this.clients.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  // 根据ID获取客户
  async getClient(id: string): Promise<Client> {
    this.loadFromStorage();
    const client = this.clients.find(c => c.id === id);
    if (!client) {
      throw new Error('Client not found');
    }
    return client;
  }

  // 创建新客户
  async createClient(clientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>): Promise<Client> {
    this.loadFromStorage();
    const newClient: Client = {
      ...clientData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.clients.unshift(newClient);
    this.saveToStorage();
    return newClient;
  }

  // 更新客户
  async updateClient(id: string, updates: Partial<Client>): Promise<Client> {
    this.loadFromStorage();
    const index = this.clients.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error('Client not found');
    }
    const updatedClient = {
      ...this.clients[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    this.clients[index] = updatedClient;
    this.saveToStorage();
    return updatedClient;
  }

  // 删除客户
  async deleteClient(id: string): Promise<void> {
    this.loadFromStorage();
    const index = this.clients.findIndex(c => c.id === id);
    if (index === -1) {
      throw new Error('Client not found');
    }
    this.clients.splice(index, 1);
    this.saveToStorage();
  }

  // 搜索客户
  async searchClients(query: string): Promise<Client[]> {
    this.loadFromStorage();
    if (!query.trim()) {
      return this.getAllClients();
    }
    const lowercaseQuery = query.toLowerCase();
    return this.clients.filter(client =>
      client.name.toLowerCase().includes(lowercaseQuery) ||
      client.clientId.toLowerCase().includes(lowercaseQuery) ||
      client.contactPerson.toLowerCase().includes(lowercaseQuery) ||
      client.industry.toLowerCase().includes(lowercaseQuery) ||
      client.country.toLowerCase().includes(lowercaseQuery)
    );
  }

  // 按状态过滤客户
  async getClientsByStatus(status: string): Promise<Client[]> {
    this.loadFromStorage();
    if (status === 'all') {
      return this.getAllClients();
    }
    return this.clients.filter(client => client.status === status);
  }

  // 按层级过滤客户
  async getClientsByTier(tier: string): Promise<Client[]> {
    this.loadFromStorage();
    if (tier === 'all') {
      return this.getAllClients();
    }
    return this.clients.filter(client => client.tier === tier);
  }

  // 获取客户统计信息
  async getClientStats(): Promise<{
    total: number;
    active: number;
    potential: number;
    inactive: number;
    totalRevenue: number;
  }> {
    this.loadFromStorage();
    const stats = {
      total: this.clients.length,
      active: this.clients.filter(c => c.status === 'active').length,
      potential: this.clients.filter(c => c.status === 'potential').length,
      inactive: this.clients.filter(c => c.status === 'inactive').length,
      totalRevenue: this.clients.reduce((sum, c) => sum + c.totalRevenue, 0)
    };
    return stats;
  }
}

export default new ClientService(); 