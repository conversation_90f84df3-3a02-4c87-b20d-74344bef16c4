import { ConnectionOptions } from 'typeorm';
import { Project } from '../entities/Project';
import { User } from '../entities/User';
import { Document } from '../entities/Document';
import { ProjectStage } from '../entities/ProjectStage';
import { TeamMember } from '../entities/TeamMember';
import { ProjectCollaborator } from '../entities/ProjectCollaborator';
import { ProjectActivity } from '../entities/ProjectActivity';
import { PermissionTemplate } from '../entities/PermissionTemplate';

const config: ConnectionOptions = {
  type: 'sqlite',
  database: 'ltc_project.sqlite',
  entities: [Project, User, Document, ProjectStage, TeamMember, ProjectCollaborator, ProjectActivity, PermissionTemplate],
  synchronize: true,
  logging: process.env.NODE_ENV === 'development'
};

export default config;