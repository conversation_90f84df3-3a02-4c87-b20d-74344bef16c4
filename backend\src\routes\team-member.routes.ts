import { Router } from 'express';
import { getRepository } from 'typeorm';
import { TeamMember } from '../entities/TeamMember';
import { authMiddleware, checkRole } from '../middlewares/auth.middleware';

const router = Router();

// 使用认证中间件保护所有路由
router.use(authMiddleware);

// 获取所有团队成员
router.get('/', async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMembers = await teamMemberRepository.find();
    res.json(teamMembers);
  } catch (error) {
    res.status(500).json({ message: '获取团队成员列表失败', error });
  }
});

// 获取单个团队成员
router.get('/:id', async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    res.json(teamMember);
  } catch (error) {
    res.status(500).json({ message: '获取团队成员失败', error });
  }
});

// 创建新团队成员
router.post('/', checkRole(['admin', 'manager']), async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    
    // 检查邮箱是否已存在
    const existingMember = await teamMemberRepository.findOne({ where: { email: req.body.email } });
    if (existingMember) {
      return res.status(400).json({ message: '该邮箱已被使用' });
    }
    
    const teamMember = teamMemberRepository.create(req.body);
    const result = await teamMemberRepository.save(teamMember);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({ message: '创建团队成员失败', error });
  }
});

// 更新团队成员
router.put('/:id', checkRole(['admin', 'manager']), async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id);
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    // 如果更新了邮箱，检查是否与其他成员冲突
    if (req.body.email && req.body.email !== teamMember.email) {
      const existingMember = await teamMemberRepository.findOne({ where: { email: req.body.email } });
      if (existingMember) {
        return res.status(400).json({ message: '该邮箱已被使用' });
      }
    }
    
    teamMemberRepository.merge(teamMember, req.body);
    const result = await teamMemberRepository.save(teamMember);
    res.json(result);
  } catch (error) {
    res.status(500).json({ message: '更新团队成员失败', error });
  }
});

// 删除团队成员
router.delete('/:id', checkRole(['admin']), async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id);
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    await teamMemberRepository.remove(teamMember);
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: '删除团队成员失败', error });
  }
});

// 获取团队成员的项目
router.get('/:id/projects', async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    res.json(teamMember.projects);
  } catch (error) {
    res.status(500).json({ message: '获取团队成员项目失败', error });
  }
});

// 将团队成员添加到项目
router.post('/:id/projects/:projectId', checkRole(['admin', 'manager']), async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    const projectRepository = getRepository('Project');
    const project = await projectRepository.findOne(req.params.projectId);
    
    if (!project) {
      return res.status(404).json({ message: '项目不存在' });
    }
    
    // 检查团队成员是否已在项目中
    if (teamMember.projects.some(p => p.id === req.params.projectId)) {
      return res.status(400).json({ message: '团队成员已在该项目中' });
    }
    
    teamMember.projects.push(project);
    await teamMemberRepository.save(teamMember);
    
    res.status(201).json({ message: '团队成员已添加到项目' });
  } catch (error) {
    res.status(500).json({ message: '添加团队成员到项目失败', error });
  }
});

// 从项目中移除团队成员
router.delete('/:id/projects/:projectId', checkRole(['admin', 'manager']), async (req, res) => {
  try {
    const teamMemberRepository = getRepository(TeamMember);
    const teamMember = await teamMemberRepository.findOne(req.params.id, { relations: ['projects'] });
    
    if (!teamMember) {
      return res.status(404).json({ message: '团队成员不存在' });
    }
    
    // 过滤掉要移除的项目
    teamMember.projects = teamMember.projects.filter(p => p.id !== req.params.projectId);
    await teamMemberRepository.save(teamMember);
    
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ message: '从项目中移除团队成员失败', error });
  }
});

export default router;
