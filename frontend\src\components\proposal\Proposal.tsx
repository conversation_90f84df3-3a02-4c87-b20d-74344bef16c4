import React, { useState, useEffect, useCallback } from 'react';
import { Card, Table, Button, Upload, Tooltip, Switch, message, Modal, Form, Input, DatePicker, Select } from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  RightOutlined,
  ExclamationCircleOutlined,
  CloudUploadOutlined,
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';
import './Proposal.css';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

interface ProposalProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

const Proposal: React.FC<ProposalProps> = ({ project, stage, onSave, onProceed }) => {
  // Get storage key based on project ID
  const getStorageKey = useCallback(() => {
    const projectId = project?.id || stage?.projectId || 'default';
    return `proposal_versions_${projectId}`;
  }, [project?.id, stage?.projectId]);

  // Load initial versions from localStorage or use default data
  const loadInitialVersions = useCallback(() => {
    try {
      const savedVersions = localStorage.getItem(getStorageKey());
      if (savedVersions) {
        return JSON.parse(savedVersions);
      }
    } catch (error) {
      console.error('Error loading versions from localStorage:', error);
    }
    
    // 新项目默认为空，不显示示例数据
    return [];
  }, [getStorageKey]);

  // Proposal version list with localStorage persistence
  const [versions, setVersions] = useState(loadInitialVersions());

  // Save versions to localStorage whenever versions change
  useEffect(() => {
    try {
      localStorage.setItem(getStorageKey(), JSON.stringify(versions));
    } catch (error) {
      console.error('Error saving versions to localStorage:', error);
    }
  }, [versions, getStorageKey]);

  // Load versions when project changes
  useEffect(() => {
    const newVersions = loadInitialVersions();
    setVersions(newVersions);
    console.log('Proposal versions loaded for project:', project?.id || stage?.projectId, newVersions.length, 'versions');
  }, [project?.id, stage?.projectId, loadInitialVersions]);

  // Proposal breakdown enabled state
  const [breakdownEnabled, setBreakdownEnabled] = useState(false);

  // Modal states
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [editForm] = Form.useForm();

  // Document preview modal states
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [previewDocument, setPreviewDocument] = useState<any>(null);

  // Proposal cards edit modal states
  const [cardEditModalVisible, setCardEditModalVisible] = useState(false);
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [cardEditForm] = Form.useForm();

  // Get storage key for proposal cards
  const getCardsStorageKey = useCallback(() => {
    const projectId = project?.id || stage?.projectId || 'default';
    return `proposal_cards_${projectId}`;
  }, [project?.id, stage?.projectId]);

  // Proposal cards state management - 初始化为空数组
  const [proposalCards, setProposalCards] = useState<any[]>([]);

  // Load cards when project changes
  useEffect(() => {
    try {
      const storageKey = getCardsStorageKey();
      
      // 强制清理旧数据，确保加载新的默认卡片
      console.log('Force clearing old proposal cards data...');
      localStorage.removeItem(storageKey);
      
      // 创建新的默认卡片
      const cardsToLoad = createDefaultCards();
      console.log('Created new default proposal cards:', cardsToLoad.length, 'cards');
      
      setProposalCards(cardsToLoad);
    } catch (error) {
      console.error('Error loading proposal cards:', error);
      // 出错时使用默认数据
      const defaultCards = createDefaultCards();
      setProposalCards(defaultCards);
    }
  }, [project?.id, stage?.projectId, getCardsStorageKey]);

  // Save proposal cards to localStorage whenever cards change
  useEffect(() => {
    if (proposalCards.length > 0) {
      try {
        const storageKey = getCardsStorageKey();
        localStorage.setItem(storageKey, JSON.stringify(proposalCards));
        console.log('Saved proposal cards to localStorage:', proposalCards.length, 'cards');
      } catch (error) {
        console.error('Error saving proposal cards to localStorage:', error);
      }
    }
  }, [proposalCards, getCardsStorageKey]);

  // Custom upload request to ensure success
  const customUploadRequest = (options: any) => {
    const { onSuccess, onError, file } = options;
    
    // Simulate upload delay
    setTimeout(() => {
      try {
        // Create appropriate URL based on file type
        let fileUrl;
        if (file.type.startsWith('image/')) {
          // For images, create object URL to display the actual uploaded image
          fileUrl = URL.createObjectURL(file);
        } else if (file.type === 'application/pdf') {
          // For PDFs, use dummy PDF URL
          fileUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        } else {
          // For other files, use generic URL
          fileUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
        }
        
        // Return a proper JSON response object
        const response = {
          success: true,
          data: {
            fileUrl: fileUrl,
            fileName: file.name,
            fileSize: file.size,
            uploadTime: new Date().toISOString()
          }
        };
        onSuccess(response);
      } catch (error) {
        onError(error);
      }
    }, 1000);
  };

  // Handle file upload
  const handleFileUpload = (info: any) => {
    // 处理多文件上传
    if (info.fileList && info.fileList.length > 0) {
      const uploadedFiles = info.fileList.filter((file: any) => file.status === 'done');
      
      if (uploadedFiles.length > 0) {
        const newRecords = uploadedFiles.map((file: any, index: number) => {
          const response = file.response;
          
          if (response && response.success) {
            const { fileName, fileUrl, fileSize } = response.data;
            
            return {
              key: String(versions.length + index + 1),
              version: `V${versions.length + index + 1}.0${versions.length === 0 && index === 0 ? ' (Latest)' : ''}`,
              date: dayjs().format('YYYY-MM-DD'),
              uploadedBy: 'Current User',
              notes: fileName,
              status: 'internal',
              fileName: fileName,
              fileUrl: fileUrl,
              fileSize: `${(fileSize / 1024 / 1024).toFixed(1)} MB`
            };
          }
          return null;
        }).filter(Boolean);

        if (newRecords.length > 0) {
          // Update other versions to remove 'Latest' tag
          const updatedVersions = versions.map((v: any) => ({
            ...v,
            version: v.version.replace(' (Latest)', '')
          }));

          // Add new versions to the top of the list
          setVersions([...newRecords, ...updatedVersions]);
          
          // Log success without showing message
          console.log(`${newRecords.length} file(s) uploaded successfully`);
        }
      }
      
      // Check for failed uploads
      const failedFiles = info.fileList.filter((file: any) => file.status === 'error');
      if (failedFiles.length > 0) {
        failedFiles.forEach((file: any) => {
          message.error(`${file.name} upload failed.`);
        });
      }
    }
  };

  // Edit version
  const handleEdit = (record: any) => {
    setSelectedRecord(record);
    editForm.setFieldsValue({
      version: record.version,
      date: dayjs(record.date),
      uploadedBy: record.uploadedBy,
      notes: record.notes,
      status: record.status
    });
    setEditModalVisible(true);
  };

  // Delete version
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete version ${record.version}? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk() {
        const newVersions = versions.filter((item: any) => item.key !== record.key);
        setVersions(newVersions);
        message.success('Version deleted successfully');
      },
    });
  };

  // Save edit
  const handleEditSave = () => {
    if (!selectedRecord) return;
    
    editForm.validateFields().then(values => {
      const newVersions = versions.map((item: any) => 
        item.key === selectedRecord.key 
          ? {
              ...item,
              version: values.version,
              date: values.date.format('YYYY-MM-DD'),
              uploadedBy: values.uploadedBy,
              notes: values.notes,
              status: values.status
            }
          : item
      );
      setVersions(newVersions);
      setEditModalVisible(false);
      message.success('Version information updated successfully');
    });
  };

  // Handle submit and proceed
  const handleSubmitAndProceed = () => {
    message.loading('Submitting...', 1.5)
      .then(() => {
        message.success('Submitted successfully! Proceeding to the next stage.');
        if (onSave) {
          onSave({
            status: 'completed',
            versions: versions,
            proposalCards: proposalCards
          });
        }
        // 自动跳转到下一个阶段
        if (onProceed) {
          onProceed('contract');
        }
      });
  };

  // Handle version click to view document
  const handleVersionClick = (record: any) => {
    const fileName = record.fileName || record.notes || `${record.version.replace(' (Latest)', '')}_proposal.pdf`;
    
    try {
      // Set preview document data
      setPreviewDocument({
        ...record,
        fileName: fileName,
        fileUrl: record.fileUrl || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
      });
      
      // Open preview modal
      setPreviewModalVisible(true);
      
      // Do not show success message
      console.log('Opening document:', fileName);
    } catch (error) {
      console.error('Error opening document:', error);
      message.error('Failed to open document');
    }
  };

  // Handle document download
  const handleDownload = (record: any) => {
    try {
      const fileName = record.fileName || record.notes || `${record.version.replace(' (Latest)', '')}_proposal.pdf`;
      const documentUrl = record.fileUrl || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
      
      // Create a temporary link for download
      const link = document.createElement('a');
      link.href = documentUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success(`Downloading document: ${fileName}`);
    } catch (error) {
      console.error('Error downloading document:', error);
      message.error('Failed to download document');
    }
  };

  // Proposal version table column definitions
  const columns = [
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      render: (text: string, record: any) => (
        <button 
          className="version-link" 
          onClick={() => handleVersionClick(record)}
          style={{ cursor: 'pointer', color: '#1890ff', border: 'none', background: 'none', padding: 0, textDecoration: 'underline' }}
        >
          {text}
        </button>
      )
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: 'Uploaded By',
      dataIndex: 'uploadedBy',
      key: 'uploadedBy',
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let className = '';
        let text = '';

        if (status.toLowerCase() === 'submitted') {
          className = 'status-submitted';
          text = 'Submitted';
        } else if (status.toLowerCase() === 'internal') {
          className = 'status-internal';
          text = 'Internal';
        }

        return <span className={`status ${className}`}>{text}</span>;
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      fixed: 'right' as const,
      render: (_: any, record: any) => (
        <div className="action-icons">
          <Tooltip title="Edit">
            <EditOutlined 
              className="action-icon edit-icon" 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <DeleteOutlined 
              className="action-icon delete-icon" 
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  // Handle card file upload
  const handleCardFileUpload = (cardId: string, info: any) => {
    // 处理多文件上传
    if (info.fileList && info.fileList.length > 0) {
      const uploadedFiles = info.fileList.filter((file: any) => file.status === 'done');
      
      if (uploadedFiles.length > 0) {
        const newFiles = uploadedFiles.map((file: any) => {
          const response = file.response;
          
          if (response && response.success) {
            const { fileName, fileUrl, fileSize } = response.data;
            
            return {
              name: fileName,
              size: `${(fileSize / 1024 / 1024).toFixed(1)} MB`,
              url: fileUrl,
              uploadTime: new Date().toISOString()
            };
          }
          return null;
        }).filter(Boolean);

        if (newFiles.length > 0) {
          // Update the specific card's files
          setProposalCards((prevCards: any[]) => 
            prevCards.map((card: any) => 
              card.id === cardId 
                ? { ...card, files: [...card.files, ...newFiles] }
                : card
            )
          );
          
          const cardTitle = proposalCards.find((c: any) => c.id === cardId)?.title;
          if (newFiles.length === 1) {
            message.success(`File uploaded to ${cardTitle} successfully`);
          } else {
            message.success(`${newFiles.length} files uploaded to ${cardTitle} successfully`);
          }
        }
      }
      
      // Check for failed uploads
      const failedFiles = info.fileList.filter((file: any) => file.status === 'error');
      if (failedFiles.length > 0) {
        failedFiles.forEach((file: any) => {
          message.error(`${file.name} upload failed.`);
        });
      }
    }
  };

  // Handle remove file from card
  const handleRemoveCardFile = (cardId: string, fileIndex: number) => {
    setProposalCards((prevCards: any[]) => 
      prevCards.map((card: any) => 
        card.id === cardId 
          ? { ...card, files: card.files.filter((_: any, index: number) => index !== fileIndex) }
          : card
      )
    );
    message.success('File removed successfully');
  };

  // Handle edit card
  const handleEditCard = (card: any) => {
    setSelectedCard(card);
    cardEditForm.setFieldsValue({
      title: card.title,
      dueDate: dayjs(card.dueDate),
      owner: card.owner,
      status: card.status,
      description: card.description
    });
    setCardEditModalVisible(true);
  };

  // Handle save card edit
  const handleSaveCardEdit = () => {
    if (!selectedCard) return;
    
    cardEditForm.validateFields().then(values => {
      setProposalCards((prevCards: any[]) => 
        prevCards.map((card: any) => 
          card.id === selectedCard.id 
            ? {
                ...card,
                title: values.title,
                dueDate: values.dueDate.format('YYYY-MM-DD'),
                owner: values.owner,
                status: values.status,
                description: values.description
              }
            : card
        )
      );
      setCardEditModalVisible(false);
      message.success('Card information updated successfully');
    });
  };

  // Handle view card file
  const handleViewCardFile = (file: any) => {
    setPreviewDocument({
      fileName: file.name,
      fileUrl: file.url || 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
    });
    setPreviewModalVisible(true);
  };

  // Render document preview content based on file type
  const renderDocumentPreview = () => {
    if (!previewDocument) return null;

    const { fileName, fileUrl } = previewDocument;
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'pdf':
        return (
          <div style={{ width: '100%', height: '600px' }}>
            <iframe
              src={fileUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                borderRadius: '4px'
              }}
              title={`Preview: ${fileName}`}
            />
          </div>
        );

      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '20px',
            maxHeight: '600px',
            overflow: 'auto'
          }}>
            <img
              src={fileUrl}
              alt={fileName}
              style={{
                maxWidth: '100%',
                maxHeight: '550px',
                objectFit: 'contain',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              onError={(e) => {
                console.error('Image failed to load:', fileUrl);
                e.currentTarget.src = 'https://via.placeholder.com/400x300?text=Image+Not+Available';
              }}
            />
          </div>
        );

      case 'txt':
      case 'md':
        return (
          <div style={{ 
            padding: '20px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            overflow: 'auto'
          }}>
            <iframe
              src={fileUrl}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                backgroundColor: 'white'
              }}
              title={`Preview: ${fileName}`}
            />
          </div>
        );

      case 'doc':
      case 'docx':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '20px', color: '#1890ff' }}>
              📄
            </div>
            <h3 style={{ marginBottom: '16px', color: '#333' }}>
              {fileName}
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              This file type requires external application to view.
            </p>
            <Button 
              type="primary" 
              onClick={() => {
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download File
            </Button>
          </div>
        );

      default:
        return (
          <div style={{ 
            textAlign: 'center', 
            padding: '40px',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            height: '600px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <div style={{ fontSize: '48px', marginBottom: '20px', color: '#faad14' }}>
              ❓
            </div>
            <h3 style={{ marginBottom: '16px', color: '#333' }}>
              Preview Not Available
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              Cannot preview {fileExtension?.toUpperCase()} files in browser.
            </p>
            <Button 
              type="primary" 
              onClick={() => {
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = fileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              }}
            >
              Download File
            </Button>
          </div>
        );
    }
  };

  // 创建默认卡片的函数
  const createDefaultCards = () => {
    return [
      {
        id: '1',
        title: 'Commercial Pricing',
        dueDate: '2024-02-15',
        owner: 'Sales Team',
        status: 'internal',
        description: 'Commercial pricing strategy, competitive analysis, value proposition, discount structures, and business terms negotiation',
        files: [],
        overdue: false,
        features: [
          'Dynamic pricing calculator',
          'Competitive benchmark analysis',
          'Value-based pricing model',
          'Discount approval workflow'
        ]
      },
      {
        id: '2', 
        title: 'Technical Solution',
        dueDate: '2024-02-18',
        owner: 'Solution Architect',
        status: 'internal',
        description: 'Technical architecture, implementation methodology, system integration plans, technology stack selection, and development roadmap',
        files: [],
        overdue: false,
        features: [
          'Architecture diagram generation',
          'Technology stack recommendation',
          'Integration complexity analysis',
          'Performance benchmarking'
        ]
      },
      {
        id: '3',
        title: 'Delivery & Implementation',
        dueDate: '2024-02-20',
        owner: 'Delivery Manager',
        status: 'internal', 
        description: 'Project timeline, milestone planning, resource allocation, delivery phases, quality assurance, and acceptance criteria',
        files: [],
        overdue: false,
        features: [
          'Automated timeline generation',
          'Resource capacity planning',
          'Risk mitigation planning',
          'Quality gate definitions'
        ]
      },
      {
        id: '4',
        title: 'Financial Analysis',
        dueDate: '2024-02-22',
        owner: 'Finance Team',
        status: 'internal',
        description: 'Cost breakdown analysis, pricing strategy, budget planning, ROI calculations, payment terms, and financial risk assessment',
        files: [],
        overdue: false,
        features: [
          'Cost estimation calculator',
          'ROI projection modeling',
          'Payment schedule optimizer',
          'Budget variance analysis'
        ]
      },
      {
        id: '5',
        title: 'Legal & Compliance',
        dueDate: '2024-02-25',
        owner: 'Legal Team',
        status: 'internal',
        description: 'Legal terms, compliance requirements, contract clauses, intellectual property rights, liability coverage, and regulatory compliance documentation',
        files: [],
        overdue: false,
        features: [
          'Contract template generation',
          'Compliance checklist validation', 
          'Legal risk assessment',
          'IP protection analysis'
        ]
      },
      {
        id: '6',
        title: 'Additional Requirements',
        dueDate: '2024-02-28',
        owner: 'Project Manager',
        status: 'internal',
        description: 'Additional documentation, supplementary materials, stakeholder requirements, change management, and project governance',
        files: [],
        overdue: false,
        features: [
          'Stakeholder mapping tool',
          'Change impact assessment',
          'Communication plan template',
          'Governance framework setup'
        ]
      }
    ];
  };

  return (
    <div className="proposal-container">
      {/* Proposal version management card */}
      <Card className="proposal-version-card" title="Proposal Documents (All Versions)">
        {/* File upload area */}
        <Upload.Dragger
          name="files"
          customRequest={customUploadRequest}
          onChange={handleFileUpload}
          className="proposal-upload-area"
          beforeUpload={(file) => {
            const isLt20M = file.size / 1024 / 1024 < 20;
            if (!isLt20M) {
              message.error('File must be smaller than 20MB!');
              return false;
            }
            return true;
          }}
          showUploadList={false}
          multiple={true}
        >
          <p className="ant-upload-drag-icon">
            <CloudUploadOutlined style={{ fontSize: '25px', color: '#FF7A00' }} />
          </p>
          <p className="ant-upload-text">Drag & drop multiple files here or click to browse (Maximum file size 20MB each)</p>
        </Upload.Dragger>

        {/* Version table */}
        <Table
          columns={columns}
          dataSource={versions}
          pagination={false}
          className="version-table"
        />
      </Card>

      {/* Proposal breakdown section */}
      <div className="proposal-breakdown">
        <div className="section-header">
          <h2 className="section-title">Proposal Breakdown</h2>
          <Switch
            checked={breakdownEnabled}
            onChange={setBreakdownEnabled}
            className="breakdown-switch"
          />
        </div>

        {breakdownEnabled && (
          <div className="proposal-cards">
            {proposalCards.map((card: any, index: number) => (
              <Card
                key={card.id || index}
                className="proposal-card"
                title={
                  <div className="proposal-card-header">
                    <h3 className="proposal-card-title">{card.title}</h3>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {card.overdue && (
                        <Tooltip title="This item is overdue by more than 3 working days">
                          <div className="due-warning">
                            <ExclamationCircleOutlined />
                            <span>Overdue ({'>'}3 working days)</span>
                          </div>
                        </Tooltip>
                      )}
                      <Tooltip title="Edit Card">
                        <EditOutlined 
                          style={{ cursor: 'pointer', color: '#1890ff' }}
                          onClick={() => handleEditCard(card)}
                        />
                      </Tooltip>
                    </div>
                  </div>
                }
              >
                <div className="proposal-info">
                  <div className="proposal-info-item">
                    <span className="proposal-info-label">Due Date</span>
                    <span className="proposal-info-value">{card.dueDate}</span>
                  </div>
                  <div className="proposal-info-item">
                    <span className="proposal-info-label">Owner</span>
                    <span className="proposal-info-value">{card.owner}</span>
                  </div>
                  <div className="proposal-info-item">
                    <span className="proposal-info-label">Status</span>
                    <span className={`status status-${card.status}`}>
                      {card.status === 'submitted' ? 'Submitted' :
                       card.status === 'internal' && card.files.length > 0 ? 'In Progress' : 'Not Started'}
                    </span>
                  </div>
                  {card.description && (
                    <div className="proposal-info-item" style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
                      <span className="proposal-info-label">Description</span>
                      <span className="proposal-info-value" style={{ marginTop: '4px' }}>{card.description}</span>
                    </div>
                  )}
                  
                  {/* Features section */}
                  {card.features && card.features.length > 0 && (
                    <div className="proposal-info-item" style={{ flexDirection: 'column', alignItems: 'flex-start' }}>
                      <span className="proposal-info-label">Key Features</span>
                      <div style={{ marginTop: '8px' }}>
                        {card.features.map((feature: string, index: number) => (
                          <div key={index} style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            marginBottom: '4px',
                            fontSize: '13px',
                            color: '#555'
                          }}>
                            <span style={{ 
                              width: '6px', 
                              height: '6px', 
                              backgroundColor: '#FF7A00', 
                              borderRadius: '50%', 
                              marginRight: '8px',
                              flexShrink: 0
                            }}></span>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* File upload area */}
                <Upload.Dragger
                  name="files"
                  customRequest={customUploadRequest}
                  onChange={(info: any) => handleCardFileUpload(card.id, info)}
                  className="file-upload-area"
                  showUploadList={false}
                  beforeUpload={(file: any) => {
                    const isLt20M = file.size / 1024 / 1024 < 20;
                    if (!isLt20M) {
                      message.error('File must be smaller than 20MB!');
                      return false;
                    }
                    return true;
                  }}
                  multiple={true}
                >
                  <p className="ant-upload-drag-icon">
                    <CloudUploadOutlined style={{ fontSize: '25px', color: '#FF7A00' }} />
                  </p>
                  <p className="ant-upload-text">Drag & drop multiple files here or click to browse (Maximum file size 20MB each)</p>
                </Upload.Dragger>

                {/* Uploaded files list */}
                {card.files.length > 0 && (
                  <div className="attached-files">
                    {card.files.map((file: any, fileIndex: number) => (
                      <div key={fileIndex} className="attached-file">
                        <span className="file-icon" onClick={() => handleViewCardFile(file)} style={{ cursor: 'pointer', color: '#1890ff' }}>
                          <i className="file-icon-inner"></i>📄
                        </span>
                        <span 
                          className="file-name" 
                          onClick={() => handleViewCardFile(file)}
                          style={{ cursor: 'pointer', color: '#1890ff' }}
                        >
                          {file.name}
                        </span>
                        <span className="file-size">{file.size}</span>
                        <Button 
                          type="text" 
                          className="file-remove" 
                          icon={<DeleteOutlined />} 
                          onClick={() => handleRemoveCardFile(card.id, fileIndex)} 
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Workflow actions area */}
      <div className="workflow-actions">
        <Button
          type="default"
          className="btn-workflow"
          onClick={handleSubmitAndProceed}
          icon={<RightOutlined />}
        >
          Save & Proceed
        </Button>
      </div>

      {/* Edit version modal */}
      <Modal
        title="Edit Version Information"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleEditSave}
        okText="Save"
        cancelText="Cancel"
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
        >
          <Form.Item
            name="version"
            label="Version Number"
            rules={[{ required: true, message: 'Please enter version number' }]}
          >
            <Input placeholder="Please enter version number" />
          </Form.Item>

          <Form.Item
            name="date"
            label="Upload Date"
            rules={[{ required: true, message: 'Please select upload date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="uploadedBy"
            label="Uploaded By"
            rules={[{ required: true, message: 'Please enter uploader name' }]}
          >
            <Input placeholder="Please enter uploader name" />
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select placeholder="Please select status">
              <Option value="internal">Internal</Option>
              <Option value="submitted">Submitted</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea rows={4} placeholder="Please enter notes" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Document preview modal */}
      <Modal
        title={`Document Preview - ${previewDocument?.version || 'Unknown Version'}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        width={900}
        style={{ top: 20 }}
        footer={[
          <Button key="download" onClick={() => handleDownload(previewDocument)}>
            Download
          </Button>,
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            Close
          </Button>
        ]}
      >
        {renderDocumentPreview()}
      </Modal>

      {/* Card edit modal */}
      <Modal
        title="Edit Proposal Card"
        open={cardEditModalVisible}
        onCancel={() => setCardEditModalVisible(false)}
        onOk={handleSaveCardEdit}
        okText="Save"
        cancelText="Cancel"
        width={600}
      >
        <Form
          form={cardEditForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter title' }]}
          >
            <Input placeholder="Please enter title" />
          </Form.Item>

          <Form.Item
            name="dueDate"
            label="Due Date"
            rules={[{ required: true, message: 'Please select due date' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="owner"
            label="Owner"
            rules={[{ required: true, message: 'Please enter owner name' }]}
          >
            <Input placeholder="Please enter owner name" />
          </Form.Item>

          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select status' }]}
          >
            <Select placeholder="Please select status">
              <Option value="internal">Internal</Option>
              <Option value="submitted">Submitted</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea rows={4} placeholder="Please enter description" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Proposal;
