import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Typography } from 'antd';
import { LockOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import '../styles/ResetPassword.css';

const { Title, Text } = Typography;

const ResetPassword: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [resetSuccess, setResetSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get('token');

  useEffect(() => {
    // 验证重置令牌
    const validateToken = async () => {
      if (!token) {
        message.error('Invalid reset link');
        navigate('/login');
        return;
      }

      try {
        // 模拟验证令牌的API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        setTokenValid(true);
      } catch (error) {
        message.error('Reset link has expired or is invalid');
        navigate('/login');
      }
    };

    validateToken();
  }, [token, navigate]);

  const onFinish = async (values: { password: string; confirmPassword: string }) => {
    try {
      setLoading(true);
      // 模拟重置密码的API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('Password reset successfully');
      setResetSuccess(true);
    } catch (error) {
      message.error('Failed to reset password, please try again');
      console.error('Reset password error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!tokenValid) {
    return (
      <div className="reset-password-container">
        <div className="reset-password-card-wrapper">
          <div className="reset-password-card">
            <Title level={2} className="reset-password-title">Validating...</Title>
            <Text className="reset-password-subtitle">
              Please wait while we validate your reset link
            </Text>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="reset-password-container">
      {/* Top Navigation */}
      <div className="reset-password-header">
        <div className="logo">
          <Link to="/">SmartLTC</Link>
        </div>
        <div className="nav-links">
          <Link to="/login" className="sign-in-link">Sign In</Link>
          <Link to="/register" className="sign-up-link">Sign Up</Link>
        </div>
      </div>

      {/* Reset Password Card */}
      <div className="reset-password-card-wrapper">
        <div className="reset-password-card">
          {!resetSuccess ? (
            <>
              <Title level={2} className="reset-password-title">Reset Password</Title>
              <Text className="reset-password-subtitle">
                Enter your new password below
              </Text>

              <Form
                name="reset-password"
                className="reset-password-form"
                onFinish={onFinish}
                size="large"
              >
                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: 'Please enter your new password' },
                    { min: 6, message: 'Password must be at least 6 characters' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="New Password"
                    className="reset-password-input"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: 'Please confirm your new password' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('The two passwords do not match'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="Confirm New Password"
                    className="reset-password-input"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="reset-password-button"
                  >
                    Reset Password
                  </Button>
                </Form.Item>

                <div className="back-to-login">
                  <Link to="/login">Back to Sign In</Link>
                </div>
              </Form>
            </>
          ) : (
            <>
              <div className="success-icon">
                <CheckCircleOutlined />
              </div>
              <Title level={2} className="reset-password-title">Password Reset Successfully</Title>
              <Text className="reset-password-subtitle">
                Your password has been reset successfully. You can now sign in with your new password.
              </Text>

              <div className="success-actions">
                <Button
                  type="primary"
                  className="reset-password-button"
                  onClick={() => navigate('/login')}
                >
                  Go to Sign In
                </Button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="reset-password-footer">
        <div className="footer-content">
          <div className="footer-copyright">
            <span>© 2024 SmartLTC - AI-Powered Business Operations Integrated Management Platform</span>
            <br />
            <span>Powered by Vlisoft.com • Empowering business with SmartLTC</span>
          </div>
          <div className="footer-buttons">
            <Link to="/privacy" className="footer-button">Privacy Notice</Link>
            <Link to="/terms" className="footer-button">Terms & Privacy</Link>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="decoration-line line-1"></div>
      <div className="decoration-line line-2"></div>
      <div className="decoration-line line-3"></div>
    </div>
  );
};

export default ResetPassword; 