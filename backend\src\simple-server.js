const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { v4: uuidv4 } = require('uuid');

const app = express();
const PORT = 5002;

// 中间件
app.use(cors());
app.use(bodyParser.json());

// 内存数据存储
const projects = [];
const stages = [];

// 不再自动创建示例数据，保持数据库干净
// 用户可以根据需要手动创建项目和阶段
const initSampleData = () => {
  console.log('数据存储已初始化，等待用户创建项目...');
  // 保持空的项目和阶段列表
};

// 初始化空数据存储
initSampleData();

// 基本路由
app.get('/', (req, res) => {
  res.send('LTC项目管理系统API服务正在运行');
});

// 项目API路由
app.get('/api/projects', (req, res) => {
  res.json(projects);
});

app.get('/api/projects/:id', (req, res) => {
  const project = projects.find(p => p.id === req.params.id);
  if (!project) {
    return res.status(404).json({ message: 'Project not found' });
  }
  res.json(project);
});

app.post('/api/projects', (req, res) => {
  const newProject = {
    ...req.body,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  projects.push(newProject);
  res.status(201).json(newProject);
});

app.put('/api/projects/:id', (req, res) => {
  const index = projects.findIndex(p => p.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ message: 'Project not found' });
  }

  projects[index] = {
    ...projects[index],
    ...req.body,
    updatedAt: new Date().toISOString()
  };

  res.json(projects[index]);
});

app.delete('/api/projects/:id', (req, res) => {
  const index = projects.findIndex(p => p.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ message: 'Project not found' });
  }

  projects.splice(index, 1);
  // 删除相关的阶段
  const projectStages = stages.filter(s => s.projectId === req.params.id);
  projectStages.forEach(stage => {
    const stageIndex = stages.findIndex(s => s.id === stage.id);
    if (stageIndex !== -1) {
      stages.splice(stageIndex, 1);
    }
  });

  res.status(204).send();
});

// 项目阶段API路由
app.get('/api/project-stages/project/:projectId', (req, res) => {
  const projectStages = stages.filter(s => s.projectId === req.params.projectId);
  res.json(projectStages);
});

app.get('/api/project-stages/:id', (req, res) => {
  const stage = stages.find(s => s.id === req.params.id);
  if (!stage) {
    return res.status(404).json({ message: 'Stage not found' });
  }
  res.json(stage);
});

app.post('/api/project-stages', (req, res) => {
  const newStage = {
    ...req.body,
    id: uuidv4(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  stages.push(newStage);
  res.status(201).json(newStage);
});

app.put('/api/project-stages/:id', (req, res) => {
  const index = stages.findIndex(s => s.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ message: 'Stage not found' });
  }

  stages[index] = {
    ...stages[index],
    ...req.body,
    updatedAt: new Date().toISOString()
  };

  res.json(stages[index]);
});

app.delete('/api/project-stages/:id', (req, res) => {
  const index = stages.findIndex(s => s.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ message: 'Stage not found' });
  }

  stages.splice(index, 1);
  res.status(204).send();
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`LTC项目管理系统API服务运行在 http://localhost:${PORT}`);
});
