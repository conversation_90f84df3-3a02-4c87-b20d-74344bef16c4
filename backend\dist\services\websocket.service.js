"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketService = void 0;
const socket_io_1 = require("socket.io");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const typeorm_1 = require("typeorm");
const User_1 = require("../entities/User");
const ProjectCollaborator_1 = require("../entities/ProjectCollaborator");
class WebSocketService {
    constructor(server) {
        this.userRepository = (0, typeorm_1.getRepository)(User_1.User);
        this.collaboratorRepository = (0, typeorm_1.getRepository)(ProjectCollaborator_1.ProjectCollaborator);
        this.projectRooms = new Map();
        this.io = new socket_io_1.Server(server, {
            cors: {
                origin: process.env.FRONTEND_URL || "http://localhost:3000",
                methods: ["GET", "POST"],
                credentials: true
            }
        });
        this.setupMiddleware();
        this.setupEventHandlers();
    }
    setupMiddleware() {
        this.io.use(async (socket, next) => {
            try {
                const token = socket.handshake.auth.token;
                if (!token) {
                    return next(new Error('Authentication error'));
                }
                const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your_secret_key');
                const user = await this.userRepository.findOne(decoded.userId);
                if (!user) {
                    return next(new Error('User not found'));
                }
                socket.userId = user.id;
                socket.user = user;
                next();
            }
            catch (error) {
                next(new Error('Authentication error'));
            }
        });
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`用户 ${socket.user.username} 已连接`);
            socket.on('join-project', async (projectId) => {
                try {
                    const hasAccess = await this.checkProjectAccess(socket.userId, projectId);
                    if (!hasAccess) {
                        socket.emit('error', { message: '没有项目访问权限' });
                        return;
                    }
                    socket.join(`project:${projectId}`);
                    this.updateOnlineUsers(projectId, socket.user, 'join');
                    socket.to(`project:${projectId}`).emit('user-joined', {
                        userId: socket.userId,
                        username: socket.user.username,
                        avatar: socket.user.avatar
                    });
                    const onlineUsers = this.getOnlineUsers(projectId);
                    socket.emit('online-users', onlineUsers);
                    console.log(`用户 ${socket.user.username} 加入项目 ${projectId}`);
                }
                catch (error) {
                    socket.emit('error', { message: '加入项目失败' });
                }
            });
            socket.on('leave-project', (projectId) => {
                socket.leave(`project:${projectId}`);
                this.updateOnlineUsers(projectId, socket.user, 'leave');
                socket.to(`project:${projectId}`).emit('user-left', {
                    userId: socket.userId,
                    username: socket.user.username
                });
                console.log(`用户 ${socket.user.username} 离开项目 ${projectId}`);
            });
            socket.on('project-activity', (data) => {
                socket.to(`project:${data.projectId}`).emit('activity-update', {
                    ...data,
                    user: {
                        id: socket.userId,
                        username: socket.user.username,
                        avatar: socket.user.avatar
                    },
                    timestamp: new Date()
                });
                this.addProjectActivity(data.projectId, {
                    ...data,
                    userId: socket.userId,
                    user: socket.user,
                    timestamp: new Date()
                });
            });
            socket.on('editing-status', (data) => {
                socket.to(`project:${data.projectId}`).emit('user-editing', {
                    userId: socket.userId,
                    username: socket.user.username,
                    section: data.section,
                    isEditing: data.isEditing
                });
            });
            socket.on('new-comment', (data) => {
                var _a;
                const comment = {
                    id: Date.now().toString(),
                    content: data.content,
                    author: {
                        id: socket.userId,
                        username: socket.user.username,
                        avatar: socket.user.avatar
                    },
                    timestamp: new Date(),
                    stageId: data.stageId
                };
                this.io.to(`project:${data.projectId}`).emit('comment-added', comment);
                if ((_a = data.mentions) === null || _a === void 0 ? void 0 : _a.length) {
                    this.sendMentionNotifications(data.projectId, data.mentions, comment);
                }
            });
            socket.on('permission-updated', (data) => {
                this.io.to(`user:${data.targetUserId}`).emit('permission-changed', {
                    projectId: data.projectId,
                    newRole: data.newRole,
                    permissions: data.permissions,
                    updatedBy: socket.user.username
                });
                socket.to(`project:${data.projectId}`).emit('collaborator-updated', data);
            });
            socket.on('disconnect', () => {
                this.handleUserDisconnect(socket.userId, socket.user);
                console.log(`用户 ${socket.user.username} 已断开连接`);
            });
        });
    }
    async checkProjectAccess(userId, projectId) {
        const collaborator = await this.collaboratorRepository.findOne({
            where: {
                project: { id: projectId },
                user: { id: userId },
                status: 'accepted'
            }
        });
        return !!collaborator;
    }
    updateOnlineUsers(projectId, user, action) {
        if (!this.projectRooms.has(projectId)) {
            this.projectRooms.set(projectId, {
                projectId,
                onlineUsers: new Map(),
                activities: []
            });
        }
        const room = this.projectRooms.get(projectId);
        if (action === 'join') {
            room.onlineUsers.set(user.id, {
                userId: user.id,
                username: user.username,
                avatar: user.avatar,
                lastSeen: new Date()
            });
        }
        else {
            room.onlineUsers.delete(user.id);
        }
        this.io.to(`project:${projectId}`).emit('online-users-updated', Array.from(room.onlineUsers.values()));
    }
    getOnlineUsers(projectId) {
        const room = this.projectRooms.get(projectId);
        return room ? Array.from(room.onlineUsers.values()) : [];
    }
    addProjectActivity(projectId, activity) {
        if (!this.projectRooms.has(projectId)) {
            this.projectRooms.set(projectId, {
                projectId,
                onlineUsers: new Map(),
                activities: []
            });
        }
        const room = this.projectRooms.get(projectId);
        room.activities.unshift(activity);
        if (room.activities.length > 100) {
            room.activities = room.activities.slice(0, 100);
        }
    }
    sendMentionNotifications(projectId, mentionedUserIds, comment) {
        mentionedUserIds.forEach(userId => {
            this.io.to(`user:${userId}`).emit('mentioned', {
                projectId,
                comment,
                type: 'mention'
            });
        });
    }
    handleUserDisconnect(userId, user) {
        for (const [projectId, room] of this.projectRooms) {
            if (room.onlineUsers.has(userId)) {
                room.onlineUsers.delete(userId);
                this.io.to(`project:${projectId}`).emit('user-left', {
                    userId,
                    username: user.username
                });
                this.io.to(`project:${projectId}`).emit('online-users-updated', Array.from(room.onlineUsers.values()));
            }
        }
    }
    broadcastProjectActivity(projectId, activity) {
        this.io.to(`project:${projectId}`).emit('activity-update', activity);
        this.addProjectActivity(projectId, activity);
    }
    sendNotification(userId, notification) {
        this.io.to(`user:${userId}`).emit('notification', notification);
    }
    broadcastPermissionChange(projectId, data) {
        this.io.to(`project:${projectId}`).emit('permission-changed', data);
    }
}
exports.WebSocketService = WebSocketService;
//# sourceMappingURL=websocket.service.js.map