import React, { useState, useEffect, useCallback } from 'react';
import {
  Drawer,
  List,
  Avatar,
  Button,
  Typography,
  Space,
  Badge,
  Empty,
  Spin,
  Tag,
  message,
  Select,
  Modal,
  Tooltip
} from 'antd';
import {
  UserAddOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  SettingOutlined,
  CheckOutlined,
  EyeOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { Notification, NotificationStats } from '../../types';
import notificationService from '../../services/notification.service';
import './NotificationPanel.css';

const { Text, Title } = Typography;
const { Option } = Select;

interface NotificationPanelProps {
  visible: boolean;
  onClose: () => void;
  onUnreadCountChange: (count: number) => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({
  visible,
  onClose,
  onUnreadCountChange
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({ unreadCount: 0, totalCount: 0 });
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'starred'>('all');
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const navigate = useNavigate();

  const loadNotifications = useCallback(async () => {
    setLoading(true);
    try {
      const data = await notificationService.getNotifications();
      setNotifications(data.notifications);
      setStats(data.stats);
      onUnreadCountChange(data.stats.unreadCount);
    } catch (error) {
      message.error('Failed to load notifications');
    } finally {
      setLoading(false);
    }
  }, [onUnreadCountChange]);

  useEffect(() => {
    let filtered = notifications;
    if (filter === 'unread') {
      filtered = notifications.filter(n => !n.isRead);
    } else if (filter === 'starred') {
        filtered = notifications.filter(n => n.isStarred);
    }
    setFilteredNotifications(filtered);
  }, [notifications, filter]);

  useEffect(() => {
    if (visible) {
      loadNotifications();
    }
  }, [visible, loadNotifications]);

  const getNotificationIcon = (type: Notification['type']) => {
    const iconStyle = { fontSize: 14, color: '#8c8c8c' };
    switch (type) {
      case 'team':
        return <UserAddOutlined style={iconStyle} />;
      case 'success':
        return <CheckCircleOutlined style={iconStyle} />;
      case 'warning':
        return <ExclamationCircleOutlined style={iconStyle} />;
      case 'error':
        return <ExclamationCircleOutlined style={iconStyle} />;
      case 'mention':
        return <MessageOutlined style={iconStyle} />;
      case 'system':
        return <SettingOutlined style={iconStyle} />;
      default:
        return <InfoCircleOutlined style={iconStyle} />;
    }
  };

  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      try {
        await notificationService.markAsRead(notification.id);
        setNotifications(prev => 
          prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
        );
        setStats(prev => {
          const newStats = { ...prev, unreadCount: prev.unreadCount - 1 };
          onUnreadCountChange(newStats.unreadCount);
          return newStats;
        });
      } catch (error) {
        console.error('Failed to mark as read:', error);
      }
    }

    setSelectedNotification(notification);
    setDetailModalVisible(true);
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllAsRead();
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setStats(prev => ({ ...prev, unreadCount: 0 }));
      onUnreadCountChange(0);
      message.success('All notifications marked as read');
    } catch (error) {
      message.error('Failed to mark all as read');
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const handleViewAll = () => {
    navigate('/messages');
    onClose();
  };

  const handleCloseDetailModal = () => {
    setDetailModalVisible(false);
    setSelectedNotification(null);
  };

  const handleNavigateToAction = () => {
    if (selectedNotification?.actionUrl) {
      navigate(selectedNotification.actionUrl);
      setDetailModalVisible(false);
      onClose();
    }
  };

  const handleToggleStar = async (notification: Notification, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    try {
      await notificationService.toggleStar(notification.id);
      setNotifications(prev => 
        prev.map(n => n.id === notification.id ? { ...n, isStarred: !n.isStarred } : n)
      );
      setSelectedNotification(prev => prev ? { ...prev, isStarred: !prev.isStarred } : null);
      message.success(notification.isStarred ? 'Unstarred' : 'Starred');
    } catch (error) {
      message.error('Failed to toggle star');
    }
  };

  const handleToggleRead = async (notification: Notification, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }
    try {
      if (notification.isRead) {
        await notificationService.markAsUnread(notification.id);
        setNotifications(prev => 
          prev.map(n => n.id === notification.id ? { ...n, isRead: false } : n)
        );
        setStats(prev => {
          const newStats = { ...prev, unreadCount: prev.unreadCount + 1 };
          onUnreadCountChange(newStats.unreadCount);
          return newStats;
        });
        message.success('Marked as unread');
      } else {
        await notificationService.markAsRead(notification.id);
        setNotifications(prev => 
          prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
        );
        setStats(prev => {
          const newStats = { ...prev, unreadCount: prev.unreadCount - 1 };
          onUnreadCountChange(newStats.unreadCount);
          return newStats;
        });
        message.success('Marked as read');
      }
    } catch (error) {
      message.error('Failed to toggle read status');
    }
  };

  return (
    <>
      <Drawer
        title={
          <div style={{ padding: '0 4px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <Space align="center">
                <Title level={5} style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>
                  Notifications
                </Title>
                {stats.unreadCount > 0 && (
                  <Badge 
                    count={stats.unreadCount} 
                    style={{ 
                      backgroundColor: '#ff4d4f',
                      fontSize: '10px',
                      height: '16px',
                      minWidth: '16px',
                      lineHeight: '16px'
                    }} 
                  />
                )}
              </Space>
              <Space size={12}>
              {stats.unreadCount > 0 && (
                  <Button 
                    type="text" 
                    size="small"
                    onClick={handleMarkAllAsRead}
                    style={{ 
                      fontSize: '12px',
                      color: '#1890ff',
                      padding: '0 8px',
                      height: '24px'
                    }}
                  >
                    Mark all read
                  </Button>
                )}
                <Button 
                  type="text" 
                  size="small"
                  onClick={handleViewAll}
                  style={{ 
                    fontSize: '12px',
                    color: '#1890ff',
                    padding: '0 8px',
                    height: '24px'
                  }}
                >
                  View all
                </Button>
              </Space>
            </div>
            
              <Select
                value={filter}
                onChange={setFilter}
                size="small"
              style={{ width: '100%' }}
              options={[
                { value: 'all', label: `All (${notifications.length})` },
                { value: 'unread', label: `Unread (${notifications.filter(n => !n.isRead).length})` },
                { value: 'starred', label: `Starred (${notifications.filter(n => n.isStarred).length})` }
              ]}
            />
          </div>
        }
        placement="right"
        width={360}
        onClose={onClose}
        open={visible}
        className="notification-panel-simple"
        styles={{
          header: { padding: '16px 20px', borderBottom: '1px solid #f0f0f0' },
          body: { padding: 0 }
        }}
      >
        <Spin spinning={loading}>
          {filteredNotifications.length === 0 ? (
            <div style={{ padding: '40px 20px', textAlign: 'center' }}>
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <Text type="secondary" style={{ fontSize: '13px' }}>
                    {filter === 'unread' ? "No unread notifications" : 
                     filter === 'starred' ? "No starred notifications" : "No notifications"}
                  </Text>
                }
            />
            </div>
          ) : (
            <List
              dataSource={filteredNotifications}
              renderItem={(notification) => (
                <List.Item
                  className={`notification-item-simple ${!notification.isRead ? 'unread' : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                  style={{
                    padding: '12px 20px',
                    borderBottom: '1px solid #f5f5f5',
                    cursor: 'pointer'
                  }}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{ 
                        width: '32px', 
                        height: '32px', 
                        borderRadius: '50%', 
                        backgroundColor: '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        {getNotificationIcon(notification.type)}
                      </div>
                    }
                    title={
                      <div style={{ 
                        fontSize: '13px', 
                        fontWeight: !notification.isRead ? 600 : 400,
                        color: !notification.isRead ? '#262626' : '#595959',
                        lineHeight: '1.4',
                        marginBottom: '2px'
                      }}>
                            {notification.title}
                      </div>
                    }
                    description={
                      <div>
                        <Text 
                          style={{ 
                            fontSize: '12px', 
                            color: '#8c8c8c',
                            lineHeight: '1.3',
                            display: 'block',
                            marginBottom: '4px'
                          }}
                        >
                          {notification.message}
                        </Text>
                        <div style={{ 
                          display: 'flex', 
                          justifyContent: 'space-between', 
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <Text style={{ fontSize: '11px', color: '#bfbfbf' }}>
                            {formatRelativeTime(notification.createdAt)}
                          </Text>
                          {notification.projectName && (
                            <Tag 
                              color="blue" 
                              style={{ 
                                fontSize: '10px', 
                                padding: '0 6px',
                                height: '16px',
                                lineHeight: '16px',
                                borderRadius: '8px'
                              }}
                            >
                              {notification.projectName}
                            </Tag>
                          )}
                        </div>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          alignItems: 'center',
                          gap: '8px',
                          paddingTop: '4px',
                          borderTop: '1px solid #f5f5f5'
                        }}>
                          <Tooltip title={notification.isStarred ? "取消收藏" : "收藏"}>
                            <Button
                              type="text"
                              size="small"
                              icon={notification.isStarred ? <StarFilled /> : <StarOutlined />}
                              onClick={(e) => handleToggleStar(notification, e)}
                              style={{
                                color: notification.isStarred ? '#faad14' : '#8c8c8c',
                                fontSize: '12px',
                                width: '24px',
                                height: '20px',
                                padding: 0,
                                minWidth: 'auto'
                              }}
                            />
                          </Tooltip>
                          <Tooltip title={notification.isRead ? "标记为未读" : "标记为已读"}>
                            <Button
                              type="text"
                              size="small"
                              icon={notification.isRead ? <EyeOutlined /> : <CheckOutlined />}
                              onClick={(e) => handleToggleRead(notification, e)}
                              style={{
                                color: notification.isRead ? '#52c41a' : '#8c8c8c',
                                fontSize: '12px',
                                width: '24px',
                                height: '20px',
                                padding: 0,
                                minWidth: 'auto'
                              }}
                            />
                          </Tooltip>
                        </div>
                      </div>
                    }
                  />
                  {!notification.isRead && (
                    <div style={{
                      width: '6px',
                      height: '6px',
                      borderRadius: '50%',
                      backgroundColor: '#1890ff',
                      marginLeft: '8px'
                    }} />
                  )}
                </List.Item>
              )}
            />
          )}
        </Spin>
      </Drawer>

      <Modal
        title={
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '12px',
            padding: '4px 0'
          }}>
            <div style={{ 
              width: '32px', 
              height: '32px', 
              borderRadius: '50%', 
              backgroundColor: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {selectedNotification && getNotificationIcon(selectedNotification.type)}
            </div>
            <div>
              <Text style={{ fontSize: '16px', fontWeight: 600, color: '#262626' }}>
                Notification Details
              </Text>
            </div>
          </div>
        }
        open={detailModalVisible}
        onCancel={handleCloseDetailModal}
        width={420}
        footer={null}
        className="notification-detail-modal"
        styles={{
          header: { 
            padding: '16px 24px', 
            borderBottom: '1px solid #f0f0f0',
            marginBottom: 0
          },
          body: { 
            padding: '20px 24px 24px 24px'
          }
        }}
      >
        {selectedNotification && (
          <div>
            <div style={{ marginBottom: 20 }}>
              <div style={{ marginBottom: 12 }}>
                <Text style={{ 
                  fontSize: '15px', 
                  fontWeight: 600,
                  color: '#262626',
                  lineHeight: '1.4',
                  display: 'block'
                }}>
                  {selectedNotification.title}
                </Text>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Text style={{ 
                  fontSize: '14px', 
                  lineHeight: '1.5', 
                  color: '#595959'
                }}>
                  {selectedNotification.message}
                </Text>
              </div>

              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                paddingTop: 12,
                borderTop: '1px solid #f5f5f5'
              }}>
                <Text style={{ fontSize: '12px', color: '#bfbfbf' }}>
                  {formatRelativeTime(selectedNotification.createdAt)}
                </Text>
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  {selectedNotification.projectName && (
                    <Tag 
                      color="blue" 
                      style={{ 
                        fontSize: '11px', 
                        padding: '2px 8px',
                        height: '20px',
                        lineHeight: '16px',
                        borderRadius: '10px',
                        margin: 0
                      }}
                    >
                      {selectedNotification.projectName}
                    </Tag>
                  )}
                  {selectedNotification.isStarred && (
                    <StarFilled style={{ color: '#faad14', fontSize: 14 }} />
                  )}
                </div>
              </div>
            </div>

            <div style={{ 
              display: 'flex', 
              gap: '12px',
              justifyContent: 'center',
              paddingTop: 16,
              borderTop: '1px solid #f5f5f5'
              }}>
                <Button
                size="middle"
                  type={selectedNotification.isStarred ? "primary" : "default"}
                  icon={selectedNotification.isStarred ? <StarFilled /> : <StarOutlined />}
                onClick={() => handleToggleStar(selectedNotification)}
                  style={{
                  minWidth: '100px',
                  height: '36px',
                  borderRadius: '6px',
                  fontSize: '13px',
                    fontWeight: 500,
                  backgroundColor: selectedNotification.isStarred ? '#faad14' : '#ffffff',
                    borderColor: selectedNotification.isStarred ? '#faad14' : '#d9d9d9',
                    color: selectedNotification.isStarred ? 'white' : '#595959'
                  }}
                >
                {selectedNotification.isStarred ? 'Starred' : 'Star'}
                </Button>

                <Button
                size="middle"
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={() => {
                    message.success('Notification confirmed');
                    handleCloseDetailModal();
                  }}
                  style={{
                  minWidth: '100px',
                  height: '36px',
                  borderRadius: '6px',
                  fontSize: '13px',
                    fontWeight: 500,
                  backgroundColor: '#1890ff',
                  borderColor: '#1890ff'
                  }}
                >
                  Confirm
                </Button>
              </div>

              {selectedNotification?.actionUrl && (
              <div style={{ 
                textAlign: 'center', 
                marginTop: '16px',
                paddingTop: '12px',
                borderTop: '1px solid #f5f5f5'
              }}>
                  <Button 
                    type="link" 
                    icon={<LinkOutlined />}
                    onClick={handleNavigateToAction}
                    style={{
                    fontSize: '13px',
                    color: '#1890ff',
                    padding: '4px 8px',
                    height: 'auto'
                    }}
                  >
                    Go to Related Page
                  </Button>
                </div>
              )}
          </div>
        )}
      </Modal>
    </>
  );
};

// 添加样式
const styles = `
  .notification-detail-modal .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
  
  .notification-detail-modal .ant-modal-header {
    background: #fafafa;
  }
  
  .notification-detail-modal .ant-modal-close {
    top: 16px;
    right: 20px;
  }
  
  .notification-detail-modal .ant-modal-close-x {
    width: 32px;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: #8c8c8c;
  }
  
  .notification-detail-modal .ant-modal-close:hover .ant-modal-close-x {
    color: #595959;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  if (!document.head.querySelector('style[data-notification-modal]')) {
    styleElement.setAttribute('data-notification-modal', 'true');
    document.head.appendChild(styleElement);
  }
}

export default NotificationPanel; 