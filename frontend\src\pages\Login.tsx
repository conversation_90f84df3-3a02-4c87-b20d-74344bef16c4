import React, { useState } from 'react';
import { Form, Input, Button, Typography, Divider, message } from 'antd';
import { UserOutlined, LockOutlined, GithubOutlined, MailOutlined } from '@ant-design/icons';
import { useNavigate, Link } from 'react-router-dom';
import { useGoogleLogin } from '@react-oauth/google';
import authService from '../services/auth.service';
import '../styles/Login.css';

const { Title, Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: { email: string; password: string }) => {
    try {
      setLoading(true);
      console.log('登录请求开始，邮箱:', values.email);
      console.log('登录密码:', values.password);

      // 模拟成功登录
      console.log('模拟成功登录');

      // 创建模拟用户数据
      const mockUserData = {
        user: {
          id: '703732e7-5236-4b5d-a46f-40bc6afa801e',
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          createdAt: '2025-05-16 08:14:42',
          updatedAt: '2025-05-16 08:14:42'
        },
        token: 'dummy-token-for-testing'
      };

      // 保存令牌和用户信息
      localStorage.setItem('token', mockUserData.token);
      localStorage.setItem('user', JSON.stringify(mockUserData.user));

      // 显示成功消息
      message.success('登录成功');

      // 导航到仪表板
      navigate('/dashboard');
    } catch (error: any) {
      message.error('登录失败，请检查您的邮箱和密码');
      console.error('登录错误详情:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      try {
        console.log('Google 登录成功，获取到令牌:', tokenResponse);
        setLoading(true);

        // 获取用户信息
        const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
          headers: {
            'Authorization': `Bearer ${tokenResponse.access_token}`
          }
        });

        const userInfo = await userInfoResponse.json();
        console.log('Google 用户信息:', userInfo);

        // 将 Google 令牌发送到后端进行验证和登录
        try {
          const response = await authService.googleLogin(tokenResponse.access_token);
          console.log('后端登录成功:', response);
          message.success('Google 登录成功');
          navigate('/dashboard');
        } catch (error: any) {
          console.error('后端登录失败:', error);
          message.error('Google 登录失败，请稍后再试');
        }
      } catch (error) {
        console.error('Google 登录过程中出错:', error);
        message.error('Google 登录失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    },
    onError: (errorResponse) => {
      console.error('Google 登录错误:', errorResponse);
      message.error('Google 登录失败，请稍后再试');
    }
  });

  const handleGithubLogin = async () => {
    try {
      setLoading(true);
      message.info('Github 登录功能正在开发中');
      // 这里将来可以实现 Github OAuth 登录
      // 可以使用类似 window.location.href = 'https://github.com/login/oauth/authorize?client_id=YOUR_CLIENT_ID&redirect_uri=YOUR_REDIRECT_URI' 的方式
    } catch (error) {
      console.error('Github 登录过程中出错:', error);
      message.error('Github 登录失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      {/* Top Navigation */}
      <div className="login-header">
        <div className="logo">
          <Link to="/">SmartLTC</Link>
        </div>
        <div className="nav-links">
          <Link to="/login" className="sign-in-link">Sign In</Link>
          <Link to="/register" className="sign-up-link">Sign Up</Link>
        </div>
      </div>

      {/* Login Card */}
      <div className="login-card-wrapper">
        <div className="login-card">
          <Title level={2} className="login-title">Sign In</Title>
          <Text className="login-subtitle">
            Hello, please enter your details to login to your account
          </Text>

          <Form
            name="login"
            className="login-form"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            size="large"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: 'Please enter your email' },
                { type: 'email', message: 'Please enter a valid email address' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="Email/Phone"
                className="login-input"
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: 'Please enter your password' }]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Password"
                className="login-input"
                visibilityToggle={{
                  visible: passwordVisible,
                  onVisibleChange: setPasswordVisible
                }}
                addonAfter={
                  <span
                    onClick={() => setPasswordVisible(!passwordVisible)}
                    className="password-toggle"
                  >
                    {passwordVisible ? 'Hide' : 'Show'}
                  </span>
                }
              />
            </Form.Item>

            <div className="login-help">
              <Link to="/forgot-password">Having trouble logging in?</Link>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="login-button"
              >
                Sign In
              </Button>
            </Form.Item>

            <div className="social-login-divider">
              <Divider>
                <span className="divider-text">Or login with</span>
              </Divider>
            </div>

            <div className="social-login-buttons">
              <Button
                icon={<MailOutlined />}
                className="social-button gmail-button"
                onClick={() => handleGoogleLogin()}
                loading={loading}
              >
                Gmail
              </Button>
              <Button
                icon={<GithubOutlined />}
                className="social-button github-button"
                onClick={() => handleGithubLogin()}
                loading={loading}
              >
                Github
              </Button>
            </div>

            <div className="register-link">
              <Text>Don't have an account?</Text>
              <Link to="/register">Sign Up now</Link>
            </div>
          </Form>
        </div>
      </div>

      {/* Footer */}
      <div className="login-footer">
        <div className="footer-content">
          <div className="footer-copyright">
                          <span>© 2024 SmartLTC - AI-Powered Business Operations Integrated Management Platform</span>
              <br />
              <span>Powered by Vlisoft.com • Empowering business with SmartLTC</span>
          </div>
          <div className="footer-buttons">
            <Link to="/privacy" className="footer-button">Privacy Notice</Link>
            <Link to="/terms" className="footer-button">Terms & Privacy</Link>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="decoration-line line-1"></div>
      <div className="decoration-line line-2"></div>
      <div className="decoration-line line-3"></div>
    </div>
  );
};

export default Login;
