Write-Host "======================================" -ForegroundColor Cyan
Write-Host "     LTC前端服务简化启动脚本" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# 确保在正确的目录
if (!(Test-Path "frontend")) {
    Write-Host "❌ 找不到frontend目录！" -ForegroundColor Red
    exit 1
}

Set-Location frontend

# 设置环境变量
$env:SKIP_PREFLIGHT_CHECK = "true"
$env:BROWSER = "none"
$env:FAST_REFRESH = "false"
$env:ESLINT_NO_DEV_ERRORS = "true"
$env:TSC_COMPILE_ON_ERROR = "true"

Write-Host "✓ 环境变量设置完成" -ForegroundColor Green

# 检查package.json
if (!(Test-Path "package.json")) {
    Write-Host "❌ 找不到package.json文件！" -ForegroundColor Red
    exit 1
}

Write-Host "✓ package.json文件存在" -ForegroundColor Green

# 检查react-scripts
if (!(Test-Path "node_modules\react-scripts")) {
    Write-Host "⚠️ react-scripts未安装，正在安装..." -ForegroundColor Yellow
    npm install react-scripts@5.0.1 --save --legacy-peer-deps
    
    if (!(Test-Path "node_modules\react-scripts")) {
        Write-Host "❌ react-scripts安装失败！" -ForegroundColor Red
        Write-Host "尝试重新安装所有依赖..." -ForegroundColor Yellow
        Remove-Item node_modules -Recurse -Force -ErrorAction SilentlyContinue
        Remove-Item package-lock.json -Force -ErrorAction SilentlyContinue
        npm install --legacy-peer-deps
    }
}

Write-Host "✓ react-scripts已就绪" -ForegroundColor Green

Write-Host ""
Write-Host "🚀 启动前端开发服务器..." -ForegroundColor Green
Write-Host "📍 服务将运行在: http://localhost:3000" -ForegroundColor Yellow
Write-Host "⏳ 请等待编译完成..." -ForegroundColor Yellow
Write-Host ""

# 启动服务
try {
    npm start
} catch {
    Write-Host "❌ 启动失败，尝试备用方法..." -ForegroundColor Red
    npx react-scripts start
} 