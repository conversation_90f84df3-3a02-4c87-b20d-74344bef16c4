# ExecutionStage 拖拽冲突修复

## 问题描述
在ExecutionStage的Task Management页面中，当用户尝试拖动进度条(Slider)时，整个任务行都会被拖动，导致进度条无法正常调整。

## 问题原因
1. **拖拽监听器冲突**：DraggableTableRow组件将拖拽监听器应用到了整个表格行(`<tr>`)上
2. **事件冒泡**：进度条的鼠标事件会冒泡到父级的表格行，触发行拖拽功能
3. **监听器优先级**：行拖拽监听器的优先级高于进度条的交互

## 解决方案

### 1. 进度条区域事件阻止
在进度条的容器div上添加事件阻止方法：
```typescript
<div 
  style={{ ... }}
  onMouseDown={(e) => e.stopPropagation()}
  onMouseMove={(e) => e.stopPropagation()}
  onMouseUp={(e) => e.stopPropagation()}
  onClick={(e) => e.stopPropagation()}
>
  <Slider ... />
</div>
```

### 2. 拖拽监听器重新分配
修改DraggableTableRow组件，将拖拽监听器只应用到第一列（拖拽句柄列）：
```typescript
const newProps = {
  ...props,
  children: React.Children.map(props.children, (child, index) => {
    if (index === 0) {
      // 第一列是拖拽句柄列，应用拖拽监听器
      return React.cloneElement(child, {
        ...listeners,
        style: { 
          ...child.props.style, 
          cursor: 'grab',
          userSelect: 'none'
        }
      });
    }
    return child;
  })
};
```

### 3. 传感器配置优化
添加拖拽激活约束，需要拖拽8px才激活拖拽功能：
```typescript
const sensors = useSensors(
  useSensor(PointerSensor, {
    activationConstraint: {
      distance: 8, // 需要拖拽8px才激活
    },
  }),
  useSensor(KeyboardSensor, {
    coordinateGetter: sortableKeyboardCoordinates,
  })
);
```

## 修复效果

### 修复前
- ❌ 拖动进度条时整个任务行被拖动
- ❌ 无法正常调整任务进度
- ❌ 用户体验差，操作混乱

### 修复后
- ✅ 进度条可以正常拖动调整
- ✅ 任务行拖拽功能保持正常
- ✅ 只有在拖拽句柄(⋮⋮)上才能拖动行
- ✅ 两个功能互不干扰

## 技术细节

### 修改的文件
- `frontend/src/components/ltc-stages/ExecutionStage.tsx`

### 关键修改点
1. **进度条容器**：添加事件阻止处理器
2. **DraggableTableRow组件**：重新分配拖拽监听器到特定列
3. **传感器配置**：添加激活约束
4. **拖拽句柄样式**：增强视觉反馈

### 兼容性
- ✅ 保持原有的拖拽排序功能
- ✅ 保持原有的进度条功能
- ✅ 不影响其他表格列的交互
- ✅ 支持键盘拖拽操作

## 用户操作指南

### 调整任务进度
1. 点击并拖动进度条滑块
2. 或直接点击进度条上的位置
3. 进度会实时更新并保存

### 拖拽任务排序
1. 鼠标悬停在拖拽句柄(⋮⋮)上
2. 按住鼠标左键拖拽
3. 拖拽到目标位置后释放

### 视觉提示
- **拖拽句柄**：显示grab光标，表示可拖拽
- **进度条**：正常的滑块交互
- **拖拽中**：行会有视觉反馈和阴影效果

## 测试验证

### 功能测试
1. ✅ 进度条可以正常拖动
2. ✅ 任务行可以通过拖拽句柄拖动
3. ✅ 两个功能不会相互干扰
4. ✅ 键盘拖拽功能正常

### 边界测试
1. ✅ 快速拖动进度条不会触发行拖拽
2. ✅ 在进度条区域外的行区域点击不会影响进度条
3. ✅ 拖拽句柄区域外不会触发行拖拽

## 后续优化建议

1. **视觉增强**：可以考虑在拖拽时添加更明显的视觉反馈
2. **触摸支持**：为移动设备优化触摸拖拽体验
3. **键盘导航**：增强键盘操作的可访问性
4. **性能优化**：对于大量任务的情况优化渲染性能

## 总结

通过精确控制事件传播和拖拽监听器的应用范围，成功解决了进度条拖拽与任务行拖拽的冲突问题。现在用户可以：
- 自由调整任务进度而不触发行拖拽
- 通过专用的拖拽句柄重新排序任务
- 享受流畅、直观的用户体验

这个修复确保了ExecutionStage组件的两个核心交互功能都能正常工作，大大提升了用户体验。 