import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Divider, Timeline, Tag, message } from 'antd';
import { 
  SyncOutlined, 
  UserOutlined, 
  ProjectOutlined, 
  HistoryOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { dataSyncEngine } from '../services/dataSyncEngine';

const { Title, Text, Paragraph } = Typography;

const DataSyncTest: React.FC = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [eventHistory, setEventHistory] = useState<any[]>([]);
  const [syncStats, setSyncStats] = useState({
    totalEvents: 0,
    queueSize: 0,
    isProcessing: false,
    handlerCount: 0
  });

  useEffect(() => {
    updateStats();
    updateHistory();
  }, []);

  const updateStats = () => {
    const stats = dataSyncEngine.getSyncStats();
    setSyncStats(stats);
    setIsProcessing(stats.isProcessing);
  };

  const updateHistory = () => {
    const history = dataSyncEngine.getEventHistory(10);
    setEventHistory(history);
  };

  const simulateClientUpdate = async () => {
    message.info('模拟客户信息更新...');
    
    const event = dataSyncEngine.createEvent(
      'CLIENT_UPDATED',
      'test-client-001',
      'client',
      {
        name: 'Acme Corporation (Updated)',
        tier: 'VIP',
        country: 'United States',
        contactPerson: 'Jane Doe',
        totalRevenue: 2500000
      },
      'DataSyncTest',
      { name: 'Acme Corporation', tier: 'BA', totalRevenue: 1500000 },
      { name: 'Acme Corporation (Updated)', tier: 'VIP', totalRevenue: 2500000 }
    );

    await dataSyncEngine.emitEvent(event);
    updateStats();
    updateHistory();
    message.success('客户更新事件已触发');
  };

  const simulateProjectCreate = async () => {
    message.info('模拟项目创建...');
    
    const event = dataSyncEngine.createEvent(
      'PROJECT_CREATED',
      'test-project-001',
      'project',
      {
        client: 'Acme Corporation',
        revenue: 850000,
        tier: 'V'
      },
      'DataSyncTest',
      undefined,
      {
        id: 'test-project-001',
        name: 'Cloud Migration Project',
        client: 'Acme Corporation',
        revenue: 850000,
        tier: 'V'
      }
    );

    await dataSyncEngine.emitEvent(event);
    updateStats();
    updateHistory();
    message.success('项目创建事件已触发');
  };

  const simulateProjectUpdate = async () => {
    message.info('模拟项目信息更新...');
    
    const event = dataSyncEngine.createEvent(
      'PROJECT_UPDATED',
      'test-project-001',
      'project',
      {
        revenue: 1200000,
        status: 'in_progress',
        client: 'Acme Corporation'
      },
      'DataSyncTest',
      { revenue: 850000, status: 'planning' },
      { revenue: 1200000, status: 'in_progress' }
    );

    await dataSyncEngine.emitEvent(event);
    updateStats();
    updateHistory();
    message.success('项目更新事件已触发');
  };

  const clearHistory = () => {
    dataSyncEngine.clearEventHistory();
    updateStats();
    updateHistory();
    message.info('事件历史已清空');
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'CLIENT_UPDATED': return 'blue';
      case 'PROJECT_CREATED': return 'green';
      case 'PROJECT_UPDATED': return 'orange';
      case 'PROJECT_DELETED': return 'red';
      case 'FINANCE_UPDATED': return 'purple';
      default: return 'default';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('zh-CN');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <SyncOutlined spin={isProcessing} /> 数据同步引擎测试
      </Title>
      
      <Paragraph>
        这个测试页面演示了DataSyncEngine的功能。当触发数据变更事件时，系统会自动处理相关数据的同步更新。
      </Paragraph>

      <Divider />

      {/* 同步统计 */}
      <Card title="同步引擎状态" style={{ marginBottom: '24px' }}>
        <Space size="large">
          <div>
            <Text strong>总事件数: </Text>
            <Tag color="blue">{syncStats.totalEvents}</Tag>
          </div>
          <div>
            <Text strong>队列长度: </Text>
            <Tag color={syncStats.queueSize > 0 ? 'orange' : 'green'}>{syncStats.queueSize}</Tag>
          </div>
          <div>
            <Text strong>处理状态: </Text>
            <Tag color={syncStats.isProcessing ? 'red' : 'green'}>
              {syncStats.isProcessing ? '处理中' : '空闲'}
            </Tag>
          </div>
          <div>
            <Text strong>处理器数量: </Text>
            <Tag color="purple">{syncStats.handlerCount}</Tag>
          </div>
        </Space>
      </Card>

      {/* 操作按钮 */}
      <Card title="测试操作" style={{ marginBottom: '24px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            icon={<UserOutlined />}
            onClick={simulateClientUpdate}
            loading={isProcessing}
          >
            模拟客户更新
          </Button>
          <Button 
            type="primary" 
            icon={<ProjectOutlined />}
            onClick={simulateProjectCreate}
            loading={isProcessing}
          >
            模拟项目创建
          </Button>
          <Button 
            type="primary" 
            icon={<ProjectOutlined />}
            onClick={simulateProjectUpdate}
            loading={isProcessing}
          >
            模拟项目更新
          </Button>
          <Button 
            onClick={clearHistory}
          >
            清空历史
          </Button>
          <Button 
            icon={<SyncOutlined />}
            onClick={() => { updateStats(); updateHistory(); }}
          >
            刷新状态
          </Button>
        </Space>
      </Card>

      {/* 事件历史 */}
      <Card title={<><HistoryOutlined /> 事件历史</>}>
        {eventHistory.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            暂无事件历史
          </div>
        ) : (
          <Timeline>
            {eventHistory.map((event, index) => (
              <Timeline.Item
                key={event.id}
                dot={
                  event.type.includes('CREATED') ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : (
                    <ClockCircleOutlined style={{ color: '#1890ff' }} />
                  )
                }
              >
                <div>
                  <div style={{ marginBottom: '8px' }}>
                    <Tag color={getEventTypeColor(event.type)}>{event.type}</Tag>
                    <Text type="secondary" style={{ marginLeft: '8px' }}>
                      {formatTimestamp(event.timestamp)}
                    </Text>
                  </div>
                  <div>
                    <Text strong>实体ID: </Text>
                    <Text code>{event.entityId}</Text>
                    <Text strong style={{ marginLeft: '16px' }}>来源: </Text>
                    <Text>{event.source}</Text>
                  </div>
                  <div style={{ marginTop: '4px' }}>
                    <Text strong>变更内容: </Text>
                    <Text style={{ 
                      fontSize: '12px',
                      backgroundColor: '#f5f5f5',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      fontFamily: 'monospace'
                    }}>
                      {JSON.stringify(event.changes, null, 2)}
                    </Text>
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        )}
      </Card>
    </div>
  );
};

export default DataSyncTest; 