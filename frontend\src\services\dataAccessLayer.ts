/**
 * 统一数据访问层 (Data Access Layer)
 * 以项目ID为主键，提供统一的数据管理接口
 */

import { Project, Client } from '../types';
import TierMappingService from './tierMappingService';

export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface StorageMetrics {
  projectsCount: number;
  clientsCount: number;
  storageSize: number;
  lastSync: string;
  cacheHitRate: number;
}

export class DataAccessLayer {
  private static instance: DataAccessLayer;
  private cache: Map<string, any> = new Map();
  private cacheStats = { hits: 0, misses: 0 };

  // 存储键定义 - 以项目为中心的存储架构
  private static readonly STORAGE_KEYS = {
    PROJECTS: 'dal_projects',              // 所有项目数据
    CLIENTS: 'dal_clients',                // 所有客户数据  
    PROJECT_STAGES: 'dal_project_stages',  // 项目阶段数据
    METADATA: 'dal_metadata',              // 元数据信息
    SYNC_LOG: 'dal_sync_log'               // 同步日志
  } as const;

  private constructor() {
    this.initializeStorage();
  }

  public static getInstance(): DataAccessLayer {
    if (!DataAccessLayer.instance) {
      DataAccessLayer.instance = new DataAccessLayer();
    }
    return DataAccessLayer.instance;
  }

  /**
   * 初始化存储 - 迁移现有数据
   */
  private initializeStorage(): void {
    try {
      const metadata = this.getStorageMetadata();
      if (!metadata.initialized) {
        console.log('🔄 Initializing Data Access Layer...');
        this.migrateExistingData();
        this.updateMetadata({ initialized: true, version: '2.0.0' });
        console.log('✅ Data Access Layer initialized successfully');
      }
    } catch (error) {
      console.error('❌ DAL initialization failed:', error);
    }
  }

  /**
   * 迁移现有localStorage数据到新架构
   */
  private migrateExistingData(): void {
    try {
      // 迁移项目数据
      const existingProjects = this.getFromStorage('projects', []);
      if (existingProjects.length > 0) {
        const migratedProjects = TierMappingService.migrateProjectData(existingProjects);
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECTS, migratedProjects);
        console.log(`📦 Migrated ${migratedProjects.length} projects`);
      }

      // 迁移客户数据
      const existingClients = this.getFromStorage('ltc_clients', []);
      if (existingClients.length > 0) {
        const migratedClients = TierMappingService.migrateClientData(existingClients);
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.CLIENTS, migratedClients);
        console.log(`👥 Migrated ${migratedClients.length} clients`);
      }

      // 迁移项目阶段数据
      this.migrateProjectStages();

    } catch (error) {
      console.error('❌ Data migration failed:', error);
      throw error;
    }
  }

  /**
   * 迁移项目阶段数据
   */
  private migrateProjectStages(): void {
    const stageKeys = Object.keys(localStorage).filter(key => key.startsWith('project_stages_'));
    const stagesData: Record<string, any> = {};
    
    stageKeys.forEach(key => {
      const projectId = key.replace('project_stages_', '');
      const stages = this.getFromStorage(key, null);
      if (stages) {
        stagesData[projectId] = stages;
      }
    });

    if (Object.keys(stagesData).length > 0) {
      this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, stagesData);
      console.log(`📋 Migrated stages for ${Object.keys(stagesData).length} projects`);
    }
  }

  /**
   * 项目数据管理 - 以项目ID为主键
   */
  
  // 获取所有项目
  getAllProjects(): Project[] {
    const cacheKey = 'all_projects';
    if (this.cache.has(cacheKey)) {
      this.cacheStats.hits++;
      return this.cache.get(cacheKey);
    }

    const projects = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.PROJECTS, []);
    this.cache.set(cacheKey, projects);
    this.cacheStats.misses++;
    return projects;
  }

  // 根据项目ID获取项目
  getProjectById(projectId: string): Project | null {
    const cacheKey = `project_${projectId}`;
    if (this.cache.has(cacheKey)) {
      this.cacheStats.hits++;
      return this.cache.get(cacheKey);
    }

    const projects = this.getAllProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (project) {
      this.cache.set(cacheKey, project);
    }
    this.cacheStats.misses++;
    return project || null;
  }

  // 保存项目数据
  saveProject(project: Project): void {
    const projects = this.getAllProjects();
    const existingIndex = projects.findIndex(p => p.id === project.id);
    
    // 数据验证
    const validation = this.validateProjectData(project);
    if (!validation.isValid) {
      throw new Error(`Project validation failed: ${validation.errors.join(', ')}`);
    }

    // 统一tier格式
    const normalizedProject = {
      ...project,
      tier: TierMappingService.normalizeToStandard(project.tier),
      updatedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      projects[existingIndex] = normalizedProject;
    } else {
      projects.unshift(normalizedProject);
    }

    this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECTS, projects);
    this.invalidateCache();
    this.logSync('project_save', project.id);
  }

  // 删除项目 - 彻底删除所有相关数据
  deleteProject(projectId: string): void {
    try {
      console.log(`🗑️ Deleting project and all related data for ID: ${projectId}`);
      
      // 1. 删除主项目数据
      const projects = this.getAllProjects();
      const filteredProjects = projects.filter(p => p.id !== projectId);
      this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECTS, filteredProjects);
      
      // 2. 删除项目阶段数据
      const allStages = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, {});
      if (allStages[projectId]) {
        delete allStages[projectId];
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, allStages);
        console.log(`🗑️ Deleted stages data for project ${projectId}`);
      }
      
      // 3. 删除其他可能的项目相关存储
      const keysToCheck = [
        `project_stages_${projectId}`,
        `project_data_${projectId}`,
        `project_cache_${projectId}`,
        `project_backup_${projectId}`
      ];
      
      keysToCheck.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`🗑️ Removed legacy storage key: ${key}`);
        }
      });
      
      // 4. 清除缓存
      this.invalidateCache();
      this.cache.delete(`project_${projectId}`);
      this.cache.delete(`stages_${projectId}`);
      
      // 5. 记录删除操作
      this.logSync('project_delete', projectId);
      
      console.log(`✅ Project ${projectId} and all related data deleted successfully`);
      
    } catch (error) {
      console.error(`❌ Error deleting project ${projectId}:`, error);
      throw error;
    }
  }

  /**
   * 客户数据管理
   */
  
  // 获取所有客户
  getAllClients(): Client[] {
    const cacheKey = 'all_clients';
    if (this.cache.has(cacheKey)) {
      this.cacheStats.hits++;
      return this.cache.get(cacheKey);
    }

    const clients = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.CLIENTS, []);
    this.cache.set(cacheKey, clients);
    this.cacheStats.misses++;
    return clients;
  }

  // 根据客户ID获取客户
  getClientById(clientId: string): Client | null {
    const cacheKey = `client_${clientId}`;
    if (this.cache.has(cacheKey)) {
      this.cacheStats.hits++;
      return this.cache.get(cacheKey);
    }

    const clients = this.getAllClients();
    const client = clients.find(c => c.id === clientId);
    
    if (client) {
      this.cache.set(cacheKey, client);
    }
    this.cacheStats.misses++;
    return client || null;
  }

  // 保存客户数据
  saveClient(client: Client): void {
    const clients = this.getAllClients();
    const existingIndex = clients.findIndex(c => c.id === client.id);
    
    // 数据验证
    const validation = this.validateClientData(client);
    if (!validation.isValid) {
      throw new Error(`Client validation failed: ${validation.errors.join(', ')}`);
    }

    // 统一tier格式
    const normalizedClient = {
      ...client,
      tier: TierMappingService.normalizeToStandard(client.tier),
      updatedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      clients[existingIndex] = normalizedClient;
    } else {
      clients.unshift(normalizedClient);
    }

    this.setToStorage(DataAccessLayer.STORAGE_KEYS.CLIENTS, clients);
    this.invalidateCache();
    this.logSync('client_save', client.id);
  }

  /**
   * 项目阶段数据管理
   */
  
  // 获取项目阶段数据
  getProjectStages(projectId: string): any {
    const cacheKey = `stages_${projectId}`;
    if (this.cache.has(cacheKey)) {
      this.cacheStats.hits++;
      return this.cache.get(cacheKey);
    }

    const allStages = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, {});
    const stages = allStages[projectId] || null;
    
    if (stages) {
      this.cache.set(cacheKey, stages);
    }
    this.cacheStats.misses++;
    return stages;
  }

  // 保存项目阶段数据
  saveProjectStages(projectId: string, stages: any): void {
    const allStages = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, {});
    allStages[projectId] = {
      ...stages,
      updatedAt: new Date().toISOString()
    };
    
    this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, allStages);
    this.invalidateCache(`stages_${projectId}`);
    this.logSync('stages_save', projectId);
  }

  /**
   * 数据验证
   */
  
  private validateProjectData(project: Project): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!project.id) errors.push('Project ID is required');
    if (!project.name) errors.push('Project name is required');
    if (!project.client) errors.push('Client is required');
    if (!TierMappingService.isValidTier(project.tier)) {
      warnings.push(`Invalid tier '${project.tier}', will be normalized`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private validateClientData(client: Client): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!client.id) errors.push('Client ID is required');
    if (!client.name) errors.push('Client name is required');
    if (!client.email) errors.push('Client email is required');
    if (!TierMappingService.isValidTier(client.tier)) {
      warnings.push(`Invalid tier '${client.tier}', will be normalized`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 关联数据查询
   */
  
  // 根据客户名称获取相关项目
  getProjectsByClient(clientName: string): Project[] {
    return this.getAllProjects().filter(project => 
      project.client.toLowerCase() === clientName.toLowerCase()
    );
  }

  // 获取客户的项目统计
  getClientProjectStats(clientName: string): { count: number; totalRevenue: number; avgRevenue: number } {
    const projects = this.getProjectsByClient(clientName);
    const totalRevenue = projects.reduce((sum, p) => sum + (p.revenue || 0), 0);
    
    return {
      count: projects.length,
      totalRevenue,
      avgRevenue: projects.length > 0 ? totalRevenue / projects.length : 0
    };
  }

  /**
   * 缓存管理
   */
  
  private invalidateCache(specific?: string): void {
    if (specific) {
      this.cache.delete(specific);
    } else {
      this.cache.clear();
    }
  }

  getCacheStats() {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    return {
      ...this.cacheStats,
      hitRate: total > 0 ? (this.cacheStats.hits / total * 100).toFixed(2) + '%' : '0%',
      cacheSize: this.cache.size
    };
  }

  /**
   * 元数据和日志管理
   */
  
  private getStorageMetadata(): any {
    return this.getFromStorage(DataAccessLayer.STORAGE_KEYS.METADATA, {
      initialized: false,
      version: '1.0.0',
      createdAt: new Date().toISOString()
    });
  }

  private updateMetadata(updates: any): void {
    const metadata = this.getStorageMetadata();
    this.setToStorage(DataAccessLayer.STORAGE_KEYS.METADATA, {
      ...metadata,
      ...updates,
      updatedAt: new Date().toISOString()
    });
  }

  private logSync(action: string, entityId: string): void {
    const logs = this.getFromStorage(DataAccessLayer.STORAGE_KEYS.SYNC_LOG, []);
    logs.unshift({
      id: Date.now().toString(),
      action,
      entityId,
      timestamp: new Date().toISOString()
    });
    
    // 只保留最近100条日志
    if (logs.length > 100) {
      logs.splice(100);
    }
    
    this.setToStorage(DataAccessLayer.STORAGE_KEYS.SYNC_LOG, logs);
  }

  /**
   * 存储操作辅助方法
   */
  
  private getFromStorage(key: string, defaultValue: any = null): any {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error(`Failed to read from storage: ${key}`, error);
      return defaultValue;
    }
  }

  private setToStorage(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Failed to write to storage: ${key}`, error);
      throw error;
    }
  }

  /**
   * 公共API - 系统监控
   */
  
  getStorageMetrics(): StorageMetrics {
    const projects = this.getAllProjects();
    const clients = this.getAllClients();
    const metadata = this.getStorageMetadata();
    
    // 计算存储大小
    let storageSize = 0;
    Object.values(DataAccessLayer.STORAGE_KEYS).forEach(key => {
      const item = localStorage.getItem(key);
      if (item) storageSize += item.length;
    });

    const cacheStats = this.getCacheStats();
    
    return {
      projectsCount: projects.length,
      clientsCount: clients.length,
      storageSize,
      lastSync: metadata.updatedAt || metadata.createdAt,
      cacheHitRate: parseFloat(cacheStats.hitRate) / 100
    };
  }

  /**
   * 数据导出和备份
   */
  
  exportAllData(): any {
    return {
      projects: this.getAllProjects(),
      clients: this.getAllClients(),
      projectStages: this.getFromStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, {}),
      metadata: this.getStorageMetadata(),
      exportedAt: new Date().toISOString()
    };
  }

  importData(data: any): void {
    try {
      if (data.projects) {
        const migratedProjects = TierMappingService.migrateProjectData(data.projects);
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECTS, migratedProjects);
      }
      
      if (data.clients) {
        const migratedClients = TierMappingService.migrateClientData(data.clients);
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.CLIENTS, migratedClients);
      }
      
      if (data.projectStages) {
        this.setToStorage(DataAccessLayer.STORAGE_KEYS.PROJECT_STAGES, data.projectStages);
      }
      
      this.invalidateCache();
      this.updateMetadata({ importedAt: new Date().toISOString() });
      
      console.log('✅ Data import completed successfully');
    } catch (error) {
      console.error('❌ Data import failed:', error);
      throw error;
    }
  }
}

// 导出单例实例
export const dal = DataAccessLayer.getInstance();
export default dal; 