"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const typeorm_1 = require("typeorm");
const dotenv_1 = __importDefault(require("dotenv"));
const project_routes_1 = __importDefault(require("./routes/project.routes"));
const auth_routes_1 = __importDefault(require("./routes/auth.routes"));
const project_stage_routes_1 = __importDefault(require("./routes/project-stage.routes"));
const team_member_routes_1 = __importDefault(require("./routes/team-member.routes"));
const document_routes_1 = __importDefault(require("./routes/document.routes"));
const notification_routes_1 = __importDefault(require("./routes/notification.routes"));
const database_1 = __importDefault(require("./config/database"));
dotenv_1.default.config();
const app = (0, express_1.default)();
app.use((0, cors_1.default)({
    origin: 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'x-request-id',
        'X-Requested-With',
        'x-csrf-token'
    ]
}));
app.use(express_1.default.json());
app.use('/api/projects', project_routes_1.default);
app.use('/api/auth', auth_routes_1.default);
app.use('/api/project-stages', project_stage_routes_1.default);
app.use('/api/team-members', team_member_routes_1.default);
app.use('/api/documents', document_routes_1.default);
app.use('/api/notifications', notification_routes_1.default);
(0, typeorm_1.createConnection)(database_1.default)
    .then(() => {
    console.log('Database connected successfully');
    const PORT = process.env.PORT || 5002;
    app.listen(PORT, () => {
        console.log(`Server is running on port ${PORT}`);
    });
})
    .catch((error) => {
    console.error('Error connecting to database:', error);
});
exports.default = app;
//# sourceMappingURL=index.js.map