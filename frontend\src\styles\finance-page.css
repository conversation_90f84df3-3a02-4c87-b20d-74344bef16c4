/* Finance Page 样式优化 */
.finance-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
  padding: 0;
}

.finance-page-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(600px circle at 10% 20%, rgba(255, 122, 0, 0.03) 0%, transparent 50%),
    radial-gradient(400px circle at 90% 80%, rgba(16, 185, 129, 0.03) 0%, transparent 50%),
    radial-gradient(800px circle at 50% 50%, rgba(59, 130, 246, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 财务模块卡片 */
.finance-section-card {
  position: relative;
  z-index: 1;
  margin-bottom: 24px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 1px 0 rgba(255, 255, 255, 0.5) inset;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  min-height: 400px;
}

.finance-section-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ff7a00 0%, #10b981 50%, #3b82f6 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.finance-section-card:hover::before {
  opacity: 1;
}

.finance-section-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 1px 0 rgba(255, 255, 255, 0.6) inset;
}

/* 收入管理模块特色 */
.revenue-section::before {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%) !important;
}

.revenue-section:hover {
  border-color: rgba(16, 185, 129, 0.3);
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 发票管理模块特色 */
.invoice-section::before {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

.invoice-section:hover {
  border-color: rgba(59, 130, 246, 0.3);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 利润管理模块特色 */
.profit-section::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%) !important;
}

.profit-section:hover {
  border-color: rgba(245, 158, 11, 0.3);
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.02) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 模块头部 */
.finance-section-header {
  display: flex;
  align-items: center;
  padding: 24px 28px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  backdrop-filter: blur(10px);
  position: relative;
}

.finance-section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 28px;
  right: 28px;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

/* 模块图标 */
.section-icon {
  font-size: 32px;
  margin-right: 16px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.finance-section-card:hover .section-icon {
  transform: scale(1.1) rotate(5deg);
}

/* 模块标题 */
.section-title {
  font-size: 16px !important;
  font-weight: 500 !important;
  margin: 0 !important;
  color: #1f2937 !important;
  flex: 1;
  background: linear-gradient(135deg, #1f2937 0%, #4b5563 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 状态徽章 */
.section-badge {
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #ff7a00 0%, #e66500 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 122, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.section-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.finance-section-card:hover .section-badge::before {
  left: 100%;
}

/* 模块内容区域 */
.finance-section-content {
  padding: 28px;
  position: relative;
}

/* 不同模块的徽章颜色 */
.revenue-section .section-badge {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.invoice-section .section-badge {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.profit-section .section-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .finance-section-header {
    padding: 20px 20px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .section-icon {
    font-size: 28px;
    margin-right: 0;
    margin-bottom: 8px;
  }

  .section-title {
    font-size: 14px !important;
  }

  .section-badge {
    align-self: flex-end;
    margin-top: -8px;
    font-size: 10px !important;
    padding: 4px 8px !important;
  }

  .finance-section-content {
    padding: 20px;
  }

  .finance-section-card {
    margin-bottom: 20px;
    border-radius: 16px;
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .finance-section-header {
    padding: 16px 16px 10px;
  }

  .section-icon {
    font-size: 24px;
  }

  .section-title {
    font-size: 12px !important;
  }

  .section-badge {
    font-size: 9px !important;
    padding: 3px 6px !important;
  }

  .finance-section-content {
    padding: 16px;
  }

  .finance-section-card {
    margin-bottom: 16px;
    border-radius: 12px;
    min-height: 250px;
  }
}

/* 动画效果 */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.finance-section-card {
  animation: slideInFromBottom 0.6s ease-out;
}

.revenue-section {
  animation-delay: 0.1s;
}

.invoice-section {
  animation-delay: 0.2s;
}

.profit-section {
  animation-delay: 0.3s;
}

/* 内容区域的表格和组件优化 */
.finance-section-content .ant-table-wrapper {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.finance-section-content .ant-table-thead > tr > th {
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.08) 0%, rgba(255, 122, 0, 0.04) 100%) !important;
  border-bottom: 2px solid rgba(255, 122, 0, 0.15) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
}

.finance-section-content .ant-table-tbody > tr > td {
  font-size: 14px !important;
}

.finance-section-content .ant-table-tbody > tr:hover {
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.02) 0%, rgba(255, 122, 0, 0.01) 100%) !important;
}

.finance-section-content .ant-tabs-tab {
  font-weight: 500 !important;
  border-radius: 8px 8px 0 0 !important;
  font-size: 14px !important;
}

.finance-section-content .ant-tabs-tab-active {
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.1) 0%, rgba(255, 122, 0, 0.05) 100%) !important;
  border-color: rgba(255, 122, 0, 0.3) !important;
}

/* 财务统计卡片优化 */
.finance-section-content .text-sm {
  font-size: 12px !important;
  font-weight: 400 !important;
  color: #6b7280 !important;
}

.finance-section-content .text-xl {
  font-size: 20px !important;
  font-weight: 600 !important;
  margin: 8px 0 !important;
}

.finance-section-content .font-bold {
  font-weight: 600 !important;
}

/* 通用文字大小规范 */
.finance-section-content p,
.finance-section-content div,
.finance-section-content span {
  font-size: 14px !important;
}

.finance-section-content .ant-btn {
  font-size: 14px !important;
  font-weight: 500 !important;
}

.finance-section-content .ant-input {
  font-size: 14px !important;
}

.finance-section-content .ant-select-selection-item {
  font-size: 14px !important;
}

.finance-section-content label {
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* 统计卡片颜色优化 */
.finance-section-content .text-green-700 {
  color: #10b981 !important;
  text-shadow: 0 1px 2px rgba(16, 185, 129, 0.1);
}

.finance-section-content .text-green-600 {
  color: #059669 !important;
}

.finance-section-content .text-yellow-500 {
  color: #f59e0b !important;
}

.finance-section-content .text-blue-600 {
  color: #3b82f6 !important;
}

.finance-section-content .text-purple-600 {
  color: #8b5cf6 !important;
}

/* 背景卡片优化 */
.finance-section-content .bg-white {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.finance-section-content .shadow {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.finance-section-content .rounded-lg {
  border-radius: 12px !important;
}

/* 进度条优化 */
.finance-section-content .bg-gray-200 {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%) !important;
}

.finance-section-content .bg-green-500 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.finance-section-content .bg-purple-500 {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%) !important;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* 按钮优化 */
.finance-section-content .bg-indigo-600 {
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
}

.finance-section-content .bg-purple-600 {
  background: linear-gradient(135deg, #9333ea 0%, #7e22ce 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3) !important;
}

.finance-section-content .bg-teal-600 {
  background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.3) !important;
}

.finance-section-content .bg-blue-600 {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
}

/* 按钮悬停效果 */
.finance-section-content .hover\:bg-indigo-700:hover {
  background: linear-gradient(135deg, #3730a3 0%, #312e81 100%) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4) !important;
}

.finance-section-content .hover\:bg-purple-700:hover {
  background: linear-gradient(135deg, #7e22ce 0%, #6b21a8 100%) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 0 6px 16px rgba(147, 51, 234, 0.4) !important;
}

.finance-section-content .hover\:bg-teal-700:hover {
  background: linear-gradient(135deg, #0f766e 0%, #134e4a 100%) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 0 6px 16px rgba(13, 148, 136, 0.4) !important;
}

.finance-section-content .hover\:bg-blue-700:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
  transform: translateY(-1px) scale(1.02) !important;
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4) !important;
}

/* 表格和列表优化 */
.finance-section-content .bg-gray-50 {
  background: linear-gradient(135deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.6) 100%) !important;
}

.finance-section-content .bg-gray-100 {
  background: linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%) !important;
}

/* 状态标签优化 */
.finance-section-content .bg-green-100 {
  background: linear-gradient(135deg, rgba(220, 252, 231, 0.8) 0%, rgba(187, 247, 208, 0.6) 100%) !important;
  border: 1px solid rgba(16, 185, 129, 0.2) !important;
}

.finance-section-content .bg-yellow-100 {
  background: linear-gradient(135deg, rgba(254, 243, 199, 0.8) 0%, rgba(253, 230, 138, 0.6) 100%) !important;
  border: 1px solid rgba(245, 158, 11, 0.2) !important;
}

.finance-section-content .bg-red-100 {
  background: linear-gradient(135deg, rgba(254, 226, 226, 0.8) 0%, rgba(252, 165, 165, 0.6) 100%) !important;
  border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

.finance-section-content .bg-blue-100 {
  background: linear-gradient(135deg, rgba(219, 234, 254, 0.8) 0%, rgba(147, 197, 253, 0.6) 100%) !important;
  border: 1px solid rgba(59, 130, 246, 0.2) !important;
}

.finance-section-content .bg-purple-100 {
  background: linear-gradient(135deg, rgba(237, 233, 254, 0.8) 0%, rgba(196, 181, 253, 0.6) 100%) !important;
  border: 1px solid rgba(139, 92, 246, 0.2) !important;
}

/* 集合标题优化 */
.finance-section-content .text-lg {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: #1f2937 !important;
  margin-bottom: 16px !important;
}

/* NRC/MRC标签优化 - 现代化设计 */
.finance-section-content .ant-tabs .ant-tabs-tab {
  background: transparent !important;
  border: none !important;
  border-bottom: 3px solid transparent !important;
  border-radius: 0 !important;
  margin-right: 8px !important;
  padding: 12px 20px !important;
  transition: all 0.3s ease !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #6B7280 !important;
  position: relative !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  z-index: -1 !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab:hover {
  color: #374151 !important;
  background: rgba(255, 122, 0, 0.08) !important;
  border-radius: 6px !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab:hover::before {
  background: rgba(255, 122, 0, 0.1) !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab-active {
  background: transparent !important;
  border-bottom: 3px solid #FF7A00 !important;
  color: #FF7A00 !important;
  font-weight: 600 !important;
  font-size: 13px !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab-active::before {
  background: rgba(255, 122, 0, 0.1) !important;
}

/* 页签容器样式 */
.finance-section-content .ant-tabs-nav-wrap {
  background: #F9FAFB !important;
  border-radius: 8px 8px 0 0 !important;
  padding: 0 16px !important;
}

.finance-section-content .ant-tabs-nav {
  margin-bottom: 0 !important;
}

.finance-section-content .ant-tabs-ink-bar {
  display: none !important;
}

.finance-section-content .ant-tabs-content-holder {
  background: white !important;
  border-radius: 0 0 8px 8px !important;
}

/* 专门针对RevenueManagement组件的页签样式 */
.finance-section-content .ant-tabs-tab-btn {
  font-size: 13px !important;
  font-weight: 500 !important;
  color: inherit !important;
  padding: 0 !important;
}

.finance-section-content .ant-tabs-tab-active .ant-tabs-tab-btn {
  font-weight: 600 !important;
  color: #FF7A00 !important;
}

/* 页签的emoji图标样式 */
.finance-section-content .ant-tabs-tab span {
  font-size: 13px !important;
  line-height: 1.5 !important;
}

/* 页签容器边框 */
.finance-section-content .ant-tabs {
  border: 1px solid #E5E7EB !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.finance-section-content .ant-tabs-nav-list {
  border-bottom: 1px solid #E5E7EB !important;
}

/* 修正页签背景和边框 */
.finance-section-content .ant-tabs .ant-tabs-tab {
  margin: 0 !important;
  border-right: 1px solid rgba(229, 231, 235, 0.5) !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab:last-child {
  border-right: none !important;
}

/* 激活状态的特殊处理 */
.finance-section-content .ant-tabs .ant-tabs-tab-active {
  position: relative !important;
}

.finance-section-content .ant-tabs .ant-tabs-tab-active::after {
  content: '' !important;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background: #FF7A00 !important;
  z-index: 1 !important;
}

/* Payment Terms InputNumber垂直居中 */
.ant-input-number {
  display: flex !important;
  align-items: center !important;
}

.ant-input-number-input {
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
  line-height: 1 !important;
}

/* Payment Terms布局优化 */
.finance-section-content .ant-form-item {
  margin-bottom: 0 !important;
}

/* 专门针对Invoice AutoComplete组件的字体大小设置 */
.invoice-autocomplete .ant-select-selector,
.invoice-autocomplete .ant-select-selection-item,
.invoice-autocomplete .ant-select-selection-placeholder,
.invoice-autocomplete input {
  font-size: 14px !important;
}

.invoice-autocomplete .ant-select-dropdown .ant-select-item {
  font-size: 14px !important;
}

/* 确保AutoComplete输入框内的文字大小 */
.invoice-autocomplete .ant-select-selection-search-input {
  font-size: 14px !important;
} 