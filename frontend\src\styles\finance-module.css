.finance-module-wrapper {
  padding: 24px;
  background-color: #fff;
  border-radius: var(--radius-md);
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.finance-content {
  background-color: #fff;
  border-radius: var(--radius-md);
  margin-top: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
}

/* Project Profit Dashboard Tailwind 样式 */
.text-revenue { color: #3B82F6; }
.text-direct-cost { color: #EF4444; }
.text-gross-profit { color: #10B981; }
.text-indirect-cost { color: #F59E0B; }
.text-net-profit { color: #047857; }

.bg-revenue { background-color: #3B82F6; }
.bg-direct-cost { background-color: #EF4444; }
.bg-gross-profit { background-color: #10B981; }
.bg-indirect-cost { background-color: #F59E0B; }
.bg-net-profit { background-color: #047857; }

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Finance Steps Styles */
.finance-steps {
  margin-bottom: 24px;
}

/* Finance Page Styles - Tailwind CSS Compatibility */
.finance-module-wrapper .p-6 {
  padding: 1.5rem;
}

.finance-module-wrapper .bg-gray-50 {
  background-color: #f9fafb;
}

.finance-module-wrapper .min-h-screen {
  min-height: calc(100vh - 200px);
}

.finance-module-wrapper .grid {
  display: grid;
}

.finance-module-wrapper .grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.finance-module-wrapper .md\:grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.finance-module-wrapper .md\:grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.finance-module-wrapper .gap-4 {
  gap: 1rem;
}

.finance-module-wrapper .mb-6 {
  margin-bottom: 1.5rem;
}

.finance-module-wrapper .mb-8 {
  margin-bottom: 2rem;
}

.finance-module-wrapper .mb-2 {
  margin-bottom: 0.5rem;
}

.finance-module-wrapper .bg-white {
  background-color: white;
}

.finance-module-wrapper .shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.finance-module-wrapper .rounded-lg {
  border-radius: 0.5rem;
}

.finance-module-wrapper .p-4 {
  padding: 1rem;
}

.finance-module-wrapper .text-sm {
  font-size: 0.875rem;
}

.finance-module-wrapper .text-gray-500 {
  color: #6b7280;
}

.finance-module-wrapper .text-xl {
  font-size: 1.25rem;
}

.finance-module-wrapper .font-bold {
  font-weight: 700;
}

.finance-module-wrapper .text-green-700 {
  color: #047857;
}

.finance-module-wrapper .text-green-600 {
  color: #059669;
}

.finance-module-wrapper .text-yellow-500 {
  color: #f59e0b;
}

.finance-module-wrapper .text-blue-600 {
  color: #2563eb;
}

.finance-module-wrapper .text-purple-600 {
  color: #9333ea;
}

.finance-module-wrapper .mt-2 {
  margin-top: 0.5rem;
}

.finance-module-wrapper .w-full {
  width: 100%;
}

.finance-module-wrapper .bg-gray-200 {
  background-color: #e5e7eb;
}

.finance-module-wrapper .h-2 {
  height: 0.5rem;
}

.finance-module-wrapper .rounded-full {
  border-radius: 9999px;
}

.finance-module-wrapper .bg-green-500 {
  background-color: #10b981;
}

.finance-module-wrapper .bg-purple-500 {
  background-color: #8b5cf6;
}

.finance-module-wrapper .w-\[30\%\] {
  width: 30%;
}

.finance-module-wrapper .text-lg {
  font-size: 1.125rem;
}

.finance-module-wrapper .font-semibold {
  font-weight: 600;
}

.finance-module-wrapper .overflow-x-auto {
  overflow-x: auto;
}

.finance-module-wrapper .min-w-full {
  min-width: 100%;
}

.finance-module-wrapper .text-left {
  text-align: left;
}

.finance-module-wrapper .bg-gray-100 {
  background-color: #f3f4f6;
}

.finance-module-wrapper .text-gray-700 {
  color: #374151;
}

.finance-module-wrapper .p-3 {
  padding: 0.75rem;
}

.finance-module-wrapper .border-b {
  border-bottom-width: 1px;
}

.finance-module-wrapper .px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.finance-module-wrapper .py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.finance-module-wrapper .bg-green-100 {
  background-color: #d1fae5;
}

.finance-module-wrapper .rounded {
  border-radius: 0.25rem;
}

.finance-module-wrapper .space-x-2 > * + * {
  margin-left: 0.5rem;
}

.finance-module-wrapper .text-blue-600 {
  color: #2563eb;
}

.finance-module-wrapper .text-gray-500 {
  color: #6b7280;
}

.finance-module-wrapper .bg-blue-100 {
  background-color: #dbeafe;
}

.finance-module-wrapper .text-blue-700 {
  color: #1d4ed8;
}

.finance-module-wrapper .flex {
  display: flex;
}

.finance-module-wrapper .items-center {
  align-items: center;
}

.finance-module-wrapper .justify-between {
  justify-content: space-between;
}

.finance-module-wrapper .bg-indigo-600 {
  background-color: #4f46e5;
}

.finance-module-wrapper .bg-purple-600 {
  background-color: #9333ea;
}

.finance-module-wrapper .text-white {
  color: white;
}

.finance-module-wrapper .px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.finance-module-wrapper .px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.finance-module-wrapper .py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.finance-module-wrapper .py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}

.finance-module-wrapper .hover\:bg-indigo-700:hover {
  background-color: #4338ca;
}

.finance-module-wrapper .hover\:bg-purple-700:hover {
  background-color: #7e22ce;
}

.finance-module-wrapper .flex {
  display: flex;
}

.finance-module-wrapper .items-center {
  align-items: center;
}

.finance-module-wrapper .justify-end {
  justify-content: flex-end;
}

.finance-module-wrapper .mb-3 {
  margin-bottom: 0.75rem;
}

.finance-module-wrapper .mr-1 {
  margin-right: 0.25rem;
}

.finance-module-wrapper .text-lg {
  font-size: 1.125rem;
}

.finance-module-wrapper .text-sm {
  font-size: 0.875rem;
}

.finance-module-wrapper .bg-gray-100 {
  background-color: #f3f4f6;
}

.finance-module-wrapper .text-gray-600 {
  color: #4b5563;
}

/* Invoice Management Styles */
.finance-module-wrapper .bg-yellow-50 {
  background-color: #fffbeb;
}

.finance-module-wrapper .bg-teal-600 {
  background-color: #0d9488;
}

.finance-module-wrapper .hover\:bg-teal-700:hover {
  background-color: #0f766e;
}

.finance-module-wrapper .bg-blue-600 {
  background-color: #2563eb;
}

.finance-module-wrapper .hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.finance-module-wrapper .text-red-700 {
  color: #b91c1c;
}

.finance-module-wrapper .bg-red-100 {
  background-color: #fee2e2;
}

.finance-module-wrapper .text-yellow-700 {
  color: #b45309;
}

.finance-module-wrapper .bg-yellow-100 {
  background-color: #fef3c7;
}

.finance-module-wrapper .text-purple-700 {
  color: #7e22ce;
}

.finance-module-wrapper .bg-purple-100 {
  background-color: #f3e8ff;
}

.finance-module-wrapper .text-orange-700 {
  color: #c2410c;
}

.finance-module-wrapper .bg-orange-100 {
  background-color: #ffedd5;
}

.finance-module-wrapper .text-pink-700 {
  color: #be185d;
}

.finance-module-wrapper .bg-pink-100 {
  background-color: #fce7f3;
}

.finance-module-wrapper .space-x-2 {
  display: flex;
  gap: 0.5rem;
}

/* Status Dropdown Styles */
.finance-module-wrapper .status-dropdown {
  position: relative;
  display: inline-block;
}

.finance-module-wrapper .status-dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: white;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 10;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.finance-module-wrapper .status-dropdown:hover .status-dropdown-content {
  display: block;
}

.finance-module-wrapper .status-option {
  color: black;
  padding: 8px 12px;
  text-decoration: none;
  display: block;
  text-align: left;
  border: none;
  background: transparent;
  width: 100%;
  cursor: pointer;
}

.finance-module-wrapper .status-option:hover {
  background-color: #f3f4f6;
}

/* Tabs Styles */
.finance-module-wrapper .charges-tabs .ant-tabs-nav,
.finance-module-wrapper .invoice-tabs .ant-tabs-nav,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.finance-module-wrapper .charges-tabs .ant-tabs-tab,
.finance-module-wrapper .invoice-tabs .ant-tabs-tab,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-tab {
  padding: 8px 16px;
  margin-right: 4px;
  border-radius: 6px 6px 0 0;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-bottom: none;
  transition: all 0.3s ease;
}

.finance-module-wrapper .charges-tabs .ant-tabs-tab-active,
.finance-module-wrapper .invoice-tabs .ant-tabs-tab-active,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-tab-active {
  background-color: white;
  border-color: #e5e7eb;
  border-bottom: none;
}

.finance-module-wrapper .charges-tabs .ant-tabs-tab:hover,
.finance-module-wrapper .invoice-tabs .ant-tabs-tab:hover,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-tab:hover {
  color: #722ed1;
}

.finance-module-wrapper .charges-tabs .ant-tabs-tab-active .ant-tabs-tab-btn,
.finance-module-wrapper .invoice-tabs .ant-tabs-tab-active .ant-tabs-tab-btn,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #722ed1;
  font-weight: 500;
}

.finance-module-wrapper .charges-tabs .ant-tabs-content-holder,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-content-holder {
  border: none;
}

.finance-module-wrapper .charges-tabs .ant-tabs-content,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-content {
  padding-top: 0;
}

.finance-module-wrapper .charges-tabs .ant-tabs-nav::before,
.finance-module-wrapper .invoice-main-tabs .ant-tabs-nav::before {
  border-bottom: none;
}

/* Invoice Management Tabs Styles */
.invoice-management-tabs {
  margin-bottom: 24px;
}

.invoice-management-tabs .ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.finance-module-wrapper button.bg-transparent {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  font: inherit;
}

.finance-module-wrapper button.bg-transparent:hover {
  text-decoration: underline;
}

.finance-step-circle {
  background-color: white;
  border: 2px solid #1890ff;
  color: #1890ff;
}

.ltc-step.active .finance-step-circle {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ltc-step.completed .finance-step-circle {
  background-color: #52c41a;
  border-color: #52c41a;
  color: white;
}

.finance-step-connector {
  background: linear-gradient(to right, #e6f7ff, #bae7ff);
}

.ltc-step.completed .finance-step-connector {
  background: linear-gradient(to right, #52c41a, #b7eb8f);
}

/* Finance Step Indicator */
.finance-step-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  padding-top: 16px;
  height: 80px;
}

.finance-line {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #e8e8e8;
  z-index: 1;
}

.finance-step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #1890ff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1);
}

.finance-step-circle span {
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.finance-step-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.2), 0 6px 10px rgba(0, 0, 0, 0.15);
}

.finance-step-label {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #1890ff;
}
