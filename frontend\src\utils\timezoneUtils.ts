/**
 * Timezone utility functions for handling time zone conversions and formatting
 */

/**
 * Get current time in a specific timezone
 * @param timezone - IANA timezone string (e.g., 'Asia/Shanghai')
 * @returns formatted time string
 */
export function getCurrentTimeInTimezone(timezone: string): string {
  try {
    const now = new Date();
    return now.toLocaleString('en-US', {
      timeZone: timezone,
      hour12: false,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    console.error('Invalid timezone:', timezone);
    return new Date().toLocaleString();
  }
}

/**
 * Get timezone abbreviation (e.g., 'CST', 'EST')
 * @param timezone - IANA timezone string
 * @returns timezone abbreviation
 */
export function getTimezoneAbbreviation(timezone: string): string {
  try {
    const now = new Date();
    const shortName = now.toLocaleDateString('en-US', {
      timeZone: timezone,
      timeZoneName: 'short'
    }).split(', ')[1];
    return shortName || timezone;
  } catch (error) {
    return timezone;
  }
}

/**
 * Get UTC offset for a timezone
 * @param timezone - IANA timezone string
 * @returns UTC offset string (e.g., '+08:00', '-05:00')
 */
export function getTimezoneOffset(timezone: string): string {
  try {
    const now = new Date();
    const utc1 = new Date(now.toLocaleString('en-US', { timeZone: 'UTC' }));
    const utc2 = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
    const diff = (utc2.getTime() - utc1.getTime()) / (1000 * 60 * 60);
    const sign = diff >= 0 ? '+' : '-';
    const hours = Math.floor(Math.abs(diff));
    const minutes = Math.round((Math.abs(diff) - hours) * 60);
    return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  } catch (error) {
    return '+00:00';
  }
}

/**
 * Get user's browser timezone
 * @returns IANA timezone string
 */
export function getBrowserTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Format time for display with timezone info
 * @param timezone - IANA timezone string
 * @returns formatted time with timezone info
 */
export function formatTimeWithTimezone(timezone: string): string {
  const time = getCurrentTimeInTimezone(timezone);
  const offset = getTimezoneOffset(timezone);
  const abbr = getTimezoneAbbreviation(timezone);
  return `${time} (${abbr} ${offset})`;
}

/**
 * List of common timezones with display names
 */
export const COMMON_TIMEZONES = [
  // Americas
  { value: 'America/New_York', label: 'Eastern Time (ET) - New York, Toronto', region: 'Americas' },
  { value: 'America/Chicago', label: 'Central Time (CT) - Chicago, Mexico City', region: 'Americas' },
  { value: 'America/Denver', label: 'Mountain Time (MT) - Denver, Phoenix', region: 'Americas' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT) - Los Angeles, Vancouver', region: 'Americas' },
  { value: 'America/Anchorage', label: 'Alaska Time (AKST) - Anchorage', region: 'Americas' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HST) - Honolulu', region: 'Americas' },
  { value: 'America/Sao_Paulo', label: 'Brasilia Time (BRT) - São Paulo, Rio de Janeiro', region: 'Americas' },
  
  // Europe
  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT) - London, Dublin', region: 'Europe' },
  { value: 'Europe/Paris', label: 'Central European Time (CET) - Paris, Berlin, Rome', region: 'Europe' },
  { value: 'Europe/Athens', label: 'Eastern European Time (EET) - Athens, Helsinki', region: 'Europe' },
  { value: 'Europe/Moscow', label: 'Moscow Time (MSK) - Moscow', region: 'Europe' },
  
  // Asia
  { value: 'Asia/Shanghai', label: 'China Standard Time (CST) - Beijing, Shanghai', region: 'Asia' },
  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST) - Tokyo, Osaka', region: 'Asia' },
  { value: 'Asia/Seoul', label: 'Korea Standard Time (KST) - Seoul', region: 'Asia' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong Time (HKT) - Hong Kong', region: 'Asia' },
  { value: 'Asia/Singapore', label: 'Singapore Time (SGT) - Singapore', region: 'Asia' },
  { value: 'Asia/Kolkata', label: 'India Standard Time (IST) - Mumbai, Delhi', region: 'Asia' },
  { value: 'Asia/Dubai', label: 'Gulf Standard Time (GST) - Dubai, Abu Dhabi', region: 'Asia' },
  
  // Oceania
  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET) - Sydney, Melbourne', region: 'Oceania' },
  { value: 'Australia/Perth', label: 'Australian Western Time (AWT) - Perth', region: 'Oceania' },
  { value: 'Pacific/Auckland', label: 'New Zealand Time (NZST) - Auckland', region: 'Oceania' },
  
  // Africa
  { value: 'Africa/Cairo', label: 'Eastern European Time (EET) - Cairo', region: 'Africa' },
  { value: 'Africa/Johannesburg', label: 'South Africa Time (SAST) - Johannesburg', region: 'Africa' },
  { value: 'Africa/Lagos', label: 'West Africa Time (WAT) - Lagos', region: 'Africa' }
]; 