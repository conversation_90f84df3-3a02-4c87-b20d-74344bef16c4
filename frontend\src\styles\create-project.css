/* 创建项目页面样式 */
.create-project-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 返回链接样式 */
.back-link {
  margin-bottom: 16px;
}

.back-link a {
  display: inline-flex;
  align-items: center;
  color: #6366f1;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s;
}

.back-link a:hover {
  color: #4f46e5;
}

.back-link .anticon {
  margin-right: 6px;
  font-size: 12px;
}

.page-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.page-card .ant-card-head {
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
}

.page-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #1F2023);
}

.page-card .ant-card-body {
  padding: 24px;
}

/* 表单输入框样式 */
.enhanced-input .ant-input,
.enhanced-input .ant-input-number,
.enhanced-input .ant-select-selector,
.enhanced-input .ant-picker {
  height: 44px !important;
  border-radius: 6px !important;
  padding: 0 12px !important;
}

/* 添加 datalist 样式支持 */
.enhanced-input input[list] {
  width: 100%;
  height: 44px !important;
  border-radius: 6px !important;
  padding: 0 12px !important;
  box-sizing: border-box;
}

.enhanced-input .ant-input-number-input,
.enhanced-input .ant-select-selection-item,
.enhanced-input .ant-picker-input > input {
  height: 42px !important;
  line-height: 42px !important;
}

.enhanced-input .ant-select-selector {
  padding: 0 11px !important;
  display: flex;
  align-items: center;
}

/* 表单布局响应式调整 */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }

  .form-actions .ant-btn {
    width: 100%;
    margin-bottom: 12px;
  }

  .form-actions .ant-btn:last-child {
    margin-bottom: 0;
  }
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}

.form-actions .ant-btn {
  min-width: 120px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.form-actions .cancel-button {
  background-color: #fff;
  border: 1px solid #d9d9d9;
  color: rgba(0, 0, 0, 0.85);
}

.form-actions .create-button {
  background-color: #6366f1;
  border-color: #6366f1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-actions .create-button:hover,
.form-actions .create-button:focus {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

/* 表单分组卡片 */
.form-section-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  height: 100%;
}

.form-section-card .form-section-header {
  background-color: #f8fafc;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section-card .form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2023;
  margin: 0;
}

.form-section-card .form-section-content {
  padding: 16px;
}

/* 必填字段标记 */
.ant-form-item-required::before {
  display: none !important;
}

/* 表单项间距 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item-label {
  padding-bottom: 6px;
}

.ant-form-item-label > label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #1F2023);
}
