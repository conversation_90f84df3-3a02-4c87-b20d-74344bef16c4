declare module 'react-apple-login' {
  import React from 'react';

  export interface AppleLoginProps {
    clientId: string;
    redirectURI: string;
    responseType?: string;
    responseMode?: string;
    scope?: string;
    state?: string;
    nonce?: string;
    usePopup?: boolean;
    designProp?: {
      height?: number;
      width?: number;
      color?: 'white' | 'black';
      border?: boolean;
      type?: 'sign-in' | 'continue';
      border_radius?: number;
      scale?: number;
      locale?: string;
    };
    callback?: (response: any) => void;
    render?: (props: { onClick: () => void }) => React.ReactNode;
  }

  const AppleLogin: React.FC<AppleLoginProps>;
  export default AppleLogin;
}
