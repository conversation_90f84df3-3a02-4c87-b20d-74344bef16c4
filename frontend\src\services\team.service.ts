import api from './api';
import { TeamMember, TeamInvitation, TeamRole, MemberStatus, TeamStats, PermissionTemplate } from '../types';

// 本地存储键
const STORAGE_KEY = 'team_members';

class TeamService {
  private teamMembers: TeamMember[] = [];
  private invitations: TeamInvitation[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载数据
  private loadFromStorage() {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (data) {
        const parsed = JSON.parse(data);
        this.teamMembers = parsed.teamMembers || [];
        this.invitations = parsed.invitations || [];
      }
    } catch (error) {
      console.error('Error loading team data from localStorage:', error);
      this.teamMembers = [];
      this.invitations = [];
    }
  }

  // 保存到localStorage
  private saveToStorage() {
    try {
      const data = {
        teamMembers: this.teamMembers,
        invitations: this.invitations,
        updatedAt: new Date().toISOString()
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving team data to localStorage:', error);
    }
  }

  /**
   * 获取项目团队成员列表
   */
  async getTeamMembers(projectId: string): Promise<TeamMember[]> {
    try {
      const response = await api.get(`/projects/${projectId}/team/members`);
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch team members:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      const projectMembers = this.teamMembers.filter(member => member.projectId === projectId);
      if (projectMembers.length > 0) {
        return projectMembers;
      }
      // 返回模拟数据以支持开发
      return this.getMockTeamMembers(projectId);
    }
  }

  /**
   * 获取项目团队统计信息
   */
  async getTeamStats(projectId: string): Promise<TeamStats> {
    try {
      const response = await api.get(`/projects/${projectId}/team/stats`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch team stats:', error);
      const members = await this.getTeamMembers(projectId);
      return this.calculateTeamStats(members);
    }
  }

  /**
   * 邀请团队成员
   */
  async inviteMember(projectId: string, email: string, role: TeamRole, message?: string): Promise<TeamInvitation> {
    try {
      const response = await api.post(`/projects/${projectId}/team/invite`, {
        email,
        role,
        message
      });
      return response.data;
    } catch (error) {
      console.error('Failed to invite member:', error);
      // 本地创建邀请记录
      const invitation: TeamInvitation = {
        id: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId,
        email,
        role,
        invitedBy: 'current_user', // 默认值
        invitedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
        token: `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'pending'
      };
      
      this.invitations.push(invitation);
      this.saveToStorage();
      
      return invitation;
    }
  }

  /**
   * 批量邀请团队成员
   */
  async inviteMultipleMembers(projectId: string, invitations: Array<{email: string, role: TeamRole}>): Promise<TeamInvitation[]> {
    try {
      const response = await api.post(`/projects/${projectId}/team/invite/batch`, {
        invitations
      });
      return response.data;
    } catch (error) {
      console.error('Failed to invite multiple members:', error);
      // 本地创建批量邀请记录
      const createdInvitations: TeamInvitation[] = invitations.map(inv => ({
        id: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        projectId,
        email: inv.email,
        role: inv.role,
        invitedBy: 'current_user', // 默认值
        invitedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        token: `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: 'pending'
      }));
      
      this.invitations.push(...createdInvitations);
      this.saveToStorage();
      
      return createdInvitations;
    }
  }

  /**
   * 移除团队成员
   */
  async removeMember(projectId: string, memberId: string): Promise<void> {
    try {
      await api.delete(`/projects/${projectId}/team/members/${memberId}`);
    } catch (error) {
      console.error('Failed to remove member:', error);
      // 本地移除成员
      this.teamMembers = this.teamMembers.filter(member => 
        !(member.id === memberId && member.projectId === projectId)
      );
      this.saveToStorage();
    }
  }

  /**
   * 更新成员角色
   */
  async updateMemberRole(projectId: string, memberId: string, role: TeamRole): Promise<TeamMember> {
    try {
      const response = await api.put(`/projects/${projectId}/team/members/${memberId}/role`, {
        role
      });
      return response.data;
    } catch (error) {
      console.error('Failed to update member role:', error);
      // 本地更新成员角色
      const member = this.teamMembers.find(m => m.id === memberId && m.projectId === projectId);
      if (member) {
        member.role = role;
        // 添加updatedAt字段到member对象
        (member as any).updatedAt = new Date().toISOString();
        this.saveToStorage();
        return member;
      }
      throw new Error('Member not found');
    }
  }

  /**
   * 更新成员权限
   */
  async updateMemberPermissions(projectId: string, memberId: string, permissions: TeamMember['permissions']): Promise<TeamMember> {
    try {
      const response = await api.put(`/projects/${projectId}/team/members/${memberId}/permissions`, {
        permissions
      });
      return response.data;
    } catch (error) {
      console.error('Failed to update member permissions:', error);
      // 本地更新成员权限
      const member = this.teamMembers.find(m => m.id === memberId && m.projectId === projectId);
      if (member) {
        member.permissions = permissions;
        // 添加updatedAt字段到member对象
        (member as any).updatedAt = new Date().toISOString();
        this.saveToStorage();
        return member;
      }
      throw new Error('Member not found');
    }
  }

  /**
   * 获取待处理的邀请
   */
  async getPendingInvitations(projectId: string): Promise<TeamInvitation[]> {
    try {
      const response = await api.get(`/projects/${projectId}/team/invitations`);
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch pending invitations:', error);
      // 从localStorage获取邀请
      this.loadFromStorage();
      return this.invitations.filter(inv => 
        inv.projectId === projectId && inv.status === 'pending'
      );
    }
  }

  /**
   * 取消邀请
   */
  async cancelInvitation(projectId: string, invitationId: string): Promise<void> {
    try {
      await api.delete(`/projects/${projectId}/team/invitations/${invitationId}`);
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
      // 本地取消邀请
      const invitation = this.invitations.find(inv => inv.id === invitationId);
      if (invitation) {
        invitation.status = 'declined'; // 使用'declined'而不是'cancelled'
        // 添加updatedAt字段到invitation对象
        (invitation as any).updatedAt = new Date().toISOString();
        this.saveToStorage();
      }
    }
  }

  /**
   * 重新发送邀请
   */
  async resendInvitation(projectId: string, invitationId: string): Promise<void> {
    try {
      await api.post(`/projects/${projectId}/team/invitations/${invitationId}/resend`);
    } catch (error) {
      console.error('Failed to resend invitation:', error);
      // 本地更新邀请时间
      const invitation = this.invitations.find(inv => inv.id === invitationId);
      if (invitation) {
        invitation.invitedAt = new Date().toISOString();
        invitation.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
        this.saveToStorage();
      }
    }
  }

  /**
   * 获取权限模板
   */
  getPermissionTemplates(): PermissionTemplate[] {
    return [
      {
        role: TeamRole.OWNER,
        description: 'Full access to all project features and settings',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: true,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        role: TeamRole.ADMIN,
        description: 'Manage project and team, but cannot delete project',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: false,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        role: TeamRole.MEMBER,
        description: 'Edit project content and view reports',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: false,
          canManageTeam: false,
          canManageFinance: false,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        role: TeamRole.VIEWER,
        description: 'View-only access to project',
        permissions: {
          canViewProject: true,
          canEditProject: false,
          canDeleteProject: false,
          canManageTeam: false,
          canManageFinance: false,
          canExportData: false,
          canViewReports: true,
        }
      }
    ];
  }

  /**
   * 搜索用户（用于邀请）
   */
  async searchUsers(query: string): Promise<Array<{id: string, name: string, email: string, avatar?: string}>> {
    try {
      const response = await api.get(`/users/search?q=${encodeURIComponent(query)}`);
      return response.data || [];
    } catch (error) {
      console.error('Failed to search users:', error);
      return [];
    }
  }

  /**
   * 获取当前用户在项目中的角色
   */
  async getCurrentUserRole(projectId: string): Promise<TeamRole | null> {
    try {
      const response = await api.get(`/projects/${projectId}/team/my-role`);
      return response.data.role;
    } catch (error) {
      console.error('Failed to get current user role:', error);
      return TeamRole.OWNER; // 开发时默认为Owner
    }
  }

  /**
   * 计算团队统计信息
   */
  private calculateTeamStats(members: TeamMember[]): TeamStats {
    const activeMembers = members.filter(m => m.status === MemberStatus.ACTIVE);
    const pendingMembers = members.filter(m => m.status === MemberStatus.PENDING);
    
    const roleDistribution = {
      [TeamRole.OWNER]: 0,
      [TeamRole.ADMIN]: 0,
      [TeamRole.MEMBER]: 0,
      [TeamRole.VIEWER]: 0,
    };

    members.forEach(member => {
      roleDistribution[member.role]++;
    });

    return {
      totalMembers: members.length,
      activeMembers: activeMembers.length,
      pendingInvitations: pendingMembers.length,
      roleDistribution
    };
  }

  /**
   * 模拟数据（开发时使用）
   */
  private getMockTeamMembers(projectId: string): TeamMember[] {
    return [
      {
        id: '1',
        userId: 'user1',
        projectId,
        email: '<EMAIL>',
        name: 'John Doe',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=random',
        role: TeamRole.OWNER,
        status: MemberStatus.ACTIVE,
        invitedBy: 'system',
        invitedAt: '2024-01-01T00:00:00Z',
        joinedAt: '2024-01-01T00:00:00Z',
        lastActiveAt: '2024-01-15T10:30:00Z',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: true,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        id: '2',
        userId: 'user2',
        projectId,
        email: '<EMAIL>',
        name: 'Jane Smith',
        avatar: 'https://ui-avatars.com/api/?name=Jane+Smith&background=random',
        role: TeamRole.ADMIN,
        status: MemberStatus.ACTIVE,
        invitedBy: 'user1',
        invitedAt: '2024-01-02T00:00:00Z',
        joinedAt: '2024-01-02T09:15:00Z',
        lastActiveAt: '2024-01-14T16:45:00Z',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: false,
          canManageTeam: true,
          canManageFinance: true,
          canExportData: true,
          canViewReports: true,
        }
      },
      {
        id: '3',
        userId: 'user3',
        projectId,
        email: '<EMAIL>',
        name: 'Mike Wilson',
        avatar: 'https://ui-avatars.com/api/?name=Mike+Wilson&background=random',
        role: TeamRole.MEMBER,
        status: MemberStatus.PENDING,
        invitedBy: 'user1',
        invitedAt: '2024-01-10T00:00:00Z',
        permissions: {
          canViewProject: true,
          canEditProject: true,
          canDeleteProject: false,
          canManageTeam: false,
          canManageFinance: false,
          canExportData: true,
          canViewReports: true,
        }
      }
    ];
  }
}

export default new TeamService(); 