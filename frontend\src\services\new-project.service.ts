import { Project } from '../types';
import TierMappingService from './tierMappingService';
import emergencyDataManager from './emergencyDataManager';

// 🚨 强制确认新代码已加载 - 当前时间戳
console.log('🚨🚨🚨 NEW-PROJECT-SERVICE.TS LOADED AT:', new Date().toISOString());

// 🔧 存储键名 - 使用emergencyDataManager统一管理
const STORAGE_KEY = 'edm_projects'; // 改为EDM管理的键名
const BACKUP_KEY = 'edm_projects_backup';
const CACHE_KEY_PREFIX = 'edm_project_cache_';
const CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟缓存过期时间

// 项目缓存
interface ProjectCache {
  data: Project;
  timestamp: number;
}

// 项目缓存管理
class ProjectCacheManager {
  private cache = new Map<string, ProjectCache>();

  // 获取缓存的项目
  get(id: string): Project | null {
    const cacheKey = `${CACHE_KEY_PREFIX}${id}`;

    // 尝试从内存缓存获取
    const memoryCache = this.cache.get(id);
    if (memoryCache && Date.now() - memoryCache.timestamp < CACHE_EXPIRY) {
      console.log(`[Cache] Memory cache hit for project ${id}`);
      return memoryCache.data;
    }

    // 尝试从localStorage获取
    try {
      const storageCache = localStorage.getItem(cacheKey);
      if (storageCache && storageCache !== 'undefined' && storageCache !== 'null') {
        const parsed = JSON.parse(storageCache) as ProjectCache;
        if (parsed && parsed.timestamp && Date.now() - parsed.timestamp < CACHE_EXPIRY) {
          console.log(`[Cache] Storage cache hit for project ${id}`);
          // 更新内存缓存
          this.cache.set(id, parsed);
          return parsed.data;
        } else {
          // 缓存过期，清除
          localStorage.removeItem(cacheKey);
        }
      }
    } catch (error) {
      console.error(`[Cache] Error reading cache for project ${id}:`, error);
      // Clear corrupted cache
      localStorage.removeItem(cacheKey);
    }

    return null;
  }

  // 设置项目缓存
  set(id: string, data: Project): void {
    const cacheKey = `${CACHE_KEY_PREFIX}${id}`;
    const cacheData: ProjectCache = {
      data,
      timestamp: Date.now()
    };

    // 更新内存缓存
    this.cache.set(id, cacheData);

    // 更新localStorage缓存
    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    } catch (error) {
      console.error(`[Cache] Error writing cache for project ${id}:`, error);
    }
  }

  // 清除项目缓存
  clear(id: string): void {
    const cacheKey = `${CACHE_KEY_PREFIX}${id}`;

    // 清除内存缓存
    this.cache.delete(id);

    // 清除localStorage缓存
    try {
      localStorage.removeItem(cacheKey);
    } catch (error) {
      console.error(`[Cache] Error clearing cache for project ${id}:`, error);
    }
  }

  // 清除所有缓存
  clearAll(): void {
    // 清除内存缓存
    this.cache.clear();

    // 清除localStorage缓存
    try {
      const keys = Object.keys(localStorage);
      for (const key of keys) {
        if (key.startsWith(CACHE_KEY_PREFIX)) {
          localStorage.removeItem(key);
        }
      }
    } catch (error) {
      console.error(`[Cache] Error clearing all caches:`, error);
    }
  }
}

// 创建缓存管理器实例
const projectCache = new ProjectCacheManager();

/**
 * 🔧 从 emergencyDataManager 获取项目列表
 */
const getProjects = (): Project[] => {
  try {
    return emergencyDataManager.getAllProjects();
  } catch (error) {
    console.error('Error getting projects from emergencyDataManager:', error);
    return [];
  }
};

/**
 * 🔧 保存项目列表到 emergencyDataManager
 */
const saveProjects = async (projects: Project[]): Promise<boolean> => {
  try {
    return await emergencyDataManager.saveAllProjects(projects);
  } catch (error) {
    console.error('Error saving projects to emergencyDataManager:', error);
    return false;
  }
};

/**
 * 初始化数据 - 仅在完全没有数据时运行
 */
export const initializeData = (): boolean => {
  try {
    console.log('initializeData - Checking for existing data...');
    console.log('initializeData - Storage key:', STORAGE_KEY);
    
    // 检查EmergencyDataManager中是否已有数据
    const existingProjects = emergencyDataManager.getAllProjects();
    console.log('initializeData - Existing projects from EDM:', existingProjects.length);
    
    if (existingProjects.length > 0) {
      console.log('✅ Data already exists with', existingProjects.length, 'projects');
      console.log('✅ Project names:', existingProjects.map((p: any) => p.name));
      console.log('🛡️ Skipping initialization to protect existing user data');
      return true;
    }

    // 检查localStorage直接存储
    const data = localStorage.getItem(STORAGE_KEY);
    if (data) {
      try {
        const projects = JSON.parse(data);
        if (Array.isArray(projects) && projects.length > 0) {
          console.log('✅ Found localStorage data with', projects.length, 'projects');
          console.log('✅ Project names:', projects.map((p: any) => p.name));
          console.log('🛡️ Skipping initialization to protect existing user data');
          return true;
        }
      } catch (e) {
        console.error('❌ Error parsing existing localStorage data:', e);
      }
    }

    // 🛡️ 检查用户删除日志，尊重用户意愿
    const deletedLog = (() => {
      try {
        return JSON.parse(localStorage.getItem('deleted_projects_log') || '[]');
      } catch (error) {
        console.warn('⚠️ Error reading deleted_projects_log:', error);
        return [];
      }
    })();
    
    if (deletedLog.length > 0) {
      console.log('🚫 Found user deletion log, respecting user intention. No auto-restoration.');
      console.log('🚫 Deleted projects:', deletedLog.map((log: any) => log.projectId));
      return true; // 返回true表示"成功"地跳过了初始化
    }
    
    // 检查是否是真正的首次使用（没有任何localStorage键）
    const allKeys = Object.keys(localStorage);
    const hasAnyProjectData = allKeys.some(key => 
      key.includes('projects') || 
      key.includes('edm_') || 
      key.includes('emergency_backup')
    );
    
    if (hasAnyProjectData) {
      console.log('🔍 Found project-related data in localStorage, skipping initialization');
      return true;
    }

    // 只有在真正首次使用时才创建示例数据
    console.log('🔍 First time user detected, creating initial sample data...');
    const initialProjects: Project[] = [
      {
        id: `proj_${Date.now()}_1`,
        projectId: `PROJ-${new Date().getFullYear()}-001`,
        name: 'Web Development',
        client: 'Global Tech Inc.',
        country: 'United States',
        tier: 'A',
        level: 'A',
        category: 'Software',
        revenue: 50000,
        stage: 'Execution',
        status: 'in_progress',
        probability: 80,
        owner: 'Admin',
        description: 'Full-stack web application development for a new e-commerce platform.',
        startDate: '2024-07-01',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: `proj_${Date.now()}_2`,
        projectId: `PROJ-${new Date().getFullYear()}-002`,
        name: 'IT Consulting & Development',
        client: 'Innovate Solutions',
        country: 'Germany',
        tier: 'V',
        level: 'V',
        category: 'Service',
        revenue: 120000,
        stage: 'Proposal',
        status: 'planning',
        probability: 60,
        owner: 'Admin',
        description: 'IT infrastructure consulting and development services.',
        startDate: '2024-08-15',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    emergencyDataManager.saveAllProjects(initialProjects);
    console.log('✅ Successfully created initial sample data for first-time user.');
    
    return true;
  } catch (error) {
    console.error('❌ Error initializing data:', error);
    return false;
  }
};

// 仅在启动时检查一次，不强制执行
// initializeData(); // 移除自动执行

class NewProjectService {
  /**
   * 获取所有项目
   */
  async getAllProjects(forceRefresh: boolean = false): Promise<Project[]> {
    try {
      console.log('📊 getAllProjects called, forceRefresh:', forceRefresh);
      
      // 清除缓存（如果强制刷新）
      if (forceRefresh) {
        console.log('🧹 Clearing project cache due to forceRefresh');
        projectCache.clearAll();
      }

      // 🔒 **修复：直接从localStorage读取最新数据，确保数据不丢失**
      let projects: Project[] = [];
      
      try {
        // 直接检查localStorage中的所有可能的项目存储位置
        const edmProjects = localStorage.getItem('edm_projects');
        if (edmProjects && edmProjects !== 'null' && edmProjects !== 'undefined') {
          const parsedEdm = JSON.parse(edmProjects);
          if (Array.isArray(parsedEdm) && parsedEdm.length > 0) {
            projects = parsedEdm;
            console.log('✅ Found projects in edm_projects:', projects.length);
          }
        }
        
        // 如果edm_projects没有数据，检查其他可能的存储位置
        if (projects.length === 0) {
          const fallbackKeys = ['projects', 'ltc_projects', 'dal_projects'];
          for (const key of fallbackKeys) {
            const fallbackData = localStorage.getItem(key);
            if (fallbackData && fallbackData !== 'null' && fallbackData !== 'undefined') {
              const parsed = JSON.parse(fallbackData);
              if (Array.isArray(parsed) && parsed.length > 0) {
                projects = parsed;
                console.log(`✅ Found projects in fallback key ${key}:`, projects.length);
                
                // 迁移到标准位置
                localStorage.setItem('edm_projects', JSON.stringify(projects));
                console.log('🔄 Migrated projects to edm_projects');
                break;
              }
            }
          }
        }
        
      } catch (parseError) {
        console.error('❌ Error parsing localStorage data:', parseError);
        projects = [];
      }

      // 记录找到的项目信息
      console.log('📋 Retrieved projects count:', projects.length);
      if (projects.length > 0) {
        console.log('📋 Project names:', projects.map(p => p.name));
        console.log('📋 Project IDs:', projects.map(p => p.id));
      }
      
      // 🛡️ 检查用户删除意愿，绝对不自动恢复
      if (projects.length === 0) {
        // 检查用户删除日志
        const deletedLog = (() => {
          try {
            return JSON.parse(localStorage.getItem('deleted_projects_log') || '[]');
          } catch (error) {
            return [];
          }
        })();
        
        if (deletedLog.length > 0) {
          console.log('🚫 No projects found, but deletion log exists. Respecting user deletion.');
          console.log('🚫 User has deleted projects, keeping system clean.');
          return [];
        }
        
        console.log('⚠️ No projects found and no deletion log. Checking if first-time user...');
        
        // 检查是否是真正首次使用
        const allKeys = Object.keys(localStorage);
        const hasAnyData = allKeys.some(key => 
          key.includes('projects') || 
          key.includes('edm_') || 
          key.includes('clients') ||
          key.includes('emergency_backup')
        );
        
        if (!hasAnyData) {
          console.log('🔍 First-time user detected, will initialize sample data');
          // 只有真正首次使用才初始化
          initializeData();
          // 重新获取数据
          projects = emergencyDataManager.getAllProjects();
        } else {
          console.log('🚫 User has used system before, respecting empty state');
        }
      }
      
      return projects || [];
    } catch (error) {
      console.error('Error in getAllProjects:', error);
      throw error;
    }
  }

  /**
   * 根据 ID 获取项目
   */
  async getProjectById(id: string): Promise<Project | null> {
    try {
      console.log(`Fetching project with ID: ${id}`);

      // 性能标记开始
      performance.mark('getProjectStart');

      // 检查ID是否有效
      if (!id) {
        console.error('Invalid project ID: empty or undefined');
        throw new Error('Invalid project ID');
      }

      // 首先尝试从缓存获取
      const cachedProject = projectCache.get(id);
      if (cachedProject) {
        console.log(`[Cache] Using cached project for ID: ${id}`);

        // 性能标记结束
        performance.mark('getProjectEnd');
        performance.measure('getProjectTime', 'getProjectStart', 'getProjectEnd');
        const measures = performance.getEntriesByName('getProjectTime');
        console.log(`[Performance] getProjectById completed in ${measures[0]?.duration.toFixed(2)}ms (from cache)`);
        performance.clearMarks();
        performance.clearMeasures('getProjectTime');

        return cachedProject;
      }

      // 获取所有项目
      const projects = getProjects();
      console.log(`Total projects found: ${projects.length}`);

      // 首先尝试通过id精确匹配
      let project = projects.find(p => p.id === id);

      // 如果没有找到，尝试通过projectId匹配
      if (!project) {
        console.log(`Project not found by id, trying projectId match`);
        project = projects.find(p => p.projectId === id);
      }

      // 如果没有找到，尝试通过字符串包含匹配
      if (!project) {
        console.log(`Project not found by projectId, trying partial match`);
        project = projects.find(p =>
          p.id.includes(id) ||
          (p.projectId && p.projectId.includes(id))
        );
      }

      // 如果没有找到，尝试通过数字ID匹配（如果id是数字）
      if (!project && !isNaN(Number(id))) {
        console.log(`Project not found by string match, trying numeric id match`);
        project = projects.find(p => p.id === Number(id).toString());
      }

      // 如果仍然没有找到项目
      if (!project) {
        console.warn(`Project with ID ${id} not found in ${projects.length} projects`);
        return null; // 返回null而不是抛出错误
      }

      console.log(`Successfully found project:`, project);

      // 缓存找到的项目
      projectCache.set(id, project);

      // 性能标记结束
      performance.mark('getProjectEnd');
      performance.measure('getProjectTime', 'getProjectStart', 'getProjectEnd');
      const measures = performance.getEntriesByName('getProjectTime');
      console.log(`[Performance] getProjectById completed in ${measures[0]?.duration.toFixed(2)}ms (from storage)`);
      performance.clearMarks();
      performance.clearMeasures('getProjectTime');

      return project;
    } catch (error) {
      console.error(`Error in getProjectById(${id}):`, error);
      throw error;
    }
  }

  /**
   * 创建新项目
   */
  async createProject(project: Partial<Project>): Promise<Project> {
    console.log('🚨🚨🚨 CRITICAL DEBUG: NEW-PROJECT-SERVICE.TS createProject CALLED! 🚨🚨🚨');
    console.log('🚀 Creating new project:', project);
    
    // 获取现有项目
    const projects = getProjects();
    console.log('📋 Current projects count before creation:', projects.length);
    console.log('📋 Current project IDs before creation:', projects.map(p => p.id));

    // 生成项目 ID
    const year = new Date().getFullYear();
    const count = projects.length + 1;
    const projectId = `Oppty-${year}-${count.toString().padStart(3, '0')}`;

    // 创建新项目
    const newProject: Project = {
      id: Date.now().toString(),
      projectId: projectId,
      name: project.name || 'New Opportunity',
      client: project.client || 'Unknown Client',
      country: project.country || '-',
      tier: project.tier ? TierMappingService.normalizeToStandard(project.tier) : 'A',
      level: project.level || '-',
      category: project.category || '-',
      revenue: project.revenue || 0,
      stage: project.stage || '-',
      status: project.status || 'planning',
      probability: project.probability || '-',
      owner: project.owner || '-',
      description: project.description || '-',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    console.log('🆕 New project object created:', newProject);
    console.log('🆕 New project ID:', newProject.id);

    // 添加到项目列表
    projects.push(newProject);
    console.log('📋 Projects count after adding:', projects.length);
    console.log('📋 Project IDs after adding:', projects.map(p => p.id));

    // 强制验证项目确实在数组中
    const foundInArray = projects.find(p => p.id === newProject.id);
    console.log('🔍 New project found in array before save:', !!foundInArray);

    // 保存到 emergencyDataManager
    console.log('💾 About to save projects to EmergencyDataManager...');
    const saved = await saveProjects(projects);
    console.log('💾 Save result:', saved);
    
    if (!saved) {
      throw new Error('Failed to save project');
    }

    // 立即验证保存是否成功
    console.log('✅ Starting verification...');
    const verifyProjects = getProjects();
    console.log('✅ Verification: projects count after save:', verifyProjects.length);
    console.log('✅ Verification: all project IDs after save:', verifyProjects.map(p => p.id));
    const savedProject = verifyProjects.find(p => p.id === newProject.id);
    console.log('✅ New project found in storage:', !!savedProject);

    if (!savedProject) {
      console.error('🚨 CRITICAL: New project was NOT saved to storage!');
      console.error('🚨 Expected project ID:', newProject.id);
      console.error('🚨 Available project IDs:', verifyProjects.map(p => p.id));
      
      // 紧急尝试：直接保存到localStorage
      console.log('🚨 Emergency save: Attempting direct localStorage save...');
      try {
        const currentData = localStorage.getItem('edm_projects');
        const currentProjects = currentData ? JSON.parse(currentData) : [];
        currentProjects.push(newProject);
        localStorage.setItem('edm_projects', JSON.stringify(currentProjects));
        
        // 验证紧急保存
        const emergencyVerify = getProjects();
        const emergencyFound = emergencyVerify.find(p => p.id === newProject.id);
        console.log('🚨 Emergency save verification:', !!emergencyFound);
        
        if (emergencyFound) {
          console.log('✅ Emergency save successful!');
        }
      } catch (emergencyError) {
        console.error('🚨 Emergency save failed:', emergencyError);
      }
    }

    return newProject;
  }

  /**
   * 更新项目
   */
  async updateProject(id: string, project: Partial<Project>): Promise<Project> {
    console.log(`Updating project with ID: ${id}`);

    // 性能标记开始
    performance.mark('updateProjectStart');

    // 获取现有项目
    const projects = getProjects();

    // 查找项目
    const index = projects.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error(`Project with ID ${id} not found`);
    }

    // 更新项目
    const updatedProject: Project = {
      ...projects[index],
      ...project,
      updatedAt: new Date().toISOString()
    };

    // 替换项目
    projects[index] = updatedProject;

    // 保存到 emergencyDataManager
    const saved = await saveProjects(projects);
    if (!saved) {
      throw new Error('Failed to update project');
    }

    // 更新缓存
    projectCache.set(id, updatedProject);
    console.log(`[Cache] Updated cache for project ${id}`);

    // 性能标记结束
    performance.mark('updateProjectEnd');
    performance.measure('updateProjectTime', 'updateProjectStart', 'updateProjectEnd');
    const measures = performance.getEntriesByName('updateProjectTime');
    console.log(`[Performance] updateProject completed in ${measures[0]?.duration.toFixed(2)}ms`);
    performance.clearMarks();
    performance.clearMeasures('updateProjectTime');

    return updatedProject;
  }

  /**
   * 删除项目 - 彻底删除所有相关数据
   */
  async deleteProject(id: string): Promise<boolean> {
    console.log('🗑️ deleteProject called with id:', id);

    try {
      // 导入emergencyDataManager
      const { emergencyDataManager } = await import('./emergencyDataManager');
      
      // 获取现有项目
      const projects = getProjects();
      console.log('Current projects count:', projects.length);

      // 查找项目（支持id和projectId两种方式）
      let projectToDelete = projects.find(p => p.id === id);
      if (!projectToDelete) {
        projectToDelete = projects.find(p => p.projectId === id);
      }

      if (!projectToDelete) {
        console.error(`❌ Project with ID ${id} not found`);
        throw new Error(`Project with ID ${id} not found`);
      }

      console.log('🎯 Project to delete:', { 
        id: projectToDelete.id, 
        projectId: projectToDelete.projectId, 
        name: projectToDelete.name 
      });

      // 使用emergencyDataManager的彻底删除功能
      const emergencyDeleted = await emergencyDataManager.deleteProject(id);
      if (!emergencyDeleted) {
        throw new Error('EmergencyDataManager deletion failed');
      }

      // 同时使用传统方法确保删除
      const filteredProjects = projects.filter(p => p.id !== id && p.projectId !== id);
      const saved = await saveProjects(filteredProjects);
      if (!saved) {
        throw new Error('Traditional deletion failed');
      }

      // 清除项目缓存
      projectCache.clear(id);
      projectCache.clear(projectToDelete.projectId || '');

      // 最终验证删除结果
      setTimeout(() => {
        const remainingProjects = getProjects();
        const stillExists = remainingProjects.some(p => p.id === id || p.projectId === id);
        console.log('🔍 Final verification - Project still exists:', stillExists);
        
        if (stillExists) {
          console.warn('⚠️ Project still exists after all deletion attempts, this may indicate a data recovery issue');
        } else {
          console.log('✅ Project successfully deleted and verified');
        }
      }, 100);

      return true;

    } catch (error) {
      console.error('❌ Error in deleteProject:', error);
      throw error;
    }
  }

  /**
   * 从备份恢复数据
   */
  async restoreFromBackup(): Promise<boolean> {
    try {
      const backup = localStorage.getItem(BACKUP_KEY);
      if (!backup) {
        console.error('No backup found');
        return false;
      }

      localStorage.setItem(STORAGE_KEY, backup);
      console.log('Restored from backup');
      return true;
    } catch (error) {
      console.error('Error restoring from backup:', error);
      return false;
    }
  }

  /**
   * 重置数据并重新初始化示例数据
   */
  async resetData(): Promise<boolean> {
    try {
      // 清除所有相关数据
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(BACKUP_KEY);
      
      // 清除缓存
      projectCache.clearAll();

      // 新系统从空白状态开始，不再初始化示例数据
      const initialProjects: Project[] = [];
      
      localStorage.setItem(STORAGE_KEY, JSON.stringify(initialProjects));
      console.log('Data reset and reinitialized with', initialProjects.length, 'sample projects');
      return true;
    } catch (error) {
      console.error('Error resetting data:', error);
      return false;
    }
  }

  /**
   * 清空所有项目数据
   */
  async clearAllProjects(): Promise<boolean> {
    try {
      // 清除localStorage中的项目数据
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(BACKUP_KEY);
      
      // 清除所有缓存
      projectCache.clearAll();
      
      // 初始化空数据
      localStorage.setItem(STORAGE_KEY, JSON.stringify([]));
      
      console.log('All project data cleared');
      return true;
    } catch (error) {
      console.error('Error clearing all projects:', error);
      return false;
    }
  }
}

export default new NewProjectService();
