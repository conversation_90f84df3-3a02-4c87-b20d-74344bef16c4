{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^6.0.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@react-oauth/google": "^0.12.2", "@tanstack/react-query": "^5.76.0", "@testing-library/dom": "^10.4.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-resizable": "^3.0.8", "@types/socket.io-client": "^1.4.36", "@types/xlsx": "^0.0.35", "antd": "^5.24.9", "axios": "^1.9.0", "chart.js": "^4.4.9", "dayjs": "^1.11.13", "moment": "^2.30.1", "react": "^18.2.0", "react-apple-login": "^1.1.6", "react-dom": "^18.2.0", "react-facebook-login": "^4.1.1", "react-resizable": "^3.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-window": "^1.8.11", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chart.js": "^2.9.41", "@types/react-facebook-login": "^4.1.11", "@types/react-window": "^1.8.8"}}