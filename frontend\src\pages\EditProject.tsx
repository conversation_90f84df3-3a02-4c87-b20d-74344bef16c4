import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Card,
  Row,
  Col,
  DatePicker,
  message,
  Spin,
  Space,
  Tag,
  AutoComplete
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UserOutlined,
  ProjectOutlined,
  BankOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import projectService from '../services/new-project.service';
import clientService from '../services/client.service';
import authService from '../services/auth.service';
import notificationService from '../services/notification.service';
import { Project, Client, Notification } from '../types';
import moment from 'moment';
import '../styles/form-styles.css';

// 国家列表数据
const COUNTRIES = [
  'Afghanistan', 'Albania', 'Algeria', 'Argentina', 'Armenia', 'Australia',
  'Austria', 'Azerbaijan', 'Bahrain', 'Bangladesh', 'Belarus', 'Belgium',
  'Bolivia', 'Bosnia and Herzegovina', 'Brazil', 'Bulgaria', 'Cambodia',
  'Canada', 'Chile', 'China', 'Colombia', 'Costa Rica', 'Croatia',
  'Czech Republic', 'Denmark', 'Ecuador', 'Egypt', 'Estonia', 'Finland',
  'France', 'Georgia', 'Germany', 'Greece', 'Hungary', 'Iceland', 'India',
  'Indonesia', 'Iran', 'Iraq', 'Ireland', 'Israel', 'Italy', 'Japan',
  'Jordan', 'Kazakhstan', 'Kenya', 'Kuwait', 'Latvia', 'Lebanon',
  'Lithuania', 'Luxembourg', 'Malaysia', 'Mexico', 'Morocco', 'Netherlands',
  'New Zealand', 'Norway', 'Pakistan', 'Philippines', 'Poland', 'Portugal',
  'Qatar', 'Romania', 'Russia', 'Saudi Arabia', 'Singapore', 'Slovakia',
  'Slovenia', 'South Africa', 'South Korea', 'Spain', 'Sweden', 'Switzerland',
  'Thailand', 'Turkey', 'Ukraine', 'United Arab Emirates', 'United Kingdom',
  'United States', 'Vietnam'
];

const { Option } = Select;

const EditProject: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState<Project | null>(null);
  const [existingClients, setExistingClients] = useState<Client[]>([]);
  const [clientOptions, setClientOptions] = useState<{ value: string; label: string; data: Client }[]>([]);

  // 在组件挂载时加载项目数据
  useEffect(() => {
    fetchProjectData();
    loadExistingClients();
  }, [id]);

  // 加载现有客户列表
  const loadExistingClients = async () => {
    try {
      const clients = await clientService.getAllClients();
      setExistingClients(clients);
      // 初始化客户选项
      const options = clients.map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
      setClientOptions(options);
    } catch (error) {
      console.error('Failed to load existing clients:', error);
    }
  };

  // 处理客户搜索
  const handleClientSearch = (value: string) => {
    if (!value) {
      const options = existingClients.map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
      setClientOptions(options);
      return;
    }

    const filteredOptions = existingClients
            .filter(client =>
        client.name.toLowerCase().includes(value.toLowerCase())
      )
      .map(client => ({
        value: client.name,
        label: `${client.name} (${client.tier} - ${client.country})`,
        data: client
      }));
    
    setClientOptions(filteredOptions);
  };

  // 处理客户选择
  const handleClientSelect = (value: string, option: any) => {
    const selectedClient = option.data as Client;
    if (selectedClient) {
      // 自动填充客户相关信息
      form.setFieldsValue({
        client: selectedClient.name,
        tier: mapClientTierToProjectTier(selectedClient.tier),
        country: selectedClient.country,
        owner: selectedClient.contactPerson || ''
      });
      
      message.success(`Selected existing client: ${selectedClient.name}`);
    }
  };

  // 客户等级映射：从客户的tier映射到项目的tier
  const mapClientTierToProjectTier = (clientTier: string): string => {
    switch (clientTier) {
      case 'SVIP': return 'S';
      case 'VIP': return 'V';
      case 'BA': return 'B';
      case 'A': return 'A';
      case 'B': return 'B';
      case 'C': return 'A'; // 默认映射到A
      default: return 'A';
    }
  };

  // 生成客户ID
  const generateClientId = () => {
    const year = new Date().getFullYear();
    const timestamp = Date.now().toString().slice(-3);
    return `CLI-${year}-${timestamp}`;
  };

  // 客户等级映射：从项目的tier映射到客户的tier
  const mapProjectTierToClientTier = (projectTier: string): 'S' | 'V' | 'B' | 'A' => {
    switch (projectTier) {
      case 'S': return 'S';
      case 'V': return 'V';
      case 'B': return 'B';
      case 'A': return 'A';
      default: return 'A';
    }
  };

  // 根据行业推断客户行业
  const inferIndustryFromCategory = (category: string): string => {
    switch (category) {
      case 'Software': return 'Technology';
      case 'Service': return 'Consulting';
      case 'Product': return 'Manufacturing';
      default: return 'Other';
    }
  };

  // 检查并创建客户（如果不存在）
  const ensureClientExists = async (clientName: string, projectData: any): Promise<void> => {
    try {
      // 搜索现有客户
      const existingClients = await clientService.searchClients(clientName);
            const exactMatch = existingClients.find(client =>
        client.name.toLowerCase() === clientName.toLowerCase()
      );

      if (exactMatch) {
        console.log('Client already exists:', exactMatch.name);
        return;
      }

      // 客户不存在，创建新客户
      const newClientData: Omit<Client, 'id' | 'createdAt' | 'updatedAt'> = {
        clientId: generateClientId(),
        name: clientName,

        email: `contact@${clientName.toLowerCase().replace(/\s+/g, '')}.com`, // 临时生成邮箱
        industry: inferIndustryFromCategory(projectData.category || 'Other'),
        country: projectData.country || 'Germany',
        tier: mapProjectTierToClientTier(projectData.tier || 'A'),
        status: 'potential', // 新创建的客户默认为潜在客户
        contactPerson: projectData.owner || 'Unknown',
        totalRevenue: 0,
        totalProjects: 0,
        notes: `Automatically created from opportunity: ${projectData.projectId || 'Unknown'} (via edit)`
      };

      const createdClient = await clientService.createClient(newClientData);
      console.log('New client created automatically during edit:', createdClient);
      
      message.info(`New client "${clientName}" has been automatically added to the client list.`);
    } catch (error) {
      console.error('Failed to create client during edit:', error);
      // 不阻断项目更新流程，只是记录错误
      console.warn('Client creation failed, but project update will continue');
    }
  };

  // 获取项目数据
  const fetchProjectData = async () => {
    if (!id) {
      message.error('No opportunity ID provided');
      navigate('/opportunity');
      return;
    }

    try {
      setLoading(true);
      const projectData = await projectService.getProjectById(id);
      
      if (!projectData) {
        console.warn('Project data is null, redirecting to opportunity list');
        message.error('Opportunity not found');
        navigate('/opportunity');
        return;
      }
      
      setProject(projectData);

      // 设置表单字段值
      form.setFieldsValue({
        ...projectData,
        startDate: projectData.startDate ? moment(projectData.startDate, 'YYYY-MM-DD') : null,
      });
    } catch (error) {
      console.error('Failed to fetch project data:', error);
      message.error('Failed to load opportunity data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    if (!id) return;
    setSaving(true);
    
    try {
      const updatedValues = {
        ...values,
        startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : null,
        revenue: parseFloat(values.revenue) || 0,
      };
      
      // 确保客户存在
      if (updatedValues.client) {
        await ensureClientExists(updatedValues.client, updatedValues);
      }

      // 更新项目
      await projectService.updateProject(id, updatedValues);
      
      // 发送通知
      try {
        const currentUser = await authService.getCurrentUser();
        if (currentUser) {
          const notificationData: Omit<Notification, 'id' | 'createdAt' | 'isRead' | 'isStarred'> = {
            title: 'Opportunity Updated',
            message: `Opportunity "${updatedValues.name}" was updated.`,
            type: 'info',
            priority: 'medium',
            projectId: id
          };
          await notificationService.createNotification(notificationData);
        }
      } catch (e) {
        console.error("Failed to send notification:", e);
      }
      
      message.success('Opportunity updated successfully!');
      navigate('/opportunity');
    } catch (error) {
      console.error('Error updating project:', error);
      message.error('Failed to update opportunity. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/opportunity')}
          style={{ marginBottom: '16px' }}
        >
          Back to Opportunity List
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          Edit Opportunity
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
        initialValues={{
          tier: 'A',
          level: 'A',
          category: 'Software',
          stage: 'Lead',
          probability: 'A',
          country: 'Germany',
          revenue: 0
        }}
      >
        <Row gutter={24}>
          {/* 左侧列 - 客户信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  <span>Client Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={
                  <span>
                    Client Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="client"
                rules={[
                  { required: true, message: 'Please enter client name' },
                  { min: 2, message: 'Client name must be at least 2 characters' }
                ]}
                tooltip="The name of the client company - start typing to see existing clients"
              >
                <AutoComplete
                  options={clientOptions}
                  onSearch={handleClientSearch}
                  onSelect={handleClientSelect}
                  placeholder="Enter client name or search existing clients"
                  filterOption={false}
                  allowClear
                  style={{ fontSize: '12px' }}
                >
                  <Input prefix={<BankOutlined />} style={{ fontSize: '12px' }} />
                </AutoComplete>
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span>
                        Client Tier <span style={{ color: 'red' }}>*</span>
                      </span>
                    }
                    name="tier"
                    rules={[{ required: true, message: 'Please select client tier' }]}
                    tooltip="Client tier based on business value and potential"
                  >
                    <Select placeholder="Select tier" style={{ fontSize: '12px' }}>
                      <Option value="S">
                        <Tag color="purple">S</Tag> - Strategic Client
                      </Option>
                      <Option value="V">
                        <Tag color="gold">V</Tag> - Valued Client
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Business Client
                      </Option>
                      <Option value="A">
                        <Tag color="green">A</Tag> - Average Client
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={
                      <span>
                        Country / Region <span style={{ color: 'red' }}>*</span>
                      </span>
                    }
                    name="country"
                    rules={[{ required: true, message: 'Please select country' }]}
                    tooltip="Client's primary operating country"
                  >
                    <Select
                      placeholder="Select country"
                      showSearch
                      style={{ fontSize: '12px' }}
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      {COUNTRIES.map(country => (
                        <Option key={country} value={country}>
                          {country}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Client Contact"
                name="owner"
                tooltip="Client contact person responsible for this opportunity"
              >
                <Input 
                  placeholder="Enter client contact person"
                  prefix={<UserOutlined />}
                  style={{ fontSize: '12px' }}
                />
              </Form.Item>
            </Card>

            {/* 项目详情 */}
            <Card
              title={
                <Space>
                  <TrophyOutlined />
                  <span>Project Details</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Level"
                    name="level"
                    tooltip="Project level classification"
                  >
                    <Select placeholder="Select project level" style={{ fontSize: '12px' }}>
                      <Option value="A">
                        <Tag color="green">A</Tag> - High Priority
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Medium Priority
                      </Option>
                      <Option value="C">
                        <Tag color="orange">C</Tag> - Low Priority
                      </Option>
                      <Option value="D">
                        <Tag color="red">D</Tag> - Monitor Only
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Category"
                    name="category"
                    tooltip="Project category type"
                  >
                    <Select placeholder="Select project category" style={{ fontSize: '12px' }}>
                      <Option value="Software">
                        <Tag color="cyan">Software</Tag>
                      </Option>
                      <Option value="Service">
                        <Tag color="purple">Service</Tag>
                      </Option>
                      <Option value="Product">
                        <Tag color="geekblue">Product</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Stage"
                    name="stage"
                    tooltip="Current stage of the opportunity"
                  >
                    <Select placeholder="Select stage" style={{ fontSize: '12px' }}>
                      <Option value="Lead">
                        <Tag color="blue">Lead</Tag>
                      </Option>
                      <Option value="Qualification">
                        <Tag color="cyan">Qualification</Tag>
                      </Option>
                      <Option value="Proposal">
                        <Tag color="orange">Proposal</Tag>
                      </Option>
                      <Option value="Negotiation">
                        <Tag color="purple">Negotiation</Tag>
                      </Option>
                      <Option value="Closed Won">
                        <Tag color="green">Closed Won</Tag>
                      </Option>
                      <Option value="Closed Lost">
                        <Tag color="red">Closed Lost</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Probability"
                    name="probability"
                    tooltip="Success probability rating"
                  >
                    <Select placeholder="Select probability" style={{ fontSize: '12px' }}>
                      <Option value="A">
                        <Tag color="green">A</Tag> - 80-100%
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - 60-80%
                      </Option>
                      <Option value="C">
                        <Tag color="orange">C</Tag> - 40-60%
                      </Option>
                      <Option value="D">
                        <Tag color="red">D</Tag> - &lt;40%
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Status"
                    name="status"
                    tooltip="Current project status"
                  >
                    <Select placeholder="Select status" style={{ fontSize: '12px' }}>
                      <Option value="planning">
                        <Tag color="blue">Planning</Tag>
                      </Option>
                      <Option value="in_progress">
                        <Tag color="processing">In Progress</Tag>
                      </Option>
                      <Option value="completed">
                        <Tag color="success">Completed</Tag>
                      </Option>
                      <Option value="on_hold">
                        <Tag color="warning">On Hold</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  {/* 保留空列以保持布局平衡 */}
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 右侧列 - 机会信息 */}
          <Col span={12}>
            <Card
              title={
                <Space>
                  <ProjectOutlined />
                  <span>Opportunity Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={
                  <span>
                    Opportunity Name <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                name="name"
                rules={[
                  { required: true, message: 'Please enter opportunity name' },
                  { min: 3, message: 'Opportunity name must be at least 3 characters' }
                ]}
                tooltip="Descriptive name for this business opportunity"
              >
                <Input 
                  placeholder="Enter opportunity name"
                  prefix={<ProjectOutlined />}
                  style={{ fontSize: '12px' }}
                />
              </Form.Item>

              <Form.Item
                label="Opportunity ID"
                name="projectId"
                tooltip="Unique identifier for this opportunity (auto-generated)"
              >
                <Input 
                  disabled
                  style={{ fontSize: '12px', backgroundColor: '#f5f5f5' }}
                />
              </Form.Item>

              {/* Currency, Estimated Revenue & VAT Rate 三个字段 */}
              <Row gutter={12}>
                <Col span={8}>
                  <Form.Item
                    label="Currency"
                    name="currency"
                    rules={[{ required: true, message: 'Please select currency!' }]}
                    tooltip="Project currency"
                    initialValue="EUR"
                  >
                    <Select style={{ fontSize: '12px' }}>
                      <Option value="EUR">EUR (€)</Option>
                      <Option value="USD">USD ($)</Option>
                      <Option value="GBP">GBP (£)</Option>
                      <Option value="CNY">CNY (¥)</Option>
                      <Option value="JPY">JPY (¥)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={<span>Estimated Revenue <span style={{ color: 'red' }}>*</span></span>}
                    name="revenue"
                    rules={[
                      { required: true, message: 'Please enter estimated revenue' },
                      { type: 'number', min: 0, message: 'Revenue must be a positive number' }
                    ]}
                    tooltip="Expected revenue from this opportunity"
                  >
                    <InputNumber
                      placeholder="0"
                      style={{ width: '100%', fontSize: '12px' }}
                      precision={2}
                      min={0}
                      max={*********}
                      formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) => (value ? value.replace(/\$\s?|(,*)/g, '') : '') as any}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="VAT Rate"
                    name="vatRate"
                    tooltip="Value Added Tax rate percentage"
                    rules={[
                      { type: 'number', min: 0, max: 100, message: 'VAT rate must be between 0% and 100%' }
                    ]}
                  >
                    <InputNumber
                      style={{ width: '100%', fontSize: '12px' }}
                      min={0}
                      max={100}
                      precision={1}
                      formatter={value => value ? `${value}%` : ''}
                      parser={(value: any) => value ? parseFloat(value.replace('%', '')) : ''}
                      placeholder="Enter VAT rate (e.g. 19)"
                      addonAfter="%"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Estimated Start Date"
                name="startDate"
                tooltip="When this opportunity is expected to begin"
              >
                <DatePicker
                  placeholder="Select start date"
                  style={{ width: '100%', fontSize: '12px' }}
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Row style={{ marginTop: '24px' }}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => navigate('/opportunity')}>
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={saving}
                icon={<SaveOutlined />}
                style={{ backgroundColor: '#FF7A00', borderColor: '#FF7A00' }}
              >
                Update Opportunity
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default EditProject;
