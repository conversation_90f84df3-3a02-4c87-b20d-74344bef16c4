export class TableColumnResizer {
  private isResizing = false;
  private currentColumn: HTMLElement | null = null;
  private startX = 0;
  private startWidth = 0;
  private tableElement: HTMLElement | null = null;
  private tableSelector: string;
  private storageKey: string;
  private globalEventBlockers: Array<{ element: Element | Document, type: string, handler: EventListener }> = [];

  constructor(tableSelector: string) {
    this.tableSelector = tableSelector;
    this.storageKey = `table-column-widths-${btoa(tableSelector).replace(/[^a-zA-Z0-9]/g, '')}`;
    this.tableElement = document.querySelector(tableSelector);
    this.init();
  }

  private init() {
    if (!this.tableElement) return;

    // 使用capture:true和passive:false确保事件优先级最高
    this.tableElement.addEventListener('mousedown', this.handleMouseDown.bind(this), { 
      capture: true, 
      passive: false 
    });
    document.addEventListener('mousemove', this.handleMouseMove.bind(this), { 
      capture: true, 
      passive: false 
    });
    document.addEventListener('mouseup', this.handleMouseUp.bind(this), { 
      capture: true, 
      passive: false 
    });
    
    // 🚀 超强点击拦截器 - 在所有可能的事件阶段拦截
    this.addPermanentClickInterceptor();
    
    // 加载保存的列宽
    this.loadColumnWidths();
  }

  private handleMouseDown(e: MouseEvent) {
    const target = e.target as HTMLElement;
    
    // 首先检查是否点击在表头单元格内
    const thElement = target.closest('th');
    if (!thElement) return;
    
    // 检查是否在拖拽区域
    if (!this.isInResizeArea(e, thElement)) return;
    
    // ⚠️ 超级强力事件阻止 - 立即阻止所有传播
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    
    // 🔥 额外的DOM层级阻止 - 防止React合成事件
    const preventReactEvent = (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    };
    
    // 在目标元素和所有父元素上添加临时阻止器
    let currentElement = target as Element | null;
    while (currentElement && currentElement !== document.body) {
      ['click', 'mousedown', 'mouseup', 'touchstart', 'touchend'].forEach(eventType => {
        currentElement!.addEventListener(eventType, preventReactEvent, { 
          capture: true, 
          passive: false,
          once: false
        });
        this.globalEventBlockers.push({
          element: currentElement!,
          type: eventType,
          handler: preventReactEvent
        });
      });
      currentElement = currentElement.parentElement;
    }
    
    // 更强力的交互元素检测
    const clickPath = e.composedPath ? e.composedPath() : [target];
    
    const hasInteractiveElement = clickPath.some((element: any) => {
      if (!element.classList) return false;
      
      return element.classList.contains('ant-table-column-sorter') ||
             element.classList.contains('ant-table-column-sorter-up') ||
             element.classList.contains('ant-table-column-sorter-down') ||
             element.classList.contains('ant-table-column-sorters') ||
             element.classList.contains('anticon') ||
             element.tagName === 'BUTTON' ||
             element.tagName === 'A' ||
             element.hasAttribute('data-icon') ||
             element.getAttribute('role') === 'button';
    });
    
    if (hasInteractiveElement) {
      console.log('TableResizer: Detected interactive element, skipping resize');
      this.clearAllEventBlockers();
      return;
    }
    
    // 设置拖拽状态
    this.isResizing = true;
    this.currentColumn = thElement;
    this.startX = e.clientX;
    this.startWidth = this.currentColumn.offsetWidth;
    
    // 设置拖拽样式
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
    
    // 🚀 添加超强全局事件拦截器
    this.addUltraStrongEventBlocker();
    
    console.log('TableResizer: Started column resize on', thElement.textContent?.trim());
  }

  private handleMouseMove(e: MouseEvent) {
    if (!this.isResizing || !this.currentColumn) {
      // 如果不在拖拽状态，检查是否需要显示resize光标
      const target = e.target as HTMLElement;
      const thElement = target.closest('th');
      
      if (thElement && this.isInResizeArea(e, thElement)) {
        document.body.style.cursor = 'col-resize';
      } else {
        if (document.body.style.cursor === 'col-resize') {
          document.body.style.cursor = '';
        }
      }
      return;
    }

    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
    
    const diff = e.clientX - this.startX;
    const newWidth = Math.max(80, this.startWidth + diff); // 最小宽度80px
    
    // 设置新的列宽
    this.setColumnWidth(this.currentColumn, newWidth);
  }

  private handleMouseUp(e: MouseEvent) {
    if (!this.isResizing) return;

    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    this.isResizing = false;
    
    // 保存列宽设置
    if (this.currentColumn) {
      this.saveColumnWidths();
    }
    
    this.currentColumn = null;
    
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    
    // 💥 延迟清理事件阻止器，确保所有潜在的点击事件都被阻止
    setTimeout(() => {
      this.clearAllEventBlockers();
    }, 100);
  }

  // 🚀 超强点击拦截器 - 永久监听
  private addPermanentClickInterceptor() {
    const interceptor = (e: Event) => {
      if (this.isResizing) {
        const target = e.target as HTMLElement;
        
        // 🔥 特别拦截Ant Design表格相关的所有点击
        const isAntTableInteraction = target?.closest('.ant-table') ||
                                     target?.closest('.ant-table-column-sorter') ||
                                     target?.closest('.ant-table-column-sorters') ||
                                     target?.closest('.ant-table-thead') ||
                                     target?.classList.contains('anticon') ||
                                     target?.classList.contains('ant-table-column-title');
        
        if (isAntTableInteraction) {
          console.log('TableResizer: 🛡️ PERMANENT BLOCK - Ant table interaction during resize!');
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          return false;
        }
      }
    };
    
    // 在多个阶段拦截事件
    const eventTypes = ['click', 'mousedown', 'mouseup', 'touchstart', 'touchend'];
    eventTypes.forEach(type => {
      document.addEventListener(type, interceptor, { capture: true, passive: false });
      document.addEventListener(type, interceptor, { capture: false, passive: false }); // bubble阶段也拦截
    });
    
    // 保存引用
    (this as any)._permanentInterceptor = { interceptor, eventTypes };
  }

  // 🚀 添加超强全局事件阻止器
  private addUltraStrongEventBlocker() {
    const ultraBlocker = (e: Event) => {
      if (this.isResizing) {
        const target = e.target as HTMLElement;
        
        // 🔥 超级严格检测 - 阻止任何可能触发表格排序的事件
        const triggerElements = [
          '.ant-table-column-sorter',
          '.ant-table-column-sorters', 
          '.ant-table-column-title',
          '.anticon',
          '.ant-table-thead th',
          '[role="button"]',
          'button',
          'a'
        ];
        
        const shouldBlock = triggerElements.some(selector => {
          try {
            return target?.closest(selector) || target?.matches(selector);
          } catch {
            return false;
          }
        });
        
        if (shouldBlock) {
          console.log('TableResizer: 💥 ULTRA BLOCK - Prevented sorting trigger!', e.type);
          e.preventDefault();
          e.stopPropagation();
          e.stopImmediatePropagation();
          
          // 🔥 强制停止事件 - 甚至阻止默认行为的发生
          if ('returnValue' in e) {
            (e as any).returnValue = false;
          }
          return false;
        }
      }
    };
    
    // 阻止更全面的事件类型
    const eventTypes = [
      'click', 'mousedown', 'mouseup', 'dblclick',
      'touchstart', 'touchend', 'touchmove',
      'pointerdown', 'pointerup', 'pointermove',
      'contextmenu', 'select', 'selectstart'
    ];
    
    eventTypes.forEach(type => {
      // 在capture和bubble两个阶段都拦截
      document.addEventListener(type, ultraBlocker, { capture: true, passive: false });
      document.addEventListener(type, ultraBlocker, { capture: false, passive: false });
      
      this.globalEventBlockers.push({
        element: document,
        type: type,
        handler: ultraBlocker
      });
    });
  }

  // 💥 清理所有事件阻止器
  private clearAllEventBlockers() {
    this.globalEventBlockers.forEach(({ element, type, handler }) => {
      try {
        element.removeEventListener(type, handler, { capture: true });
        element.removeEventListener(type, handler, { capture: false });
      } catch (error) {
        // 静默处理移除失败
      }
    });
    this.globalEventBlockers = [];
  }

  private isInResizeArea(e: MouseEvent, th: HTMLElement): boolean {
    const rect = th.getBoundingClientRect();
    const rightEdge = rect.right;
    const mouseX = e.clientX;
    
    // 检查鼠标是否在右边框附近8px范围内，并且不是最后一列（Actions列）
    const isLastColumn = !th.nextElementSibling || 
                        th.textContent?.trim().toLowerCase() === 'actions' ||
                        th.querySelector('.ant-table-column-title')?.textContent?.trim().toLowerCase() === 'actions';
    const inResizeZone = mouseX >= rightEdge - 8 && mouseX <= rightEdge + 2;
    
    if (!inResizeZone || isLastColumn) {
      return false;
    }
    
    // 🔍 超强排序区域检测 - 绝对不能在排序区域内
    const sorterElements = th.querySelectorAll('.ant-table-column-sorter, .ant-table-column-sorters, .anticon');
    for (let i = 0; i < sorterElements.length; i++) {
      const sorter = sorterElements[i];
      const sorterRect = sorter.getBoundingClientRect();
      // 大幅扩大排序区域的安全边界
      const safeLeft = sorterRect.left - 15;
      const safeRight = sorterRect.right + 10;
      const safeTop = sorterRect.top - 5;
      const safeBottom = sorterRect.bottom + 5;
      
      const inSafeArea = mouseX >= safeLeft && mouseX <= safeRight && 
                        e.clientY >= safeTop && e.clientY <= safeBottom;
      
      if (inSafeArea) {
        console.log('TableResizer: Mouse in sorting safe area, blocked');
        return false;
      }
    }
    
    // 检查是否在任何可点击元素区域
    const clickableElements = th.querySelectorAll('button, a, [role="button"], [data-icon]');
    for (let i = 0; i < clickableElements.length; i++) {
      const element = clickableElements[i];
      const elementRect = element.getBoundingClientRect();
      const safeLeft = elementRect.left - 8;
      const safeRight = elementRect.right + 8;
      const safeTop = elementRect.top - 3;
      const safeBottom = elementRect.bottom + 3;
      
      const inElementArea = mouseX >= safeLeft && mouseX <= safeRight && 
                           e.clientY >= safeTop && e.clientY <= safeBottom;
      
      if (inElementArea) {
        console.log('TableResizer: Mouse in clickable element area, blocked');
        return false;
      }
    }
    
    console.log('TableResizer: Mouse in valid resize area');
    return true;
  }

  private setColumnWidth(column: HTMLElement, width: number) {
    // 设置表头列宽
    column.style.width = `${width}px`;
    column.style.minWidth = `${width}px`;
    column.style.maxWidth = `${width}px`;
    
    // 获取列索引
    const columnIndex = Array.from(column.parentElement!.children).indexOf(column);
    
    // 更新对应的列定义宽度
    const colElements = this.tableElement!.querySelectorAll(`col:nth-child(${columnIndex + 1})`);
    colElements.forEach(col => {
      (col as HTMLElement).style.width = `${width}px`;
      (col as HTMLElement).style.minWidth = `${width}px`;
      (col as HTMLElement).style.maxWidth = `${width}px`;
    });
    
    // 更新对应的表体单元格宽度
    const bodyRows = this.tableElement!.querySelectorAll('.ant-table-tbody tr');
    bodyRows.forEach(row => {
      const cell = row.children[columnIndex] as HTMLElement;
      if (cell) {
        cell.style.width = `${width}px`;
        cell.style.minWidth = `${width}px`;
        cell.style.maxWidth = `${width}px`;
      }
    });
  }

  private saveColumnWidths() {
    try {
      const headers = this.tableElement!.querySelectorAll('.ant-table-thead th');
      const widths: { [index: number]: number } = {};
      
      headers.forEach((header, index) => {
        const width = (header as HTMLElement).offsetWidth;
        if (width > 0) {
          widths[index] = width;
        }
      });
      
      localStorage.setItem(this.storageKey, JSON.stringify(widths));
    } catch (error) {
      console.warn('Failed to save column widths:', error);
    }
  }

  private loadColumnWidths() {
    try {
      const savedWidths = localStorage.getItem(this.storageKey);
      if (!savedWidths) return;
      
      const widths: { [index: number]: number } = JSON.parse(savedWidths);
      
      // 延迟应用宽度，确保表格已完全渲染
      setTimeout(() => {
        const headers = this.tableElement!.querySelectorAll('.ant-table-thead th');
        headers.forEach((header, index) => {
          if (widths[index]) {
            this.setColumnWidth(header as HTMLElement, widths[index]);
          }
        });
      }, 100);
    } catch (error) {
      console.warn('Failed to load column widths:', error);
    }
  }

  // 清除保存的列宽设置
  public clearSavedWidths() {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear saved column widths:', error);
    }
  }

  // 销毁方法，用于清理事件监听器
  destroy() {
    if (this.tableElement) {
      this.tableElement.removeEventListener('mousedown', this.handleMouseDown.bind(this), { capture: true });
    }
    document.removeEventListener('mousemove', this.handleMouseMove.bind(this), { capture: true });
    document.removeEventListener('mouseup', this.handleMouseUp.bind(this), { capture: true });
    
    // 清理永久事件拦截器
    const permanentInterceptor = (this as any)._permanentInterceptor;
    if (permanentInterceptor) {
      permanentInterceptor.eventTypes.forEach((type: string) => {
        document.removeEventListener(type, permanentInterceptor.interceptor, { capture: true });
        document.removeEventListener(type, permanentInterceptor.interceptor, { capture: false });
      });
      delete (this as any)._permanentInterceptor;
    }
    
    // 清理所有事件阻止器
    this.clearAllEventBlockers();
    
    // 重置样式
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }
}

// 工具函数：为表格添加列宽调整功能
export const enableTableColumnResize = (tableSelector: string): TableColumnResizer => {
  return new TableColumnResizer(tableSelector);
};

// 工具函数：清除指定表格的保存列宽
export const clearTableColumnWidths = (tableSelector: string): void => {
  const storageKey = `table-column-widths-${btoa(tableSelector).replace(/[^a-zA-Z0-9]/g, '')}`;
  try {
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.warn('Failed to clear table column widths:', error);
  }
};

// 自动初始化常用的表格
export const initTableResizers = () => {
  // 使用setTimeout确保DOM元素已渲染
  setTimeout(() => {
    const selectors = [
      '.client-list-container .ant-table',
      '.project-list-container .ant-table', 
      '.finance-management .ant-table'
    ];
    
    const resizers: TableColumnResizer[] = [];
    
    selectors.forEach(selector => {
      const element = document.querySelector(selector);
      if (element) {
        resizers.push(new TableColumnResizer(selector));
      }
    });
    
    return resizers;
  }, 100);
}; 