.project-filters {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;

  .ant-space {
    width: 100%;
  }

  .ant-input-affix-wrapper {
    border-radius: 6px;
    
    &:hover, &:focus {
      border-color: #1890ff;
    }
  }

  .ant-select {
    .ant-select-selector {
      border-radius: 6px;
    }

    &:hover .ant-select-selector {
      border-color: #1890ff;
    }
  }

  .ant-select-suffix {
    color: #8c8c8c;
  }

  .ant-select-item {
    padding: 8px 12px;
  }

  .ant-select-item-option-selected {
    background-color: #e6f7ff;
    color: #1890ff;
  }

  .ant-select-item-option-active {
    background-color: #f5f5f5;
  }
} 