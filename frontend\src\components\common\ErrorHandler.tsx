import React from 'react';
import { Alert, Space, Typography, Button } from 'antd';
import { CloseCircleOutlined, ReloadOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { ErrorType } from '../../utils/errorHandler';

const { Text, Paragraph } = Typography;

interface ErrorDisplayProps {
  error: any;
  errorType?: ErrorType;
  message?: string;
  onRetry?: () => void;
  onDismiss?: () => void;
}

/**
 * 增强的错误显示组件
 * 根据错误类型显示不同的错误信息和建议
 */
const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  errorType,
  message,
  onRetry,
  onDismiss
}) => {
  // 确定错误类型
  const type = errorType || getErrorType(error);
  
  // 获取错误消息
  const errorMessage = message || getErrorMessage(error, type);
  
  // 获取错误建议
  const suggestion = getErrorSuggestion(type);
  
  // 确定警告类型
  const alertType = getAlertType(type);

  return (
    <Alert
      type={alertType}
      showIcon
      icon={<CloseCircleOutlined />}
      message={
        <Text strong>
          {getErrorTitle(type)}
        </Text>
      }
      description={
        <Space direction="vertical" style={{ width: '100%' }}>
          <Paragraph>{errorMessage}</Paragraph>
          {suggestion && (
            <Paragraph>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              {suggestion}
            </Paragraph>
          )}
          {(onRetry || onDismiss) && (
            <Space>
              {onRetry && (
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />} 
                  onClick={onRetry}
                  size="small"
                >
                  重试
                </Button>
              )}
              {onDismiss && (
                <Button 
                  onClick={onDismiss}
                  size="small"
                >
                  关闭
                </Button>
              )}
            </Space>
          )}
        </Space>
      }
      closable
      onClose={onDismiss}
    />
  );
};

// 根据错误对象确定错误类型
function getErrorType(error: any): ErrorType {
  if (!error) return ErrorType.UNKNOWN;
  
  if (error.code === 'ERR_NETWORK' || error.message?.includes('network')) {
    return ErrorType.NETWORK;
  }
  
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return ErrorType.TIMEOUT;
  }
  
  if (error.response) {
    const status = error.response.status;
    
    if (status === 401) return ErrorType.AUTHENTICATION;
    if (status === 403) return ErrorType.AUTHORIZATION;
    if (status === 404) return ErrorType.NOT_FOUND;
    if (status === 422) return ErrorType.VALIDATION;
    if (status >= 500) return ErrorType.SERVER;
    
    return ErrorType.API;
  }
  
  return ErrorType.CLIENT;
}

// 获取错误消息
function getErrorMessage(error: any, type: ErrorType): string {
  if (!error) return '发生未知错误';
  
  // 如果有响应数据中的消息，优先使用
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  // 使用错误对象的消息
  if (error.message) {
    return error.message;
  }
  
  // 根据错误类型提供默认消息
  switch (type) {
    case ErrorType.NETWORK:
      return '网络连接失败，请检查您的网络连接';
    case ErrorType.TIMEOUT:
      return '请求超时，服务器响应时间过长';
    case ErrorType.AUTHENTICATION:
      return '身份验证失败，请重新登录';
    case ErrorType.AUTHORIZATION:
      return '您没有权限执行此操作';
    case ErrorType.NOT_FOUND:
      return '请求的资源不存在';
    case ErrorType.VALIDATION:
      return '提交的数据无效';
    case ErrorType.SERVER:
      return '服务器错误，请稍后再试';
    case ErrorType.CLIENT:
      return '客户端错误，请刷新页面后重试';
    default:
      return '发生未知错误';
  }
}

// 获取错误标题
function getErrorTitle(type: ErrorType): string {
  switch (type) {
    case ErrorType.NETWORK:
      return '网络错误';
    case ErrorType.TIMEOUT:
      return '请求超时';
    case ErrorType.AUTHENTICATION:
      return '身份验证错误';
    case ErrorType.AUTHORIZATION:
      return '权限错误';
    case ErrorType.NOT_FOUND:
      return '资源不存在';
    case ErrorType.VALIDATION:
      return '数据验证错误';
    case ErrorType.SERVER:
      return '服务器错误';
    case ErrorType.CLIENT:
      return '客户端错误';
    default:
      return '错误';
  }
}

// 获取错误建议
function getErrorSuggestion(type: ErrorType): string | null {
  switch (type) {
    case ErrorType.NETWORK:
      return '请检查您的网络连接，确保您已连接到互联网';
    case ErrorType.TIMEOUT:
      return '服务器响应时间过长，请稍后再试或联系管理员';
    case ErrorType.AUTHENTICATION:
      return '您的登录会话可能已过期，请重新登录';
    case ErrorType.AUTHORIZATION:
      return '您没有执行此操作的权限，请联系管理员获取权限';
    case ErrorType.NOT_FOUND:
      return '请求的资源不存在，请检查URL或ID是否正确';
    case ErrorType.VALIDATION:
      return '请检查您提交的数据是否符合要求';
    case ErrorType.SERVER:
      return '服务器出现问题，请稍后再试或联系技术支持';
    case ErrorType.CLIENT:
      return '请刷新页面后重试，如果问题持续存在，请清除浏览器缓存';
    default:
      return null;
  }
}

// 获取Alert组件的类型
function getAlertType(type: ErrorType): 'error' | 'warning' | 'info' {
  switch (type) {
    case ErrorType.NETWORK:
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
    case ErrorType.SERVER:
      return 'error';
    case ErrorType.TIMEOUT:
    case ErrorType.NOT_FOUND:
    case ErrorType.VALIDATION:
      return 'warning';
    default:
      return 'info';
  }
}

export default ErrorDisplay;
