# Team Management System

SmartLTC项目的完整团队管理解决方案，提供企业级的成员管理、权限控制和协作功能。

## 功能特性

### 📊 团队概览
- 实时团队统计（总成员数、活跃成员、待处理邀请）
- 角色分布可视化
- 团队活动监控

### 👥 成员管理
- **成员列表**：支持搜索、筛选、排序
- **角色管理**：Owner、Admin、Member、Viewer四级权限
- **状态跟踪**：Active、Pending、Inactive状态管理
- **批量操作**：支持批量邀请和权限管理

### 🔐 权限控制
- **细粒度权限**：7种核心权限类型
  - View Project：查看项目
  - Edit Project：编辑项目
  - Delete Project：删除项目
  - Manage Team：管理团队
  - Manage Finance：财务管理
  - Export Data：数据导出
  - View Reports：查看报告

- **权限模板**：预定义的角色权限模板
- **自定义权限**：支持为个别成员定制权限
- **权限继承**：基于角色的权限继承机制

### ✉️ 邀请系统
- **单个邀请**：支持邮箱搜索和角色选择
- **批量邀请**：支持多成员同时邀请
- **CSV导入**：支持批量导入邀请列表
- **邀请管理**：查看、重发、取消待处理邀请
- **到期管理**：自动处理过期邀请

### ⚙️ 团队设置
- **访问控制**：公开加入、审批机制、客人访问
- **安全设置**：2FA要求、密码策略、会话超时
- **通知设置**：成员变动通知、活动摘要
- **限制设置**：团队规模限制、邀请权限

## 组件架构

```
TeamManagement/
├── TeamManagementModal.tsx    # 主模态框，集成所有功能
├── MemberList.tsx             # 成员列表和管理
├── InviteMember.tsx           # 邀请新成员
├── PendingInvitations.tsx     # 待处理邀请管理
├── MemberPermissionModal.tsx  # 成员权限详细设置
├── TeamSettings.tsx           # 团队全局设置
└── index.ts                   # 组件导出
```

## 角色权限矩阵

| 功能 | Owner | Admin | Member | Viewer |
|------|-------|-------|--------|--------|
| 查看项目 | ✅ | ✅ | ✅ | ✅ |
| 编辑项目 | ✅ | ✅ | ✅ | ❌ |
| 删除项目 | ✅ | ❌ | ❌ | ❌ |
| 管理团队 | ✅ | ✅ | ❌ | ❌ |
| 财务管理 | ✅ | ✅ | ❌ | ❌ |
| 导出数据 | ✅ | ✅ | ✅ | ❌ |
| 查看报告 | ✅ | ✅ | ✅ | ✅ |

## 使用方法

### 基本使用
```tsx
import { TeamManagementModal } from '../components/TeamManagement';

const MyComponent = () => {
  const [visible, setVisible] = useState(false);
  const [project, setProject] = useState<Project>(null);

  return (
    <TeamManagementModal
      visible={visible}
      onCancel={() => setVisible(false)}
      project={project}
    />
  );
};
```

### 独立组件使用
```tsx
import { MemberList, InviteMember } from '../components/TeamManagement';

// 使用成员列表
<MemberList
  members={members}
  projectId={projectId}
  currentUserRole={currentUserRole}
  onMemberRemoved={handleMemberRemoved}
  onMemberUpdated={handleMemberUpdated}
/>

// 使用邀请组件
<InviteMember
  projectId={projectId}
  onMemberInvited={handleMemberInvited}
/>
```

## API集成

团队管理系统与后端API紧密集成，提供完整的CRUD操作：

- `GET /projects/:id/team/members` - 获取团队成员
- `POST /projects/:id/team/invite` - 邀请成员
- `PUT /projects/:id/team/members/:memberId/role` - 更新角色
- `PUT /projects/:id/team/members/:memberId/permissions` - 更新权限
- `DELETE /projects/:id/team/members/:memberId` - 移除成员

## 安全特性

- **角色验证**：所有操作都经过角色权限验证
- **操作审计**：记录所有团队变更操作
- **数据保护**：敏感操作需要确认
- **会话管理**：支持会话超时和强制登出

## 业界标准对比

| 特性 | SmartLTC | Slack | Microsoft Teams | Notion |
|------|----------|-------|-----------------|--------|
| 细粒度权限 | ✅ | ❌ | ✅ | ✅ |
| 批量邀请 | ✅ | ❌ | ✅ | ❌ |
| CSV导入 | ✅ | ❌ | ✅ | ❌ |
| 权限模板 | ✅ | ❌ | ❌ | ✅ |
| 实时统计 | ✅ | ✅ | ✅ | ❌ |

## 扩展性

系统设计支持未来扩展：
- 集成SSO（单点登录）
- 支持LDAP/AD集成
- 移动端适配
- 多租户支持
- 高级审计日志 