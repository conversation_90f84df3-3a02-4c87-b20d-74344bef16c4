// 🚨 紧急数据修复脚本
// 用于恢复丢失的项目数据

console.log('🚨 执行紧急数据修复...');

// 检查当前数据状态
function checkDataStatus() {
    console.log('📊 当前数据状态检查:');
    
    const projects = JSON.parse(localStorage.getItem('edm_projects') || '[]');
    const clients = JSON.parse(localStorage.getItem('clients') || '[]');
    const transactions = JSON.parse(localStorage.getItem('finance_transactions') || '[]');
    const vendors = JSON.parse(localStorage.getItem('vendors') || '[]');
    
    console.log(`- 项目数量: ${projects.length}`);
    console.log(`- 客户数量: ${clients.length}`);
    console.log(`- 交易数量: ${transactions.length}`);
    console.log(`- 供应商数量: ${vendors.length}`);
    
    return {
        projects: projects.length,
        clients: clients.length,
        transactions: transactions.length,
        vendors: vendors.length
    };
}

// 恢复基础数据
function restoreBasicData() {
    console.log('🔧 恢复基础数据...');
    
    // 恢复项目数据
    const sampleProjects = [
        {
            id: 'web-dev-2024',
            name: 'Web Development',
            client: 'Yuan\'t Hot Pot',
            tier: 'S',
            revenue: 1950.00,
            stage: 'Execution',
            category: 'Software',
            country: 'Netherlands',
            probability: 'A',
            owner: 'Mr. Sun',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        },
        {
            id: 'mobile-app-2024',
            name: 'Mobile App Development',
            client: 'Tech Solutions Ltd',
            tier: 'A',
            revenue: 3500.00,
            stage: 'Proposal',
            category: 'Software',
            country: 'Germany',
            probability: 'B',
            owner: 'Development Team',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        }
    ];
    
    // 恢复客户数据
    const sampleClients = [
        {
            id: 'cli-2024-001',
            name: 'Yuan\'t Hot Pot',
            tier: 'SVIP',
            industry: 'Technology',
            country: 'Netherlands',
            status: 'active',
            email: '<EMAIL>',
            phone: '+31-20-1234567',
            totalRevenue: 1950.00,
            contactPerson: 'Mr. Sun',
            contactEmail: '<EMAIL>',
            createdAt: new Date().toISOString(),
            tags: ['Premium', 'Technology', 'Strategic']
        },
        {
            id: 'cli-2024-002',
            name: 'Tech Solutions Ltd',
            tier: 'VIP',
            industry: 'Technology',
            country: 'Germany',
            status: 'potential',
            email: '<EMAIL>',
            phone: '+49-30-9876543',
            totalRevenue: 0,
            contactPerson: 'Development Team',
            contactEmail: '<EMAIL>',
            createdAt: new Date().toISOString(),
            tags: ['Enterprise', 'International']
        }
    ];
    
    // 恢复交易数据 (Finance Transactions)
    const sampleTransactions = [
        {
            id: 'txn-2025-001',
            transactionId: 'TXN-2025-001',
            type: 'income',
            category: 'project_revenue',
            description: 'Web Development - Initial Payment',
            amount: 975.00,
            totalAmount: 975.00,
            paymentMethod: 'bank_transfer',
            status: 'Completed',
            date: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            project: 'web-dev-2024',
            revenueItem: 'NRC-WEB',
            clientName: 'Yuan\'t Hot Pot',
            createdAt: new Date().toISOString()
        },
        {
            id: 'txn-2025-002',
            transactionId: 'TXN-2025-002',
            type: 'income',
            category: 'project_revenue',
            description: 'Web Development - Final Payment',
            amount: 975.00,
            totalAmount: 975.00,
            paymentMethod: 'bank_transfer',
            status: 'Pending',
            date: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            project: 'web-dev-2024',
            revenueItem: 'NRC-WEB',
            clientName: 'Yuan\'t Hot Pot',
            createdAt: new Date().toISOString()
        },
        {
            id: 'txn-2025-003',
            transactionId: 'TXN-2025-003',
            type: 'expense',
            category: 'project_costs',
            description: 'Mobile App Development - Server Costs',
            amount: 200.00,
            totalAmount: 200.00,
            paymentMethod: 'credit_card',
            status: 'Completed',
            date: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            project: 'mobile-app-2024',
            revenueItem: 'Infrastructure',
            clientName: 'Tech Solutions Ltd',
            createdAt: new Date().toISOString()
        }
    ];
    
    // 恢复供应商数据 (空数组但保持结构)
    const sampleVendors = [];
    
    // 保存到localStorage
    try {
        localStorage.setItem('edm_projects', JSON.stringify(sampleProjects));
        console.log('✅ 项目数据已恢复');
        
        localStorage.setItem('clients', JSON.stringify(sampleClients));
        console.log('✅ 客户数据已恢复');
        
        localStorage.setItem('finance_transactions', JSON.stringify(sampleTransactions));
        console.log('✅ 交易数据已恢复');
        
        localStorage.setItem('vendors', JSON.stringify(sampleVendors));
        console.log('✅ 供应商数据结构已恢复');
        
        // 创建项目阶段数据
        const stagesData = {
            'web-dev-2024': {
                basic_info: {
                    data: {
                        clientName: 'Yuan\'t Hot Pot',
                        clientTier: 'S',
                        totalRevenue: 1950.00,
                        winProbability: 90,
                        currentStage: 'Execution'
                    }
                }
            }
        };
        localStorage.setItem('edm_stages', JSON.stringify(stagesData));
        console.log('✅ 项目阶段数据已恢复');
        
        // 创建Revenue Items数据 (NRC和MRC)
        const webDevNrcItems = [
            {
                id: 'nrc-web-2024-001',
                name: 'NRC-WEB',
                description: 'Web Development Service - One-time Setup',
                currency: 'EUR',
                amount: 1950.00,
                vatRate: 21,
                vatAmount: 409.50,
                totalAmount: 2359.50,
                notes: 'Initial web development project for Yuan\'t Hot Pot',
                paymentStatus: 'Partial Payment',
                collectionProgress: 50,  // 会被自动计算更新
                totalReceived: 975.00,   // 会被自动计算更新
                totalPending: 975.00     // 会被自动计算更新
            }
        ];
        
        const mobileAppMrcItems = [
            {
                id: 'mrc-mobile-2024-001',
                name: 'Mobile Support',
                description: 'Monthly mobile app support service',
                unitPrice: 500.00,
                vatRate: 21,
                totalUnitPrice: 605.00,
                startDate: new Date().toISOString(),
                endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1年后
                cycle: 'Monthly',
                totalCycleRevenue: 3500.00,
                received: 0,
                pending: 3500.00,
                status: 'Active',
                paymentStatus: 'Pending',
                collectionProgress: 0,   // 会被自动计算更新
                totalReceived: 0,        // 会被自动计算更新
                totalPending: 3500.00    // 会被自动计算更新
            }
        ];
        
        // 保存Revenue Items到localStorage
        localStorage.setItem('nrc_items_web-dev-2024', JSON.stringify(webDevNrcItems));
        localStorage.setItem('mrc_items_mobile-app-2024', JSON.stringify(mobileAppMrcItems));
        console.log('✅ Revenue Items数据已恢复');
        
        return true;
    } catch (error) {
        console.error('❌ 数据恢复失败:', error);
        return false;
    }
}

// 检查并迁移旧数据
function migrateOldData() {
    console.log('🔄 检查旧数据...');
    
    const oldSources = ['projects', 'ltc_projects', 'dal_projects'];
    let migrated = 0;
    
    const currentProjects = JSON.parse(localStorage.getItem('edm_projects') || '[]');
    
    // 获取删除日志，避免恢复已删除的项目
    const deletedLog = JSON.parse(localStorage.getItem('deleted_projects_log') || '[]');
    const deletedProjectIds = deletedLog.map(log => log.projectId);
    console.log('🚫 检测到已删除项目ID:', deletedProjectIds);
    
    oldSources.forEach(source => {
        try {
            const oldData = JSON.parse(localStorage.getItem(source) || '[]');
            if (Array.isArray(oldData) && oldData.length > 0) {
                console.log(`📦 发现旧数据源: ${source} (${oldData.length} 个项目)`);
                
                oldData.forEach(project => {
                    const projectId = project.id || project.projectId;
                    
                    // 检查项目是否已被用户删除
                    if (deletedProjectIds.includes(projectId)) {
                        console.log(`🚫 跳过已删除项目: ${project.name || projectId}`);
                        return;
                    }
                    
                    // 检查项目是否已存在
                    if (!currentProjects.find(p => p.id === projectId)) {
                        // 标准化旧项目数据
                        const standardProject = {
                            id: projectId || `migrated-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                            name: project.name || project.title || 'Migrated Project',
                            client: project.client || project.clientName || 'Unknown Client',
                            tier: project.tier || 'A',
                            revenue: project.revenue || project.totalRevenue || 0,
                            stage: project.stage || project.currentStage || 'Planning',
                            category: project.category || project.type || 'Other',
                            country: project.country || project.region || 'Unknown',
                            probability: project.probability || 'B',
                            owner: project.owner || project.projectManager || 'Unassigned',
                            createdAt: project.createdAt || new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        };
                        
                        currentProjects.push(standardProject);
                        migrated++;
                    }
                });
            }
        } catch (error) {
            console.warn(`⚠️ 无法迁移 ${source}:`, error);
        }
    });
    
    if (migrated > 0) {
        localStorage.setItem('edm_projects', JSON.stringify(currentProjects));
        console.log(`✅ 成功迁移 ${migrated} 个项目（已排除用户删除的项目）`);
    } else {
        console.log('ℹ️ 未发现需要迁移的旧数据');
    }
    
    return migrated;
}

// 主修复函数
function executeEmergencyFix() {
    console.log('🚨🚨🚨 开始紧急数据修复 🚨🚨🚨');
    
    // 1. 检查当前状态
    const initialStatus = checkDataStatus();
    
    // 2. 尝试迁移旧数据
    const migratedCount = migrateOldData();
    
    // 3. 如果仍然没有数据，创建示例数据
    const statusAfterMigration = checkDataStatus();
    if (statusAfterMigration.projects === 0) {
        console.log('📝 创建示例数据...');
        const restored = restoreBasicData();
        
        if (restored) {
            console.log('🎉 示例数据创建成功！');
        } else {
            console.log('❌ 示例数据创建失败！');
            return false;
        }
    }
    
    // 4. 最终检查
    const finalStatus = checkDataStatus();
    
    console.log('📊 修复完成统计:');
    console.log(`- 修复前: ${initialStatus.projects} 个项目`);
    console.log(`- 迁移了: ${migratedCount} 个项目`);
    console.log(`- 修复后: ${finalStatus.projects} 个项目`);
    console.log(`- 客户数量: ${finalStatus.clients}`);
    console.log(`- 交易数量: ${finalStatus.transactions}`);
    
    if (finalStatus.projects > 0) {
        console.log('🎉 紧急修复完成！请刷新页面查看恢复的数据。');
        
        // 创建备份
        const backup = {
            projects: JSON.parse(localStorage.getItem('edm_projects') || '[]'),
            clients: JSON.parse(localStorage.getItem('clients') || '[]'),
            transactions: JSON.parse(localStorage.getItem('finance_transactions') || '[]'),
            vendors: JSON.parse(localStorage.getItem('vendors') || '[]'),
            stages: JSON.parse(localStorage.getItem('edm_stages') || '{}'),
            timestamp: new Date().toISOString(),
            source: 'emergency_fix'
        };
        
        localStorage.setItem('emergency_backup_' + Date.now(), JSON.stringify(backup));
        console.log('💾 已创建紧急备份');
        
        return true;
    } else {
        console.log('❌ 紧急修复失败，无法恢复数据');
        return false;
    }
}

// 如果在浏览器环境中，仅在特定条件下自动执行修复
if (typeof window !== 'undefined') {
    // 在浏览器控制台中显示帮助信息
    console.log('💡 紧急数据修复工具已加载');
    console.log('💡 如需手动修复数据，在控制台执行: executeEmergencyFix()');
    
    // 🛡️ 只有在真正需要时才自动执行修复
    // 检查是否是首次运行（没有任何localStorage键）
    const allKeys = Object.keys(localStorage);
    const hasAnyProjectData = allKeys.some(key => 
        key.includes('edm_projects') || 
        key.includes('projects') || 
        key.includes('clients') ||
        key.includes('emergency_backup')
    );
    
    // 检查是否有用户删除记录
    const deletedProjects = JSON.parse(localStorage.getItem('deleted_projects_log') || '[]');
    
    // 只有在完全没有任何项目相关数据且不是用户主动清空时才自动修复
    if (!hasAnyProjectData && deletedProjects.length === 0) {
        console.log('🔍 检测到首次使用状态，执行初始数据设置...');
        executeEmergencyFix();
    } else {
        if (deletedProjects.length > 0) {
            console.log('✅ 检测到用户删除记录，尊重用户意愿，跳过自动恢复');
        } else {
            console.log('✅ 检测到现有数据，跳过自动修复以保护用户数据');
        }
    }
}

// 导出函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        executeEmergencyFix,
        checkDataStatus,
        restoreBasicData,
        migrateOldData
    };
}

// 在window对象上添加函数供浏览器控制台使用
if (typeof window !== 'undefined') {
    window.executeEmergencyFix = executeEmergencyFix;
    window.checkDataStatus = checkDataStatus;
    window.restoreBasicData = restoreBasicData;
    window.migrateOldData = migrateOldData;
} 