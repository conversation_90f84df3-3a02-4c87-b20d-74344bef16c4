import React, { useState } from 'react';
import { Tabs } from 'antd';
import RevenueManagement from './RevenueManagement';
import InvoiceManagement from './InvoiceManagement';
import ProfitManagement from './ProfitManagement';
import type { Project } from '../../types';
import '../../styles/table-row-spacing.css';
import '../../styles/ltc-stages.css';

const { TabPane } = Tabs;

interface FinancePageProps {
  project?: Project;
}

const FinancePage: React.FC<FinancePageProps> = ({ project }) => {
  const [activeTab, setActiveTab] = useState('revenue');

  return (
    <div style={{ padding: '24px' }}>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="line"
        className="ltc-tabs"
        style={{ 
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}
        tabBarStyle={{
          margin: '0',
          padding: '16px 24px 0 24px',
          borderBottom: '1px solid #E5E7EB'
        }}
      >
        <TabPane 
          tab="Revenue Management" 
          key="revenue"
        >
          <RevenueManagement project={project} />
        </TabPane>
        
        <TabPane 
          tab="Invoice Management" 
          key="invoice"
        >
          <InvoiceManagement />
        </TabPane>
        
        <TabPane 
          tab="Profit Management" 
          key="profit"
        >
          <ProfitManagement />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default FinancePage;
