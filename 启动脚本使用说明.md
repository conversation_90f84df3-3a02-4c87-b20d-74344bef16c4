# LTC项目管理系统 - 新版启动脚本使用说明

## 🚀 新版启动脚本优势

新版启动脚本解决了以下问题：
- ✅ 修复了 `concurrently` 包缺失的问题
- ✅ 自动处理端口冲突（3000、5002端口）
- ✅ 智能检测服务启动状态
- ✅ 提供详细的启动过程反馈
- ✅ 支持中文显示（UTF-8编码）
- ✅ 自动打开浏览器访问应用

## 📁 文件列表

### Windows批处理版本
- `start-ltc-new.bat` - 启动脚本
- `stop-ltc-new.bat` - 停止脚本

### PowerShell版本  
- `start-ltc-new.ps1` - 启动脚本
- `stop-ltc-new.ps1` - 停止脚本

## 🔧 使用方法

### 方法一：使用批处理脚本（推荐）

1. **启动项目**
   ```
   双击 start-ltc-new.bat
   ```

2. **停止项目**
   ```
   双击 stop-ltc-new.bat
   ```

### 方法二：使用PowerShell脚本

1. **启动项目**
   ```
   右键点击 start-ltc-new.ps1 → "使用PowerShell运行"
   ```

2. **停止项目**
   ```
   右键点击 stop-ltc-new.ps1 → "使用PowerShell运行"
   ```

**注意**: 如果PowerShell执行策略限制，请以管理员身份运行PowerShell并执行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope CurrentUser
```

## 🔍 启动脚本功能

### 启动脚本会自动：
1. 检查Node.js和npm版本
2. 停止现有的Node.js进程（避免端口冲突）
3. 检查并释放3000和5002端口
4. 启动后端服务（端口5002）
5. 等待后端启动完成
6. 启动前端服务（端口3000）
7. 等待前端启动完成
8. 自动打开浏览器访问 http://localhost:3000

### 停止脚本会自动：
1. 显示当前运行的Node.js进程
2. 停止占用3000端口的进程（前端）
3. 停止占用5002端口的进程（后端）
4. 询问是否停止所有Node.js进程
5. 验证端口释放状态

## 📊 服务信息

- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:5002
- **登录邮箱**: <EMAIL>
- **登录密码**: admin123

## ⚠️ 常见问题

### 问题1：端口被占用
**现象**: 启动时提示端口3000或5002被占用
**解决**: 新版脚本会自动处理端口冲突，无需手动干预

### 问题2：Node.js未安装
**现象**: 提示"未找到Node.js"
**解决**: 请先安装Node.js (推荐版本: 14.x或更高)

### 问题3：服务启动失败
**现象**: 脚本显示警告"服务可能未正常启动"
**解决**: 
1. 检查dependencies是否已安装
2. 运行 `npm install` 在frontend和backend目录
3. 检查控制台错误信息

### 问题4：浏览器未自动打开
**现象**: 脚本完成但浏览器未打开
**解决**: 手动访问 http://localhost:3000

## 🆚 新旧版本对比

| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 端口冲突处理 | ❌ 需手动处理 | ✅ 自动处理 |
| 服务状态检测 | ❌ 无检测 | ✅ 智能检测 |
| 中文显示 | ❌ 乱码 | ✅ 正常显示 |
| 错误处理 | ❌ 基础 | ✅ 完善 |
| 启动反馈 | ❌ 简单 | ✅ 详细 |
| concurrently依赖 | ❌ 需要安装 | ✅ 不依赖 |

## 💡 使用建议

1. **推荐使用批处理版本**，兼容性更好
2. **首次使用前**确保已安装所有npm依赖
3. **遇到问题时**先运行停止脚本，再重新启动
4. **开发时**可以保持脚本窗口打开，方便查看日志

## 🔄 升级说明

如果你之前使用旧版启动脚本，建议：
1. 备份旧版脚本（如需要）
2. 使用新版脚本启动项目
3. 如果一切正常，可以删除旧版脚本

新版脚本完全独立，不会影响旧版脚本的使用。 