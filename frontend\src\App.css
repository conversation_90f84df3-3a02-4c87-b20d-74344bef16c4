/* 现代简约风格全局样式 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 导入统一样式 */
@import './styles/unified-components.css';
@import './styles/unified-forms.css';
@import './styles/unified-cards-tables.css';
@import './styles/custom-slider.css';
@import './styles/font-scale.css';
@import './styles/ltc-stages.css';

/*
  注意：CSS变量现在由ThemeContext组件动态设置
  这里保留一些默认值，以防ThemeContext未加载
*/
:root {
  /* 主色调 - 橙色，透明度30% */
  --primary: #FF7A00;
  --primary-light: rgba(255, 122, 0, 0.3);
  --secondary: #10B981;

  /* 状态颜色 */
  --success: #10B981;
  --warning: #F59E0B;
  --danger: #EF4444;
  --info: #3B82F6;

  /* 文本颜色 */
  --text-primary: #1F2023;
  --text-secondary: #4B5563;
  --text-light: #9CA3AF;

  /* 背景颜色 */
  --background: #F9FAFB;
  --card-background: #FFFFFF;

  /* 边框颜色 */
  --border: #E5E7EB;
  --border-focus: #FF7A00;

  /* 灰度 */
  --gray-light: #F3F4F6;
  --gray: #9CA3AF;
  --gray-dark: #4B5563;

  /* 悬停效果 */
  --hover: rgba(255, 122, 0, 0.1);

  /* 阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;

  /* 过渡 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.25s ease-in-out;
  --transition-slow: 0.35s ease-in-out;

  /* 字体大小缩小30% (原1.1倍改为0.77倍) */
  --font-size-xs: calc(12px * 0.77);
  --font-size-sm: calc(14px * 0.77);
  --font-size-md: calc(16px * 0.77);
  --font-size-lg: calc(18px * 0.77);
  --font-size-xl: calc(20px * 0.77);
  --font-size-xxl: calc(24px * 0.77);

  /* 行高减少到70% */
  --line-height: 0.7;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-dark);
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background-color: var(--background) !important;
}

.ant-card {
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-sm) !important;
  border: none !important;
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.ant-card:hover {
  box-shadow: var(--shadow-md) !important;
  transform: translateY(-2px);
}

.ant-btn {
  border-radius: var(--radius-sm) !important;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.ant-btn-primary {
  background-color: var(--primary) !important;
  border-color: var(--primary) !important;
}

.ant-btn-primary:hover {
  background-color: var(--primary) !important;
  filter: brightness(1.1);
}

.ant-table {
  border-radius: var(--radius-md) !important;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: var(--gray-light) !important;
  color: var(--text-secondary) !important;
  font-weight: 600;
}

.ant-statistic-title {
  color: var(--text-secondary) !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
}

.ant-statistic-content {
  font-size: 28px !important;
  font-weight: 600 !important;
}
