import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Row, 
  Col, 
  Button, 
  message, 
  Alert,
  Divider
} from 'antd';
import { SaveOutlined } from '@ant-design/icons';

const { Option } = Select;

interface BasicInfoTestProps {
  project?: any;
  stage?: any;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
  id?: string;
}

const BasicInfoTest: React.FC<BasicInfoTestProps> = ({ project, stage, onSave, onProceed, id }) => {
  const [testForm] = Form.useForm();
  const [debugInfo, setDebugInfo] = useState('');

  useEffect(() => {
    console.log('🧪 BASIC INFO TEST COMPONENT LOADED');
    setDebugInfo('Test component loaded successfully');
    
    // 强制CSS覆盖
    const style = document.createElement('style');
    style.id = 'force-enable-test-style';
    style.textContent = `
      .test-input {
        background-color: white !important;
        color: black !important;
        pointer-events: auto !important;
        cursor: text !important;
        border: 1px solid #d9d9d9 !important;
      }
      .test-input:disabled {
        background-color: white !important;
        color: black !important;
        pointer-events: auto !important;
        cursor: text !important;
        opacity: 1 !important;
      }
    `;
    document.head.appendChild(style);
  }, []);

  const handleSubmit = (values: any) => {
    console.log('✅ Form submitted:', values);
    message.success('Form submitted successfully!');
    setDebugInfo(JSON.stringify(values, null, 2));
    if (onSave) onSave(values);
  };

  const forceEnable = () => {
    const allInputs = document.querySelectorAll('input, textarea, select');
    allInputs.forEach((input: any) => {
      input.disabled = false;
      input.readOnly = false;
      input.removeAttribute('disabled');
      input.removeAttribute('readonly');
      input.style.backgroundColor = 'white';
      input.style.color = 'black';
      input.style.pointerEvents = 'auto';
      input.style.cursor = 'text';
    });
    message.info('All inputs force enabled!');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Alert
        message="🧪 BASIC INFO TEST COMPONENT"
        description="Simplified test version to isolate editing issues"
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      <Card title="🔧 Controls" style={{ marginBottom: '16px' }}>
        <Button type="primary" onClick={forceEnable} style={{ marginRight: '8px' }}>
          🔧 Force Enable All
        </Button>
        <Button onClick={() => testForm.resetFields()}>
          🔄 Reset
        </Button>
        <div style={{ marginTop: '8px', background: '#f5f5f5', padding: '8px', fontSize: '12px' }}>
          <pre>{debugInfo}</pre>
        </div>
      </Card>

      <Card title="📝 Test Form">
        <Form form={testForm} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Client Name" name="clientName">
                <Input
                  className="test-input"
                  placeholder="Type client name..."
                  onChange={(e) => setDebugInfo(`Client Name: ${e.target.value}`)}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Address (Problem Field)" name="address">
                <Input
                  className="test-input"
                  placeholder="Type address..."
                  onChange={(e) => setDebugInfo(`Address: ${e.target.value}`)}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Key Stakeholder (Problem Field)" name="keyStakeholder">
                <Input
                  className="test-input"
                  placeholder="Type stakeholder..."
                  onChange={(e) => setDebugInfo(`Stakeholder: ${e.target.value}`)}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Contact Phone (Problem Field)" name="contactPhone">
                <Input
                  className="test-input"
                  placeholder="Type phone..."
                  onChange={(e) => setDebugInfo(`Phone: ${e.target.value}`)}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Client Tier" name="clientTier">
                <Select
                  placeholder="Select tier..."
                  onChange={(value) => setDebugInfo(`Tier: ${value}`)}
                >
                  <Option value="SVIP">SVIP</Option>
                  <Option value="VIP">VIP</Option>
                  <Option value="BA">BA</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Email" name="email">
                <Input
                  className="test-input"
                  placeholder="Type email..."
                  onChange={(e) => setDebugInfo(`Email: ${e.target.value}`)}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            Submit Test Form
          </Button>
        </Form>
      </Card>

      <Card title="🔧 Native HTML Test" style={{ marginTop: '16px' }}>
        <input
          type="text"
          placeholder="Native HTML input test..."
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            marginBottom: '8px'
          }}
          onChange={(e) => setDebugInfo(`Native: ${e.target.value}`)}
        />
      </Card>
    </div>
  );
};

export default BasicInfoTest; 