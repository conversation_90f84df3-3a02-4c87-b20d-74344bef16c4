/* 专门消除表格间隙的CSS - 精简版 */

/* 消除表头和表体间隙，但保持正常的列宽 */
.project-list-container .ant-table-wrapper {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table {
  margin: 0 !important;
  border-spacing: 0 !important;
  border-collapse: collapse !important;
}

.project-list-container .ant-table-container {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-header {
  margin: 0 !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  border-bottom: none !important;
}

.project-list-container .ant-table-body {
  margin: 0 !important;
  padding: 0 !important;
  margin-top: 0 !important;
  border-top: none !important;
  padding-top: 0 !important;
}

.project-list-container .ant-table-content {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-thead {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-tbody {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-thead > tr {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-tbody > tr {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-thead > tr > th {
  margin: 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.project-list-container .ant-table-tbody > tr > td {
  margin: 0 !important;
  border-top: none !important;
}

/* 强制第一行无间隙 */
.project-list-container .ant-table-tbody > tr:first-child {
  margin-top: 0 !important;
  border-top: none !important;
  padding-top: 0 !important;
}

.project-list-container .ant-table-tbody > tr:first-child > td {
  margin-top: 0 !important;
  border-top: none !important;
}

/* 消除可能的伪元素间隙 */
.project-list-container .ant-table-header::before,
.project-list-container .ant-table-header::after,
.project-list-container .ant-table-body::before,
.project-list-container .ant-table-body::after,
.project-list-container .ant-table-thead::before,
.project-list-container .ant-table-thead::after,
.project-list-container .ant-table-tbody::before,
.project-list-container .ant-table-tbody::after {
  display: none !important;
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
}

/* 特殊处理表头和表体的连接 */
.project-list-container .ant-table-header + .ant-table-body,
.project-list-container .ant-table-thead + .ant-table-tbody {
  margin-top: 0 !important;
  border-top: none !important;
  padding-top: 0 !important;
}

/* 重置所有表格元素的box-sizing */
.project-list-container .ant-table *,
.project-list-container .ant-table *::before,
.project-list-container .ant-table *::after {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: inherit !important;
}

/* 重新设置单元格的padding */
.project-list-container .ant-table-thead > tr > th {
  padding: 12px 16px !important;
}

.project-list-container .ant-table-tbody > tr > td {
  padding: 8px 16px !important;
} 