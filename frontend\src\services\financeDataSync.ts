import type { Transaction, SalesInvoice, RevenueItem, Project, FinanceSyncResult } from '../types';

/**
 * 财务数据同步服务
 * 实现按照用户要求的数据流架构：
 * 1. 创建 Sales Invoice → 生成 Invoice Number
 * 2. 用户在 Transaction 生成一条 Income 记录
 * 3. Sales Invoice 更新状态-已支付
 * 4. Revenue Management 收入进度更新
 */
class FinanceDataSyncService {
  private static instance: FinanceDataSyncService;
  private readonly STORAGE_KEY_TRANSACTIONS = 'finance_transactions';
  private readonly STORAGE_KEY_SALES_INVOICES = 'sales_invoices';
  private readonly STORAGE_KEY_REVENUE_ITEMS = 'revenue_items';
  private readonly STORAGE_KEY_PROJECTS = 'edm_projects';

  private constructor() {}

  public static getInstance(): FinanceDataSyncService {
    if (!FinanceDataSyncService.instance) {
      FinanceDataSyncService.instance = new FinanceDataSyncService();
    }
    return FinanceDataSyncService.instance;
  }

  // ============= Step 1: Sales Invoice Management =============
  
  /**
   * 生成新的发票号码
   */
  public generateInvoiceNumber(): string {
    const currentYear = new Date().getFullYear();
    const allInvoices = this.getAllSalesInvoices();
    const currentYearInvoices = allInvoices.filter(invoice => 
      invoice.invoiceNumber.startsWith(`INV-${currentYear}`)
    );
    const nextNumber = currentYearInvoices.length + 1;
    return `INV-${currentYear}-${String(nextNumber).padStart(3, '0')}`;
  }

  /**
   * 创建销售发票 - 工作流第一步
   */
  public async createSalesInvoice(invoiceData: Partial<SalesInvoice>): Promise<SalesInvoice> {
    try {
      const invoices = this.getAllSalesInvoices();
      
      // 生成发票号码（如果没有提供）
      if (!invoiceData.invoiceNumber) {
        invoiceData.invoiceNumber = this.generateInvoiceNumber();
      }

      const totalAmount = invoiceData.totalAmount || ((invoiceData.amount || 0) * (1 + (invoiceData.vatRate || 21) / 100));
      
      const newInvoice: SalesInvoice = {
        id: `si_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        invoiceNumber: invoiceData.invoiceNumber,
        projectId: invoiceData.projectId || '',
        revenueItem: invoiceData.revenueItem || '',
        amount: invoiceData.amount || 0,
        vatRate: invoiceData.vatRate || 21,
        vatAmount: invoiceData.vatAmount || ((invoiceData.amount || 0) * (invoiceData.vatRate || 21) / 100),
        totalAmount: totalAmount,
        invoiceDate: invoiceData.invoiceDate || new Date().toISOString().split('T')[0],
        dueDate: invoiceData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'draft', // 新创建的发票默认为草稿状态
        paymentStatus: 'unpaid', // 新创建的发票默认未支付
        paidAmount: 0,
        pendingAmount: totalAmount, // 初始待回款金额等于总金额
        collectionProgress: 0, // 初始回款进度为0%
        description: invoiceData.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      invoices.push(newInvoice);
      this.saveToStorage(this.STORAGE_KEY_SALES_INVOICES, invoices);

      console.log('✅ Sales Invoice created:', newInvoice.invoiceNumber);
      return newInvoice;
    } catch (error) {
      console.error('❌ Failed to create sales invoice:', error);
      throw error;
    }
  }

  // ============= Step 2 & 3: Transaction Management and Invoice Status Update =============

  /**
   * 根据Transaction更新发票支付状态 - 工作流第三步
   * 优化版本：支持多种Invoice Number关联方式和多种数据源
   */
  public async updateInvoicePaymentStatusFromTransaction(transactionId: string): Promise<FinanceSyncResult> {
    try {
      const transactions = this.getAllTransactions();
      const transaction = transactions.find(t => t.id === transactionId);
      
      if (!transaction || transaction.type !== 'income') {
        console.warn('❌ Transaction not found or not an income transaction:', transactionId);
        throw new Error('Transaction not found or not an income transaction');
      }

      console.log('🔍 Looking for invoice related to transaction:', {
        transactionId,
        description: transaction.description,
        tags: transaction.tags,
        invoiceNumber: (transaction as any).invoiceNumber,
        revenueItem: transaction.revenueItem
      });

      // 🎯 多数据源查找对应的发票
      let invoice: any;
      let invoiceDataSource: 'sales_invoices' | 'finance_invoices' | null = null;
      
      // 数据源1: 查找sales_invoices (SalesInvoice格式)
      const salesInvoices = this.getAllSalesInvoices();
      invoice = this.findMatchingInvoice(salesInvoices, transaction);
      if (invoice) {
        invoiceDataSource = 'sales_invoices';
        console.log('✅ Found invoice in sales_invoices:', invoice.invoiceNumber || invoice.number);
      }
      
      // 数据源2: 查找finance_invoices (普通Invoice格式)
      if (!invoice) {
        const financeInvoices = this.getFinanceInvoices();
        invoice = this.findMatchingInvoice(financeInvoices, transaction);
        if (invoice) {
          invoiceDataSource = 'finance_invoices';
          console.log('✅ Found invoice in finance_invoices:', invoice.number);
        }
      }
      
      if (!invoice) {
        console.warn('⚠️ No corresponding invoice found for transaction:', {
          transactionId,
          description: transaction.description,
          tags: transaction.tags,
          invoiceNumber: (transaction as any).invoiceNumber,
          revenueItem: transaction.revenueItem,
          searchedSources: ['sales_invoices', 'finance_invoices']
        });
        return {
          success: false,
          updated: 0,
          total: 0,
          errors: ['Corresponding invoice not found in any data source'],
          syncedAt: new Date().toISOString()
        };
      }

      // 💰 更新发票支付状态 - 统一处理不同格式
      const updatedInvoice = this.updateInvoicePaymentStatus(invoice, transaction, invoiceDataSource!);
      
      // 🔄 保存更新的发票到对应数据源
      this.saveUpdatedInvoice(updatedInvoice, invoiceDataSource!);

      // 🔄 工作流第四步：更新Revenue Management
      if (transaction.projectId || (transaction as any).project) {
        const projectId = transaction.projectId || (transaction as any).project;
        await this.syncProjectRevenueProgress(projectId);
      }

      console.log('✅ Invoice payment status updated:', {
        invoiceNumber: updatedInvoice.invoiceNumber || updatedInvoice.number,
        paymentStatus: updatedInvoice.paymentStatus || updatedInvoice.status,
        dataSource: invoiceDataSource,
        paidAmount: updatedInvoice.paidAmount,
        collectionProgress: updatedInvoice.collectionProgress
      });

      return {
        success: true,
        updated: 1,
        total: 1,
        errors: [],
        syncedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to update invoice payment status:', error);
      return {
        success: false,
        updated: 0,
        total: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        syncedAt: new Date().toISOString()
      };
    }
  }

  // 🔍 查找匹配的发票（统一匹配逻辑）
  private findMatchingInvoice(invoices: any[], transaction: any): any | null {
    console.log(`🔍 Searching ${invoices.length} invoices for transaction...`);
    
    // 方式1: 通过Transaction的invoiceNumber字段直接匹配
    if ((transaction as any).invoiceNumber) {
      const invoice = invoices.find(inv => {
        const invNumber = inv.invoiceNumber || inv.number;
        return invNumber === (transaction as any).invoiceNumber;
      });
      if (invoice) {
        console.log('✅ Found by invoiceNumber direct match:', (transaction as any).invoiceNumber);
        return invoice;
      }
    }
    
    // 方式2: 通过Invoice Number在description中匹配
    if (transaction.description) {
      const invoice = invoices.find(inv => {
        const invNumber = inv.invoiceNumber || inv.number;
        return invNumber && transaction.description.includes(invNumber);
      });
      if (invoice) {
        console.log('✅ Found by description match:', invoice.invoiceNumber || invoice.number);
        return invoice;
      }
    }
    
    // 方式3: 通过Invoice Number在tags中匹配
    if (transaction.tags && Array.isArray(transaction.tags)) {
      const invoice = invoices.find(inv => {
        const invNumber = inv.invoiceNumber || inv.number;
        return invNumber && transaction.tags.includes(invNumber);
      });
      if (invoice) {
        console.log('✅ Found by tags match:', invoice.invoiceNumber || invoice.number);
        return invoice;
      }
    }
    
    // 方式4: 通过Revenue Item + 金额匹配（备用方案）
    if (transaction.revenueItem) {
      const invoice = invoices.find(inv => {
        const revenueMatch = inv.revenueItem === transaction.revenueItem;
        const amountMatch = Math.abs((inv.totalAmount || 0) - (transaction.totalAmount || 0)) < 0.01;
        return revenueMatch && amountMatch;
      });
      if (invoice) {
        console.log('✅ Found by revenueItem + amount match:', invoice.invoiceNumber || invoice.number);
        return invoice;
      }
    }
    
    console.log('❌ No matching invoice found');
    return null;
  }

  // 💰 更新发票支付状态（统一处理逻辑）
  private updateInvoicePaymentStatus(invoice: any, transaction: any, dataSource: string): any {
    const updatedInvoice = { ...invoice };
    
    // 增加已付金额
    updatedInvoice.paidAmount = (invoice.paidAmount || 0) + (transaction.totalAmount || 0);
    updatedInvoice.updatedAt = new Date().toISOString();
    
    // 重新计算相关字段
    const totalAmount = invoice.totalAmount || 0;
    updatedInvoice.pendingAmount = Math.max(0, totalAmount - updatedInvoice.paidAmount);
    updatedInvoice.collectionProgress = totalAmount > 0 ? 
      Math.min((updatedInvoice.paidAmount / totalAmount) * 100, 100) : 0;

    // 计算支付状态 - 适配不同数据格式
    if (updatedInvoice.paidAmount >= totalAmount) {
      updatedInvoice.paymentStatus = 'paid';
      updatedInvoice.status = dataSource === 'finance_invoices' ? 'Paid' : 'paid';
    } else if (updatedInvoice.paidAmount > 0) {
      updatedInvoice.paymentStatus = 'partial';
      updatedInvoice.status = dataSource === 'finance_invoices' ? 'Partial' : 'sent';
    } else {
      updatedInvoice.paymentStatus = 'unpaid';
      updatedInvoice.status = dataSource === 'finance_invoices' ? 'Pending' : 'draft';
    }
    
    return updatedInvoice;
  }

  // 💾 保存更新的发票到对应数据源
  private saveUpdatedInvoice(updatedInvoice: any, dataSource: 'sales_invoices' | 'finance_invoices'): void {
    if (dataSource === 'sales_invoices') {
      const invoiceList = this.getAllSalesInvoices();
      const invoiceIndex = invoiceList.findIndex(inv => inv.id === updatedInvoice.id);
      if (invoiceIndex !== -1) {
        invoiceList[invoiceIndex] = updatedInvoice;
        this.saveToStorage(this.STORAGE_KEY_SALES_INVOICES, invoiceList);
      }
    } else if (dataSource === 'finance_invoices') {
      const invoiceList = this.getFinanceInvoices();
      const invoiceIndex = invoiceList.findIndex(inv => inv.id === updatedInvoice.id);
      if (invoiceIndex !== -1) {
        invoiceList[invoiceIndex] = updatedInvoice;
        this.saveToStorage('finance_invoices', invoiceList);
      }
    }
  }

  // 📊 获取finance_invoices数据
  private getFinanceInvoices(): any[] {
    try {
      const data = localStorage.getItem('finance_invoices');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load finance_invoices:', error);
      return [];
    }
  }

  // ============= Step 4: Revenue Management Sync =============
  
  /**
   * 同步项目收入进度 - 基于Sales Invoice支付状态
   */
  public async syncProjectRevenueProgress(projectId: string): Promise<FinanceSyncResult> {
    try {
      console.log('🔄 Syncing revenue progress for project:', projectId);
      
      // 获取项目的所有发票
      const allInvoices = this.getAllSalesInvoices();
      const projectInvoices = allInvoices.filter(inv => inv.projectId === projectId);
      
      // 获取项目的所有Income Transaction
      const allTransactions = this.getAllTransactions();
      const projectIncomeTransactions = allTransactions.filter(t => 
        t.projectId === projectId && 
        t.type === 'income' && 
        t.status === 'completed'
      );
      
      // 按Revenue Item分组统计
      const revenueItemStats = new Map<string, {
        invoicedAmount: number;
        receivedAmount: number;
        progress: number;
      }>();

      // 统计发票数据
      projectInvoices.forEach(invoice => {
        const current = revenueItemStats.get(invoice.revenueItem) || {
          invoicedAmount: 0,
          receivedAmount: 0,
          progress: 0
        };
        
        current.invoicedAmount += invoice.totalAmount;
        current.receivedAmount += invoice.paidAmount || 0;
        
        revenueItemStats.set(invoice.revenueItem, current);
      });

      // 统计Transaction数据（补充收款信息）
      projectIncomeTransactions.forEach(transaction => {
        if (transaction.revenueItem) {
          const current = revenueItemStats.get(transaction.revenueItem) || {
            invoicedAmount: 0,
            receivedAmount: 0,
            progress: 0
          };
          
          // 只有在没有对应发票的情况下才添加Transaction金额
          const hasCorrespondingInvoice = projectInvoices.some(inv => 
            inv.revenueItem === transaction.revenueItem && 
            inv.paidAmount && inv.paidAmount > 0
          );
          
          if (!hasCorrespondingInvoice) {
            current.receivedAmount += transaction.amount;
          }
          
          revenueItemStats.set(transaction.revenueItem, current);
        }
      });

      // 计算并更新进度
      const updatedItems: string[] = [];
      revenueItemStats.forEach((stats, revenueItem) => {
        stats.progress = stats.invoicedAmount > 0 ? 
          Math.min((stats.receivedAmount / stats.invoicedAmount) * 100, 100) : 0;
        updatedItems.push(revenueItem);
      });

      console.log('✅ Revenue progress sync completed:', {
        projectId,
        updatedItems: updatedItems.length,
        revenueItemStats: Object.fromEntries(revenueItemStats)
      });

      return {
        success: true,
        updated: updatedItems.length,
        total: revenueItemStats.size,
        errors: [],
        syncedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to sync revenue progress:', error);
      return {
        success: false,
        updated: 0,
        total: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        syncedAt: new Date().toISOString()
      };
    }
  }

  // ============= CRUD Operations =============

  public getAllSalesInvoices(): SalesInvoice[] {
    try {
      return JSON.parse(localStorage.getItem(this.STORAGE_KEY_SALES_INVOICES) || '[]');
    } catch (error) {
      console.error('Failed to load sales invoices:', error);
      return [];
    }
  }

  public getSalesInvoiceById(id: string): SalesInvoice | null {
    const invoices = this.getAllSalesInvoices();
    return invoices.find(invoice => invoice.id === id) || null;
  }

  public async updateSalesInvoice(id: string, updateData: Partial<SalesInvoice>): Promise<SalesInvoice> {
    try {
      const invoices = this.getAllSalesInvoices();
      const index = invoices.findIndex(invoice => invoice.id === id);
      
      if (index === -1) {
        throw new Error('Sales invoice not found');
      }

      const updatedInvoice = {
        ...invoices[index],
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      invoices[index] = updatedInvoice;
      this.saveToStorage(this.STORAGE_KEY_SALES_INVOICES, invoices);

      console.log('✅ Sales invoice updated:', updatedInvoice.invoiceNumber);
      return updatedInvoice;
    } catch (error) {
      console.error('❌ Failed to update sales invoice:', error);
      throw error;
    }
  }

  public async deleteSalesInvoice(id: string): Promise<void> {
    try {
      const invoices = this.getAllSalesInvoices();
      const filteredInvoices = invoices.filter(invoice => invoice.id !== id);
      this.saveToStorage(this.STORAGE_KEY_SALES_INVOICES, filteredInvoices);
      console.log('✅ Sales invoice deleted:', id);
    } catch (error) {
      console.error('❌ Failed to delete sales invoice:', error);
      throw error;
    }
  }

  private getAllTransactions(): Transaction[] {
    try {
      return JSON.parse(localStorage.getItem(this.STORAGE_KEY_TRANSACTIONS) || '[]');
    } catch (error) {
      console.error('Failed to load transactions:', error);
      return [];
    }
  }

  // ============= Helper Methods =============

  private saveToStorage(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error(`Failed to save to localStorage (${key}):`, error);
    }
  }

  // ============= Integration with existing Revenue Management =============
  
  /**
   * 集成现有Revenue Management的同步逻辑
   */
  public async syncWithExistingRevenueManagement(projectId: string): Promise<void> {
    try {
      console.log('✅ Revenue Management sync will use the syncProjectRevenueProgress method instead');
      await this.syncProjectRevenueProgress(projectId);
    } catch (error) {
      console.error('❌ Failed to sync with existing Revenue Management:', error);
    }
  }
}

export { FinanceDataSyncService };
export default FinanceDataSyncService.getInstance(); 