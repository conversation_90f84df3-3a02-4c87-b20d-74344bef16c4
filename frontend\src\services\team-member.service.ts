import api from './api';
import { TeamMember } from '../types';

// 本地存储键
const STORAGE_KEY = 'team_members_global';

class TeamMemberService {
  private teamMembers: TeamMember[] = [];

  constructor() {
    this.loadFromStorage();
  }

  // 从localStorage加载数据
  private loadFromStorage() {
    try {
      const data = localStorage.getItem(STORAGE_KEY);
      if (data) {
        this.teamMembers = JSON.parse(data);
      }
    } catch (error) {
      console.error('Error loading team members from localStorage:', error);
      this.teamMembers = [];
    }
  }

  // 保存到localStorage
  private saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.teamMembers));
    } catch (error) {
      console.error('Error saving team members to localStorage:', error);
    }
  }

  async getAllTeamMembers(): Promise<TeamMember[]> {
    try {
    const response = await api.get<TeamMember[]>('/team-members');
    return response;
    } catch (error) {
      console.error('Failed to fetch team members from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      return this.teamMembers;
    }
  }

  async getTeamMemberById(id: string): Promise<TeamMember> {
    try {
    const response = await api.get<TeamMember>(`/team-members/${id}`);
    return response;
    } catch (error) {
      console.error('Failed to fetch team member from API:', error);
      // 从localStorage获取数据
      this.loadFromStorage();
      const member = this.teamMembers.find(m => m.id === id);
      if (member) {
        return member;
      }
      throw new Error('Team member not found');
    }
  }

  async createTeamMember(teamMember: Partial<TeamMember>): Promise<TeamMember> {
    try {
    const response = await api.post<TeamMember>('/team-members', teamMember);
    return response;
    } catch (error) {
      console.error('Failed to create team member via API:', error);
      // 本地创建团队成员
      const newMember: TeamMember = {
        id: `tm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: teamMember.userId || `user_${Date.now()}`,
        projectId: teamMember.projectId || '',
        email: teamMember.email || '',
        name: teamMember.name || '',
        avatar: teamMember.avatar,
        role: teamMember.role || 'member' as any,
        status: teamMember.status || 'active' as any,
        invitedBy: teamMember.invitedBy || 'current_user',
        invitedAt: teamMember.invitedAt || new Date().toISOString(),
        joinedAt: teamMember.joinedAt,
        lastActiveAt: teamMember.lastActiveAt,
        position: teamMember.position,
        department: teamMember.department,
        phone: teamMember.phone,
        bio: teamMember.bio,
        createdAt: teamMember.createdAt || new Date().toISOString(),
        permissions: teamMember.permissions || {
          canViewProject: true,
          canEditProject: false,
          canDeleteProject: false,
          canManageTeam: false,
          canManageFinance: false,
          canExportData: false,
          canViewReports: true,
        }
      };
      
      this.teamMembers.push(newMember);
      this.saveToStorage();
      
      return newMember;
    }
  }

  async updateTeamMember(id: string, teamMember: Partial<TeamMember>): Promise<TeamMember> {
    try {
    const response = await api.put<TeamMember>(`/team-members/${id}`, teamMember);
    return response;
    } catch (error) {
      console.error('Failed to update team member via API:', error);
      // 本地更新团队成员
      const memberIndex = this.teamMembers.findIndex(m => m.id === id);
      if (memberIndex !== -1) {
        this.teamMembers[memberIndex] = {
          ...this.teamMembers[memberIndex],
          ...teamMember
        } as TeamMember;
        // 添加updatedAt字段
        (this.teamMembers[memberIndex] as any).updatedAt = new Date().toISOString();
        this.saveToStorage();
        return this.teamMembers[memberIndex];
      }
      throw new Error('Team member not found');
    }
  }

  async deleteTeamMember(id: string): Promise<void> {
    try {
    await api.delete(`/team-members/${id}`);
    } catch (error) {
      console.error('Failed to delete team member via API:', error);
      // 本地删除团队成员
      this.teamMembers = this.teamMembers.filter(m => m.id !== id);
      this.saveToStorage();
    }
  }

  async getTeamMemberProjects(id: string): Promise<any[]> {
    try {
    const response = await api.get(`/team-members/${id}/projects`);
    return response;
    } catch (error) {
      console.error('Failed to fetch team member projects via API:', error);
      // 从localStorage获取项目信息
      const member = this.teamMembers.find(m => m.id === id);
      if (member && member.projectId) {
        // 返回项目ID作为项目信息
        return [{ id: member.projectId, name: `Project ${member.projectId}` }];
      }
      return [];
    }
  }

  async addTeamMemberToProject(teamMemberId: string, projectId: string): Promise<void> {
    try {
    await api.post(`/team-members/${teamMemberId}/projects/${projectId}`);
    } catch (error) {
      console.error('Failed to add team member to project via API:', error);
      // 本地更新团队成员的项目ID
      const member = this.teamMembers.find(m => m.id === teamMemberId);
      if (member) {
        member.projectId = projectId;
        this.saveToStorage();
      }
    }
  }

  async removeTeamMemberFromProject(teamMemberId: string, projectId: string): Promise<void> {
    try {
    await api.delete(`/team-members/${teamMemberId}/projects/${projectId}`);
    } catch (error) {
      console.error('Failed to remove team member from project via API:', error);
      // 本地移除团队成员的项目关联
      const member = this.teamMembers.find(m => m.id === teamMemberId);
      if (member && member.projectId === projectId) {
        member.projectId = '';
        this.saveToStorage();
      }
    }
  }
}

export default new TeamMemberService();
