/**
 * 安全的本地存储工具类
 * 防止 "undefined" is not valid JSON 错误
 */
export class StorageUtils {
  /**
   * 安全获取localStorage值
   */
  static safeGetItem(key: string): string | null {
    try {
      const value = localStorage.getItem(key);
      if (value === 'undefined' || value === 'null') {
        localStorage.removeItem(key);
        return null;
      }
      return value;
    } catch (error) {
      console.warn(`Failed to get localStorage item "${key}":`, error);
      return null;
    }
  }

  /**
   * 安全设置localStorage值
   */
  static safeSetItem(key: string, value: string): boolean {
    try {
      if (value === 'undefined' || value === 'null') {
        localStorage.removeItem(key);
        return false;
      }
      localStorage.setItem(key, value);
      return true;
    } catch (error) {
      console.warn(`Failed to set localStorage item "${key}":`, error);
      return false;
    }
  }

  /**
   * 安全解析JSON
   */
  static safeJSONParse<T = any>(data: string | null, defaultValue: T | null = null): T | null {
    if (!data || data === 'undefined' || data === 'null') {
      return defaultValue;
    }

    try {
      return JSON.parse(data) as T;
    } catch (error) {
      console.warn('JSON parsing failed:', error, 'Raw data:', data);
      return defaultValue;
    }
  }

  /**
   * 安全获取并解析JSON
   */
  static safeGetJSON<T = any>(key: string, defaultValue: T | null = null): T | null {
    const data = this.safeGetItem(key);
    return this.safeJSONParse(data, defaultValue);
  }

  /**
   * 安全设置JSON值
   */
  static safeSetJSON<T = any>(key: string, value: T): boolean {
    try {
      const jsonString = JSON.stringify(value);
      return this.safeSetItem(key, jsonString);
    } catch (error) {
      console.warn(`Failed to stringify and set localStorage item "${key}":`, error);
      return false;
    }
  }

  /**
   * 清理所有无效的localStorage项
   */
  static cleanInvalidItems(): void {
    try {
      const keysToRemove: string[] = [];
      
      for (let i = localStorage.length - 1; i >= 0; i--) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value === 'undefined' || value === 'null' || value === null) {
            keysToRemove.push(key);
          }
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log(`Removed invalid localStorage item: ${key}`);
      });

      if (keysToRemove.length > 0) {
        console.log(`Cleaned ${keysToRemove.length} invalid localStorage items`);
      }
    } catch (error) {
      console.error('Failed to clean invalid localStorage items:', error);
    }
  }

  /**
   * 强制清理所有存储 - 紧急修复功能
   */
  static emergencyCleanup(): void {
    try {
      // 清除localStorage
      localStorage.clear();
      console.log('Emergency: localStorage cleared');
      
      // 清除sessionStorage
      sessionStorage.clear();
      console.log('Emergency: sessionStorage cleared');
      
      // 清除可能的IndexedDB数据
      if ('indexedDB' in window) {
        const databases = ['app_cache', 'project_cache', 'ltc_cache'];
        databases.forEach(dbName => {
          try {
            const deleteReq = indexedDB.deleteDatabase(dbName);
            deleteReq.onsuccess = () => console.log(`Emergency: Deleted IndexedDB ${dbName}`);
          } catch (e) {
            console.warn(`Failed to delete IndexedDB ${dbName}:`, e);
          }
        });
      }
      
    } catch (error) {
      console.error('Emergency cleanup failed:', error);
    }
  }

  /**
   * 监控并自动修复存储问题
   */
  static startAutoRepair(): void {
    // 立即检查并修复
    this.cleanInvalidItems();
    
    // 监听存储事件
    window.addEventListener('storage', (event) => {
      if (event.newValue === 'undefined' || event.newValue === 'null') {
        console.warn(`Detected invalid storage value for key: ${event.key}`);
        if (event.key) {
          localStorage.removeItem(event.key);
          console.log(`Auto-repaired: Removed invalid key ${event.key}`);
        }
      }
    });

    // 监听未捕获的错误
    window.addEventListener('error', (event) => {
      if (event.message && event.message.includes('JSON')) {
        console.warn('Detected JSON parsing error, attempting emergency cleanup...');
        this.emergencyCleanup();
        
        // 延迟重新加载页面
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    });

    // 定期清理（更频繁 - 每2分钟）
    setInterval(() => {
      this.cleanInvalidItems();
    }, 2 * 60 * 1000);
    
    console.log('Auto-repair system activated');
  }

  /**
   * 在页面加载时初始化存储清理
   */
  static initStorageCleanup(): void {
    // 立即清理无效项
    this.cleanInvalidItems();

    // 启动自动修复系统
    this.startAutoRepair();
  }
}

// 页面加载时自动初始化
if (typeof window !== 'undefined') {
  StorageUtils.initStorageCleanup();
} 