/**
 * 🚨 紧急数据管理器 (Emergency Data Manager)
 * 立即修复当前数据架构的关键问题
 */

import { Project, ProjectStage } from '../types';

class EmergencyDataManager {
  private static instance: EmergencyDataManager;
  private cache = new Map<string, any>();
  
  // 统一存储键
  private readonly STORAGE_KEYS = {
    PROJECTS: 'edm_projects',
    STAGES: 'edm_stages'
  } as const;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): EmergencyDataManager {
    if (!EmergencyDataManager.instance) {
      EmergencyDataManager.instance = new EmergencyDataManager();
    }
    return EmergencyDataManager.instance;
  }

  private initialize(): void {
    console.log('🚨 Emergency Data Manager: Starting...');
    this.migrateOldData();
    console.log('✅ Emergency Data Manager: Ready');
  }

  private migrateOldData(): void {
    // 迁移项目数据
    const sources = ['projects', 'ltc_projects', 'dal_projects'];
    const allProjects: Project[] = [];
    
    sources.forEach(source => {
      try {
        const data = this.getFromStorage(source, []);
        if (Array.isArray(data)) allProjects.push(...data);
      } catch (error) {
        console.warn(`Migration failed for ${source}:`, error);
      }
    });
    
    if (allProjects.length > 0) {
      const unique = this.removeDuplicates(allProjects);
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, unique);
    }
    
    // 迁移阶段数据
    const stageKeys = Object.keys(localStorage).filter(k => k.startsWith('project_stages_'));
    const allStages: Record<string, ProjectStage[]> = {};
    
    stageKeys.forEach(key => {
      const projectId = key.replace('project_stages_', '');
      const stages = this.getFromStorage(key, []);
      if (Array.isArray(stages)) allStages[projectId] = stages;
    });
    
    if (Object.keys(allStages).length > 0) {
      this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
    }
  }

  // 项目操作
  public getAllProjects(): Project[] {
    // 始终从localStorage读取最新数据，确保不丢失新创建的项目
    const projects = this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []);
    console.log('📊 EmergencyDataManager: Loading projects from storage, count:', projects.length);
    
    // 更新缓存
    this.cache.set('projects', projects);
    return projects;
  }

  public getProjectById(id: string): Project | null {
    const projects = this.getAllProjects();
    return projects.find(p => p.id === id) || null;
  }

  public async saveProject(project: Project): Promise<boolean> {
    try {
      const projects = this.getAllProjects();
      const index = projects.findIndex(p => p.id === project.id);
      
      if (index >= 0) {
        projects[index] = { ...project, updatedAt: new Date().toISOString() };
      } else {
        projects.unshift({ ...project, updatedAt: new Date().toISOString() });
      }
      
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, projects);
      this.cache.delete('projects');
      return true;
    } catch (error) {
      console.error('Save project failed:', error);
      return false;
    }
  }

  public async saveAllProjects(projects: Project[]): Promise<boolean> {
    try {
      console.log('💾 EmergencyDataManager: Saving all projects, count:', projects.length);
      console.log('💾 Projects being saved:', projects.map(p => ({ id: p.id, name: p.name, projectId: p.projectId })));
      
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, projects);
      this.cache.delete('projects');
      
      // 验证保存是否成功
      const savedProjects = this.getFromStorage(this.STORAGE_KEYS.PROJECTS, []);
      console.log('✅ EmergencyDataManager: Verification - saved projects count:', savedProjects.length);
      console.log('✅ Saved project IDs:', savedProjects.map((p: any) => p.id));
      
      // 直接检查localStorage
      const rawData = localStorage.getItem(this.STORAGE_KEYS.PROJECTS);
      console.log('🔍 Raw localStorage data length:', rawData?.length || 0);
      console.log('🔍 Raw localStorage preview:', rawData?.substring(0, 200) + '...');
      
      return true;
    } catch (error) {
      console.error('❌ Save all projects failed:', error);
      return false;
    }
  }

  public async deleteProject(projectId: string): Promise<boolean> {
    try {
      console.log('🗑️ EmergencyDataManager: Deleting project and all related data for ID:', projectId);
      
      // 1. 删除主项目数据
      const projects = this.getAllProjects();
      const filteredProjects = projects.filter(p => p.id !== projectId && p.projectId !== projectId);
      
      this.setToStorage(this.STORAGE_KEYS.PROJECTS, filteredProjects);
      this.cache.delete('projects');
      
      // 2. 删除阶段数据
      const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      if (allStages[projectId]) {
        delete allStages[projectId];
        this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
        console.log('🗑️ EmergencyDataManager: Deleted stages data for project', projectId);
      }
      
      // 3. 删除所有可能的项目相关存储键
      const keysToCheck = [
        `project_stages_${projectId}`,
        `edm_project_${projectId}`,
        `dal_project_${projectId}`,
        `project_data_${projectId}`,
        `project_cache_${projectId}`,
        `project_backup_${projectId}`,
        // 还要检查其他可能的存储格式
        'projects', 'ltc_projects', 'dal_projects'
      ];
      
      // 对于主要存储键，删除其中的项目
      ['projects', 'ltc_projects', 'dal_projects'].forEach(key => {
        try {
          const data = this.getFromStorage(key, []);
          if (Array.isArray(data)) {
            const filtered = data.filter((p: any) => p.id !== projectId && p.projectId !== projectId);
            if (filtered.length !== data.length) {
              this.setToStorage(key, filtered);
              console.log(`🗑️ EmergencyDataManager: Cleaned project from ${key}`);
            }
          }
        } catch (error) {
          console.warn(`Failed to clean ${key}:`, error);
        }
      });
      
      // 删除特定的项目存储键
      [
        `project_stages_${projectId}`,
        `edm_project_${projectId}`,
        `dal_project_${projectId}`,
        `project_data_${projectId}`,
        `project_cache_${projectId}`,
        `project_backup_${projectId}`
      ].forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`🗑️ EmergencyDataManager: Removed storage key: ${key}`);
        }
      });
      
             // 4. 记录删除日志，防止自动恢复
       try {
         const deletedLog = this.getFromStorage('deleted_projects_log', []);
         deletedLog.push({
           projectId: projectId,
           deletedAt: new Date().toISOString(),
           action: 'user_delete'
         });
         this.setToStorage('deleted_projects_log', deletedLog);
         console.log('📝 EmergencyDataManager: Recorded deletion log to prevent auto-recovery');
       } catch (logError) {
         console.warn('⚠️ Failed to record deletion log:', logError);
       }
       
       console.log('✅ EmergencyDataManager: Project deletion completed');
      return true;
      
    } catch (error) {
      console.error('❌ EmergencyDataManager: Delete project failed:', error);
      return false;
    }
  }

  // 阶段操作
  public getProjectStages(projectId: string): ProjectStage[] {
    const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    return allStages[projectId] || [];
  }

  public async saveProjectStages(projectId: string, stages: ProjectStage[]): Promise<boolean> {
    try {
      const allStages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
      allStages[projectId] = stages;
      this.setToStorage(this.STORAGE_KEYS.STAGES, allStages);
      return true;
    } catch (error) {
      console.error('Save stages failed:', error);
      return false;
    }
  }

  // 工具方法
  private removeDuplicates<T extends { id: string }>(items: T[]): T[] {
    const seen = new Set();
    return items.filter(item => {
      if (seen.has(item.id)) return false;
      seen.add(item.id);
      return true;
    });
  }

  private getFromStorage(key: string, defaultValue: any): any {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      return defaultValue;
    }
  }

  private setToStorage(key: string, value: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Storage write failed:', error);
      throw error;
    }
  }

  public getStatus(): any {
    const projects = this.getAllProjects();
    const stages = this.getFromStorage(this.STORAGE_KEYS.STAGES, {});
    
    return {
      projectsCount: projects.length,
      stagesCount: Object.keys(stages).length,
      cacheSize: this.cache.size
    };
  }
}

export const emergencyDataManager = EmergencyDataManager.getInstance();
export default emergencyDataManager; 