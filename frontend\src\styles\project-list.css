/* 项目列表页面样式 */

/* 删除按钮样式 */
.delete-button {
  transition: all 0.3s ease;
}

.delete-button:hover {
  background-color: #fff1f0;
  transform: scale(1.1);
  box-shadow: 0 0 5px rgba(255, 77, 79, 0.2);
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover {
  background-color: #f5f5f5;
}

/* 项目卡片样式 */
.project-card {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 16px;
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.project-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.project-card__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.project-card__title {
  font-weight: 600;
  font-size: 12px !important;
  margin-bottom: 4px;
}

.project-card__id {
  color: var(--text-secondary);
  font-size: 11px !important;
}

.project-card__client {
  font-weight: 500;
  margin-bottom: 12px;
}

.project-card__footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.project-card__owner {
  display: flex;
  align-items: center;
}

.project-card__owner-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px !important;
  margin-right: 8px;
}

.project-card__owner-name {
  font-size: 11px !important;
  color: var(--text-secondary);
}

/* 确认删除对话框样式 */
.ant-modal-confirm-body .ant-modal-confirm-content {
  margin-top: 16px;
}

.ant-modal-confirm-body .ant-modal-confirm-content p {
  margin-bottom: 8px;
}

.ant-modal-confirm-btns .ant-btn-dangerous {
  background-color: #ff4d4f;
  color: white;
  border-color: #ff4d4f;
}

.ant-modal-confirm-btns .ant-btn-dangerous:hover {
  background-color: #ff7875;
  border-color: #ff7875;
}
