import { useState, useCallback, useEffect, useRef } from 'react';
import { message } from 'antd';
import projectService from '../services/new-project.service';
import { Project, ProjectFilters } from '../types';
import { <PERSON>rrorHandler } from '../utils/errorHandler';
import { Compression } from '../utils/compression';
import { Performance } from '../utils/performance';

const STORAGE_KEYS = {
  FILTERS: 'project_filters',
  CACHE: 'project_cache',
  CACHE_TIMESTAMP: 'project_cache_timestamp'
};

const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
const PRELOAD_INTERVAL = 4 * 60 * 1000; // 4分钟预加载

const initialFilters: ProjectFilters = {
  searchText: '',
  status: 'all',
  country: 'all',
  tier: 'all',
  category: 'all',
  revenue: 'all',
  probability: 'all',
  level: 'all',
  createDate: 'all'
};

const loadFiltersFromStorage = (): ProjectFilters => {
  try {
    const savedFilters = localStorage.getItem(STORAGE_KEYS.FILTERS);
    if (savedFilters && savedFilters !== 'undefined' && savedFilters !== 'null') {
      return JSON.parse(savedFilters);
    }
    return initialFilters;
  } catch (error) {
    console.error('加载过滤器状态失败:', error);
    // Clear corrupted data
    localStorage.removeItem(STORAGE_KEYS.FILTERS);
    return initialFilters;
  }
};

const saveFiltersToStorage = (filters: ProjectFilters) => {
  try {
    localStorage.setItem(STORAGE_KEYS.FILTERS, JSON.stringify(filters));
  } catch (error) {
    console.error('保存过滤器状态失败:', error);
  }
};

const loadCacheFromStorage = (): { data: Project[]; timestamp: number } | null => {
  try {
    const cache = localStorage.getItem(STORAGE_KEYS.CACHE);
    const timestamp = localStorage.getItem(STORAGE_KEYS.CACHE_TIMESTAMP);
    if (cache && timestamp && cache !== 'undefined' && cache !== 'null' && timestamp !== 'undefined' && timestamp !== 'null') {
      const data = Compression.isCompressed(cache)
        ? Compression.decompress(cache)
        : JSON.parse(cache);
      return {
        data,
        timestamp: parseInt(timestamp, 10)
      };
    }
    return null;
  } catch (error) {
    console.error('加载缓存失败:', error);
    // Clear corrupted cache data
    localStorage.removeItem(STORAGE_KEYS.CACHE);
    localStorage.removeItem(STORAGE_KEYS.CACHE_TIMESTAMP);
    return null;
  }
};

const saveCacheToStorage = (data: Project[]) => {
  try {
    const compressed = Compression.compress(data);
    localStorage.setItem(STORAGE_KEYS.CACHE, compressed);
    localStorage.setItem(STORAGE_KEYS.CACHE_TIMESTAMP, Date.now().toString());
  } catch (error) {
    console.error('保存缓存失败:', error);
  }
};

const isCacheValid = (timestamp: number): boolean => {
  return Date.now() - timestamp < CACHE_DURATION;
};

export const useProjectList = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [filters, setFilters] = useState<ProjectFilters>(loadFiltersFromStorage);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const preloadTimerRef = useRef<NodeJS.Timeout | null>(null);

  const loadProjects = useCallback(async (forceRefresh = false) => {
    const startTime = performance.now();
    try {
      setLoading(true);
      setError(null);

      // 检查缓存
      if (!forceRefresh) {
        const cache = loadCacheFromStorage();
        if (cache && isCacheValid(cache.timestamp)) {
          setProjects(cache.data);
          setFilteredProjects(cache.data);
          setLoading(false);
          Performance.measure('loadFromCache', startTime);
          return;
        }
      }

      const data = await projectService.getAllProjects();
      setProjects(data);
      setFilteredProjects(data);
      saveCacheToStorage(data);
      Performance.measure('loadFromApi', startTime);
    } catch (error) {
      const errorDetails = ErrorHandler.handle(error);
      setError(errorDetails.message);
      message.error('加载项目列表失败');
      Performance.measure('loadError', startTime);
    } finally {
      setLoading(false);
    }
  }, []);

  const preloadData = useCallback(async () => {
    if (isPreloading) return;

    const startTime = performance.now();
    try {
      setIsPreloading(true);
      const data = await projectService.getAllProjects();
      saveCacheToStorage(data);
      Performance.measure('preload', startTime);
    } catch (error) {
      console.error('预加载数据失败:', error);
      Performance.measure('preloadError', startTime);
    } finally {
      setIsPreloading(false);
    }
  }, [isPreloading]);

  const startPreloadTimer = useCallback(() => {
    if (preloadTimerRef.current) {
      clearTimeout(preloadTimerRef.current);
    }

    preloadTimerRef.current = setTimeout(() => {
      preloadData();
    }, PRELOAD_INTERVAL);
  }, [preloadData]);

  const applyFilters = useCallback((projects: Project[], filters: ProjectFilters) => {
    return projects.filter(project => {
      const matchesSearch = !filters.searchText ||
        project.name.toLowerCase().includes(filters.searchText.toLowerCase()) ||
        project.projectId.toLowerCase().includes(filters.searchText.toLowerCase()) ||
        project.client.toLowerCase().includes(filters.searchText.toLowerCase());

      const matchesStatus = filters.status === 'all' || project.status === filters.status;
      const matchesCountry = filters.country === 'all' || project.country === filters.country;
      const matchesTier = filters.tier === 'all' || project.tier === filters.tier;
      const matchesCategory = filters.category === 'all' || project.category === filters.category;
      const matchesRevenue = filters.revenue === 'all' || project.revenue.toString() === filters.revenue;
      const matchesProbability = filters.probability === 'all' || project.probability === filters.probability;
      const matchesLevel = filters.level === 'all' || project.level === filters.level;

      let matchesDate = true;
      if (filters.createDate !== 'all') {
        const createDate = new Date(project.createdAt);
        const now = new Date();
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        switch (filters.createDate) {
          case '7days':
            matchesDate = createDate >= sevenDaysAgo;
            break;
          case '30days':
            matchesDate = createDate >= thirtyDaysAgo;
            break;
          case 'thisYear':
            matchesDate = createDate.getFullYear() === now.getFullYear();
            break;
        }
      }

      return matchesSearch && matchesStatus && matchesCountry && matchesTier &&
        matchesCategory && matchesRevenue && matchesProbability && matchesLevel && matchesDate;
    });
  }, []);

  const handleFilterChange = useCallback((newFilters: Partial<ProjectFilters>) => {
    const startTime = performance.now();
    setFilters(prev => {
      const updated = { ...prev, ...newFilters };
      const filtered = applyFilters(projects, updated);
      setFilteredProjects(filtered);
      saveFiltersToStorage(updated);
      Performance.measure('filter', startTime);
      return updated;
    });
  }, [projects, applyFilters]);

  const handleResetFilters = useCallback(() => {
    setFilters(initialFilters);
    setFilteredProjects(projects);
    localStorage.removeItem(STORAGE_KEYS.FILTERS);
  }, [projects]);

  const handleDeleteProject = useCallback(async (id: string) => {
    const startTime = performance.now();
    try {
      setError(null);
      await projectService.deleteProject(id);
      message.success('项目删除成功');
      setProjects(prev => prev.filter(p => p.id !== id));
      setFilteredProjects(prev => prev.filter(p => p.id !== id));
      // 更新缓存
      const updatedProjects = projects.filter(p => p.id !== id);
      saveCacheToStorage(updatedProjects);
      // 重置预加载计时器
      startPreloadTimer();
      Performance.measure('delete', startTime);
    } catch (error) {
      const errorDetails = ErrorHandler.handle(error);
      setError(errorDetails.message);
      message.error('删除项目失败');
      Performance.measure('deleteError', startTime);
    }
  }, [projects, startPreloadTimer]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await loadProjects(true);
      message.success('数据已更新');
      // 重置预加载计时器
      startPreloadTimer();
    } finally {
      setIsRefreshing(false);
    }
  }, [loadProjects, startPreloadTimer]);

  useEffect(() => {
    loadProjects();
    startPreloadTimer();

    return () => {
      if (preloadTimerRef.current) {
        clearTimeout(preloadTimerRef.current);
      }
    };
  }, [loadProjects, startPreloadTimer]);

  return {
    projects,
    filteredProjects,
    filters,
    loading,
    error,
    isRefreshing,
    isPreloading,
    handleFilterChange,
    handleResetFilters,
    handleDeleteProject,
    handleRefresh
  };
};