import React, { useState, useEffect } from 'react';
import { Layout, Dropdown, Avatar, Space, Badge, Button, Tooltip, Select } from 'antd';
import type { MenuProps } from 'antd';
import {
  UserOutlined,
  BellOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import authService from '../services/auth.service';

import NotificationPanel from './Notification/NotificationPanel';
import notificationService from '../services/notification.service';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTheme } from '../contexts/ThemeContext';
import './Header.css';

const { Option } = Select;

const { Header: AntHeader } = Layout;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const user = authService.getCurrentUser();
  const [notificationPanelVisible, setNotificationPanelVisible] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const { selectedCurrency, setCurrency, supportedCurrencies, exchangeRates } = useCurrency();
  const { theme, toggleTheme } = useTheme();

  // 提取获取通知数量的函数
  const loadUnreadCount = async () => {
    try {
      console.log('Header: Loading unread count...');
      const stats = await notificationService.getNotificationStats();
      console.log('Header: Received stats:', stats);
      setUnreadCount(stats.unreadCount);
      console.log('Header: Set unread count to:', stats.unreadCount);
    } catch (error) {
      console.error('Failed to load notification stats:', error);
    }
  };

  useEffect(() => {
    console.log('Header: Component mounted, loading initial count');
    // 初始化时获取未读通知数量
    loadUnreadCount();

    // 货币设置由CurrencyContext管理，无需在这里设置

    // 定期刷新通知数量（每30秒）
    const interval = setInterval(loadUnreadCount, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  const handleNotificationClick = () => {
    console.log('Header: Notification clicked, opening panel');
    setNotificationPanelVisible(true);
  };

  const handleNotificationPanelClose = () => {
    console.log('Header: Notification panel closing, refreshing count');
    setNotificationPanelVisible(false);
    // 面板关闭时重新获取最新的通知数据
    loadUnreadCount();
  };

  const handleUnreadCountChange = (count: number) => {
    console.log('Header: Unread count changed to:', count);
    setUnreadCount(count);
  };

  const handleCurrencyChange = (value: string) => {
    setCurrency(value);
    console.log('Currency changed to:', value);
  };

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout,
    },
  ];

  return (
    <AntHeader className="site-header">


      {/* 右侧工具栏 */}
      <div className="header-toolbar">


        {/* 货币选择 */}
        <div className="toolbar-item">
          <Tooltip title={`Current Exchange Rates (based on EUR): ${Object.entries(exchangeRates).map(([curr, rate]) => `${curr}: ${rate.toFixed(3)}`).join(', ')}`}>
            <Select
              value={selectedCurrency}
              onChange={handleCurrencyChange}
              style={{ 
                width: 70,
                fontSize: '12px'
              }}
              size="small"
              bordered={false}
              suffixIcon={null}
            >
              {supportedCurrencies.map(c => <Select.Option key={c} value={c}>{c}</Select.Option>)}
            </Select>
          </Tooltip>
        </div>

        {/* 通知 */}
        <div className="toolbar-item">
          <Tooltip title="Notifications">
            <Badge count={unreadCount} size="small" className="notification-badge">
              <BellOutlined 
                style={{ fontSize: '20px', cursor: 'pointer' }} 
                onClick={handleNotificationClick}
              />
            </Badge>
          </Tooltip>
        </div>

        {/* 用户菜单 */}
        <div className="toolbar-item">
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <div className="user-menu">
              <Avatar
                icon={<UserOutlined />}
                className="user-avatar"
              />
              <span className="user-name">{user?.username || 'User'}</span>
            </div>
          </Dropdown>
        </div>
      </div>

      {/* 通知面板 */}
      <NotificationPanel
        visible={notificationPanelVisible}
        onClose={handleNotificationPanelClose}
        onUnreadCountChange={handleUnreadCountChange}
      />
    </AntHeader>
  );
};

export default Header;
