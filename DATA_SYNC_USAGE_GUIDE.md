# 数据同步引擎使用指南

## 🎯 快速开始

### 1. 启动系统
```bash
# 启动前端和后端
npm run start-ltc
# 或者分别启动
cd frontend && npm start
cd backend && npm run dev
```

### 2. 访问测试页面
打开浏览器访问：`http://localhost:3000/data-sync-test`

## 🔧 测试步骤

### 测试场景1：客户信息自动同步
1. **准备工作**：
   - 确保系统中有一些测试项目数据
   - 记下项目列表中的客户名称

2. **执行测试**：
   - 进入 Client 管理页面
   - 编辑任意一个客户的信息（比如更改客户名称、等级、国家等）
   - 保存客户信息

3. **验证结果**：
   - 去 Opportunity 页面检查相关项目
   - 确认项目中的客户信息已自动更新
   - 控制台会显示同步日志

### 测试场景2：项目创建自动同步
1. **执行测试**：
   - 进入 Opportunity 页面，点击"Create New Opportunity"
   - 填写项目信息，选择或创建客户
   - 保存项目

2. **验证结果**：
   - 进入 Client 页面
   - 查看相关客户的"Total Projects"数量是否增加
   - 检查客户的"Total Revenue"是否包含新项目收入

### 测试场景3：使用专用测试页面
1. **访问**：`http://localhost:3000/data-sync-test`

2. **功能测试**：
   - 点击"模拟客户更新"按钮
   - 点击"模拟项目创建"按钮
   - 点击"模拟项目更新"按钮

3. **监控**：
   - 查看"同步引擎状态"面板
   - 查看"事件历史"时间线
   - 检查每个操作的成功反馈

## 📊 监控与调试

### 控制台日志
打开浏览器开发工具（F12），查看Console标签页：
```
[DataSyncEngine] Emitting event: {...}
[DataSyncEngine] Processing event CLIENT_UPDATED with 1 handlers
[DataSyncEngine] Found 3 projects to update for client test-client-001
[DataSyncEngine] Updated project PRJ-001 with client changes
```

### 事件状态监控
在测试页面可以看到：
- **总事件数**：累计处理的同步事件数量
- **队列长度**：待处理的事件数量（通常为0）
- **处理状态**：显示引擎是否正在处理事件
- **处理器数量**：注册的事件处理器数量（应该是5个）

### 事件历史
查看最近的同步事件：
- 事件类型和时间戳
- 实体ID和数据源
- 具体的变更内容

## 🚨 故障排除

### 同步没有生效？
1. **检查控制台**：看是否有错误信息
2. **验证事件触发**：确认操作确实触发了同步事件
3. **检查数据匹配**：确认客户名称在项目和客户模块中完全匹配

### 数据不一致？
1. **手动刷新**：刷新页面重新加载数据
2. **检查存储**：清除浏览器localStorage缓存
3. **重启服务**：重启前端和后端服务

### 性能问题？
1. **检查队列**：确认事件队列长度不会持续增长
2. **监控日志**：查看是否有处理时间超过1秒的警告
3. **减少频率**：避免短时间内大量连续操作

## 🎨 自定义配置

### 添加新的同步规则
编辑 `frontend/src/services/dataSyncEngine.ts`：

```typescript
// 在 SYNC_RULES 中添加新规则
CLIENT_UPDATED: [
  {
    target: 'PROJECT',
    action: 'UPDATE_CLIENT_SNAPSHOT',
    fields: ['name', 'tier', 'country', 'contactPerson'],
    description: '更新项目中的客户信息快照'
  },
  // 添加你的新规则
  {
    target: 'FINANCE',
    action: 'UPDATE_CLIENT_INFO',
    fields: ['name', 'tier'],
    description: '更新财务模块中的客户信息'
  }
]
```

### 集成到新页面
在任何需要数据同步的页面中：

```typescript
import { dataSyncEngine } from '../services/dataSyncEngine';

// 在数据更新后触发事件
const handleDataUpdate = async (newData, oldData) => {
  // 更新数据
  await someService.updateData(newData);
  
  // 触发同步事件
  const event = dataSyncEngine.createEvent(
    'YOUR_EVENT_TYPE',
    entityId,
    'entity_type',
    changes,
    'YourComponent'
  );
  
  await dataSyncEngine.emitEvent(event);
};
```

## 📈 最佳实践

### 1. 事件设计
- 保持事件粒度适中（不要太细也不要太粗）
- 包含足够的上下文信息
- 使用描述性的事件类型名称

### 2. 错误处理
- 同步失败不应该阻止主要业务流程
- 提供用户友好的错误提示
- 记录详细的错误日志用于调试

### 3. 性能优化
- 避免循环依赖（A更新B，B又更新A）
- 批量处理相关的多个更新
- 使用防抖（debounce）避免频繁触发

### 4. 测试策略
- 定期使用测试页面验证功能
- 在开发新功能前先测试现有同步
- 监控生产环境的同步事件日志

## 🔗 相关文档

- [项目架构文档](PROJECT_DATA_ARCHITECTURE_OPTIMIZATION.md)
- [功能演示文档](DATA_SYNC_ENGINE_DEMO.md)
- [API参考文档](frontend/src/services/dataSyncEngine.ts)

---

**温馨提示**：数据同步是自动进行的，用户无需手动操作。如果遇到问题，请参考故障排除部分或联系技术支持。 