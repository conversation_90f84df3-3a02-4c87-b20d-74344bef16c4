import React from 'react';
import { Skeleton, Card, Row, Col } from 'antd';

interface ProjectSkeletonProps {
  count?: number;
  type?: 'card' | 'list' | 'table';
}

const ProjectSkeleton: React.FC<ProjectSkeletonProps> = ({ 
  count = 3, 
  type = 'card' 
}) => {
  // 卡片骨架屏
  const renderCardSkeleton = () => {
    return Array(count).fill(null).map((_, index) => (
      <Col xs={24} sm={12} md={8} key={`card-skeleton-${index}`}>
        <Card style={{ marginBottom: 16 }}>
          <Skeleton active avatar paragraph={{ rows: 3 }} />
        </Card>
      </Col>
    ));
  };

  // 列表骨架屏
  const renderListSkeleton = () => {
    return Array(count).fill(null).map((_, index) => (
      <div 
        key={`list-skeleton-${index}`}
        style={{ 
          padding: '16px', 
          borderBottom: '1px solid #f0f0f0',
          marginBottom: index === count - 1 ? 0 : 16
        }}
      >
        <Skeleton active avatar paragraph={{ rows: 2 }} />
      </div>
    ));
  };

  // 表格骨架屏
  const renderTableSkeleton = () => {
    return (
      <div className="table-skeleton">
        <div className="table-skeleton-header">
          {Array(5).fill(null).map((_, index) => (
            <div key={`header-${index}`} className="table-skeleton-cell">
              <Skeleton.Button active style={{ width: '100%', height: 24 }} />
            </div>
          ))}
        </div>
        <div className="table-skeleton-body">
          {Array(count).fill(null).map((_, rowIndex) => (
            <div key={`row-${rowIndex}`} className="table-skeleton-row">
              {Array(5).fill(null).map((_, cellIndex) => (
                <div key={`cell-${rowIndex}-${cellIndex}`} className="table-skeleton-cell">
                  <Skeleton.Button active style={{ width: '100%', height: 24 }} />
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  };

  switch (type) {
    case 'card':
      return <Row gutter={[16, 16]}>{renderCardSkeleton()}</Row>;
    case 'list':
      return <div>{renderListSkeleton()}</div>;
    case 'table':
      return renderTableSkeleton();
    default:
      return <Row gutter={[16, 16]}>{renderCardSkeleton()}</Row>;
  }
};

export default ProjectSkeleton;
