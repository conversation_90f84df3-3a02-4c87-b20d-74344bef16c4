.virtual-list {
  position: relative;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  will-change: transform;
  box-sizing: border-box;
}

.virtual-list.horizontal {
  overflow-x: auto;
  overflow-y: hidden;
}

.virtual-list.vertical {
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-list-inner {
  position: relative;
}

.virtual-list-item {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
}

.virtual-list-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.virtual-list-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}
