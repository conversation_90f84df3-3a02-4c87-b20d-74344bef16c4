import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  message,
  Row,
  Col,
  Table,
  Modal,
  DatePicker,
  Progress,
  Space,
  Tag,
  Upload,

  Statistic,
  Tabs,
  Tooltip
} from 'antd';
import {
  CloudUploadOutlined,
  FileTextOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  PlusOutlined,
  ReloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  RightOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import type { Project, ProjectStage } from '../../types';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface ProposalStageProps {
  project?: Project;
  stage?: ProjectStage;
  onSave?: (data: any) => void;
  onProceed?: (nextStage: string) => void;
}

interface ProposalVersion {
  key: string;
  version: string;
  date: string;
  uploadedBy: string;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  notes: string;
  fileUrl?: string;
}

const ProposalStage: React.FC<ProposalStageProps> = ({ project, stage, onSave, onProceed }) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('overview');
  const [saving, setSaving] = useState(false);
  const [versionModalVisible, setVersionModalVisible] = useState(false);
  // 新项目默认为空版本列表
  const [versions, setVersions] = useState<ProposalVersion[]>([]);

  const [completionStatus, setCompletionStatus] = useState({
    basicInfo: false,
    solution: false,
    pricing: false,
    timeline: false,
    documents: false
  });

  // 计算整体完成度
  const overallCompletion = Object.values(completionStatus).filter(Boolean).length * 20;
  const currentVersion = versions.find(v => v.status === 'approved') || versions[0];

  useEffect(() => {
    if (stage?.data) {
      try {
        const stageData = JSON.parse(stage.data);
        form.setFieldsValue(stageData);
        
        // 检查完成状态
        setCompletionStatus({
          basicInfo: !!(stageData.proposalName && stageData.proposalVersion),
          solution: !!(stageData.painPoints && stageData.solution),
          pricing: !!(stageData.totalValue && stageData.paymentTerms),
          timeline: !!(stageData.deliveryDate && stageData.projectDuration),
          documents: versions.some(v => v.status === 'submitted' || v.status === 'approved')
        });
      } catch (error) {
        console.error('Error parsing stage data:', error);
      }
    }
  }, [stage, form, versions]);

  // 保存数据
  const handleSave = async (values: any, section?: string) => {
    try {
      setSaving(true);
      
      const allData = {
        ...form.getFieldsValue(),
        ...values,
        versions,
        type: 'proposal',
        status: overallCompletion === 100 ? 'completed' : 'in_progress',
        updatedAt: new Date().toISOString()
      };

      if (onSave) {
        onSave(allData);
      }

      // 更新完成状态
      if (section) {
        setCompletionStatus(prev => ({
          ...prev,
          [section]: true
        }));
      }

      message.success('Proposal data saved successfully');
    } catch (error) {
      console.error('Save error:', error);
      message.error('Failed to save, please try again');
    } finally {
      setSaving(false);
    }
  };

  // 进入下一阶段
  const handleProceed = async () => {
    try {
      const values = form.getFieldsValue();
      await handleSave(values);
      
      setTimeout(() => {
        message.success('Proposal completed! Proceeding to Contract stage...');
        if (onProceed) {
          onProceed('contract');
        }
      }, 1000);
    } catch (error) {
      message.error('Please complete all proposal sections before proceeding');
    }
  };

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      'draft': { color: 'default', text: 'Draft' },
      'submitted': { color: 'processing', text: 'Submitted' },
      'approved': { color: 'success', text: 'Approved' },
      'rejected': { color: 'error', text: 'Rejected' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 版本表格列
  const versionColumns = [
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      width: 100,
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Uploaded By',
      dataIndex: 'uploadedBy',
      key: 'uploadedBy',
      width: 120
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => renderStatusTag(status)
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_: any, record: ProposalVersion) => (
        <Space size="small">
          <Tooltip title="View">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>
          <Tooltip title="Download">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              size="small"
            />
          </Tooltip>
          {record.status === 'draft' && (
            <Tooltip title="Delete">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="ltc-stage-content">
      {/* 页面标题和进度概览 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <h1 className="ltc-stage-title">
          Proposal Development
        </h1>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{ textAlign: 'center' }}>
            <Progress
              type="circle"
              percent={overallCompletion}
              size={60}
              strokeColor="#52c41a"
            />
            <div className="ltc-progress-label">
              Complete
            </div>
          </div>
          <Button
            type="primary"
            icon={<RightOutlined />}
            onClick={handleProceed}
            disabled={overallCompletion < 80}
            size="large"
          >
            Proceed to Contract
          </Button>
        </div>
      </div>

      {/* 状态卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Current Version"
              value={currentVersion?.version || 'N/A'}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Status"
              value={currentVersion?.status || 'Draft'}
              prefix={renderStatusTag(currentVersion?.status || 'draft')}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card className="ltc-stage-card">
            <Statistic
              title="Last Updated"
              value={currentVersion?.date ? new Date(currentVersion.date).toLocaleDateString() : 'N/A'}
              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Card className="ltc-card-content">
        <Tabs className="ltc-tabs" activeKey={activeTab} onChange={setActiveTab}>
          {/* 概览 */}
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                Overview
              </span>
            } 
            key="overview"
          >
            <Row gutter={[24, 16]}>
              <Col span={16}>
                <Card title="Proposal Versions" size="small">
                  <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                      <Button 
                        type="primary" 
                        icon={<PlusOutlined />}
                        onClick={() => setVersionModalVisible(true)}
                      >
                        New Version
                      </Button>
                      <Button icon={<ReloadOutlined />}>
                        Refresh
                      </Button>
                    </Space>
                  </div>
                  <Table
                    columns={versionColumns}
                    dataSource={versions}
                    pagination={false}
                    size="small"
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card title="Quick Stats" size="small">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Total Versions:</span>
                      <strong>{versions.length}</strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Approved:</span>
                      <strong style={{ color: '#52c41a' }}>
                        {versions.filter(v => v.status === 'approved').length}
                      </strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>In Review:</span>
                      <strong style={{ color: '#1890ff' }}>
                        {versions.filter(v => v.status === 'submitted').length}
                      </strong>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>Draft:</span>
                      <strong style={{ color: '#d9d9d9' }}>
                        {versions.filter(v => v.status === 'draft').length}
                      </strong>
                    </div>
                  </Space>
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* 提案基本信息 */}
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                Basic Information
                {completionStatus.basicInfo && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="basic"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'basicInfo')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="proposalName" 
                    label="Proposal Name"
                    rules={[{ required: true, message: 'Please enter proposal name' }]}
                  >
                    <Input placeholder="e.g. Cloud Migration Proposal for Acme Corp" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="proposalVersion" 
                    label="Version"
                    rules={[{ required: true, message: 'Please enter version' }]}
                  >
                    <Input placeholder="e.g. V1.2" />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="proposalDate" 
                    label="Proposal Date"
                    rules={[{ required: true, message: 'Please select proposal date' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="expiryDate" label="Validity Until">
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="proposalType" label="Proposal Type">
                    <Select placeholder="Select proposal type">
                      <Option value="Technical Proposal">Technical Proposal</Option>
                      <Option value="Commercial Proposal">Commercial Proposal</Option>
                      <Option value="Combined Proposal">Combined Proposal</Option>
                      <Option value="RFP Response">RFP Response</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="priority" label="Priority">
                    <Select placeholder="Select priority">
                      <Option value="High">High</Option>
                      <Option value="Medium">Medium</Option>
                      <Option value="Low">Low</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="executiveSummary" label="Executive Summary">
                    <TextArea 
                      rows={6} 
                      placeholder="Brief executive summary of the proposal..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Basic Information
                </Button>
              </div>
            </Form>
          </TabPane>

          {/* 解决方案详情 */}
          <TabPane 
            tab={
              <span>
                <FileTextOutlined />
                Solution Details
                {completionStatus.solution && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="solution"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'solution')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item name="painPoints" label="Client Pain Points">
                    <TextArea 
                      rows={8} 
                      placeholder="Describe the main challenges and problems the client is facing..."
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="solution" label="Proposed Solution">
                    <TextArea 
                      rows={8} 
                      placeholder="Describe your proposed solution and how it addresses the pain points..."
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="keyBenefits" label="Key Benefits">
                    <TextArea 
                      rows={6} 
                      placeholder="List the main benefits and value proposition..."
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="deliverables" label="Key Deliverables">
                    <TextArea 
                      rows={6} 
                      placeholder="List the main deliverables and outcomes..."
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="technicalApproach" label="Technical Approach">
                    <TextArea 
                      rows={4} 
                      placeholder="Describe the technical methodology and approach..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Solution Details
                </Button>
              </div>
            </Form>
          </TabPane>

          {/* 商业条款 */}
          <TabPane 
            tab={
              <span>
                <DollarOutlined />
                Pricing & Terms
                {completionStatus.pricing && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="pricing"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'pricing')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item 
                    name="totalValue" 
                    label="Total Value (€)"
                    rules={[{ required: true, message: 'Please enter total value' }]}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      formatter={value => `€ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value!.replace(/\€\s?|(,*)/g, '')}
                      placeholder="1500000"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="paymentTerms" label="Payment Terms">
                    <Select placeholder="Select payment terms">
                      <Option value="30 days">30 days</Option>
                      <Option value="45 days">45 days</Option>
                      <Option value="60 days">60 days</Option>
                      <Option value="Milestone based">Milestone based</Option>
                      <Option value="Custom">Custom</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="warrantyPeriod" label="Warranty Period">
                    <Select placeholder="Select warranty period">
                      <Option value="6 months">6 months</Option>
                      <Option value="12 months">12 months</Option>
                      <Option value="24 months">24 months</Option>
                      <Option value="36 months">36 months</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="supportLevel" label="Support Level">
                    <Select placeholder="Select support level">
                      <Option value="Basic">Basic</Option>
                      <Option value="Standard">Standard</Option>
                      <Option value="Premium">Premium</Option>
                      <Option value="Enterprise">Enterprise</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="commercialTerms" label="Commercial Terms">
                    <TextArea 
                      rows={6} 
                      placeholder="Detailed commercial terms and conditions..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Pricing & Terms
                </Button>
              </div>
            </Form>
          </TabPane>

          {/* 时间线 */}
          <TabPane 
            tab={
              <span>
                <CalendarOutlined />
                Timeline
                {completionStatus.timeline && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: '8px' }} />}
              </span>
            } 
            key="timeline"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => handleSave(values, 'timeline')}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12}>
                  <Form.Item name="proposalSubmissionDate" label="Proposal Submission Date">
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="clientResponseDate" label="Expected Client Response">
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="projectStartDate" label="Proposed Project Start">
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12}>
                  <Form.Item name="projectDuration" label="Project Duration (months)">
                    <InputNumber 
                      style={{ width: '100%' }} 
                      min={1} 
                      max={36}
                      placeholder="6"
                    />
                  </Form.Item>
                </Col>
                <Col xs={24}>
                  <Form.Item name="keyMilestones" label="Key Milestones">
                    <TextArea 
                      rows={6} 
                      placeholder="List key project milestones and timelines..."
                    />
                  </Form.Item>
                </Col>
              </Row>
              <div style={{ textAlign: 'right', marginTop: '24px' }}>
                <Button type="primary" htmlType="submit" loading={saving}>
                  Save Timeline
                </Button>
              </div>
            </Form>
          </TabPane>
        </Tabs>
      </Card>

      {/* 新版本模态框 */}
      <Modal
        title="Create New Proposal Version"
        open={versionModalVisible}
        onCancel={() => setVersionModalVisible(false)}
        onOk={() => {
          // 这里应该处理新版本创建逻辑
          message.success('New version created successfully');
          setVersionModalVisible(false);
        }}
      >
        <Form layout="vertical">
          <Form.Item name="newVersion" label="Version Number">
            <Input placeholder="e.g. V1.3" />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={4} placeholder="What's new in this version..." />
          </Form.Item>
          <Form.Item name="proposalFile" label="Upload Proposal File">
            <Upload>
                              <Button icon={<CloudUploadOutlined style={{ fontSize: '16px', color: '#FF7A00' }} />}>Click to Upload</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProposalStage;
