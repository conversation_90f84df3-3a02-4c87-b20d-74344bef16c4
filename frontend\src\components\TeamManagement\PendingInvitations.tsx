import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Popconfirm,
  message,
  Empty,
  Typography
} from 'antd';
import {
  DeleteOutlined,
  MailOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { TeamInvitation, TeamRole } from '../../types';
import teamService from '../../services/team.service';

interface PendingInvitationsProps {
  projectId: string;
  onInvitationUpdated: () => void;
}

const { Text } = Typography;

const PendingInvitations: React.FC<PendingInvitationsProps> = ({
  projectId,
  onInvitationUpdated
}) => {
  const [invitations, setInvitations] = useState<TeamInvitation[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadInvitations();
  }, [projectId]);

  const loadInvitations = async () => {
    setLoading(true);
    try {
      const data = await teamService.getPendingInvitations(projectId);
      setInvitations(data);
    } catch (error) {
      console.error('Failed to load invitations:', error);
      message.error('Failed to load pending invitations');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      await teamService.cancelInvitation(projectId, invitationId);
      setInvitations(invitations.filter(inv => inv.id !== invitationId));
      onInvitationUpdated();
      message.success('Invitation cancelled successfully');
    } catch (error) {
      message.error('Failed to cancel invitation');
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    try {
      await teamService.resendInvitation(projectId, invitationId);
      message.success('Invitation resent successfully');
    } catch (error) {
      message.error('Failed to resend invitation');
    }
  };

  const getRoleColor = (role: TeamRole) => {
    switch (role) {
      case TeamRole.OWNER:
        return 'gold';
      case TeamRole.ADMIN:
        return 'blue';
      case TeamRole.MEMBER:
        return 'green';
      case TeamRole.VIEWER:
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'expired':
        return 'red';
      case 'declined':
        return 'red';
      default:
        return 'default';
    }
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const formatTimeRemaining = (expiresAt: string) => {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days}d ${hours}h remaining`;
    if (hours > 0) return `${hours}h remaining`;
    
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${minutes}m remaining`;
  };

  const columns = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => (
        <Text strong>{email}</Text>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: TeamRole) => (
        <Tag color={getRoleColor(role)}>
          {role.charAt(0).toUpperCase() + role.slice(1)}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string, record: TeamInvitation) => {
        const expired = isExpired(record.expiresAt);
        const displayStatus = expired ? 'expired' : status;
        
        return (
          <Tag color={getStatusColor(displayStatus)} icon={<ClockCircleOutlined />}>
            {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
          </Tag>
        );
      },
    },
    {
      title: 'Invited',
      dataIndex: 'invitedAt',
      key: 'invitedAt',
      render: (invitedAt: string) => (
        <div>
          <div>{new Date(invitedAt).toLocaleDateString()}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {new Date(invitedAt).toLocaleTimeString()}
          </Text>
        </div>
      ),
    },
    {
      title: 'Expires',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      render: (expiresAt: string) => {
        const expired = isExpired(expiresAt);
        return (
          <div>
            <div style={{ color: expired ? '#ff4d4f' : '#8c8c8c' }}>
              {new Date(expiresAt).toLocaleDateString()}
            </div>
            <Text 
              type={expired ? 'danger' : 'secondary'} 
              style={{ fontSize: '12px' }}
            >
              {formatTimeRemaining(expiresAt)}
            </Text>
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: TeamInvitation) => {
        const expired = isExpired(record.expiresAt);
        
        return (
          <Space>
            {!expired && (
              <Button
                type="link"
                icon={<MailOutlined />}
                onClick={() => handleResendInvitation(record.id)}
              >
                Resend
              </Button>
            )}
            
            <Popconfirm
              title="Are you sure you want to cancel this invitation?"
              onConfirm={() => handleCancelInvitation(record.id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
              >
                Cancel
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  if (invitations.length === 0 && !loading) {
    return (
      <Empty
        description="No pending invitations"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div>
      <Table
        columns={columns}
        dataSource={invitations}
        rowKey="id"
        loading={loading}
        pagination={{
          total: invitations.length,
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} invitations`,
        }}
        rowClassName={(record) => {
          return isExpired(record.expiresAt) ? 'expired-row' : '';
        }}
      />
      
      <style>{`
        .expired-row {
          background-color: #fff2f0;
        }
        .expired-row:hover {
          background-color: #ffebe6 !important;
        }
      `}</style>
    </div>
  );
};

export default PendingInvitations; 