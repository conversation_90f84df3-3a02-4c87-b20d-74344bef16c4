import React, { useEffect } from 'react';
import { notification } from 'antd';
import {
  CheckCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';

type NotificationType = 'success' | 'info' | 'warning' | 'error' | 'loading';

interface ToastProps {
  type: NotificationType;
  message: string;
  description?: string;
  duration?: number;
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
  onClose?: () => void;
  key?: string;
}

// 扩展 FC 类型，添加静态方法
interface ToastComponent extends React.FC<ToastProps> {
  show: (props: ToastProps) => React.ReactNode;
  success: (message: string, description?: string, duration?: number) => React.ReactNode;
  info: (message: string, description?: string, duration?: number) => React.ReactNode;
  warning: (message: string, description?: string, duration?: number) => React.ReactNode;
  error: (message: string, description?: string, duration?: number) => React.ReactNode;
  loading: (message: string, description?: string, key?: string) => React.ReactNode;
}

const Toast: ToastComponent = ({
  type,
  message,
  description,
  duration = 3,
  placement = 'topRight',
  onClose,
  key
}) => {
  useEffect(() => {
    // 根据类型选择图标
    const getIcon = () => {
      switch (type) {
        case 'success':
          return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
        case 'info':
          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
        case 'warning':
          return <WarningOutlined style={{ color: '#faad14' }} />;
        case 'error':
          return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
        case 'loading':
          return <LoadingOutlined style={{ color: '#1890ff' }} />;
        default:
          return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      }
    };

    // 显示通知
    notification.open({
      message,
      description,
      icon: getIcon(),
      duration,
      placement,
      onClose,
      key
    });

    // 清理函数
    return () => {
      if (key) {
        // 使用 notification.destroy 替代 close
        notification.destroy(key);
      }
    };
  }, [type, message, description, duration, placement, onClose, key]);

  // 组件不渲染任何内容
  return null;
};

// 静态方法
Toast.show = (props: ToastProps) => {
  const ToastComponent = () => <Toast {...props} />;
  return <ToastComponent />;
};

Toast.success = (message: string, description?: string, duration?: number) => {
  return Toast.show({ type: 'success', message, description, duration });
};

Toast.info = (message: string, description?: string, duration?: number) => {
  return Toast.show({ type: 'info', message, description, duration });
};

Toast.warning = (message: string, description?: string, duration?: number) => {
  return Toast.show({ type: 'warning', message, description, duration });
};

Toast.error = (message: string, description?: string, duration?: number) => {
  return Toast.show({ type: 'error', message, description, duration });
};

Toast.loading = (message: string, description?: string, key?: string) => {
  return Toast.show({ type: 'loading', message, description, duration: 0, key });
};

export default Toast;
