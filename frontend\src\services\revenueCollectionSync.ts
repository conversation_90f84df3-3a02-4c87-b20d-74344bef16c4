/**
 * Revenue Collection Sync Service
 * 基于实际回款金额计算收款进度百分比
 */

export interface TransactionData {
  id: string;
  transactionId: string;
  type: 'income' | 'expense';
  revenueItem?: string;
  project?: string | string[];
  status: string;
  totalAmount: number;
  date: string;
  lastModified: string;
}

export interface RevenueItemData {
  id: string;
  name: string;
  amount: number;
  paymentStatus: string;
  collectionProgress?: number;
  totalReceived?: number;
  totalPending?: number;
  [key: string]: any;
}

export interface CollectionDetails {
  status: string;
  progress: number;
  totalAmount: number;
  receivedAmount: number;
  pendingAmount: number;
  overdueAmount: number;
}

/**
 * 解析项目ID - 处理项目名称到ID的转换
 */
function resolveProjectId(projectInput: string | string[]): string | null {
  if (!projectInput) return null;
  
  // 如果是数组，取第一个元素
  const projectStr = Array.isArray(projectInput) ? projectInput[0] : projectInput;
  
  if (!projectStr || typeof projectStr !== 'string') return null;
  
  console.log(`🔍 Resolving project ID from input: "${projectStr}"`);
  
  // 🔑 新增：从项目名称查找项目ID
  try {
    const projectsData = localStorage.getItem('edm_projects');
    if (projectsData) {
      const projects = JSON.parse(projectsData);
      const matchedProject = projects.find((p: any) => 
        p.name === projectStr || 
        p.id === projectStr ||
        p.name?.toLowerCase() === projectStr.toLowerCase()
      );
      if (matchedProject && matchedProject.id) {
        console.log(`✅ Found project ID "${matchedProject.id}" for name "${projectStr}"`);
        return matchedProject.id;
      }
    }
  } catch (e) {
    console.error('Error reading projects from localStorage:', e);
  }
  
  // 如果找不到匹配的项目，直接使用输入值
  console.log(`⚠️ Using input "${projectStr}" as project ID (no match found)`);
  return projectStr;
}

/**
 * 查找指定Revenue Item的相关Transactions
 */
function findTransactionsForRevenueItem(revenueItemName: string, projectId: string): TransactionData[] {
  try {
    const transactions = JSON.parse(localStorage.getItem('finance_transactions') || '[]');
    console.log(`🔍 Searching transactions for Revenue Item: "${revenueItemName}" in project: "${projectId}"`);
    
    const matchedTransactions = transactions.filter((tx: TransactionData) => {
      // 必须是income类型
      if (tx.type !== 'income') return false;
      
      // 必须有revenueItem
      if (!tx.revenueItem || tx.revenueItem === 'Others') return false;
      
      // Revenue Item匹配
      const revenueItemMatch = 
        tx.revenueItem === revenueItemName ||
        tx.revenueItem.includes(revenueItemName) ||
        revenueItemName.includes(tx.revenueItem) ||
        tx.revenueItem.startsWith(revenueItemName.split(' ')[0]);
      
      if (!revenueItemMatch) return false;
      
      // 项目匹配
      if (!tx.project) return false;
      const txProjectId = resolveProjectId(tx.project);
      const projectMatch = 
        txProjectId === projectId ||
        tx.project === projectId ||
        (typeof tx.project === 'string' && resolveProjectId(tx.project) === projectId);
      
      if (projectMatch) {
        console.log(`✅ Found matching transaction: ${tx.transactionId} - €${tx.totalAmount} (${tx.status})`);
      }
      
      return projectMatch;
    });
    
    console.log(`📊 Found ${matchedTransactions.length} matching transactions for "${revenueItemName}"`);
    return matchedTransactions;
    
  } catch (error) {
    console.error('Error finding transactions:', error);
    return [];
  }
}

/**
 * 计算Revenue Item的收款详情
 */
function calculateCollectionDetails(transactions: TransactionData[], totalAmount: number): CollectionDetails {
  let receivedAmount = 0;
  let pendingAmount = 0;
  let overdueAmount = 0;
  let cancelledAmount = 0;
  
  console.log(`💰 Calculating collection details for ${transactions.length} transactions, Total Amount: €${totalAmount}`);
  
  transactions.forEach(tx => {
    console.log(`📊 Processing transaction: ${tx.transactionId} - €${tx.totalAmount} (${tx.status})`);
    
    // 🔑 修复状态判断：支持大小写不敏感的状态匹配
    const normalizedStatus = tx.status.toLowerCase();
    console.log(`🔍 Normalized status: "${normalizedStatus}"`);
    
    if (['completed', 'paid', 'received', 'confirmed'].includes(normalizedStatus)) {
      receivedAmount += tx.totalAmount;
      console.log(`✅ Added €${tx.totalAmount} to received (now €${receivedAmount})`);
      console.log(`🎯 Status "${normalizedStatus}" matched RECEIVED category`);
    } else if (['failed', 'overdue', 'rejected'].includes(normalizedStatus)) {
      overdueAmount += tx.totalAmount;
      console.log(`❌ Added €${tx.totalAmount} to overdue (now €${overdueAmount})`);
      console.log(`🎯 Status "${normalizedStatus}" matched OVERDUE category`);
    } else if (['cancelled', 'canceled'].includes(normalizedStatus)) {
      cancelledAmount += tx.totalAmount;
      console.log(`🚫 Transaction cancelled - €${tx.totalAmount} not counted in revenue (total cancelled: €${cancelledAmount})`);
      console.log(`🎯 Status "${normalizedStatus}" matched CANCELLED category (excluded from revenue)`);
    } else {
      pendingAmount += tx.totalAmount;
      console.log(`⏳ Added €${tx.totalAmount} to pending (now €${pendingAmount})`);
      console.log(`🎯 Status "${normalizedStatus}" matched PENDING category`);
    }
  });
  
  const progress = totalAmount > 0 ? Math.round((receivedAmount / totalAmount) * 100) : 0;
  
  console.log(`📈 Final calculation: ${receivedAmount}/${totalAmount} = ${progress}%`);
  console.log(`📊 Breakdown: Received=€${receivedAmount}, Pending=€${pendingAmount}, Overdue=€${overdueAmount}, Cancelled=€${cancelledAmount} (excluded)`);
  
  // 确定状态
  let status = 'Pending';
  if (progress === 100) {
    status = 'Paid';
  } else if (overdueAmount > 0) {
    status = 'Overdue';
  } else if (receivedAmount > 0) {
    status = 'Partial Payment';
  } else if (pendingAmount > 0) {
    status = 'Invoiced';
  } else if (cancelledAmount > 0 && receivedAmount === 0 && pendingAmount === 0) {
    status = 'Cancelled';
  }
  
  console.log(`🎯 Status determined: ${status}`);
  
  return {
    status,
    progress: Math.min(progress, 100),
    totalAmount,
    receivedAmount,
    pendingAmount: Math.max(0, totalAmount - receivedAmount),
    overdueAmount
  };
}

/**
 * 更新单个Revenue Item的收款进度
 */
export function updateRevenueItemProgress(revenueItem: RevenueItemData, projectId: string): boolean {
  console.log(`🔄 Updating progress for Revenue Item: "${revenueItem.name}" in project: ${projectId}`);
  
  // 查找相关的transactions
  const transactions = findTransactionsForRevenueItem(revenueItem.name, projectId);
  
  if (transactions.length === 0) {
    console.log(`ℹ️ No transactions found for "${revenueItem.name}" - keeping current progress`);
    return false;
  }
  
  // 获取Revenue Item总金额 - 🔑 关键修复：正确计算MRC的统计期总金额
  let totalAmount = 0;
  
  if ((revenueItem as any).type === 'mrc' || (revenueItem as any).cycle) {
    // 对于MRC项目，计算统计期内的真实总收入
    const mrcItem = revenueItem as any;
    const unitPrice = mrcItem.unitPrice || mrcItem.amount || 0;
    
    if (mrcItem.startDate && mrcItem.endDate) {
      // 基于实际日期计算期数
      const startDate = new Date(mrcItem.startDate);
      const endDate = new Date(mrcItem.endDate);
      
      let totalCycles = 0;
      const cycle = mrcItem.cycle || 'monthly';
      
      if (cycle === 'monthly') {
        const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                          (endDate.getMonth() - startDate.getMonth()) + 1;
        totalCycles = Math.max(0, monthsDiff);
      } else if (cycle === 'quarterly') {
        const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                          (endDate.getMonth() - startDate.getMonth());
        totalCycles = Math.max(0, Math.ceil(monthsDiff / 3));
      } else if (cycle === 'annually') {
        totalCycles = Math.max(0, endDate.getFullYear() - startDate.getFullYear() + 1);
      }
      
      totalAmount = unitPrice * totalCycles;
      console.log(`📊 MRC "${revenueItem.name}": ${unitPrice} × ${totalCycles} ${cycle} cycles = €${totalAmount}`);
    } else {
      // 如果没有日期信息，使用已保存的totalCycleRevenue或fallback
      totalAmount = mrcItem.totalCycleRevenue || unitPrice || 0;
      console.log(`📊 MRC "${revenueItem.name}": Using stored totalCycleRevenue = €${totalAmount}`);
    }
  } else {
    // 对于NRC项目，使用amount或totalAmount
    totalAmount = revenueItem.amount || revenueItem.totalAmount || 0;
    console.log(`📊 NRC "${revenueItem.name}": Using amount = €${totalAmount}`);
  }
  
  console.log(`💰 Revenue Item "${revenueItem.name}" - Total Amount (VAT-EXCLUDED): €${totalAmount} (from field: ${revenueItem.amount ? 'amount' : (revenueItem as any).unitPrice ? 'unitPrice*12' : revenueItem.totalAmount ? 'totalAmount' : 'totalCycleRevenue'})`);
  
  // 🔑 重要修复：Transaction金额本身就是实际收款金额，不需要VAT调整
  // Transaction记录的是实际收到的钱，不管是含税还是不含税
  console.log(`💰 Processing ${transactions.length} transactions with actual received amounts (no VAT adjustment needed)`);
  
  // 直接使用transaction的实际金额，不进行VAT调整
  const details = calculateCollectionDetails(transactions, totalAmount);
  
  // 检查是否需要更新
  const currentProgress = revenueItem.collectionProgress || 0;
  const needsUpdate = currentProgress !== details.progress;
  
  if (needsUpdate) {
    // 更新Revenue Item数据
    revenueItem.collectionProgress = details.progress;
    revenueItem.paymentStatus = details.status;
    revenueItem.totalReceived = details.receivedAmount;
    revenueItem.totalPending = details.pendingAmount;
    
    console.log(`✅ Updated "${revenueItem.name}": ${currentProgress}% → ${details.progress}%`);
    return true;
  } else {
    console.log(`ℹ️ No update needed for "${revenueItem.name}" (already ${details.progress}%)`);
    return false;
  }
}

/**
 * 同步指定项目的所有Revenue Item进度
 */
export function syncAllRevenueItemProgresses(projectId: string): { total: number; updated: number } {
  console.log(`🔄 Starting full sync for project: ${projectId}`);
  
  let totalItems = 0;
  let updatedItems = 0;
  
  try {
    // 处理NRC items
    const nrcKey = `nrc_items_${projectId}`;
    const nrcItems = JSON.parse(localStorage.getItem(nrcKey) || '[]');
    
    if (nrcItems.length > 0) {
      console.log(`📊 Processing ${nrcItems.length} NRC items`);
      
      nrcItems.forEach((item: RevenueItemData) => {
        totalItems++;
        if (updateRevenueItemProgress(item, projectId)) {
          updatedItems++;
        }
      });
      
      // 保存更新后的NRC数据
      localStorage.setItem(nrcKey, JSON.stringify(nrcItems));
      console.log(`💾 Saved updated NRC data to ${nrcKey}`);
    }
    
    // 处理MRC items
    const mrcKey = `mrc_items_${projectId}`;
    const mrcItems = JSON.parse(localStorage.getItem(mrcKey) || '[]');
    
    if (mrcItems.length > 0) {
      console.log(`📊 Processing ${mrcItems.length} MRC items`);
      
      mrcItems.forEach((item: RevenueItemData) => {
        totalItems++;
        if (updateRevenueItemProgress(item, projectId)) {
          updatedItems++;
        }
      });
      
      // 保存更新后的MRC数据
      localStorage.setItem(mrcKey, JSON.stringify(mrcItems));
      console.log(`💾 Saved updated MRC data to ${mrcKey}`);
    }
    
  } catch (error) {
    console.error('Error during sync:', error);
  }
  
  console.log(`🎯 Sync completed: ${updatedItems}/${totalItems} items updated`);
  return { total: totalItems, updated: updatedItems };
}

/**
 * Transaction状态变化时的回调函数
 */
export function onTransactionStatusChanged(transaction: TransactionData): void {
  console.log(`🔔 Transaction status changed: ${transaction.transactionId} (${transaction.status})`);
  
  // 只处理income类型的transaction
  if (transaction.type !== 'income' || !transaction.revenueItem || transaction.revenueItem === 'Others') {
    console.log('ℹ️ Skipping non-income or unlinked transaction');
    return;
  }
  
  // 解析项目ID
  if (!transaction.project) {
    console.warn('⚠️ Transaction has no project information');
    return;
  }
  const projectId = resolveProjectId(transaction.project);
  if (!projectId) {
    console.warn('⚠️ Cannot resolve project ID from transaction');
    return;
  }
  
  console.log(`🔄 Triggering sync for project: ${projectId}`);
  
  // 触发同步
  const result = syncAllRevenueItemProgresses(projectId);
  
  // 分发事件通知UI更新
  const event = new CustomEvent('revenueCollectionSync', {
    detail: {
      projectId,
      transactionId: transaction.transactionId,
      revenueItem: transaction.revenueItem,
      result
    }
  });
  
  window.dispatchEvent(event);
  console.log(`📡 Dispatched revenueCollectionSync event for project ${projectId}`);
} 