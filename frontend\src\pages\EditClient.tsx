import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  DatePicker,
  InputNumber,
  Row,
  Col,
  Space,
  Typography,
  message,
  Spin,
  Tag,
  Divider
} from 'antd';
import dayjs from 'dayjs';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  BankOutlined,
  PlusOutlined,
  DeleteOutlined,
  HistoryOutlined,
  LoadingOutlined,
  DollarOutlined,
  PoundCircleOutlined,
  EuroOutlined
} from '@ant-design/icons';
import clientService from '../services/client.service';
import projectService from '../services/new-project.service';
import { useCurrency } from '../contexts/CurrencyContext';
import { dataSyncEngine } from '../services/dataSyncEngine';

const { Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const EditClient: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [contactRecords, setContactRecords] = useState<Array<{
    id: string;
    date: string;
    clientContactPerson: string;
    internalContactPerson: string;
    notes: string;
  }>>([]);
  const [calculatedRevenue, setCalculatedRevenue] = useState<number>(0);
  const { selectedCurrency } = useCurrency();

  // 监听客户名称变化并计算实际收入
  const calculateClientRevenue = useCallback(async (clientName: string) => {
    if (!clientName || clientName.trim() === '') {
      setCalculatedRevenue(0);
      form.setFieldsValue({ totalRevenue: 0 });
      return;
    }

    try {
      const projects = await projectService.getAllProjects();
      const clientProjects = projects.filter(
        project => project.client.toLowerCase() === clientName.toLowerCase()
      );
      
      const totalRevenue = clientProjects.reduce((sum, project) => {
        return sum + (project.revenue || 0);
      }, 0);

      setCalculatedRevenue(totalRevenue);
      form.setFieldsValue({ totalRevenue: totalRevenue });
    } catch (error) {
      console.error('Failed to calculate client revenue:', error);
    }
  }, [form]);

  // 获取货币图标
  const getCurrencyIcon = () => {
    switch (selectedCurrency) {
      case 'USD':
        return <DollarOutlined />;
      case 'EUR':
        return <EuroOutlined />;
      case 'GBP':
        return <PoundCircleOutlined />;
      default:
        return <DollarOutlined />;
    }
  };

  // 简化的国家变化处理
  const handleCountryChange = useCallback((country: string) => {
    // 只更新表单值，不再处理电话号码前缀
  }, []);

  // 加载客户数据
  const loadClientData = useCallback(async (clientId: string) => {
    try {
      setDataLoading(true);
      const client = await clientService.getClient(clientId);
      
      // 填充表单数据
      form.setFieldsValue({
        name: client.name,
        email: client.email,
        phone: client.phone,
        industry: client.industry,
        country: client.country,
        tier: client.tier,
        status: client.status,
        contactPerson: client.contactPerson,
        contactEmail: client.contactEmail,
        contactPhone: client.contactPhone,
        totalRevenue: client.totalRevenue,
        website: client.website,
        address: client.address,
        tags: client.tags || []
      });

      // 加载联系记录
      setContactRecords((client as any).contactRecords || []);
      
      // 计算实际收入
      if (client.name) {
        await calculateClientRevenue(client.name);
      }
    } catch (error) {
      console.error('Failed to load client:', error);
      message.error('Failed to load client data');
      navigate('/client');
    } finally {
      setDataLoading(false);
    }
  }, [form, navigate, calculateClientRevenue]);

  useEffect(() => {
    if (id) {
      loadClientData(id);
    }
  }, [id, loadClientData]);

  // 添加新的接触记录
  const addContactRecord = useCallback(() => {
    const newRecord = {
      id: Date.now().toString(),
      date: new Date().toISOString().split('T')[0],
      clientContactPerson: '',
      internalContactPerson: '',
      notes: ''
    };
    setContactRecords(prev => [...prev, newRecord]);
  }, []);

  // 删除接触记录
  const removeContactRecord = useCallback((recordId: string) => {
    setContactRecords(prev => prev.filter(record => record.id !== recordId));
  }, []);

  // 更新接触记录
  const updateContactRecord = useCallback((recordId: string, field: 'date' | 'clientContactPerson' | 'internalContactPerson' | 'notes', value: string) => {
    setContactRecords(prev => prev.map(record => 
      record.id === recordId ? { ...record, [field]: value } : record
    ));
  }, []);

  // 处理表单提交
  const handleSubmit = useCallback(async (values: any) => {
    if (!id) return;

    try {
      setLoading(true);
      
      // 获取原始客户数据用于比较
      const originalClient = await clientService.getClient(id);
      
      const clientData = {
        ...values,
        contactRecords: contactRecords.filter(record => record.notes.trim() !== ''),
        updatedAt: new Date().toISOString()
      };

      // 更新客户信息
      await clientService.updateClient(id, clientData);
      
      // 触发数据同步事件
      const changeEvent = dataSyncEngine.createEvent(
        'CLIENT_UPDATED',
        id,
        'client',
        {
          name: values.name,
          tier: values.tier,
          country: values.country,
          contactPerson: values.contactPerson,
          totalRevenue: values.totalRevenue
        },
        'EditClient',
        originalClient,
        clientData
      );
      
      await dataSyncEngine.emitEvent(changeEvent);
      
      message.success('Client updated successfully! Related projects will be synchronized automatically.');
      navigate('/client');
    } catch (error) {
      console.error('Failed to update client:', error);
      message.error('Failed to update client. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [id, contactRecords, navigate]);

  // 行业选项
  const industries = [
    'Technology', 'Financial Services', 'Healthcare', 'Manufacturing', 'Retail',
    'Education', 'Food & Beverage', 'Hospitality', 'Real Estate', 'Construction',
    'Transportation', 'Energy', 'Telecommunications', 'Media & Entertainment',
    'Government', 'Non-profit', 'Agriculture', 'Automotive', 'Aerospace',
    'Consulting', 'Legal', 'Insurance', 'Pharmaceuticals', 'Chemicals',
    'Textiles', 'Mining', 'Sports & Fitness', 'Beauty & Personal Care',
    'Gaming', 'E-commerce', 'Other'
  ];

  // 国家选项
  const countries = [
    'United States', 'Germany', 'United Kingdom', 'France', 'Canada',
    'Australia', 'Japan', 'China', 'India', 'Brazil', 'Netherlands',
    'Sweden', 'Switzerland', 'Spain', 'Italy', 'South Korea', 'Singapore',
    'Belgium', 'Denmark', 'Norway', 'Finland', 'Austria', 'Ireland', 'Other'
  ];

  if (dataLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/client')}
          style={{ marginBottom: '16px' }}
        >
          Back to Client List
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          Edit Client
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        scrollToFirstError
      >
        <Row gutter={24}>
          {/* 左侧列 - 基本信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  <span>Basic Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label="Client ID"
                name="clientId"
                rules={[
                  { required: true, message: 'Please enter client ID' },
                  { pattern: /^CLI-\d{4}-\d{3}$/, message: 'Client ID format should be CLI-YYYY-XXX' }
                ]}
                tooltip="Unique identifier for the client. Format: CLI-YEAR-XXX"
              >
                <Input 
                  placeholder="CLI-2025-001"
                  readOnly
                />
              </Form.Item>

              <Form.Item
                label={<span>Client Name <span style={{ color: 'red' }}>*</span></span>}
                name="name"
                rules={[
                  { required: true, message: 'Please enter client name' },
                  { min: 2, message: 'Client name must be at least 2 characters' }
                ]}
                tooltip="The full name of the client contact person"
              >
                <Input 
                  placeholder="Enter client name"
                  prefix={<UserOutlined />}
                  onChange={(e) => calculateClientRevenue(e.target.value)}
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={<span>Client Tier <span style={{ color: 'red' }}>*</span></span>}
                    name="tier"
                    rules={[{ required: true, message: 'Please select client tier' }]}
                    tooltip="Client tier based on business value and potential"
                  >
                    <Select placeholder="Select tier">
                      <Option value="S">
                        <Tag color="purple">S</Tag> - Strategic Client
                      </Option>
                      <Option value="V">
                        <Tag color="gold">V</Tag> - Valued Client
                      </Option>
                      <Option value="B">
                        <Tag color="blue">B</Tag> - Business Client
                      </Option>
                      <Option value="A">
                        <Tag color="green">A</Tag> - Average Client
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span>Status <span style={{ color: 'red' }}>*</span></span>}
                    name="status"
                    rules={[{ required: true, message: 'Please select status' }]}
                    tooltip="Current relationship status with the client"
                  >
                    <Select placeholder="Select status">
                      <Option value="active">
                        <Tag color="green">Active</Tag>
                      </Option>
                      <Option value="potential">
                        <Tag color="blue">Potential</Tag>
                      </Option>
                      <Option value="inactive">
                        <Tag color="red">Inactive</Tag>
                      </Option>
                      <Option value="churned">
                        <Tag color="volcano">Churned</Tag>
                      </Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label={<span>Industry <span style={{ color: 'red' }}>*</span></span>}
                    name="industry"
                    rules={[{ required: true, message: 'Please select industry' }]}
                    tooltip="Primary industry or business sector"
                  >
                    <Select
                      placeholder="Type to search industry..."
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      <Option value="Technology">💻 Technology</Option>
                      <Option value="Financial Services">🏦 Financial Services</Option>
                      <Option value="Healthcare">🏥 Healthcare</Option>
                      <Option value="Manufacturing">🏭 Manufacturing</Option>
                      <Option value="Retail">🛍️ Retail</Option>
                      <Option value="Education">🎓 Education</Option>
                      <Option value="Food & Beverage">🍽️ Food & Beverage</Option>
                      <Option value="Hospitality">🏨 Hospitality</Option>
                      <Option value="Real Estate">🏢 Real Estate</Option>
                      <Option value="Construction">🏗️ Construction</Option>
                      <Option value="Transportation">🚛 Transportation</Option>
                      <Option value="Energy">⚡ Energy</Option>
                      <Option value="Telecommunications">📡 Telecommunications</Option>
                      <Option value="Media & Entertainment">🎬 Media & Entertainment</Option>
                      <Option value="Government">🏛️ Government</Option>
                      <Option value="Non-profit">❤️ Non-profit</Option>
                      <Option value="Agriculture">🌾 Agriculture</Option>
                      <Option value="Automotive">🚗 Automotive</Option>
                      <Option value="Aerospace">✈️ Aerospace</Option>
                      <Option value="Consulting">💼 Consulting</Option>
                      <Option value="Legal">⚖️ Legal</Option>
                      <Option value="Insurance">🛡️ Insurance</Option>
                      <Option value="Pharmaceuticals">💊 Pharmaceuticals</Option>
                      <Option value="Chemicals">🧪 Chemicals</Option>
                      <Option value="Textiles">👕 Textiles</Option>
                      <Option value="Mining">⛏️ Mining</Option>
                      <Option value="Sports & Fitness">🏃 Sports & Fitness</Option>
                      <Option value="Beauty & Personal Care">💄 Beauty & Personal Care</Option>
                      <Option value="Gaming">🎮 Gaming</Option>
                      <Option value="E-commerce">🛒 E-commerce</Option>
                      <Option value="Other">📋 Other</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={<span>Country <span style={{ color: 'red' }}>*</span></span>}
                    name="country"
                    rules={[{ required: true, message: 'Please select country' }]}
                    tooltip="Primary operating country"
                  >
                    <Select
                      placeholder="Select country"
                      showSearch
                      onChange={handleCountryChange}
                      filterOption={(input, option) =>
                        String(option?.children || '')?.toLowerCase().includes(input.toLowerCase())
                      }
                    >
                      {countries.map(country => (
                        <Option key={country} value={country}>
                          {country}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="Client Tags"
                name="tags"
                tooltip="Keywords or labels to categorize this client"
              >
                <Select
                  mode="tags"
                  placeholder="Add tags (select from presets or type custom tags)"
                  style={{ width: '100%' }}
                  tokenSeparators={[',']}
                  options={[
                    { label: '🎯 Strategic', value: 'Strategic' },
                    { label: '💎 High-Value', value: 'High-Value' },
                    { label: '⏰ Long-term', value: 'Long-term' },
                    { label: '👑 Premium', value: 'Premium' },
                    { label: '⭐ VIP', value: 'VIP' },
                    { label: '🏢 Enterprise', value: 'Enterprise' },
                    { label: '🏪 SMB', value: 'SMB' },
                    { label: '🚀 Startup', value: 'Startup' },
                    { label: '🏛️ Government', value: 'Government' },
                    { label: '❤️ Non-profit', value: 'Non-profit' },
                    { label: '🌍 International', value: 'International' },
                    { label: '🏠 Domestic', value: 'Domestic' },
                    { label: '💻 Technology', value: 'Technology' },
                    { label: '💰 Finance', value: 'Finance' },
                    { label: '⚕️ Healthcare', value: 'Healthcare' },
                    { label: '🏭 Manufacturing', value: 'Manufacturing' },
                    { label: '🛍️ Retail', value: 'Retail' },
                    { label: '🎓 Education', value: 'Education' },
                    { label: '🏛️ Recurring', value: 'Recurring' },
                    { label: '⚡ Quick-Response', value: 'Quick-Response' }
                  ]}
                />
              </Form.Item>
            </Card>

            {/* 财务信息 */}
            <Card 
              title={
                <Space>
                  <BankOutlined />
                  <span>Business Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label="Website"
                name="website"
                tooltip="Company website URL"
                rules={[
                  { type: 'url', message: 'Please enter a valid URL' }
                ]}
              >
                <Input 
                  placeholder="https://example.com"
                  prefix={<GlobalOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Business Address"
                name="address"
                tooltip="Primary business address"
              >
                <TextArea 
                  placeholder="Enter business address"
                  rows={3}
                />
              </Form.Item>

              <Form.Item
                label={`Total Revenue (${selectedCurrency})`}
                name="totalRevenue"
                tooltip={`Calculated based on all associated projects in ${selectedCurrency}`}
                extra={calculatedRevenue > 0 ? `Auto-calculated from ${calculatedRevenue > 0 ? 'existing' : 'no'} projects` : undefined}
              >
                <InputNumber
                  placeholder="Auto-calculated from projects"
                  style={{ width: '100%' }}
                  min={0}
                  precision={0}
                  addonBefore={getCurrencyIcon()}
                  readOnly
                  disabled
                  formatter={value => {
                    if (!value) return '';
                    return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  }}
                  parser={(value: any) => {
                    if (!value) return 0;
                    const cleanValue = value.replace(/[€$£¥\s,]/g, '');
                    const parsed = parseFloat(cleanValue);
                    return isNaN(parsed) ? 0 : parsed;
                  }}
                />
              </Form.Item>
            </Card>
          </Col>

          {/* 右侧列 - 联系信息 */}
          <Col span={12}>
            <Card 
              title={
                <Space>
                  <PhoneOutlined />
                  <span>Contact Information</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
            >
              <Form.Item
                label={<span>Primary Contact Person <span style={{ color: 'red' }}>*</span></span>}
                name="contactPerson"
                rules={[
                  { required: true, message: 'Please enter contact person name' },
                  { min: 2, message: 'Contact person name must be at least 2 characters' }
                ]}
                tooltip="Main point of contact at the client organization"
              >
                <Input 
                  placeholder="Enter contact person name"
                  prefix={<UserOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Primary Email"
                name="email"
                rules={[
                  { type: 'email', message: 'Please enter a valid email address' }
                ]}
                tooltip="Primary email for business communications"
              >
                <Input 
                  placeholder="<EMAIL>"
                  prefix={<MailOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Primary Phone"
                name="phone"
                tooltip="Primary phone number for business communications"
              >
                <Input 
                  placeholder="******-123-4567"
                />
              </Form.Item>

              <Divider orientation="left">
                <Text strong>Alternative Contact</Text>
              </Divider>

              <Form.Item
                label="Contact Person Email"
                name="contactEmail"
                rules={[
                  { type: 'email', message: 'Please enter a valid email address' }
                ]}
                tooltip="Alternative email for the contact person"
              >
                <Input 
                  placeholder="<EMAIL>"
                  prefix={<MailOutlined />}
                />
              </Form.Item>

              <Form.Item
                label="Contact Person Phone"
                name="contactPhone"
                tooltip="Direct phone number for the contact person"
              >
                <Input 
                  placeholder="******-987-6543"
                />
              </Form.Item>
            </Card>

            {/* Contact Log */}
            <Card 
              title={
                <Space>
                  <HistoryOutlined />
                  <span>Contact Log</span>
                </Space>
              }
              style={{ marginBottom: '24px' }}
              extra={
                <Button
                  type="primary"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={addContactRecord}
                >
                  Add Record
                </Button>
              }
            >
              {contactRecords.length === 0 ? (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '40px 20px',
                  background: '#fafafa',
                  borderRadius: '8px',
                  border: '1px dashed #d9d9d9'
                }}>
                  <HistoryOutlined style={{ fontSize: '24px', color: '#d9d9d9', marginBottom: '8px' }} />
                  <div style={{ color: '#999', marginBottom: '16px' }}>No contact records yet</div>
                  <Button
                    type="dashed"
                    icon={<PlusOutlined />}
                    onClick={addContactRecord}
                  >
                    Add First Record
                  </Button>
                </div>
              ) : (
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {/* Header Row */}
                  <div style={{
                    display: 'flex',
                    backgroundColor: '#fafafa',
                    padding: '12px 16px',
                    borderRadius: '6px',
                    marginBottom: '12px',
                    border: '1px solid #e8e8e8',
                    fontWeight: 'bold',
                    fontSize: '11px',
                    color: '#666'
                  }}>
                    <div style={{ width: '120px', flexShrink: 0 }}>Date</div>
                    <div style={{ flex: '1', marginLeft: '12px' }}>Client Contact</div>
                    <div style={{ flex: '1', marginLeft: '12px' }}>Internal Contact</div>
                    <div style={{ flex: '1.5', marginLeft: '12px' }}>Notes</div>
                    <div style={{ width: '40px', textAlign: 'center', marginLeft: '12px' }}>Action</div>
                  </div>

                  {contactRecords.map((record) => (
                    <div
                      key={record.id}
                      style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        padding: '12px 16px',
                        border: '1px solid #e8e8e8',
                        borderRadius: '6px',
                        marginBottom: '8px',
                        backgroundColor: '#ffffff',
                        transition: 'box-shadow 0.2s',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    >
                      <div style={{ width: '120px', flexShrink: 0 }}>
                        <DatePicker
                          style={{ width: '100%' }}
                          value={record.date ? dayjs(record.date) : null}
                          onChange={(date) => {
                            const dateStr = date ? date.format('YYYY-MM-DD') : '';
                            updateContactRecord(record.id, 'date', dateStr);
                          }}
                          placeholder="Select date"
                          size="small"
                          format="DD-MM-YYYY"
                        />
                      </div>
                      <div style={{ flex: '1', marginLeft: '12px' }}>
                        <Input
                          placeholder="Client contact"
                          value={record.clientContactPerson}
                          onChange={(e) => updateContactRecord(record.id, 'clientContactPerson', e.target.value)}
                          size="small"
                        />
                      </div>
                      <div style={{ flex: '1', marginLeft: '12px' }}>
                        <Input
                          placeholder="Internal contact"
                          value={record.internalContactPerson}
                          onChange={(e) => updateContactRecord(record.id, 'internalContactPerson', e.target.value)}
                          size="small"
                        />
                      </div>
                      <div style={{ flex: '1.5', marginLeft: '12px' }}>
                        <Input
                          value={record.notes}
                          onChange={(e) => updateContactRecord(record.id, 'notes', e.target.value)}
                          placeholder="Notes and details..."
                          size="small"
                        />
                      </div>
                      <div style={{ width: '40px', textAlign: 'center', marginLeft: '12px' }}>
                        <Button
                          type="text"
                          size="small"
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => removeContactRecord(record.id)}
                          style={{ 
                            minWidth: 'auto',
                            padding: '4px',
                            height: '24px',
                            width: '24px'
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </Col>
        </Row>

        {/* 提交按钮 */}
        <Card>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button 
                size="large"
                onClick={() => navigate('/client')}
              >
                Cancel
              </Button>
              <Button 
                type="primary" 
                size="large"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                Update Client
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default EditClient; 