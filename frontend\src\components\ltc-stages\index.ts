import ProposalStage from './ProposalStage';
import ContractStage from './ContractStage';
import ExecutionStage from './ExecutionStage';
import AcceptanceStage from './AcceptanceStage';
import FinanceStage from './FinanceStage';
import CloseoutStage from './CloseoutStage';
import AfterSalesStage from './AfterSalesStage';
import NewBasicInfo from '../basic-info/BasicInfo';
import BasicInfoFixed from '../basic-info/BasicInfoFixed';
import TestBasicInfo from '../TestBasicInfo';
import NewProposal from '../proposal';
import NewContract from '../contract';

export {
  ProposalStage,
  ContractStage,
  ExecutionStage,
  AcceptanceStage,
  FinanceStage,
  CloseoutStage,
  AfterSalesStage,
  NewBasicInfo,
  BasicInfoFixed,
  TestBasicInfo,
  NewProposal,
  NewContract
};

export const LTC_STAGES = [
  {
    key: 'basic_info',
    title: 'Basic Info',
    component: NewBasicInfo
  },
  {
    key: 'proposal',
    title: 'Proposal',
    component: NewProposal
  },
  {
    key: 'contract',
    title: 'Contract',
    component: NewContract
  },
  {
    key: 'execution',
    title: 'Execution',
    component: ExecutionStage
  },
  {
    key: 'acceptance',
    title: 'Acceptance',
    component: AcceptanceStage
  },
  {
    key: 'finance',
    title: 'Finance',
    component: FinanceStage
  },
  {
    key: 'closeout',
    title: 'Closeout',
    component: CloseoutStage
  },
  {
    key: 'after_sales',
    title: 'After-sales',
    component: AfterSalesStage
  }
];
