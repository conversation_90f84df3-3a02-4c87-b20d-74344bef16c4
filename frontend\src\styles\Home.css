/* 全局样式 */
.home-container {
  min-height: 100vh;
  background-color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #1F2023;
  overflow-x: hidden;
}

/* 顶部导航 */
.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  position: sticky;
  top: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #FF7A00;
}

.nav-buttons {
  display: flex;
  gap: 12px;
}

.nav-login-btn {
  color: #FF7A00;
  font-weight: 500;
}

.nav-login-btn:hover {
  color: #FF7A00 !important;
  background-color: rgba(255, 122, 0, 0.05) !important;
}

.nav-register-btn {
  background-color: #FF7A00;
  border: none;
  color: white;
  font-weight: 500;
}

.nav-register-btn:hover {
  background-color: #FF8C1A !important;
  color: white !important;
}

/* 英雄区域 */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60px 40px;
  max-width: 1200px;
  margin: 0 auto;
  gap: 40px;
}

.hero-content {
  flex: 1;
  max-width: 600px;
}

.hero-title {
  font-size: 48px !important;
  line-height: 1.2 !important;
  margin-bottom: 24px !important;
  font-weight: 700 !important;
  color: #1F2023;
}

.hero-description {
  font-size: 20px !important;
  line-height: 1.6 !important;
  margin-bottom: 40px !important;
  color: #4B5563;
}

.highlight {
  color: #FF7A00;
  font-weight: 600;
}

.hero-buttons {
  display: flex;
  gap: 16px;
}

.login-btn {
  background-color: #FF7A00;
  border: none;
  color: white;
  font-weight: 500;
  height: 48px;
  border-radius: 8px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.login-btn:hover {
  background-color: #FF8C1A !important;
  color: white !important;
}

.register-btn {
  border: 1px solid #FF7A00;
  color: #FF7A00;
  font-weight: 500;
  height: 48px;
  border-radius: 8px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.register-btn:hover {
  border-color: #FF8C1A !important;
  color: #FF8C1A !important;
  background-color: rgba(255, 122, 0, 0.05) !important;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 100%;
  max-width: 500px;
  height: 400px;
  background: linear-gradient(135deg, rgba(255, 122, 0, 0.1), rgba(255, 122, 0, 0.3));
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 特性区域 */
.features-section {
  padding: 80px 40px;
  background-color: #F9FAFB;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  text-align: center;
  margin-bottom: 48px !important;
  font-weight: 600 !important;
  color: #1F2023;
}

.feature-card {
  height: 100%;
  border-radius: 12px;
  border: 1px solid #E5E7EB;
  transition: all 0.3s ease;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  border-color: #FF7A00;
}

.feature-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  background-color: rgba(255, 122, 0, 0.1);
  border-radius: 12px;
  margin-bottom: 16px;
}

.feature-icon {
  font-size: 24px;
  color: #FF7A00;
}

.feature-title {
  margin-bottom: 12px !important;
  font-weight: 600 !important;
  color: #1F2023;
}

.feature-description {
  color: #4B5563;
  display: block;
}

/* 页脚 */
.home-footer {
  background-color: #1F2023;
  color: white;
  padding: 60px 40px 30px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.footer-logo {
  font-size: 20px;
  font-weight: 600;
}

.footer-links {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #E5E7EB;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: #FF7A00;
}

.footer-copyright {
  color: #9CA3AF;
  font-size: 14px;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .hero-section {
    flex-direction: column;
    padding: 40px 20px;
  }

  .hero-content {
    max-width: 100%;
    text-align: center;
  }

  .hero-buttons {
    justify-content: center;
  }

  .hero-title {
    font-size: 36px !important;
  }

  .hero-description {
    font-size: 18px !important;
  }

  .features-section {
    padding: 60px 20px;
  }
}

@media (max-width: 768px) {
  .home-header {
    padding: 16px 20px;
  }

  .feature-card {
    text-align: center;
  }

  .feature-icon-wrapper {
    margin: 0 auto 16px;
  }
}

@media (max-width: 480px) {
  .hero-buttons {
    flex-direction: column;
    width: 100%;
  }

  .login-btn, .register-btn {
    width: 100%;
  }

  .footer-links {
    flex-direction: column;
    gap: 16px;
  }
}
