/* LTC阶段组件样式 */
.ltc-stage-content {
  padding: 24px 0;
}

.ltc-module-card {
  margin-bottom: 16px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

/* Process Section Styles */
.process-section {
  position: relative;
}

.process-title {
  font-size: 16px !important;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
  position: relative;
  display: inline-block;
  margin: 0;
}

/* 只在内容区域显示下划线，Card标题中不显示 */
.process-section .process-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #1890ff;
  border-radius: 3px;
}

.ltc-module-card .ant-card-head {
  border-bottom: 1px solid var(--border);
  padding: 0 24px;
}

.ltc-module-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
}

.ltc-module-card .ant-card-body {
  padding: 24px;
}

/* 步骤指示器样式 - 参照Acceptance Progress设计 */
.ltc-steps {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 32px;
  position: relative;
  padding: 24px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  overflow-x: auto;
}

/* 步骤连接线 - 已移除背景连接线 */
.ltc-steps::before {
  content: '';
  position: absolute;
  top: 40px;
  left: 60px;
  right: 60px;
  height: 2px;
  background: transparent;
  z-index: 0;
}

.ltc-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 16px;
  min-width: 120px;
  flex: 1;
}

.ltc-step:hover {
  transform: translateY(-3px);
}

.ltc-step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 2px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 12px;
  color: #8c8c8c;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 活跃状态 - 使用深橙色主题 */
.ltc-step.active .ltc-step-circle {
  background: #FF7A00;
  border-color: #FF7A00;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.3);
  transform: scale(1.05);
}

.ltc-step.active .ltc-step-circle::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 1px solid rgba(255, 122, 0, 0.3);
  animation: pulse 2s infinite;
}

/* 已完成状态 - 使用浅橙色主题 */
.ltc-step.completed .ltc-step-circle {
  background: #FFF2E8;
  border-color: #FF7A00;
  color: #FF7A00;
  box-shadow: 0 2px 8px rgba(255, 122, 0, 0.15);
}

.ltc-step.completed .ltc-step-circle::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  border: 0.5px solid rgba(255, 122, 0, 0.08);
}

/* 连接线样式 */
.ltc-step-connector {
  position: absolute;
  top: 16px;
  left: calc(100% - 16px);
  width: 32px;
  height: 2px;
  background: transparent;
  z-index: 1;
}

.ltc-step.completed .ltc-step-connector {
  background: linear-gradient(to right, #FF7A00, #FF9500);
}

.ltc-step.active .ltc-step-connector {
  background: linear-gradient(to right, #722ed1, #d9d9d9);
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Finance支线流程样式 */
.finance-process-container {
  position: relative;
  max-width: 1000px;
  margin: 24px auto 0;
  padding-top: 0;
  display: flex;
  justify-content: center;
}

/* 水平连接线 */
.finance-process-connector {
  position: absolute;
  top: 17px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, rgba(114, 46, 209, 0.1), rgba(114, 46, 209, 0.6), rgba(114, 46, 209, 0.1));
  z-index: 0;
}

.finance-process-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0 8px;
}

.finance-process-item:hover {
  transform: translateY(-3px);
}

.finance-process-circle {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-weight: 600;
  color: #722ed1;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(114, 46, 209, 0.1);
  position: relative;
  border: 2px solid #722ed1;
}

.finance-process-circle::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  border: 1px solid rgba(114, 46, 209, 0.3);
  opacity: 0;
  transition: all 0.3s ease;
}

.finance-process-circle:hover {
  background-color: #F3F0FF;
  box-shadow: 0 6px 16px rgba(114, 46, 209, 0.3);
  border-color: #722ed1;
}

.finance-process-circle:hover::after {
  opacity: 1;
  transform: scale(1.1);
}

.finance-process-circle.active {
  background-color: #722ed1;
  color: white !important; /* 确保文本颜色为白色 */
  transform: scale(1.05);
  box-shadow: 0 0 0 6px rgba(114, 46, 209, 0.15), 0 6px 16px rgba(114, 46, 209, 0.3);
  border-color: #722ed1;
}

.finance-process-circle.active span {
  color: white !important; /* 确保€符号为白色 */
}

/* 激活状态下的标签样式 */
.finance-process-label.active {
  color: #722ed1;
  font-weight: 600;
}

.finance-process-label {
  font-size: 12px;
  font-weight: 500;
  color: #722ed1;
  transition: all 0.3s ease;
}

.finance-process-item:hover .finance-process-label {
  color: #722ed1;
  font-weight: 600;
}

.ltc-step-label {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
  white-space: nowrap;
  transition: all 0.3s ease;
  font-weight: 500;
  margin-top: 4px;
  line-height: 1.4;
}

.ltc-step.active .ltc-step-label {
  color: #FF7A00;
  font-weight: 600;
  transform: scale(1.02);
}

.ltc-step.completed .ltc-step-label {
  color: #FF7A00 !important;
  font-weight: 600;
}

/* 语音收集卡片样式 */
.voice-collection-card {
  background-color: var(--primary-light);
  border: none;
}

.voice-collection-card .ant-card-head {
  background-color: transparent;
  border-bottom: none;
}

/* 表格样式 */
.ltc-module-card .ant-table-thead > tr > th {
  background-color: var(--gray-light);
  color: var(--text-primary);
  font-weight: 600;
}

.ltc-module-card .ant-table-tbody > tr:hover > td {
  background-color: var(--hover);
}

.approved-row {
  background-color: rgba(16, 185, 129, 0.05);
}

/* 表单样式 */
.ltc-module-card .ant-form-item-label > label {
  font-weight: 500;
  color: var(--text-primary);
}

/* 标签样式 */
.ltc-module-card .ant-tag {
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 8px;
}

/* 进度条样式 */
.ltc-module-card .ant-progress-text {
  color: var(--text-primary);
  font-weight: 600;
}

/* 统计数字样式 */
.ltc-module-card .ant-statistic-title {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
}

.ltc-module-card .ant-statistic-content {
  color: var(--text-primary);
  font-weight: 600;
}

/* 时间线样式 */
.ltc-module-card .ant-timeline-item-tail {
  border-left: 2px solid var(--border);
}

.ltc-module-card .ant-timeline-item-head {
  background-color: var(--white);
}

/* 选项卡样式 */
.ltc-module-card .ant-tabs-nav::before {
  border-bottom: 1px solid var(--border);
}

.ltc-module-card .ant-tabs-tab {
  padding: 12px 16px;
}

.ltc-module-card .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: var(--primary);
  font-weight: 500;
}

.ltc-module-card .ant-tabs-ink-bar {
  background-color: var(--primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ltc-steps {
    overflow-x: auto;
    padding: 16px 12px;
    margin-bottom: 24px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  .ltc-steps::before {
    left: 50px;
    right: 50px;
    top: 32px;
    background: transparent;
  }

  .ltc-steps::-webkit-scrollbar {
    height: 4px;
  }

  .ltc-steps::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .ltc-steps::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 4px;
  }

  .ltc-step {
    min-width: 100px;
    flex-shrink: 0;
    padding: 0 12px;
  }

  .ltc-step-circle {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .ltc-step-label {
    font-size: 10px;
  }

  .ltc-step-connector {
    top: 14px;
    width: 24px;
    left: calc(100% - 12px);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .ltc-steps::before {
    background: linear-gradient(to right, #2d3748, #1a202c, #2d3748);
  }

  .ltc-step-circle {
    background-color: #1a202c;
    border-color: #4a5568;
    color: #a0aec0;
  }

  .ltc-step-label {
    color: #a0aec0;
  }
}

/* LTC阶段页面统一样式 */

/* 页面标题统一规范 */
.ltc-stage-title {
  font-size: 19.2px !important;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 阶段内容容器 */
.ltc-stage-content {
  padding: 24px;
}

/* 统计卡片样式 - 适配新的div布局 */
.ltc-stage-card {
  /* 清除之前的样式干扰，让组件内联样式生效 */
  height: auto !important;
  min-height: auto !important;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09) !important;
}

.ltc-stage-card .ant-card-body {
  padding: 16px !important; /* 恢复正常内边距以配合新布局 */
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.ltc-stage-card .ant-statistic-title {
  font-size: 12px !important;
  color: #666 !important;
  margin-bottom: 4px !important; /* 减少标题和数值间距 */
}

.ltc-stage-card .ant-statistic-content {
  font-size: 20px !important;
  font-weight: 600 !important;
}

/* 进度圆环统一样式 */
.ltc-progress-circle {
  text-align: center;
}

.ltc-progress-label {
  margin-top: 8px;
  font-size: 12px !important;
  color: #666;
}

/* 按钮统一样式 */
.ltc-proceed-button {
  font-size: 12px !important;
}

/* 表格标题统一样式 */
.ltc-table-title {
  font-size: 13px !important;
  font-weight: 600;
}

/* 卡片内容统一样式 */
.ltc-card-content .ant-form-item-label > label {
  font-size: 12px !important;
}

.ltc-card-content .ant-input,
.ltc-card-content .ant-select-selection-item,
.ltc-card-content .ant-textarea {
  font-size: 12px !important;
}

/* Tab标签统一样式 */
.ltc-tabs .ant-tabs-tab {
  font-size: 13px !important;
}

.ltc-tabs .ant-tabs-tab-btn {
  font-size: 13px !important;
  color: #666 !important;
  font-weight: normal !important;
}

/* Tab激活状态样式 */
.ltc-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #FF7A00 !important;
  font-weight: 500 !important;
}

.ltc-tabs .ant-tabs-ink-bar {
  background-color: #FF7A00 !important;
}

/* 标签页标题左侧留白 */
.ltc-tabs .ant-tabs-nav {
  padding-left: 24px;
}

/* 标签页内容与上方卡片对齐 */
.ltc-tabs .ant-tabs-tabpane {
  padding-left: 0;
}

/* Modal标题统一样式 */
.ltc-modal .ant-modal-title {
  font-size: 14px !important;
}

.ltc-modal .ant-modal-body {
  font-size: 12px !important;
}

/* 图标统一大小 */
.ltc-icon {
  font-size: 14px;
}

/* LTC阶段页面内所有文本统一 */
.ltc-stage-content h1 {
  font-size: 19.2px !important;
}

.ltc-stage-content h2 {
  font-size: 16px !important;
}

.ltc-stage-content h3 {
  font-size: 14px !important;
}

.ltc-stage-content h4 {
  font-size: 13px !important;
}

.ltc-stage-content h5 {
  font-size: 12px !important;
}

.ltc-stage-content h6 {
  font-size: 11px !important;
}

/* 强制覆盖所有Form组件样式 */
.ltc-stage-content .ant-form-item-label > label {
  font-size: 12px !important;
}

.ltc-stage-content .ant-input {
  font-size: 12px !important;
}

.ltc-stage-content .ant-input::placeholder {
  font-size: 12px !important;
}

.ltc-stage-content .ant-select-selection-item {
  font-size: 12px !important;
}

.ltc-stage-content .ant-select-selection-placeholder {
  font-size: 12px !important;
}

.ltc-stage-content .ant-input-number-input {
  font-size: 12px !important;
}

.ltc-stage-content .ant-picker-input > input {
  font-size: 12px !important;
}

.ltc-stage-content textarea.ant-input {
  font-size: 12px !important;
}

/* LTC阶段页面的所有suffix和addonAfter统一样式 */
.ltc-stage-content .ant-input-suffix,
.ltc-stage-content .ant-input-group-addon,
.ltc-stage-content .ant-input-number-group-addon {
  font-size: 12px !important;
  color: #666 !important;
}

/* 强制覆盖表格样式 */
.ltc-stage-content .ant-table-thead > tr > th {
  font-size: 11px !important;
}

.ltc-stage-content .ant-table-tbody > tr > td {
  font-size: 11px !important;
}

/* 强制覆盖Tab样式 */
.ltc-stage-content .ant-tabs-tab,
.ltc-stage-content .ant-tabs-tab span,
.ltc-stage-content .ant-tabs-tab div {
  font-size: 13px !important;
}

.ltc-stage-content .ant-tabs-tab-btn,
.ltc-stage-content .ant-tabs-tab-btn span,
.ltc-stage-content .ant-tabs-tab-btn div {
  font-size: 13px !important;
}

/* 强制覆盖按钮样式 */
.ltc-stage-content .ant-btn {
  font-size: 12px !important;
}

/* 强制覆盖标签样式 */
.ltc-stage-content .ant-tag {
  font-size: 11px !important;
}

/* 强制覆盖Card标题样式 */
.ltc-stage-content .ant-card-head-title {
  font-size: 13px !important;
}

.ltc-stage-content .ant-card-body {
  font-size: 12px !important;
}

/* 强制覆盖List样式 */
.ltc-stage-content .ant-list-item-meta-title {
  font-size: 12px !important;
}

.ltc-stage-content .ant-list-item-meta-description {
  font-size: 11px !important;
}

/* 强制覆盖Statistic样式 */
.ltc-stage-content .ant-statistic-title {
  font-size: 12px !important;
}

.ltc-stage-content .ant-statistic-content {
  font-size: 20px !important;
}

/* 强制覆盖Progress样式 */
.ltc-stage-content .ant-progress-text {
  font-size: 12px !important;
}

/* 强制覆盖Timeline样式 */
.ltc-stage-content .ant-timeline-item-content {
  font-size: 12px !important;
}

/* 强制覆盖Steps样式 */
.ltc-stage-content .ant-steps-item-title {
  font-size: 12px !important;
}

.ltc-stage-content .ant-steps-item-description {
  font-size: 11px !important;
}

/* 强制覆盖Alert样式 */
.ltc-stage-content .ant-alert-message {
  font-size: 12px !important;
}

.ltc-stage-content .ant-alert-description {
  font-size: 11px !important;
}

/* 强制覆盖Upload样式 */
.ltc-stage-content .ant-upload-text {
  font-size: 12px !important;
}

.ltc-stage-content .ant-upload-hint {
  font-size: 11px !important;
}

/* 强制覆盖Modal样式 */
.ltc-stage-content .ant-modal-title {
  font-size: 14px !important;
}

.ltc-stage-content .ant-modal-body {
  font-size: 12px !important;
}

/* 强制覆盖Radio和Checkbox样式 */
.ltc-stage-content .ant-radio-wrapper {
  font-size: 12px !important;
}

.ltc-stage-content .ant-checkbox-wrapper {
  font-size: 12px !important;
}

/* 强制覆盖Select下拉选项样式 */
.ltc-stage-content .ant-select-dropdown .ant-select-item {
  font-size: 12px !important;
}

/* 强制覆盖所有文本内容 - 排除Tab元素 */
.ltc-stage-content div:not(.ant-tabs-tab):not(.ant-tabs-tab-btn) {
  font-size: 12px;
}

.ltc-stage-content span:not(.ant-tabs-tab):not(.ant-tabs-tab-btn) {
  font-size: 12px;
}

/* Tab页签的特殊处理 - 确保不被通用样式覆盖 */
.ltc-stage-content .ant-tabs-tab,
.ltc-stage-content .ant-tabs-tab *,
.ltc-stage-content .ant-tabs-tab-btn,
.ltc-stage-content .ant-tabs-tab-btn * {
  font-size: 13px !important;
}

.ltc-stage-content p {
  font-size: 12px !important;
}

/* 特殊处理一些组件的内联样式重写 */
.ltc-stage-content [style*="fontSize"] {
  font-size: 12px !important;
}

.ltc-stage-content [style*="font-size"] {
  font-size: 12px !important;
}

/* 特别处理表格中的强文本 */
.ltc-stage-content .ant-table strong {
  font-size: 11px !important;
}

/* 特别处理表格中的普通文本 */
.ltc-stage-content .ant-table div {
  font-size: 11px !important;
}

/* 特别处理表格中的span */
.ltc-stage-content .ant-table span {
  font-size: 11px !important;
}

/* 保持Statistic的特殊处理 */
.ltc-stage-content .ant-statistic-content-value {
  font-size: 20px !important;
}

.ltc-stage-content .ant-statistic-content-suffix {
  font-size: 16px !important;
}

/* 保持页面标题的特殊处理 */
.ltc-stage-content .ltc-stage-title {
  font-size: 19.2px !important;
}

/* 强力覆盖内联fontSize样式 - 排除Tab元素 */
.ltc-stage-content [style*="fontSize: '14px'"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="fontSize: '12px'"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="fontSize: '11px'"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="fontSize: '16px'"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="font-size: 14px"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="font-size: 12px"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="font-size: 11px"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn),
.ltc-stage-content [style*="font-size: 16px"]:not(.ant-tabs-tab):not(.ant-tabs-tab-btn) {
  font-size: 12px !important;
}

/* 表格内容特殊处理 */
.ltc-stage-content .ant-table *:not(.ant-tabs-tab):not(.ant-tabs-tab-btn) {
  font-size: 11px !important;
}

/* Typography文字组件的特殊处理 */
.ltc-stage-content .ant-typography-title {
  font-size: 14px !important;
}

.ltc-stage-content .ant-typography-caption {
  font-size: 11px !important;
}

/* BasicInfo组件的输入框suffix文本统一样式 */
.basic-info-content .ant-input-suffix,
.basic-info-content .ant-input-group-addon,
.basic-info-content .ant-input-number-group-addon {
  font-size: 12px !important;
  color: #666 !important;
}

/* 专门针对BasicInfo组件中的所有表单元素 */
.basic-info-content .ant-form-item-label > label {
  font-size: 12px !important;
}

.basic-info-content .ant-input,
.basic-info-content .ant-input::placeholder,
.basic-info-content .ant-select-selection-item,
.basic-info-content .ant-select-selection-placeholder,
.basic-info-content .ant-input-number-input,
.basic-info-content textarea.ant-input {
  font-size: 12px !important;
}

/* BasicInfo组件中的tab内容区域 */
.basic-info-content .tab-pane {
  font-size: 12px !important;
}

.basic-info-content .form-grid {
  font-size: 12px !important;
}

.basic-info-content .form-group {
  font-size: 12px !important;
}

/* BasicInfo组件中的滑动条样式统一规范 */
.basic-info-content .slider-value {
  font-size: 12px !important;
  min-width: 35px;
}

.basic-info-content input[type="range"]::-webkit-slider-thumb {
  width: 14px !important;
  height: 14px !important;
  margin-top: -4px !important;
}

.basic-info-content input[type="range"]::-moz-range-thumb {
  width: 14px !important;
  height: 14px !important;
}

/* ==== Tab页签字体大小的最终覆盖 - 最高优先级 ==== */
/* 确保ExecutionStage页面中的Tab字体为13px，不被任何其他样式覆盖 */
.ltc-stage-content .ltc-tabs .ant-tabs-tab,
.ltc-stage-content .ltc-tabs .ant-tabs-tab *,
.ltc-stage-content .ltc-tabs .ant-tabs-tab-btn,
.ltc-stage-content .ltc-tabs .ant-tabs-tab-btn *,
.ltc-stage-content .ant-tabs .ant-tabs-tab,
.ltc-stage-content .ant-tabs .ant-tabs-tab *,
.ltc-stage-content .ant-tabs .ant-tabs-tab-btn,
.ltc-stage-content .ant-tabs .ant-tabs-tab-btn *,
.ltc-stage-content .ant-tabs-nav .ant-tabs-tab,
.ltc-stage-content .ant-tabs-nav .ant-tabs-tab *,
.ltc-stage-content .ant-tabs-nav .ant-tabs-tab-btn,
.ltc-stage-content .ant-tabs-nav .ant-tabs-tab-btn * {
  font-size: 13px !important;
}

/* 滑块样式统一 */
.ltc-stage-content .ant-slider-rail {
  height: 4px;
  background-color: #e5e7eb;
}

.ltc-stage-content .ant-slider-track {
  height: 4px;
  background-color: #FF7A00;
}

.ltc-stage-content .ant-slider-handle {
  width: 16px;
  height: 16px;
  margin-top: -6px;
  border-color: #FF7A00;
  background-color: #fff;
}

.ltc-stage-content .ant-slider-handle:hover,
.ltc-stage-content .ant-slider-handle:focus {
  border-color: #FF7A00;
  box-shadow: 0 0 0 3px rgba(255, 122, 0, 0.12);
}

.ltc-stage-content .ant-slider-mark-text {
  font-size: 12px;
  color: #666;
}

.ltc-stage-content .ant-slider-dot {
  width: 6px;
  height: 6px;
  top: -1px;
  border-color: #e5e7eb;
}

.ltc-stage-content .ant-slider-dot-active {
  border-color: #FF7A00;
}

/* Budget Items表格样式 - 移除加粗显示 */
.budget-items-table .ant-table-tbody > tr > td {
  font-weight: normal !important;
}

.budget-items-table .ant-table-tbody > tr > td span {
  font-weight: normal !important;
}

.budget-items-table .ant-table-tbody > tr > td div {
  font-weight: normal !important;
}
/* Force CSS refresh - 06/13/2025 20:31:42 */

