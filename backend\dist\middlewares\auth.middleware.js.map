{"version": 3, "file": "auth.middleware.js", "sourceRoot": "", "sources": ["../../src/middlewares/auth.middleware.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,qCAAwC;AACxC,2CAAwC;AACxC,uDAAsE;AAe/D,MAAM,cAAc,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACtF,IAAI;QAIF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAG7C,IAAI,2BAAa,EAAE;YAEjB,IAAI,UAAU,EAAE;gBACd,IAAI;oBACF,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;wBAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBACvB,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,4BAAc,CAAC,GAAG,CAAC,MAAM,CAAiB,CAAC;wBAG1E,MAAM,cAAc,GAAG,IAAA,uBAAa,EAAC,WAAI,CAAC,CAAC;wBAC3C,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;wBAE1E,IAAI,IAAI,EAAE;4BAER,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;yBACjB;qBACF;iBACF;gBAAC,OAAO,UAAU,EAAE;oBACnB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;iBACpC;aACF;iBAAM;gBACL,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;aACnC;YAGD,OAAO,IAAI,EAAE,CAAC;SACf;QAID,IAAI,CAAC,UAAU,EAAE;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;SACrD;QAGD,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;YAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;SACpD;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAGvB,MAAM,IAAI,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,4BAAc,CAAC,GAAG,CAAC,MAAM,CAAiB,CAAC;QAG1E,MAAM,cAAc,GAAG,IAAA,uBAAa,EAAC,WAAI,CAAC,CAAC;QAC3C,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;SACnD;QAGD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAEhB,OAAO,IAAI,EAAE,CAAC;KACf;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;KACnD;AACH,CAAC,CAAC;AArEW,QAAA,cAAc,kBAqEzB;AAEK,MAAM,SAAS,GAAG,CAAC,KAAe,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QAIzD,IAAI,2BAAa,EAAE;YACjB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC3B,OAAO,IAAI,EAAE,CAAC;SACf;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;SACjD;QAED,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACjC,OAAO,IAAI,EAAE,CAAC;SACf;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;IACxD,CAAC,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,SAAS,aAoBpB"}