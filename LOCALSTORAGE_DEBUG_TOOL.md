# 🔧 BasicInfo数据持久化调试工具

## 🎯 快速诊断指南

### 第一步：启用调试模式
在浏览器开发者工具Console中执行：
```javascript
// 启用详细调试信息
localStorage.setItem('basicinfo-debug', 'true');
console.log('✅ 调试模式已启用');
```

### 第二步：数据监控脚本
复制并执行以下脚本来监控数据变化：
```javascript
// 数据监控脚本
(function() {
  const projectId = window.location.pathname.split('/').pop();
  const storageKey = `project_stages_${projectId}`;
  
  console.log(`🔍 项目ID: ${projectId}`);
  console.log(`🔍 存储键: ${storageKey}`);
  
  // 查看当前数据
  function checkData() {
    const data = localStorage.getItem(storageKey);
    const parsed = data ? JSON.parse(data) : null;
    const basicInfo = parsed?.find(stage => stage.type === 'basic_info');
    
    console.log('📊 当前localStorage数据:', {
      原始数据: data,
      解析后: parsed,
      BasicInfo阶段: basicInfo,
      BasicInfo数据: basicInfo?.data,
      数据字段数: basicInfo?.data ? Object.keys(basicInfo.data).length : 0
    });
    
    return basicInfo?.data;
  }
  
  // 设置定时监控
  window.debugInterval = setInterval(checkData, 3000);
  console.log('⚡ 每3秒监控一次数据变化...');
  
  // 立即检查一次
  checkData();
})();
```

### 第三步：测试数据持久化
1. **填写测试数据**：
   - Client Name: "测试客户ABC"
   - Opportunity Name: "测试项目XYZ"

2. **切换模块测试**：
   - 填写Client信息后，切换到Opportunity模块
   - 再切换回Client模块，检查数据是否保留

3. **页面刷新测试**：
   - 刷新页面
   - 检查数据是否自动恢复

### 第四步：问题诊断
根据Console输出判断问题类型：

#### 情况A：数据正常保存
```
✅ 正常情况 - Console显示：
📊 当前localStorage数据: {
  BasicInfo数据: { clientName: "测试客户ABC", opportunityName: "测试项目XYZ" }
  数据字段数: 15
}
⚡ BasicInfo: Field change saved { clientName: "测试客户ABC" }
```

#### 情况B：数据没有保存
```
❌ 问题情况 - Console显示：
📊 当前localStorage数据: { BasicInfo数据: null, 数据字段数: 0 }
⚠️ BasicInfo: Real-time save failed: [错误信息]
```

#### 情况C：数据保存但读取失败
```
❌ 问题情况 - Console显示：
📊 当前localStorage数据: { 数据字段数: 15 }
但表单显示为空
```

## 🛠️ 根据问题类型的解决方案

### 解决方案A：数据收集问题
如果数据没有保存，执行以下脚本强制保存：
```javascript
// 强制数据收集和保存
(function() {
  const forms = ['clientForm', 'opportunityForm', 'competitorForm', 'otherForm'];
  let allData = {};
  
  // 尝试从DOM直接收集数据
  const inputs = document.querySelectorAll('input, textarea, select');
  inputs.forEach(input => {
    if (input.value && input.name) {
      allData[input.name] = input.value;
    }
  });
  
  console.log('🔍 从DOM收集的数据:', allData);
  
  // 强制保存
  const projectId = window.location.pathname.split('/').pop();
  const storageKey = `project_stages_${projectId}`;
  const stageData = [{
    id: `manual_save_${Date.now()}`,
    projectId: projectId,
    type: 'basic_info',
    data: allData,
    updatedAt: new Date().toISOString()
  }];
  
  localStorage.setItem(storageKey, JSON.stringify(stageData));
  console.log('💾 手动保存完成');
})();
```

### 解决方案B：表单值恢复
如果数据已保存但表单没有显示，执行：
```javascript
// 强制表单值恢复
(function() {
  const projectId = window.location.pathname.split('/').pop();
  const storageKey = `project_stages_${projectId}`;
  const data = localStorage.getItem(storageKey);
  
  if (data) {
    const parsed = JSON.parse(data);
    const basicInfo = parsed.find(stage => stage.type === 'basic_info');
    
    if (basicInfo?.data) {
      // 直接设置DOM值
      Object.entries(basicInfo.data).forEach(([key, value]) => {
        const input = document.querySelector(`[name="${key}"]`);
        if (input) {
          input.value = value;
          input.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });
      
      console.log('🔄 强制恢复表单值完成');
    }
  }
})();
```

## 📋 测试检查表

### 基础功能测试
- [ ] 输入Client Name后3秒内Console显示保存日志
- [ ] 切换模块后数据仍然保留
- [ ] 页面刷新后数据自动恢复
- [ ] localStorage中存在project_stages_{id}数据

### 高级功能测试  
- [ ] 多模块数据同时保存
- [ ] Win Probability滑块值保存
- [ ] Competitor开关状态保存
- [ ] 表单验证状态保持

## 🆘 紧急修复

如果所有方法都无效，执行终极修复脚本：
```javascript
// 终极数据恢复脚本
(function() {
  console.log('🚨 执行终极修复...');
  
  // 1. 清除所有可能的冲突数据
  const projectId = window.location.pathname.split('/').pop();
  Object.keys(localStorage).forEach(key => {
    if (key.includes(projectId)) {
      console.log(`🗑️ 清理: ${key}`);
      localStorage.removeItem(key);
    }
  });
  
  // 2. 重新初始化
  localStorage.setItem(`project_stages_${projectId}`, JSON.stringify([]));
  
  // 3. 刷新页面
  console.log('🔄 重新加载页面...');
  setTimeout(() => window.location.reload(), 1000);
})();
```

## 📞 问题反馈

请将以下信息提供给开发者：
1. **问题类型**：A/B/C中的哪一种
2. **Console错误信息**：完整的错误堆栈
3. **测试步骤**：具体的操作序列
4. **数据示例**：localStorage中的实际数据内容

这将帮助快速定位和解决问题。 