# Competitor Information 页面功能增强

## 修改概述
根据用户需求，为 BasicInfo 组件中的 Competitor Information 模块增加了四个主要功能：
1. 竞争对手存在性的"是/否"切换开关
2. 竞争对手名称的多标签输入功能
3. SWOT 分析框架的引导信息
4. 完全英文化的界面

## 新增功能详情

### 1. 竞争对手存在性开关
- **组件类型**: `Switch` 组件
- **功能**: 用户可以选择"Yes"或"No"来表示是否有竞争对手
- **状态管理**: 通过 `hasCompetitors` 状态控制
- **交互逻辑**: 
  - 选择"No"时自动清空所有竞争对手相关数据
  - 切换状态时提供视觉反馈（绿色/灰色背景）

### 2. 竞争对手名称标签输入
- **组件类型**: `Select` 组件（tags 模式）
- **功能**: 
  - 支持多个竞争对手名称输入
  - 预设常见竞争对手选项（Microsoft、Google、Amazon等）
  - 支持自定义输入新的竞争对手名称
- **限制**: 
  - 最多10个标签
  - 每个标签最大50个字符
  - 仅在 `hasCompetitors = true` 时可见

### 3. SWOT 分析框架引导
- **组件类型**: `Alert` 组件（info 类型）
- **内容**: 
  - **Strengths**: 竞争对手的优势（市场份额、技术、品牌认知度等）
  - **Weaknesses**: 竞争对手的劣势（定价、客服、过时技术等）
  - **Opportunities**: 可利用的市场空白（未服务细分市场、新兴技术等）
  - **Threats**: 竞争风险（新进入者、价格战、市场整合等）
- **示例**: 提供实际的 SWOT 分析示例
- **显示条件**: 仅在有竞争对手时显示

### 4. 无竞争对手指导
- **组件类型**: `Alert` 组件（success 类型）
- **内容**: 
  - 间接竞争对手或替代产品/服务分析
  - 潜在未来竞争对手进入市场的可能性
  - 独特价值主张和市场定位
  - 保护市场地位的进入壁垒
- **显示条件**: 仅在无竞争对手时显示

## 技术实现细节

### 状态管理
```typescript
const [hasCompetitors, setHasCompetitors] = useState(false);
const [competitorTags, setCompetitorTags] = useState<string[]>([]);
```

### 数据保存逻辑
```typescript
const dataToSave = {
  ...values,
  ...(activeModule === 'competitor' && { 
    hasCompetitors: hasCompetitors,
    competitorNames: competitorTags 
  })
};
```

### 数据恢复逻辑
```typescript
if (mergedData.hasCompetitors !== undefined) {
  setHasCompetitors(mergedData.hasCompetitors);
}
if (mergedData.competitorNames && Array.isArray(mergedData.competitorNames)) {
  setCompetitorTags(mergedData.competitorNames);
}
```

### 条件渲染
- 竞争对手名称输入框：仅在 `hasCompetitors = true` 时显示
- 分析文本框：根据竞争对手存在性调整高度和禁用状态
- SWOT 框架指导：仅在有竞争对手时显示
- 无竞争对手指导：仅在无竞争对手时显示

## 用户界面优化

### 英文化内容
- 所有标签和提示文本使用英文
- SWOT 分析框架使用标准商业术语
- 示例文本采用专业的商业分析语言

### 视觉设计
- **开关组件**: 绿色（有竞争对手）/灰色（无竞争对手）
- **标签颜色**: 
  - Strengths: 绿色
  - Weaknesses: 红色
  - Opportunities: 蓝色
  - Threats: 橙色
- **Alert 组件**: 信息类型（蓝色）和成功类型（绿色）

### 交互体验
- 切换开关时自动清理数据
- 动态调整文本框大小和状态
- 智能的占位符文本
- 预定义竞争对手选项便于快速输入

## 数据结构

### 保存的数据格式
```typescript
{
  hasCompetitors: boolean,
  competitorNames: string[],
  competitorNotes: string,
  // ... 其他字段
}
```

### 预定义竞争对手列表
- Microsoft
- Google  
- Amazon
- IBM
- Oracle
- SAP
- Salesforce
- Adobe

## 使用场景

### 有竞争对手的情况
1. 用户打开开关选择"Yes"
2. 输入具体竞争对手名称（可选择预设或自定义）
3. 根据 SWOT 框架填写详细的竞争分析
4. 保存完整的竞争情报

### 无竞争对手的情况
1. 用户打开开关选择"No"
2. 系统自动禁用相关输入字段
3. 显示无竞争对手情况下的分析指导
4. 用户可以分析间接竞争和市场独特性

## 兼容性

- ✅ 向后兼容：现有数据结构不受影响
- ✅ 状态持久化：用户的选择和输入会被正确保存和恢复
- ✅ 表单验证：保持原有的表单验证机制
- ✅ 数据同步：与其他模块的数据流完全兼容

## 总结

此次增强为 Competitor Information 模块提供了：
1. 更灵活的竞争对手信息管理
2. 专业的 SWOT 分析框架指导
3. 针对不同情况的智能化用户界面
4. 完全英文化的专业商业分析体验

用户现在可以根据实际业务情况选择合适的竞争分析方式，系统会提供相应的指导和支持，提升了竞争情报收集的专业性和实用性。 