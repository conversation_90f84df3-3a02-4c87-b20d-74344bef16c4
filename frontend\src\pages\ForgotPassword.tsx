import React, { useState } from 'react';
import { Form, Input, Button, message, Typography } from 'antd';
import { MailOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import '../styles/ForgotPassword.css';

const { Title, Text } = Typography;

const ForgotPassword: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const onFinish = async (values: { email: string }) => {
    try {
      setLoading(true);
      // 模拟发送重置密码邮件的API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('Password reset email sent successfully');
      setEmailSent(true);
    } catch (error) {
      message.error('Failed to send reset email, please try again');
      console.error('Forgot password error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="forgot-password-container">
      {/* Top Navigation */}
      <div className="forgot-password-header">
        <div className="logo">
          <Link to="/">SmartLTC</Link>
        </div>
        <div className="nav-links">
          <Link to="/login" className="sign-in-link">Sign In</Link>
          <Link to="/register" className="sign-up-link">Sign Up</Link>
        </div>
      </div>

      {/* Forgot Password Card */}
      <div className="forgot-password-card-wrapper">
        <div className="forgot-password-card">
          {!emailSent ? (
            <>
              <Title level={2} className="forgot-password-title">Forgot Password</Title>
              <Text className="forgot-password-subtitle">
                Enter your email address and we'll send you a link to reset your password
              </Text>

              <Form
                name="forgot-password"
                className="forgot-password-form"
                onFinish={onFinish}
                size="large"
              >
                <Form.Item
                  name="email"
                  rules={[
                    { required: true, message: 'Please enter your email' },
                    { type: 'email', message: 'Please enter a valid email address' }
                  ]}
                >
                  <Input
                    prefix={<MailOutlined />}
                    placeholder="Email"
                    className="forgot-password-input"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="forgot-password-button"
                  >
                    Send Reset Link
                  </Button>
                </Form.Item>

                <div className="back-to-login">
                  <Link to="/login">
                    <ArrowLeftOutlined /> Back to Sign In
                  </Link>
                </div>
              </Form>
            </>
          ) : (
            <>
              <Title level={2} className="forgot-password-title">Check Your Email</Title>
              <Text className="forgot-password-subtitle">
                We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.
              </Text>

              <div className="email-sent-actions">
                <Button
                  type="primary"
                  className="forgot-password-button"
                  onClick={() => setEmailSent(false)}
                >
                  Send Another Email
                </Button>
                
                <div className="back-to-login">
                  <Link to="/login">
                    <ArrowLeftOutlined /> Back to Sign In
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="forgot-password-footer">
        <div className="footer-content">
          <div className="footer-copyright">
            <span>© 2024 SmartLTC - AI-Powered Business Operations Integrated Management Platform</span>
            <br />
            <span>Powered by Vlisoft.com • Empowering business with SmartLTC</span>
          </div>
          <div className="footer-buttons">
            <Link to="/privacy" className="footer-button">Privacy Notice</Link>
            <Link to="/terms" className="footer-button">Terms & Privacy</Link>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="decoration-line line-1"></div>
      <div className="decoration-line line-2"></div>
      <div className="decoration-line line-3"></div>
    </div>
  );
};

export default ForgotPassword; 