# 客户信息自动同步功能测试指南

## 功能概述

当在Lead to Cash项目管理页面中创建或编辑项目时，如果包含新的客户信息，系统会自动检查客户是否已存在于Client管理页面中。如果客户不存在，系统将自动创建一条新的客户记录。

## 功能特性

### 1. 自动客户检测
- 系统会搜索现有客户列表，按客户名称或公司名称进行精确匹配
- 支持大小写不敏感的匹配

### 2. 智能字段映射
- **客户名称**: 直接使用项目中的客户名称
- **公司名称**: 与客户名称相同
- **邮箱**: 根据客户名称自动生成临时邮箱 (<EMAIL>)
- **行业**: 根据项目类别智能推断
  - Software → Technology
  - Service → Consulting  
  - Product → Manufacturing
  - Other → Other
- **国家**: 使用项目中的国家信息
- **客户等级**: 从项目tier映射到客户tier
  - S → SVIP
  - V → VIP  
  - B → BA
  - A → A
- **状态**: 新创建的客户默认设为"potential"
- **联系人**: 使用项目中的owner信息
- **备注**: 自动添加"从机会自动创建"的说明

### 3. 非阻塞设计
- 如果客户创建失败，不会阻止项目的创建或更新
- 系统会记录错误但继续完成项目操作

## 测试步骤

### 测试场景1: 创建新项目时自动创建客户

1. **进入创建页面**
   - 访问 http://localhost:3000/opportunity
   - 点击"New Opportunity"按钮

2. **填写项目信息**
   - Client Name: 输入一个全新的客户名称，如"Test Company ABC"
   - Client Tier: 选择"S - Strategic Client"
   - Country/Region: 选择"Germany"
   - Client Contact: 输入"John Doe"
   - Category: 选择"Software"
   - 填写其他必填字段

3. **提交项目**
   - 点击"Create Opportunity"按钮
   - 观察成功消息，应显示"Opportunity created successfully! Client information has been synchronized."
   - 应该看到额外的提示消息："New client 'Test Company ABC' has been automatically added to the client list."

4. **验证客户创建**
   - 导航到Client管理页面 (/client)
   - 搜索"Test Company ABC"
   - 验证新客户记录的字段：
     - Name: Test Company ABC
     - Company: Test Company ABC
     - Industry: Technology
     - Country: Germany
     - Tier: SVIP
     - Status: potential
     - Contact Person: John Doe
     - Notes: 包含"Automatically created from opportunity"

### 测试场景2: 编辑项目时更换客户

1. **选择现有项目**
   - 在Opportunities列表中选择一个现有项目
   - 点击编辑按钮

2. **更改客户名称**
   - 将Client Name修改为另一个不存在的客户名称，如"New Test Client XYZ"
   - 修改其他相关信息

3. **保存更改**
   - 点击"Update Opportunity"按钮
   - 观察成功消息，应显示客户同步提示

4. **验证新客户创建**
   - 检查Client页面，确认新客户已创建

### 测试场景3: 使用现有客户名称

1. **创建项目时使用现有客户**
   - 在Client Name字段输入已存在的客户名称
   - 完成项目创建

2. **验证无重复创建**
   - 系统应识别现有客户
   - 不应创建重复的客户记录
   - 控制台应显示"Client already exists"消息

## 测试要点

### ✅ 成功指标
- [ ] 新客户能正确创建
- [ ] 字段映射准确
- [ ] 现有客户不会重复创建
- [ ] 用户看到明确的反馈消息
- [ ] 项目操作不受客户创建影响

### ⚠️ 注意事项
- 客户邮箱是临时生成的，需要后续手动更新
- 新创建的客户状态为"potential"，可根据需要调整
- 如果客户创建失败，项目操作仍会继续

### 🔧 调试信息
- 查看浏览器开发者工具的Console选项卡
- 搜索"Client already exists"或"New client created"消息
- 检查网络请求是否成功

## 配置说明

该功能无需额外配置，默认启用。客户等级映射和行业推断逻辑可在以下文件中修改：
- `frontend/src/pages/CreateProject.tsx`
- `frontend/src/pages/EditProject.tsx` 