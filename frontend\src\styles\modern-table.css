/* 极简表格设计 - 最基本样式 */

/* 全局强制移除所有表格相关间距 */
.project-list-container * {
  box-sizing: border-box !important;
}

.project-list-container [class*="ant-table"] {
  margin: 0 !important;
}

/* 整体容器 */
.project-list-container {
  background: #ffffff;
  padding: 0;
  margin: 0;
}

/* 表格容器 */
.project-list-container .ant-table-wrapper {
  background: #ffffff;
  border: 1px solid #d9d9d9;
  margin: 0 !important;
}

/* 表格内容区域 */
.project-list-container .ant-table-content {
  margin: 0 !important;
}

/* 表格容器内部 */
.project-list-container .ant-table-container {
  margin: 0 !important;
  padding: 0 !important;
}

/* Ant Design 内部元素强制零间距 */
.project-list-container .ant-table-header {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-body {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table-placeholder {
  margin: 0 !important;
  padding: 0 !important;
}

/* 移除任何可能的行间距 */
.project-list-container .ant-table tbody {
  margin: 0 !important;
  padding: 0 !important;
}

.project-list-container .ant-table thead {
  margin: 0 !important;
  padding: 0 !important;
}

/* 表格本体 */
.project-list-container .ant-table {
  background: #ffffff;
  margin: 0 !important;
}

/* 表头容器 */
.project-list-container .ant-table-thead {
  margin: 0 !important;
}

/* 表体容器 */
.project-list-container .ant-table-tbody {
  margin: 0 !important;
}

/* 表头设计 */
.project-list-container .ant-table-thead > tr > th {
  background: #fafafa !important;
  color: #000 !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  padding: 16px !important;
  border-bottom: 0 !important;
  margin: 0 !important;
  height: auto !important;
  line-height: 1 !important;
}

/* 表体行设计 */
.project-list-container .ant-table-tbody > tr {
  background: #ffffff;
  margin: 0 !important;
}

.project-list-container .ant-table-tbody > tr:hover {
  background: #f5f5f5 !important;
}

/* 表体单元格 */
.project-list-container .ant-table-tbody > tr > td {
  padding: 16px !important;
  font-size: 14px !important;
  color: #000 !important;
  border-bottom: 1px solid #f0f0f0 !important;
  margin: 0 !important;
}

/* 链接样式 */
.project-list-container .ant-table-tbody a {
  color: #1890ff !important;
  text-decoration: none !important;
}

.project-list-container .ant-table-tbody a:hover {
  text-decoration: underline !important;
}

/* 分页组件 */
.project-list-container .ant-pagination {
  margin-top: 16px !important;
  text-align: center;
}

/* 空状态 */
.project-list-container .ant-empty {
  padding: 60px 0;
}

.project-list-container .ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}



/* 表格滚动条 */
.project-list-container .ant-table-body::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.project-list-container .ant-table-body::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.project-list-container .ant-table-body::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.project-list-container .ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 确保文本不换行 */
.project-list-container .ant-table-tbody > tr > td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 操作按钮 */
.project-list-container .ant-btn {
  border-radius: 4px;
  border: none;
  background: transparent;
  color: #8c8c8c;
  transition: all 0.2s ease;
}

.project-list-container .ant-btn:hover {
  color: #1890ff;
  background: #f0f0f0;
}

/* 固定列样式 */
.project-list-container .ant-table-fixed-left {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.05);
}

.project-list-container .ant-table-fixed-right {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.05);
}

/* 去除表格内部复杂边框 */
.project-list-container .ant-table-container table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制移除所有间距 */
.project-list-container .ant-table-thead,
.project-list-container .ant-table-tbody,
.project-list-container .ant-table-thead > tr,
.project-list-container .ant-table-tbody > tr,
.project-list-container .ant-table-thead > tr > th,
.project-list-container .ant-table-tbody > tr > td {
  margin: 0 !important;
  border-spacing: 0 !important;
}

/* 表头和表体连接处 */
.project-list-container .ant-table-thead > tr:last-child > th {
  border-bottom: 0 !important;
}

.project-list-container .ant-table-tbody > tr:first-child > td {
  border-top: 0 !important;
}

/* 响应式处理 */
@media (max-width: 768px) {
  .project-list-container .ant-table-thead > tr > th,
  .project-list-container .ant-table-tbody > tr > td {
    padding: 8px 12px !important;
    font-size: 12px !important;
  }
  
  .project-list-container .ant-table-thead > tr > th:first-child,
  .project-list-container .ant-table-tbody > tr > td:first-child {
    padding-left: 16px !important;
  }
  
  .project-list-container .ant-table-thead > tr > th:last-child,
  .project-list-container .ant-table-tbody > tr > td:last-child {
    padding-right: 16px !important;
  }
} 