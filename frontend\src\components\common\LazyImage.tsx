import React, { useState, useEffect, useRef } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
  placeholderColor?: string;
  threshold?: number;
  onLoad?: () => void;
  onError?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  placeholderColor = '#f0f0f0',
  threshold = 0.1,
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    // 创建Intersection Observer实例
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setIsInView(true);
            // 图片进入视口后，取消观察
            if (imgRef.current) {
              observer.unobserve(imgRef.current);
            }
          }
        });
      },
      { threshold }
    );

    // 开始观察图片元素
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    // 组件卸载时清理
    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, [threshold]);

  // 图片加载处理
  const handleImageLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // 图片加载错误处理
  const handleImageError = () => {
    if (onError) onError();
  };

  // 合并样式
  const combinedStyle: React.CSSProperties = {
    ...style,
    width: width,
    height: height,
    backgroundColor: !isLoaded ? placeholderColor : 'transparent',
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0,
    display: 'block'
  };

  return (
    <div
      style={{
        width: width,
        height: height,
        backgroundColor: placeholderColor,
        position: 'relative',
        overflow: 'hidden'
      }}
      className={className}
    >
      {isInView && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          style={combinedStyle}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}
      {!isLoaded && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: placeholderColor
          }}
        >
          <div
            style={{
              width: '30%',
              height: '30%',
              borderRadius: '50%',
              background: 'rgba(0, 0, 0, 0.1)',
              animation: 'pulse 1.5s infinite ease-in-out'
            }}
          />
        </div>
      )}
    </div>
  );
};

export default LazyImage;
