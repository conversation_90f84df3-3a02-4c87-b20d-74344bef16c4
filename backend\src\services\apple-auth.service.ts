import axios from 'axios';
import jwt from 'jsonwebtoken';

/**
 * 验证 Apple ID 令牌并获取用户信息
 * @param idToken Apple 提供的 ID 令牌
 * @returns 用户信息对象
 */
export async function verifyAppleToken(idToken: string) {
  try {
    // 解码 JWT 令牌以获取用户信息
    // 注意：在生产环境中，应该验证令牌的签名
    const decoded: any = jwt.decode(idToken);
    
    if (!decoded) {
      throw new Error('Invalid Apple ID token');
    }
    
    return {
      appleId: decoded.sub,
      email: decoded.email,
      username: decoded.email ? decoded.email.split('@')[0] : `apple_user_${Date.now()}`,
      verified: true // Apple 已经验证了用户的电子邮件
    };
  } catch (error) {
    console.error('Error verifying Apple token:', error);
    throw new Error('Invalid Apple ID token');
  }
}

/**
 * 在生产环境中，您需要实现完整的 Apple Sign In 流程
 * 这包括：
 * 1. 验证令牌的签名
 * 2. 验证令牌的发行者和受众
 * 3. 验证令牌的过期时间
 * 
 * 以下是一个更完整的实现示例（需要适当配置）：
 */
export async function verifyAppleTokenProduction(idToken: string) {
  try {
    // 获取 Apple 的公钥
    const response = await axios.get('https://appleid.apple.com/auth/keys');
    const keys = response.data.keys;
    
    // 验证令牌
    // 注意：这需要额外的库来处理 JWK 格式的密钥
    // 例如 'jwks-rsa' 和 'jsonwebtoken'
    
    // 解码令牌
    const decoded: any = jwt.decode(idToken, { complete: true });
    
    if (!decoded) {
      throw new Error('Invalid Apple ID token');
    }
    
    // 验证发行者
    if (decoded.payload.iss !== 'https://appleid.apple.com') {
      throw new Error('Invalid token issuer');
    }
    
    // 验证受众（您的客户端 ID）
    if (decoded.payload.aud !== process.env.APPLE_CLIENT_ID) {
      throw new Error('Invalid token audience');
    }
    
    // 验证令牌未过期
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.payload.exp < currentTime) {
      throw new Error('Token has expired');
    }
    
    return {
      appleId: decoded.payload.sub,
      email: decoded.payload.email,
      username: decoded.payload.email ? decoded.payload.email.split('@')[0] : `apple_user_${Date.now()}`,
      verified: true
    };
  } catch (error) {
    console.error('Error verifying Apple token:', error);
    throw new Error('Invalid Apple ID token');
  }
}
