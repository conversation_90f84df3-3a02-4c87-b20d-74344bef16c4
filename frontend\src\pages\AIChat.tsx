import React, { useState, useEffect, useRef } from 'react';
import { 
  Card, Button, Input, Space, Typography, List, Avatar, Tag, message, 
  Spin, Row, Col 
} from 'antd';
import {
  ArrowLeftOutlined,
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  MessageOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'suggestion';
}

const AIChat: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Quick question suggestions
  const quickQuestions = [
    "What's our total revenue this quarter?",
    "Show me all pending payments",
    "Which clients have the highest value?",
    "How do I create a new project?",
    "What's the VAT calculation method?",
    "Show me overdue opportunities"
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize with welcome message and handle initial question
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: '1',
      content: "👋 Hello! I'm your AI assistant. I can help you with system operations, data analysis, and answer questions about your business. What would you like to know?",
      sender: 'ai',
      timestamp: new Date(),
      type: 'text'
    };
    setMessages([welcomeMessage]);

    // Handle initial question from route state
    const initialQuestion = location.state?.initialQuestion;
    if (initialQuestion) {
      setTimeout(() => {
        handleSendMessage(initialQuestion);
      }, 500);
    }
  }, [location.state]);

  // Handle sending messages
  const handleSendMessage = async (customMessage?: string) => {
    const messageText = customMessage || inputValue.trim();
    if (!messageText) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString() + '-user',
      content: messageText,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI thinking time
    setTimeout(async () => {
      const aiResponse = await generateAIResponse(messageText);
      const aiMessage: ChatMessage = {
        id: Date.now().toString() + '-ai',
        content: aiResponse,
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000 + Math.random() * 1500);
  };

  // Generate AI response based on user input
  const generateAIResponse = (input: string): Promise<string> => {
    return new Promise((resolve) => {
      const lowerInput = input.toLowerCase();
      
      let response = '';
      
      if (lowerInput.includes('revenue') || lowerInput.includes('income')) {
        response = "📊 Based on your current financial data:\n\n• Q4 2024 Revenue: €275,000\n• Monthly Average: €91,667\n• Top Revenue Source: Digital Transformation Projects (45%)\n• Growth Rate: +18% compared to last quarter\n\nWould you like me to show you a detailed breakdown by client or project?";
      } else if (lowerInput.includes('payment') || lowerInput.includes('pending')) {
        response = "💰 Current Payment Status:\n\n• Pending Payments: €150,000\n• Overdue (>30 days): €25,000\n• Expected this month: €85,000\n\n🔍 Top Pending Clients:\n1. European Manufacturing Co. - €85,000\n2. Asia Pacific Retail - €65,000\n\nShould I generate payment reminder emails for overdue accounts?";
      } else if (lowerInput.includes('client') || lowerInput.includes('customer')) {
        response = "👥 Client Analysis:\n\n🏆 Highest Value Clients:\n1. Global Tech Solutions - €125,000 (Active)\n2. European Manufacturing Co. - €85,000 (In Progress)\n3. Asia Pacific Retail - €65,000 (Pipeline)\n\n📈 Client Insights:\n• Total Active Clients: 12\n• Average Deal Size: €75,000\n• Client Retention Rate: 94%\n\nNeed help managing a specific client relationship?";
      } else if (lowerInput.includes('project') || lowerInput.includes('create')) {
        response = "🛠️ To create a new project:\n\n1. Go to 'Lead to Cash' module\n2. Click 'New Opportunity' button\n3. Fill in project details:\n   • Project name and description\n   • Client information\n   • Expected value and timeline\n   • Project type and priority\n\n4. Set team members and milestones\n5. Save and track progress\n\n💡 Pro tip: Link projects to client records for better relationship management. Need help with any specific step?";
      } else if (lowerInput.includes('vat') || lowerInput.includes('tax')) {
        response = "📋 VAT Calculation Guide:\n\n🧮 Current VAT Settings:\n• Standard Rate: 21% (configurable)\n• Available Rates: 0%, 5%, 10%, 20%, 21%, 25%\n\n💡 How it works:\n1. Enter amount without VAT\n2. Select appropriate VAT rate\n3. System auto-calculates:\n   • VAT amount = Base × Rate\n   • Total = Base + VAT\n\n📊 This quarter's VAT summary:\n• VAT Collected: €57,750\n• VAT Paid: €5,250\n• Net VAT Due: €52,500\n\nNeed help with VAT settings or calculations?";
      } else if (lowerInput.includes('overdue') || lowerInput.includes('opportunity')) {
        response = "⏰ Opportunity Pipeline Status:\n\n🔴 Overdue Opportunities:\n• Digital Platform Upgrade - 15 days overdue\n• Cloud Migration Project - 8 days overdue\n• Mobile App Development - 3 days overdue\n\n📅 Actions Needed:\n1. Follow up with prospects\n2. Update opportunity status\n3. Reassess timelines\n\n💼 Pipeline Health:\n• Total Opportunities: 8\n• Value: €420,000\n• Conversion Rate: 65%\n\nShall I help you prioritize follow-up actions?";
      } else if (lowerInput.includes('help') || lowerInput.includes('how')) {
        response = "🤝 I'm here to help! I can assist you with:\n\n📊 Data Analysis:\n• Financial reports and insights\n• Client and project analytics\n• Performance metrics\n\n⚙️ System Operations:\n• Navigation guidance\n• Feature explanations\n• Best practice tips\n\n🔍 Quick Tasks:\n• Find specific information\n• Generate reports\n• Track deadlines\n\nWhat specific area would you like help with?";
      } else {
        response = "🤔 I understand you're asking about: \"" + input + "\"\n\nWhile I can help with most system-related questions, I might need more context for this specific query. Here are some areas I'm particularly good at:\n\n• Financial data analysis\n• Client and project management\n• System navigation\n• Business insights\n• Process guidance\n\nCould you rephrase your question or ask about one of these topics?";
      }

      resolve(response);
    });
  };

  const handleQuickQuestion = (question: string) => {
    handleSendMessage(question);
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Page title */}
      <div style={{ marginBottom: '48px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/ai')}
          style={{ marginBottom: '16px' }}
        >
          Back to AI Assistant
        </Button>
        
        <div style={{ 
          margin: 0, 
          color: '#262626', 
          fontSize: '20px', 
          textAlign: 'center', 
          fontWeight: '600',
          lineHeight: '1.2',
          marginBottom: '24px'
        }}>
          <MessageOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
          AI Chat Assistant
        </div>
      </div>

      <Row gutter={24} style={{ height: 'calc(100vh - 200px)' }}>
        {/* Chat area */}
        <Col xs={24} lg={18}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <RobotOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                <span>AI Conversation</span>
              </div>
            }
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
          >
            {/* Messages area */}
            <div style={{ 
              flex: 1, 
              overflowY: 'auto', 
              padding: '16px',
              maxHeight: 'calc(100vh - 380px)',
              background: '#fafafa'
            }}>
              <List
                dataSource={messages}
                renderItem={(message) => (
                  <List.Item style={{ border: 'none', padding: '8px 0' }}>
                    <div style={{ 
                      display: 'flex', 
                      flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
                      width: '100%',
                      alignItems: 'flex-start'
                    }}>
                      <Avatar 
                        icon={message.sender === 'user' ? <UserOutlined /> : <RobotOutlined />}
                        style={{ 
                          backgroundColor: message.sender === 'user' ? '#FF7A00' : '#10B981',
                          margin: message.sender === 'user' ? '0 0 0 8px' : '0 8px 0 0'
                        }}
                      />
                      <div style={{ 
                        maxWidth: '70%',
                        background: message.sender === 'user' ? '#FF7A00' : '#fff',
                        color: message.sender === 'user' ? 'white' : '#262626',
                        padding: '12px 16px',
                        borderRadius: '12px',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        border: message.sender === 'ai' ? '1px solid #e8e8e8' : 'none'
                      }}>
                        <div style={{ whiteSpace: 'pre-line', fontSize: '14px', lineHeight: '1.6' }}>
                          {message.content}
                        </div>
                        <div style={{ 
                          fontSize: '12px', 
                          opacity: 0.7, 
                          marginTop: '8px',
                          textAlign: message.sender === 'user' ? 'right' : 'left'
                        }}>
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
              
              {isLoading && (
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  padding: '16px',
                  color: '#FF7A00'
                }}>
                  <Avatar 
                    icon={<RobotOutlined />}
                    style={{ backgroundColor: '#10B981', marginRight: '8px' }}
                  />
                  <Spin size="small" style={{ marginRight: '8px' }} />
                  <Text style={{ color: '#666' }}>AI is thinking...</Text>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input area */}
            <div style={{ 
              padding: '16px', 
              borderTop: '1px solid #e8e8e8',
              background: 'white'
            }}>
              <Space.Compact style={{ width: '100%' }}>
                <TextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="Ask me anything about the system..."
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  onPressEnter={(e) => {
                    if (!e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                  style={{ flex: 1 }}
                />
                <Button 
                  type="primary" 
                  icon={<SendOutlined />}
                  onClick={() => handleSendMessage()}
                  loading={isLoading}
                  disabled={!inputValue.trim()}
                  style={{ 
                    height: 'auto',
                    minHeight: '32px',
                    backgroundColor: '#FF7A00',
                    borderColor: '#FF7A00'
                  }}
                >
                  Send
                </Button>
              </Space.Compact>
            </div>
          </Card>
        </Col>

        {/* Quick questions sidebar */}
        <Col xs={24} lg={6}>
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <QuestionCircleOutlined style={{ marginRight: '8px', color: '#10B981' }} />
                <span>Quick Questions</span>
              </div>
            }
            size="small"
            style={{ height: 'fit-content' }}
          >
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Click on any question to get started:
              </Text>
            </div>
            
            <Space direction="vertical" style={{ width: '100%' }}>
              {quickQuestions.map((question, index) => (
                <Button
                  key={index}
                  size="small"
                  style={{ 
                    width: '100%', 
                    textAlign: 'left', 
                    height: 'auto',
                    padding: '8px 12px',
                    whiteSpace: 'normal',
                    fontSize: '12px',
                    borderColor: '#ffe7d3',
                    color: '#FF7A00'
                  }}
                  onClick={() => handleQuickQuestion(question)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = '#FF7A00';
                    e.currentTarget.style.backgroundColor = '#fff7f0';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = '#ffe7d3';
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                >
                  {question}
                </Button>
              ))}
            </Space>

            <div style={{ 
              marginTop: '16px', 
              padding: '12px', 
              background: '#f6ffed',
              borderRadius: '6px',
              border: '1px solid #b7eb8f'
            }}>
              <Text strong style={{ color: '#10B981', fontSize: '12px' }}>
                💡 Pro Tips:
              </Text>
              <ul style={{ 
                margin: '8px 0 0 12px', 
                fontSize: '11px', 
                color: '#10B981',
                paddingLeft: '12px'
              }}>
                <li>Ask specific questions for better results</li>
                <li>Use keywords like "revenue", "client", "project"</li>
                <li>I can help with system navigation</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AIChat; 