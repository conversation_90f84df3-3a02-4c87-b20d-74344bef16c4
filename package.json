{"name": "ltc-project", "version": "0.1.0", "private": true, "scripts": {"start": "cd frontend && npm start", "start:backend": "cd backend && npm run dev", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "dev": "concurrently \"npm run start:backend\" \"npm run start\""}, "dependencies": {"@tanstack/react-query": "^5.76.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "concurrently": "^8.2.2", "express": "^5.1.0"}, "devDependencies": {"@types/react-window": "^1.8.8", "@types/xlsx": "^0.0.35", "typescript": "^5.8.3"}}