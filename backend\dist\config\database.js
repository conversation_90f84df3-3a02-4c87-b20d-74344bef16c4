"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const Project_1 = require("../entities/Project");
const User_1 = require("../entities/User");
const Document_1 = require("../entities/Document");
const ProjectStage_1 = require("../entities/ProjectStage");
const TeamMember_1 = require("../entities/TeamMember");
const ProjectCollaborator_1 = require("../entities/ProjectCollaborator");
const ProjectActivity_1 = require("../entities/ProjectActivity");
const PermissionTemplate_1 = require("../entities/PermissionTemplate");
const config = {
    type: 'sqlite',
    database: 'ltc_project.sqlite',
    entities: [Project_1.Project, User_1.User, Document_1.Document, ProjectStage_1.ProjectStage, TeamMember_1.TeamMember, ProjectCollaborator_1.ProjectCollaborator, ProjectActivity_1.ProjectActivity, PermissionTemplate_1.PermissionTemplate],
    synchronize: true,
    logging: process.env.NODE_ENV === 'development'
};
exports.default = config;
//# sourceMappingURL=database.js.map