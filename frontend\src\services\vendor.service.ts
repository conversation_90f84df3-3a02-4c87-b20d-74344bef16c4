import { Vendor } from '../types';

const STORAGE_KEY = 'vendors';

const mockVendors: Vendor[] = [];

class VendorService {
  private vendors: Vendor[] = [];

  constructor() {
    this.loadFromStorage();
  }

  private loadFromStorage() {
    const data = localStorage.getItem(STORAGE_KEY);
    if (data) {
      try {
        this.vendors = JSON.parse(data);
      } catch {
        this.vendors = [];
      }
    } else {
      this.vendors = [...mockVendors];
    }
  }

  private saveToStorage() {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(this.vendors));
  }

  async getAllVendors(): Promise<Vendor[]> {
    this.loadFromStorage();
    return this.vendors.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  async getVendor(id: string): Promise<Vendor> {
    this.loadFromStorage();
    const vendor = this.vendors.find(v => v.id === id);
    if (!vendor) throw new Error('Vendor not found');
    return vendor;
  }

  async createVendor(vendorData: Omit<Vendor, 'id' | 'createdAt' | 'updatedAt'>): Promise<Vendor> {
    this.loadFromStorage();
    const newVendor: Vendor = {
      ...vendorData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.vendors.unshift(newVendor);
    this.saveToStorage();
    return newVendor;
  }

  async updateVendor(id: string, updates: Partial<Vendor>): Promise<Vendor> {
    this.loadFromStorage();
    const index = this.vendors.findIndex(v => v.id === id);
    if (index === -1) throw new Error('Vendor not found');
    const updatedVendor = {
      ...this.vendors[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    this.vendors[index] = updatedVendor;
    this.saveToStorage();
    return updatedVendor;
  }

  async deleteVendor(id: string): Promise<void> {
    this.loadFromStorage();
    const index = this.vendors.findIndex(v => v.id === id);
    if (index === -1) throw new Error('Vendor not found');
    this.vendors.splice(index, 1);
    this.saveToStorage();
  }

  async searchVendors(query: string): Promise<Vendor[]> {
    this.loadFromStorage();
    if (!query.trim()) return this.getAllVendors();
    const lower = query.toLowerCase();
    return this.vendors.filter(v =>
      v.name.toLowerCase().includes(lower) ||
      v.vendorId.toLowerCase().includes(lower) ||
      v.contactPerson.toLowerCase().includes(lower) ||
      v.category.toLowerCase().includes(lower) ||
      v.country.toLowerCase().includes(lower)
    );
  }

  // 按类别获取供应商
  async getVendorsByCategory(category: string): Promise<Vendor[]> {
    this.loadFromStorage();
    return this.vendors.filter(vendor => vendor.category === category);
  }

  // 按状态获取供应商
  async getVendorsByStatus(status: string): Promise<Vendor[]> {
    this.loadFromStorage();
    return this.vendors.filter(vendor => vendor.status === status);
  }

  // 获取供应商统计
  async getVendorStats(): Promise<{
    totalVendors: number;
    activeVendors: number;
    totalSpend: number;
    averageSpend: number;
    categoryDistribution: { [key: string]: number };
    statusDistribution: { [key: string]: number };
  }> {
    this.loadFromStorage();
    const stats = {
      totalVendors: this.vendors.length,
      activeVendors: this.vendors.filter(v => v.status === 'active').length,
      totalSpend: this.vendors.reduce((sum, v) => sum + v.totalSpend, 0),
      averageSpend: this.vendors.length > 0 ? 
        this.vendors.reduce((sum, v) => sum + v.totalSpend, 0) / this.vendors.length : 0,
      categoryDistribution: this.vendors.reduce((acc, vendor) => {
        acc[vendor.category] = (acc[vendor.category] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
      statusDistribution: this.vendors.reduce((acc, vendor) => {
        acc[vendor.status] = (acc[vendor.status] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number })
    };
    return stats;
  }
}

export default new VendorService(); 