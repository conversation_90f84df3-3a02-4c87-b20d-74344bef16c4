import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Descriptions, Avatar, Tag, Button, Tabs, Table, Space, message, Spin } from 'antd';
import { UserOutlined, ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import teamMemberService from '../services/team-member.service';
import { TeamMember, Project } from '../types';
import moment from 'moment';

const { TabPane } = Tabs;

const TeamMemberDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [teamMember, setTeamMember] = useState<TeamMember | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    if (id) {
      fetchTeamMemberData();
    }
  }, [id]);

  const fetchTeamMemberData = async () => {
    try {
      setLoading(true);
      const memberData = await teamMemberService.getTeamMemberById(id!);
      setTeamMember(memberData);
      
      // 获取团队成员的项目
      const projectsData = await teamMemberService.getTeamMemberProjects(id!);
      setProjects(projectsData);
    } catch (error) {
      console.error('Error fetching team member data:', error);
      message.error('获取团队成员数据失败');
    } finally {
      setLoading(false);
    }
  };

  const projectColumns = [
    {
      title: '项目编号',
      dataIndex: 'projectId',
      key: 'projectId',
      render: (text: string, record: Project) => (
        <a onClick={() => navigate(`/projects/${record.id}`)}>{text}</a>
      ),
    },
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '客户',
      dataIndex: 'client',
      key: 'client',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'default';
        let text = status;
        
        switch (status) {
          case 'planning':
            color = 'blue';
            text = '规划中';
            break;
          case 'in_progress':
            color = 'green';
            text = '进行中';
            break;
          case 'completed':
            color = 'purple';
            text = '已完成';
            break;
          case 'on_hold':
            color = 'orange';
            text = '已暂停';
            break;
        }
        
        return <Tag color={color}>{text}</Tag>;
      },
    },
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!teamMember) {
    return (
      <div>
        <Button 
          icon={<ArrowLeftOutlined />} 
          style={{ marginBottom: 16 }}
          onClick={() => navigate('/team')}
        >
          返回团队列表
        </Button>
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            团队成员不存在或已被删除
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <Button 
        icon={<ArrowLeftOutlined />} 
        style={{ marginBottom: 16 }}
        onClick={() => navigate('/team')}
      >
        返回团队列表
      </Button>

      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Avatar 
              size={64} 
              icon={<UserOutlined />} 
              src={teamMember.avatar}
              style={{ marginRight: 16 }}
            />
            <div>
              <h2 style={{ margin: 0 }}>{teamMember.name}</h2>
              <p style={{ margin: 0, color: '#666' }}>{teamMember.position || '未设置'} | {teamMember.department || '未设置'}</p>
            </div>
          </div>
          <Button 
            type="primary" 
            icon={<EditOutlined />}
            onClick={() => navigate(`/team/${id}/edit`)}
          >
            编辑
          </Button>
        </div>

        <Descriptions bordered>
          <Descriptions.Item label="邮箱">{teamMember.email}</Descriptions.Item>
          <Descriptions.Item label="电话">{teamMember.phone || '未设置'}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={teamMember.status === 'active' ? 'green' : 'red'}>
              {teamMember.status === 'active' ? '在职' : '离职'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="创建日期" span={3}>
            {teamMember.createdAt ? moment(teamMember.createdAt).format('YYYY-MM-DD') : '未设置'}
          </Descriptions.Item>
          <Descriptions.Item label="简介" span={3}>
            {teamMember.bio || '暂无简介'}
          </Descriptions.Item>
        </Descriptions>

        <Tabs defaultActiveKey="projects" style={{ marginTop: 20 }}>
          <TabPane tab="参与项目" key="projects">
            <Table 
              columns={projectColumns} 
              dataSource={projects} 
              rowKey="id"
              pagination={{ pageSize: 5 }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TeamMemberDetail;
