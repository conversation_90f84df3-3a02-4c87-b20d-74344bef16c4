# LTC 项目管理系统

这是一个基于 React + Node.js + PostgreSQL 的 LTC（Lead to Cash）项目管理系统，用于跟踪项目从线索到现金的完整流程。

## 功能特点

- 项目管理：创建、查看、更新和删除项目
- 项目跟踪：跟踪项目从线索到现金的完整流程（基本信息、提案、合同、执行、验收、财务、结项、售后）
- 用户认证：用户注册和登录
- 权限控制：基于角色的访问控制
- 数据统计：项目状态和进度统计

## 技术栈

### 前端
- React.js
- TypeScript
- Ant Design
- React Router
- Axios

### 后端
- Node.js
- Express.js
- TypeScript
- TypeORM
- PostgreSQL
- JWT 认证

## 项目结构

```
ltc-project/
├── frontend/          # React 前端项目
├── backend/           # Node.js 后端项目
├── database/          # 数据库迁移和种子文件
└── docs/              # 项目文档
```

## 开发环境设置

### 前提条件
- Node.js (v14+)
- PostgreSQL (v12+)
- npm 或 yarn

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/你的用户名/ltc-project.git
cd ltc-project
```

2. 安装后端依赖
```bash
cd backend
npm install
```

3. 配置后端环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，设置数据库连接信息和JWT密钥
```

4. 启动后端服务器
```bash
npm run dev
```

5. 安装前端依赖
```bash
cd ../frontend
npm install
```

6. 启动前端开发服务器
```bash
npm start
```

## 贡献指南

1. Fork 这个仓库
2. 创建你的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交你的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启一个 Pull Request

## 许可证

[MIT](LICENSE)