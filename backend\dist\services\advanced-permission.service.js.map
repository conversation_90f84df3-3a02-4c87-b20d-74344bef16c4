{"version": 3, "file": "advanced-permission.service.js", "sourceRoot": "", "sources": ["../../src/services/advanced-permission.service.ts"], "names": [], "mappings": ";;;AAAA,qCAAwC;AACxC,uEAAkF;AAClF,yEAAwF;AACxF,2CAAwC;AACxC,iDAA8C;AAC9C,4CAAiF;AA6BjF,MAAa,yBAAyB;IAAtC;QACU,uBAAkB,GAAG,IAAA,uBAAa,EAAC,uCAAkB,CAAC,CAAC;QACvD,2BAAsB,GAAG,IAAA,uBAAa,EAAC,yCAAmB,CAAC,CAAC;QAC5D,mBAAc,GAAG,IAAA,uBAAa,EAAC,WAAI,CAAC,CAAC;QACrC,sBAAiB,GAAG,IAAA,uBAAa,EAAC,iBAAO,CAAC,CAAC;IAmUrD,CAAC;IA9TC,KAAK,CAAC,cAAc,CAClB,IAAY,EACZ,WAAmB,EACnB,WAAgC,EAChC,OAAqB,iCAAY,CAAC,MAAM,EACxC,WAAoB;QAGpB,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;QACpF,IAAI,gBAAgB,EAAE;YACpB,MAAM,IAAI,wBAAe,CAAC,WAAW,CAAC,CAAC;SACxC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC9C,IAAI;YACJ,WAAW;YACX,IAAI;YACJ,WAAW;YACX,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;SACzD,CAAC,CAAC;QAEH,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzB,SAAS,EAAE,CAAC,WAAW,CAAC;YACxB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,IAAkB;QACzC,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACxC,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE;YAC/B,SAAS,EAAE,CAAC,WAAW,CAAC;YACxB,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,2BAA2B,CAC/B,UAAkB,EAClB,cAAsB,EACtB,WAAmB;QAEnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACnC,MAAM,IAAI,sBAAa,CAAC,aAAa,CAAC,CAAC;SACxC;QAED,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7E,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,IAAI,sBAAa,CAAC,QAAQ,CAAC,CAAC;SACnC;QAGD,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;QAG1E,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;QAEhD,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,UAAkB,EAClB,eAAyB,EACzB,WAAmB;QAEnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACnE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACnC,MAAM,IAAI,sBAAa,CAAC,aAAa,CAAC,CAAC;SACxC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC,eAAe,EAAE;YACjF,SAAS,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SAC/B,CAAC,CAAC;QAEH,MAAM,OAAO,GAA0B,EAAE,CAAC;QAE1C,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE;YACxC,IAAI;gBAEF,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;gBAE1E,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;gBAChD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACrB;YAAC,OAAO,KAAK,EAAE;gBAEd,OAAO,CAAC,IAAI,CAAC,4CAA4C,YAAY,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;aACrF;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,KAAK,CAAC,8BAA8B,CAClC,SAAiB,EACjB,MAAc,EACd,WAA0D,EAC1D,gBAAiC,EACjC,WAAmB;QAEnB,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAG5D,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE;YAEjB,YAAY,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;gBAChD,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;gBAC1B,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;gBACpB,IAAI,EAAE,sCAAgB,CAAC,YAAY;gBACnC,MAAM,EAAE,UAAU;gBAClB,WAAW,EAAE;oBACX,GAAG,WAAW;oBACd,gBAAgB;iBACjB;aACF,CAAC,CAAC;SACJ;aAAM;YAEL,YAAY,CAAC,WAAW,GAAG;gBACzB,GAAG,WAAW;gBACd,gBAAgB;aACjB,CAAC;SACH;QAED,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC9D,CAAC;IAKD,qBAAqB,CAAC,gBAAiC,EAAE,cAAoB,IAAI,IAAI,EAAE;QACrF,IAAI,CAAC,gBAAgB;YAAE,OAAO,IAAI,CAAC;QAGnC,IAAI,gBAAgB,CAAC,SAAS,IAAI,WAAW,GAAG,gBAAgB,CAAC,SAAS,EAAE;YAC1E,OAAO,KAAK,CAAC;SACd;QACD,IAAI,gBAAgB,CAAC,OAAO,IAAI,WAAW,GAAG,gBAAgB,CAAC,OAAO,EAAE;YACtE,OAAO,KAAK,CAAC;SACd;QAGD,IAAI,gBAAgB,CAAC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzE,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;gBACrD,OAAO,KAAK,CAAC;aACd;SACF;QAGD,IAAI,gBAAgB,CAAC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzE,MAAM,cAAc,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9D,MAAM,SAAS,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACzD,OAAO,cAAc,IAAI,KAAK,CAAC,KAAK,IAAI,cAAc,IAAI,KAAK,CAAC,GAAG,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO,KAAK,CAAC;aACd;SACF;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,mBAAmB,CAAC,cAAwB,EAAE,QAAgB;QAC5D,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEhE,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YAErC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAE3B,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAErD,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aAC/F;iBAAM;gBAEL,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,GAAG,CAAC;aACpD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,2BAA2B,CAC/B,SAAiB,EACjB,MAAc,EACd,UAAkB,EAClB,QAAiB,EACjB,cAAoB,IAAI,IAAI,EAAE;QAE9B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;SAChF,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;SAC9C;QAED,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QAG7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC9E,IAAI,CAAC,kBAAkB,EAAE;YACvB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;SAC7C;QAGD,IAAI,WAAW,CAAC,gBAAgB,EAAE;YAChC,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;YAC1F,IAAI,CAAC,WAAW,EAAE;gBAChB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;aAChD;SACF;QAGD,IAAI,WAAW,CAAC,cAAc,IAAI,QAAQ,EAAE;YAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YACjF,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;aAClD;SACF;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAKD,KAAK,CAAC,sBAAsB,CAC1B,OAGE,EACF,WAAmB;QAEnB,MAAM,OAAO,GAA0B,EAAE,CAAC;QAE1C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI;gBACF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE;oBACpF,SAAS,EAAE,CAAC,SAAS,CAAC;iBACvB,CAAC,CAAC;gBAEH,IAAI,YAAY,EAAE;oBAChB,MAAM,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;oBAC1E,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;oBAC9C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACnE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACrB;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,IAAI,CAAC,iCAAiC,MAAM,CAAC,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;aAChF;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAIO,KAAK,CAAC,wBAAwB,CAAC,SAAiB,EAAE,MAAc;QACtE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE;SAC5D,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,sCAAgB,CAAC,KAAK,EAAE,sCAAgB,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;YAClG,MAAM,IAAI,uBAAc,CAAC,QAAQ,CAAC,CAAC;SACpC;IACH,CAAC;IAEO,oBAAoB,CAAC,WAAgC,EAAE,UAAkB;QAC/E,QAAQ,UAAU,EAAE;YAClB,KAAK,MAAM;gBACT,OAAO,WAAW,CAAC,OAAO,CAAC;YAC7B,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,KAAK,QAAQ;gBACX,OAAO,WAAW,CAAC,SAAS,CAAC;YAC/B,KAAK,cAAc;gBACjB,OAAO,WAAW,CAAC,eAAe,CAAC;YACrC,KAAK,iBAAiB;gBACpB,OAAO,WAAW,CAAC,kBAAkB,CAAC;YACxC,KAAK,gBAAgB;gBACnB,OAAO,WAAW,CAAC,iBAAiB,CAAC;YACvC,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC,UAAU,CAAC;YAChC,KAAK,SAAS;gBACZ,OAAO,WAAW,CAAC,UAAU,CAAC;YAChC;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;CACF;AAvUD,8DAuUC"}