import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export const supportedCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];

// 汇率数据接口
interface ExchangeRates {
  [key: string]: number;
}

interface CurrencyContextType {
  selectedCurrency: string;
  supportedCurrencies: string[];
  exchangeRates: ExchangeRates;
  setCurrency: (currency: string) => void;
  getCurrencySymbol: (currencyCode?: string) => string;
  formatAmount: (amount: number, currencyCode?: string) => string;
  convertAmount: (amount: number, fromCurrency: string, toCurrency?: string) => number;
  updateExchangeRates: () => Promise<void>;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export const useCurrency = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};

interface CurrencyProviderProps {
  children: ReactNode;
}

// 默认汇率数据（基于EUR）
const defaultExchangeRates: ExchangeRates = {
  EUR: 1.0,      // 基准货币
  USD: 1.08,     // 1 EUR = 1.08 USD
  GBP: 0.85,     // 1 EUR = 0.85 GBP
  CNY: 7.72,     // 1 EUR = 7.72 CNY
  JPY: 158.5     // 1 EUR = 158.5 JPY
};

export const CurrencyProvider: React.FC<CurrencyProviderProps> = ({ children }) => {
  const [selectedCurrency, setCurrencyState] = useState<string>('EUR');
  const [exchangeRates, setExchangeRates] = useState<ExchangeRates>(defaultExchangeRates);

  useEffect(() => {
    // 从localStorage读取货币设置
    const savedCurrency = localStorage.getItem('selectedCurrency');
    if (savedCurrency && supportedCurrencies.includes(savedCurrency)) {
      setCurrencyState(savedCurrency);
    }

    // 从localStorage读取汇率数据
    const savedRates = localStorage.getItem('exchangeRates');
    if (savedRates) {
      try {
        const rates = JSON.parse(savedRates);
        setExchangeRates({ ...defaultExchangeRates, ...rates });
      } catch (e) {
        console.warn('Failed to parse saved exchange rates, using defaults');
      }
    }

    // 初始化时更新汇率（延迟执行避免依赖问题）
    const timer = setTimeout(() => {
      updateExchangeRates();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  const setCurrency = (newCurrency: string) => {
    if (supportedCurrencies.includes(newCurrency)) {
      setCurrencyState(newCurrency);
      localStorage.setItem('selectedCurrency', newCurrency);
    }
  };

  const getCurrencySymbol = (currencyCode?: string) => {
    const code = currencyCode || selectedCurrency;
    switch (code) {
      case 'EUR':
        return '€';
      case 'USD':
        return '$';
      case 'GBP':
        return '£';
      case 'CNY':
        return '¥';
      case 'JPY':
        return '¥';
      default:
        return '€';
    }
  };

  // 更新汇率数据
  const updateExchangeRates = async () => {
    try {
      // 模拟API调用获取实时汇率
      // 在实际项目中，这里会调用真实的汇率API
      const mockRates = {
        EUR: 1.0,
        USD: 1.08 + (Math.random() - 0.5) * 0.02, // 模拟汇率波动
        GBP: 0.85 + (Math.random() - 0.5) * 0.01,
        CNY: 7.72 + (Math.random() - 0.5) * 0.1,
        JPY: 158.5 + (Math.random() - 0.5) * 2
      };

      setExchangeRates(mockRates);
      localStorage.setItem('exchangeRates', JSON.stringify(mockRates));
      localStorage.setItem('exchangeRatesUpdated', new Date().toISOString());
      
      console.log('📈 Exchange rates updated:', mockRates);
    } catch (error) {
      console.error('Failed to update exchange rates:', error);
    }
  };

  // 金额转换功能
  const convertAmount = (amount: number, fromCurrency: string, toCurrency?: string): number => {
    const targetCurrency = toCurrency || selectedCurrency;
    if (fromCurrency === targetCurrency) {
      return Math.round(amount * 10) / 10;
    }
    const fromRate = exchangeRates[fromCurrency] || 1;
    const toRate = exchangeRates[targetCurrency] || 1;
    const eurAmount = amount / fromRate;
    const convertedAmount = eurAmount * toRate;
    return Math.round(convertedAmount * 10) / 10; // 保留1位小数
  };

  const formatAmount = (amount: number, currencyCode?: string) => {
    // 🔧 安全检查：处理undefined/null/NaN等异常值
    if (amount === undefined || amount === null || isNaN(amount)) {
      const symbol = getCurrencySymbol(currencyCode || selectedCurrency);
      return `${symbol}0.0`;
    }
    
    const code = currencyCode || selectedCurrency;
    const symbol = getCurrencySymbol(code);
    return `${symbol}${amount.toLocaleString('en-US', {
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    })}`;
  };

  const value = {
    selectedCurrency,
    supportedCurrencies,
    exchangeRates,
    setCurrency,
    getCurrencySymbol,
    formatAmount,
    convertAmount,
    updateExchangeRates
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
}; 