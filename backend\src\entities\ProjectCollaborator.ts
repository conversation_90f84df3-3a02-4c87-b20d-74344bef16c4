import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, Index } from 'typeorm';
import { Project } from './Project';
import { User } from './User';

export enum CollaboratorRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  EDITOR = 'editor',
  VIEWER = 'viewer',
  GUEST_EDITOR = 'guest_editor',
  GUEST_VIEWER = 'guest_viewer'
}

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired'
}

@Entity('project_collaborators')
@Index(['project', 'user'], { unique: true })
export class ProjectCollaborator {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Project, project => project.collaborators, { onDelete: 'CASCADE' })
  project: Project;

  @ManyToOne(() => User, { nullable: true, onDelete: 'CASCADE' })
  user: User;

  @Column({ nullable: true })
  invitedEmail: string;

  @Column({
    type: 'varchar',
    default: CollaboratorRole.VIEWER
  })
  role: CollaboratorRole;

  @Column({
    type: 'varchar',
    default: InvitationStatus.PENDING
  })
  status: InvitationStatus;

  @Column({ type: 'datetime', nullable: true })
  expiresAt: Date;

  @Column({ nullable: true })
  invitedBy: string;

  @Column({ type: 'text', nullable: true })
  invitationMessage: string;

  @Column({ type: 'text', nullable: true })
  permissions: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  lastAccessedAt: Date;
} 