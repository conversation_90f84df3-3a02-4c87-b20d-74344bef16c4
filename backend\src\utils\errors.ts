export class BadRequestError extends Error {
  public statusCode: number = 400;

  constructor(message: string) {
    super(message);
    this.name = 'BadRequestError';
  }
}

export class NotFoundError extends Error {
  public statusCode: number = 404;

  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class ForbiddenError extends Error {
  public statusCode: number = 403;

  constructor(message: string) {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class UnauthorizedError extends Error {
  public statusCode: number = 401;

  constructor(message: string) {
    super(message);
    this.name = 'UnauthorizedError';
  }
} 