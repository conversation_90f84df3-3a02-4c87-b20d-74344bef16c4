import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Switch,
  Button,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  Space,
  Tag,
  message,
  Alert
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  SafetyCertificateOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { TeamMember, TeamRole, PermissionTemplate } from '../../types';
import teamService from '../../services/team.service';

interface MemberPermissionModalProps {
  visible: boolean;
  onCancel: () => void;
  member: TeamMember;
  projectId: string;
  currentUserRole: TeamRole | null;
  onPermissionUpdated: () => void;
}

const { Title, Text } = Typography;

const MemberPermissionModal: React.FC<MemberPermissionModalProps> = ({
  visible,
  onCancel,
  member,
  projectId,
  currentUserRole,
  onPermissionUpdated
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState(member.permissions);
  const [selectedTemplate, setSelectedTemplate] = useState<TeamRole | null>(member.role);

  const permissionTemplates = teamService.getPermissionTemplates();
  
  const canEditPermissions = currentUserRole === TeamRole.OWNER || 
    (currentUserRole === TeamRole.ADMIN && member.role !== TeamRole.OWNER);

  useEffect(() => {
    setPermissions(member.permissions);
    setSelectedTemplate(member.role);
    form.setFieldsValue(member.permissions);
  }, [member, form]);

  const permissionLabels = {
    canViewProject: 'View Project',
    canEditProject: 'Edit Project',
    canDeleteProject: 'Delete Project',
    canManageTeam: 'Manage Team',
    canManageFinance: 'Manage Finance',
    canExportData: 'Export Data',
    canViewReports: 'View Reports'
  };

  const permissionDescriptions = {
    canViewProject: 'Access and view project details',
    canEditProject: 'Modify project content and settings',
    canDeleteProject: 'Permanently delete the project',
    canManageTeam: 'Add, remove, and manage team members',
    canManageFinance: 'Access financial data and billing',
    canExportData: 'Export project data and reports',
    canViewReports: 'View analytics and reports'
  };

  const handleApplyTemplate = (template: PermissionTemplate) => {
    setPermissions(template.permissions);
    setSelectedTemplate(template.role);
    form.setFieldsValue(template.permissions);
  };

  const handlePermissionChange = (permission: keyof typeof permissions, checked: boolean) => {
    const newPermissions = { ...permissions, [permission]: checked };
    setPermissions(newPermissions);
    
    // Check if current permissions match any template
    const matchingTemplate = permissionTemplates.find(template => 
      JSON.stringify(template.permissions) === JSON.stringify(newPermissions)
    );
    setSelectedTemplate(matchingTemplate?.role || null);
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      await teamService.updateMemberPermissions(projectId, member.id, permissions);
      onPermissionUpdated();
      onCancel();
      message.success('Permissions updated successfully');
    } catch (error) {
      message.error('Failed to update permissions');
    } finally {
      setLoading(false);
    }
  };

  const getDangerousPermissions = () => {
    return Object.entries(permissions)
      .filter(([key, value]) => value && ['canDeleteProject', 'canManageTeam', 'canManageFinance'].includes(key))
      .map(([key]) => key);
  };

  const dangerousPermissions = getDangerousPermissions();

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          <span>Manage Permissions - {member.name}</span>
          <Tag color="blue">{member.role.charAt(0).toUpperCase() + member.role.slice(1)}</Tag>
        </Space>
      }
      visible={visible}
      onCancel={onCancel}
      width={700}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={loading}
          onClick={handleSave}
          disabled={!canEditPermissions}
          icon={<SafetyCertificateOutlined />}
        >
          Save Permissions
        </Button>
      ]}
    >
      {!canEditPermissions && (
        <Alert
          message="You don't have permission to edit this member's permissions"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {dangerousPermissions.length > 0 && (
        <Alert
          message="Dangerous Permissions Enabled"
          description={`This member has access to: ${dangerousPermissions.map(p => permissionLabels[p as keyof typeof permissionLabels]).join(', ')}`}
          type="warning"
          showIcon
          icon={<ExclamationCircleOutlined />}
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 权限模板 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={5}>Permission Templates</Title>
        <Row gutter={8}>
          {permissionTemplates.map(template => (
            <Col span={6} key={template.role}>
              <Card
                size="small"
                hoverable
                onClick={() => canEditPermissions && handleApplyTemplate(template)}
                style={{
                  borderColor: selectedTemplate === template.role ? '#1890ff' : undefined,
                  backgroundColor: selectedTemplate === template.role ? '#f6ffed' : undefined,
                  cursor: canEditPermissions ? 'pointer' : 'not-allowed'
                }}
              >
                <div style={{ textAlign: 'center' }}>
                  <Tag color={template.role === TeamRole.OWNER ? 'gold' : 
                              template.role === TeamRole.ADMIN ? 'blue' :
                              template.role === TeamRole.MEMBER ? 'green' : 'default'}>
                    {template.role.charAt(0).toUpperCase() + template.role.slice(1)}
                  </Tag>
                  <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: 4 }}>
                    {template.description}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      <Divider />

      {/* 详细权限设置 */}
      <div>
        <Title level={5}>
          <SettingOutlined /> Detailed Permissions
        </Title>
        
        <Form form={form} layout="vertical">
          {Object.entries(permissionLabels).map(([key, label]) => {
            const isEnabled = permissions[key as keyof typeof permissions];
            const isDangerous = ['canDeleteProject', 'canManageTeam', 'canManageFinance'].includes(key);
            
            return (
              <Row key={key} style={{ marginBottom: 16 }}>
                <Col span={24}>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '12px',
                    border: '1px solid #f0f0f0',
                    borderRadius: '6px',
                    backgroundColor: isEnabled ? '#f6ffed' : '#fafafa'
                  }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 8 
                      }}>
                        <Text strong style={{ color: isEnabled ? '#52c41a' : '#8c8c8c' }}>
                          {label}
                        </Text>
                                                 {isDangerous && (
                           <Tag color="orange" style={{ fontSize: '11px' }}>
                             <ExclamationCircleOutlined /> Critical
                           </Tag>
                         )}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {permissionDescriptions[key as keyof typeof permissionDescriptions]}
                      </Text>
                    </div>
                    <Switch
                      checked={isEnabled}
                      onChange={(checked) => handlePermissionChange(key as keyof typeof permissions, checked)}
                      disabled={!canEditPermissions}
                    />
                  </div>
                </Col>
              </Row>
            );
          })}
        </Form>
      </div>

      {/* 权限总结 */}
      <Divider />
      <div>
        <Title level={5}>Permission Summary</Title>
        <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
          {Object.entries(permissions)
            .filter(([_, value]) => value)
            .map(([key]) => (
              <Tag 
                key={key} 
                color={['canDeleteProject', 'canManageTeam', 'canManageFinance'].includes(key) ? 'orange' : 'green'}
              >
                {permissionLabels[key as keyof typeof permissionLabels]}
              </Tag>
            ))}
          {Object.values(permissions).every(v => !v) && (
            <Text type="secondary">No permissions enabled</Text>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default MemberPermissionModal; 