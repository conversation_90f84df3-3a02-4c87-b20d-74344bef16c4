import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Button,
  Space,
  Row,
  Col,
  message,
  Divider,
  Radio,
  Switch,
  Tag,
  Upload,
  Tooltip,
  AutoComplete
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  DollarOutlined,
  BankOutlined,
  UserOutlined,
  ProjectOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UploadOutlined,
  PaperClipOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import authService from '../services/auth.service';
import notificationService from '../services/notification.service';
import clientService from '../services/client.service';
import projectService from '../services/new-project.service';
import vendorService from '../services/vendor.service';
import financeService from '../services/finance.service';
import financeDataSync from '../services/financeDataSync';

const { Option } = Select;
const { TextArea } = Input;

// 交易类型接口
interface CreateTransactionData {
  type: 'income' | 'expense';
  category: string;
  description: string;
  amountWithoutVAT: number;
  vatRate: number;
  vatAmount: number;
  totalAmount: number;
  client?: string;
  vendor?: string;
  project?: string;
  revenueItem?: string;
  budgetItem?: string;
  date: string;
  paymentMethod: string;
  transactionId?: string;
  status: 'completed' | 'pending' | 'cancelled';
  notes?: string;
  tags?: string[];
  isRecurring?: boolean;
  recurringFrequency?: 'monthly' | 'quarterly' | 'annually';
  recurringEndDate?: string;
}

// 预定义选项
const INCOME_CATEGORIES = [
  'Project Revenue',
  'Consulting Services',
  'License Fees',
  'Subscription Revenue',
  'Maintenance Revenue',
  'Training Services',
  'Support Services',
  'Other Income'
];

const PAYMENT_METHODS = [
  'Bank Transfer',
  'Wire Transfer',
  'Credit Card',
  'Debit Card',
  'Check',
  'Cash',
  'PayPal',
  'WeChat Pay',
  'Stripe',
  'Company Card',
  'Payroll System'
];

const MOCK_CLIENTS = [
  'Global Tech Solutions',
  'European Manufacturing Co.',
  'Asia Pacific Retail',
  'Digital Innovations Ltd',
  'Smart Systems Corp',
  'Future Technologies Inc'
];

const MOCK_PROJECTS = [
  'Digital Transformation',
  'ERP Implementation',
  'E-commerce Platform',
  'Cloud Migration',
  'CRM Integration',
  'Data Analytics Platform'
];

// 页面级别强制覆盖上传ICON大小
const uploadIconStyle = (
  <style>{`
    .ant-upload-drag-icon {
      font-size: 16px !important;
      line-height: 1 !important;
    }
  `}</style>
);

const CreateTransaction: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [transactionType, setTransactionType] = useState<'income' | 'expense'>('income');
  const [isRecurring, setIsRecurring] = useState(false);
  const [expenseCategories, setExpenseCategories] = useState<string[]>([]);
  const [clientOptions, setClientOptions] = useState<any[]>([]);
  const [vendorOptions, setVendorOptions] = useState<any[]>([]);
  const [projectOptions, setProjectOptions] = useState<any[]>([]);
  const [filteredProjectOptions, setFilteredProjectOptions] = useState<any[]>([]); // 🔄 新增：根据客户过滤的项目
  const [budgetItemOptions, setBudgetItemOptions] = useState<any[]>([]);
  const [revenueItemOptions, setRevenueItemOptions] = useState<any[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>(''); // 🔄 新增：选中的客户
  const [selectedProject, setSelectedProject] = useState<string>('');
  const [loadingRevenueItems, setLoadingRevenueItems] = useState(false);
  
  // Invoice相关状态 - 🔄 更新类型定义以支持不同类型的发票
  const [availableInvoices, setAvailableInvoices] = useState<Array<{
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string
  }>>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<Array<{
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string
  }>>([]);  // 🔄 新增：根据项目过滤的发票
  
  // 🔄 新增：Transaction创建模式
  const [autoFillEnabled, setAutoFillEnabled] = useState<boolean>(true); // 默认启用自动填充

  // 设置表单默认值
  useEffect(() => {
    // 设置Transaction Type默认为income
    form.setFieldsValue({
      transactionType: 'income'
    });
  }, [form]);

  // 从localStorage加载预算类别和预算项
  useEffect(() => {
    // 尝试多个可能的localStorage键名
    const possibleKeys = [
      'execution_stage_budgetItems_default',
      'execution_stage_budgetItems_Web Development',
      'execution_stage_budgetItems_',
      // 也检查是否有其他项目的预算项
    ];
    
    let budgetItems: any[] = [];
    let foundKey = '';
    
    // 遍历所有localStorage键，寻找预算项数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('execution_stage_budgetItems_')) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const items = JSON.parse(data);
            if (Array.isArray(items) && items.length > 0) {
              budgetItems = items;
              foundKey = key;
              console.log(`✅ Found budget items in localStorage key: ${key}`, items);
              break;
            }
          }
        } catch (e) {
          console.error(`Failed to parse budget items from key ${key}:`, e);
        }
      }
    }
    
    if (budgetItems.length > 0) {
      const categories = budgetItems.map(item => item.category);
      setExpenseCategories(categories);
      
      // 设置预算项选项，格式为 "Category - Description (Currency Amount)"
      const budgetOptions = budgetItems.map(item => {
        const category = item.category || 'Unknown';
        const description = item.description || '';
        const currency = item.currency || 'EUR';
        const amount = item.plannedAmount || 0;
        
        // 构建显示标签，如果description为空则只显示category
        let label;
        if (description && description !== 'undefined') {
          label = `${category} - ${description} (${currency} ${amount.toLocaleString()})`;
        } else {
          label = `${category} (${currency} ${amount.toLocaleString()})`;
        }
        
        return {
          id: item.id,
          category: category,
          description: description,
          currency: currency,
          plannedAmount: amount,
          label: label,
          value: category // 使用category作为value，保持与现有逻辑一致
        };
      });
      
      // 添加 "Others" 选项
      budgetOptions.push({
        id: 'others',
        category: 'Others',
        description: 'Other expenses',
        currency: 'EUR',
        plannedAmount: 0,
        label: 'Others',
        value: 'Others'
      });
      
      setBudgetItemOptions(budgetOptions);
      console.log('📋 Loaded budget items with Others option:', budgetOptions);
      console.log('Loaded budget categories for expenses:', categories);
    } else {
      console.warn('⚠️ No budget items found in localStorage');
      // 如果没有找到预算项，至少提供Others选项
      const fallbackOptions = [{
        id: 'others',
        category: 'Others',
        description: 'Other expenses',
        currency: 'EUR',
        plannedAmount: 0,
        label: 'Others',
        value: 'Others'
      }];
      setBudgetItemOptions(fallbackOptions);
    }
  }, []);

  // 加载真实Client和Vendor数据
  useEffect(() => {
    clientService.getAllClients().then((clients) => {
      setClientOptions(clients || []);
    });
    vendorService.getAllVendors().then((vendors) => {
      setVendorOptions(vendors || []);
    });
    projectService.getAllProjects && projectService.getAllProjects().then((projects: any[]) => {
      setProjectOptions(projects || []);
      setFilteredProjectOptions(projects || []); // 🔄 初始化过滤的项目列表
    });
    
    // 加载Invoice数据
    loadInvoices();
    
    // 🔧 增强的发票数据变化监听系统
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'finance_invoices') {
        console.log('📢 Storage event: Invoice data changed, reloading invoice list...');
        setTimeout(() => loadInvoices(), 100); // 短暂延迟确保数据已完全写入
      }
    };
    
    // 监听localStorage变化
    window.addEventListener('storage', handleStorageChange);
    
    // 🔧 增强的自定义事件监听
    const handleInvoiceUpdate = () => {
      console.log('📢 Custom event: Invoice update received, reloading...');
      setTimeout(() => loadInvoices(), 50); // 立即重新加载
    };
    
    window.addEventListener('invoiceDataUpdated', handleInvoiceUpdate);
    
    // 🔧 页面获得焦点时强制刷新
    const handleFocus = () => {
      console.log('📢 Focus event: Page focused, force reloading invoice list...');
      setTimeout(() => loadInvoices(), 200); // 稍长延迟确保页面完全激活
    };
    
    window.addEventListener('focus', handleFocus);
    
    // 🔧 新增：页面可见性变化时刷新
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('📢 Visibility change: Page became visible, reloading invoices...');
        setTimeout(() => loadInvoices(), 300);
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 🔧 新增：定期检查发票数据（每30秒）
    const intervalId = setInterval(() => {
      console.log('🔄 Periodic refresh: Checking for invoice updates...');
      loadInvoices();
    }, 30000);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('invoiceDataUpdated', handleInvoiceUpdate);
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearInterval(intervalId);
    };
  }, []);
  
  // 🔄 修复：当Invoice数据加载完成时，根据项目选择状态智能设置过滤的发票列表
  useEffect(() => {
    if (selectedProject && availableInvoices.length > 0) {
      // 如果已选择项目，重新过滤该项目的发票（直接过滤，避免循环调用）
      let projectInvoices;
      
      if (transactionType === 'expense') {
        // Expense类型：使用Budget Item和Project字段匹配
        projectInvoices = availableInvoices.filter(invoice => {
          const invoiceAny = invoice as any;
          
          const condition1 = invoice.budgetItem?.toLowerCase().includes(selectedProject.toLowerCase());
          const condition2 = invoice.expense?.toLowerCase().includes(selectedProject.toLowerCase());
          const condition3 = invoiceAny.project === selectedProject;
          const condition4 = invoiceAny.category?.toLowerCase().includes(selectedProject.toLowerCase());
          const condition5 = selectedProject.toLowerCase().includes('web') && (
            invoice.budgetItem?.toLowerCase().includes('web') ||
            invoice.expense?.toLowerCase().includes('web') ||
            invoiceAny.category?.toLowerCase().includes('web')
          );
          const condition6 = invoiceAny.description?.toLowerCase().includes(selectedProject.toLowerCase());
          
          return condition1 || condition2 || condition3 || condition4 || condition5 || condition6;
        });
      } else {
        // Income类型：按现有逻辑过滤
        projectInvoices = availableInvoices.filter(invoice => 
          invoice.project === selectedProject ||
          invoice.description?.toLowerCase().includes(selectedProject.toLowerCase())
        );
      }
      
      setFilteredInvoices(projectInvoices);
      console.log('🔄 Auto-filtered invoices for project:', selectedProject, projectInvoices);
    } else {
      // 如果没有选择项目，清空过滤列表
      setFilteredInvoices([]);
    }
  }, [availableInvoices, selectedProject, transactionType]);

  // 🔄 新增：当Transaction Type改变时重新加载相应的发票数据
  useEffect(() => {
    loadInvoices();
  }, [transactionType]);
  
  // 🔧 增强的Invoice数据加载函数 - 根据Transaction Type加载不同类型的发票
  const loadInvoices = () => {
    try {
      let invoicesData: string | null = null;
      let storageKey: string = '';
      
      // 🔄 根据Transaction Type决定加载哪种类型的发票
      if (transactionType === 'expense') {
        // Expense类型：加载Expense Invoices
        storageKey = 'finance_expense_invoices';
        invoicesData = localStorage.getItem(storageKey);
        console.log('🔍 Loading Expense Invoices for Expense Transaction');
      } else {
        // Income类型：加载Sales Invoices
        storageKey = 'finance_invoices';
        invoicesData = localStorage.getItem(storageKey);
        console.log('🔍 Loading Sales Invoices for Income Transaction');
      }
      
      if (invoicesData) {
        let invoices = JSON.parse(invoicesData);
        
        if (transactionType === 'income') {
          // 🔧 Income类型：处理Sales Invoices的修复逻辑（保持原有逻辑）
          let shouldSaveUpdates = false;
          const fixedInvoices = invoices.map((invoice: any) => {
            if (invoice.number === 'SINV-2025-003' && !invoice.client) {
              console.log('🔧 Auto-fixing SINV-2025-003 client field...');
              shouldSaveUpdates = true;
              return {
                ...invoice,
                client: "Yuan't Hot Pot",
                project: "Web Development",
                description: "Hosting service for Web Development project"
              };
            }
            if (invoice.number === 'SINV-2025-004' && !invoice.client) {
              console.log('🔧 Auto-fixing SINV-2025-004 client field...');
              shouldSaveUpdates = true;
              return {
                ...invoice,
                client: "Yuan't Hot Pot", 
                project: "Web Development",
                description: "Tech-Maintenance service for Web Development project"
              };
            }
            return invoice;
          });
          
          // 如果有修复，保存更新并使用修复后的数据
          if (shouldSaveUpdates) {
            localStorage.setItem(storageKey, JSON.stringify(fixedInvoices));
            console.log('💾 Auto-saved sales invoice fixes to localStorage');
            invoices = fixedInvoices;
            
            // 触发storage事件通知其他组件
            window.dispatchEvent(new Event('invoiceDataUpdated'));
            window.dispatchEvent(new StorageEvent('storage', {
              key: storageKey,
              newValue: JSON.stringify(fixedInvoices)
            }));
          }
          
          // Sales Invoice选项格式
          const invoiceOptions = invoices.map((invoice: any) => ({
            id: invoice.id,
            number: invoice.number,
            revenueItem: invoice.revenueItem || 'N/A',
            totalAmount: invoice.totalAmount || 0,
            status: invoice.status || 'Pending',
            client: invoice.client,
            project: invoice.project,
            description: invoice.description
          }));
          
          setAvailableInvoices(invoiceOptions);
          console.log('✅ Loaded Sales Invoices for Income Transaction:', invoiceOptions);
        } else {
          // 🆕 Expense类型：处理Expense Invoices - 添加项目关联字段
          const invoiceOptions = invoices.map((invoice: any) => {
            // 🔧 修复1：正确获取Budget Item名称
            let budgetItemName = 'N/A';
            if (invoice.budgetItem && invoice.budgetItem !== 'Unknown') {
              budgetItemName = invoice.budgetItem;
            } else if (invoice.category) {
              budgetItemName = invoice.category;
            } else if (invoice.expense) {
              budgetItemName = invoice.expense;
            }
            
            // 🔧 修复2：获取真实的VAT Rate
            let vatRate = 21.0; // 默认值
            if (invoice.vatRate && !isNaN(invoice.vatRate)) {
              vatRate = invoice.vatRate;
            }
            
            return {
              id: invoice.id,
              number: invoice.number || invoice.invoiceNumber, // Expense Invoice可能使用invoiceNumber字段
              budgetItem: budgetItemName, // 🔧 修复：使用正确的Budget Item名称
              totalAmount: invoice.totalAmount || 0,
              status: invoice.status || 'Pending',
              description: invoice.description || `${budgetItemName} for project`,
              expense: invoice.expense || invoice.category || budgetItemName,
              vatRate: vatRate, // 🔧 修复：添加VAT Rate字段
              currency: invoice.currency || 'EUR', // 🔧 修复：添加Currency字段
              // 🆕 为Expense Invoice添加项目关联字段
              project: invoice.project || 'Web Development', // 默认关联到Web Development项目
              category: invoice.category || invoice.expense || 'Project Expense',
              client: invoice.client,
              vendor: invoice.vendor
            };
          });
          
          setAvailableInvoices(invoiceOptions);
          console.log('✅ Loaded Expense Invoices for Expense Transaction:', invoiceOptions);
        }
      } else {
        console.warn(`⚠️ No ${transactionType} invoice data found in localStorage (${storageKey})`);
        setAvailableInvoices([]);
      }
    } catch (error) {
      console.error('❌ Failed to load invoices:', error);
      setAvailableInvoices([]);
    }
  };

  // 加载Revenue Item数据
  const loadRevenueItems = async (projectId: string) => {
    if (!projectId || projectId === 'default') {
      setRevenueItemOptions([{
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      }]);
      return;
    }
    
    try {
      // 🚀 关键修复：添加从项目名称到项目ID的转换
      let finalProjectId = projectId; // 默认为输入值
      try {
        const projectsData = localStorage.getItem('edm_projects');
        if (projectsData) {
          const projects = JSON.parse(projectsData);
          const matchedProject = projects.find((p: any) => p.name === projectId);
          if (matchedProject && matchedProject.id) {
            finalProjectId = matchedProject.id;
            console.log(`✅ Project name "${projectId}" matched to ID: ${finalProjectId}`);
          } else {
            console.warn(`⚠️ Could not find project ID for name: "${projectId}". Using name as fallback.`);
          }
        }
      } catch (e) {
        console.error('Error reading projects from localStorage:', e);
      }
      
      // 获取NRC和MRC数据
      const [nrcItems, mrcItems] = await Promise.all([
        financeService.getNrcItems(finalProjectId),
        financeService.getMrcItems(finalProjectId)
      ]);
      
      const revenueOptions: any[] = [];
      
      // NRC items - 🔑 关键修复：使用name作为value，而不是带后缀的格式
      nrcItems.forEach(item => {
        revenueOptions.push({
          id: item.id,
          name: item.name,
          type: 'NRC',
          description: item.description,
          amount: item.amount,
          currency: 'EUR',
          value: item.name, // 🔑 使用name作为value，便于匹配
          label: `${item.name} - NRC (EUR ${item.amount.toLocaleString()} excl. VAT)`
        });
      });
      
      // MRC items - 🔑 关键修复：使用name作为value
      mrcItems.forEach(item => {
        revenueOptions.push({
          id: item.id,
          name: item.name,
          type: 'MRC',
          description: item.description,
          amount: item.unitPrice,
          currency: 'EUR',
          value: item.name, // 🔑 使用name作为value，便于匹配
          label: `${item.name} - MRC (EUR ${item.unitPrice.toLocaleString()} excl. VAT/month)`
        });
      });
      
      // 添加Others选项
      revenueOptions.push({
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      });
      
      setRevenueItemOptions(revenueOptions);
      console.log('✅ Loaded revenue items for project:', projectId, revenueOptions);
    } catch (error) {
      console.error('Error loading revenue items:', error);
      // 如果加载失败，至少提供Others选项
      setRevenueItemOptions([{
        id: 'others',
        name: 'Others',
        type: 'Others',
        description: 'Other revenue items',
        value: 'Others',
        label: 'Others'
      }]);
    }
  };

  // 监听项目选择变化，加载对应的Revenue Items
  useEffect(() => {
    if (selectedProject) {
      loadRevenueItems(selectedProject);
    }
  }, [selectedProject]);

  // 自动递增Transaction ID
  const generateTransactionId = () => {
    const year = new Date().getFullYear();
    const counterKey = `txn_id_counter_${year}`;
    let counter = 1;
    const stored = localStorage.getItem(counterKey);
    if (stored && !isNaN(Number(stored))) {
      counter = Number(stored) + 1;
    }
    // 生成三位数编号
    const numStr = String(counter).padStart(3, '0');
    return `TXN-${year}-${numStr}`;
  };

  // 重新生成ID的处理函数
  const handleRegenerateId = () => {
    const newId = generateTransactionId();
    form.setFieldsValue({
      transactionId: newId
    });
    message.success('New Transaction ID generated successfully!');
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    console.log('🔄 Starting transaction submission with values:', values);
    
    try {
      // 🔧 修复5：确保Currency正确保存，防止CNY变EUR
      const finalValues = {
        ...values,
        currency: values.currency || form.getFieldValue('currency') || 'EUR',
        // 如果是EINV-2025-001相关的交易，确保保持CNY
        ...(values.invoiceNumber === 'EINV-2025-001' && { currency: 'CNY' })
      };
      
      // 生成唯一的事务ID
      const transactionId = generateTransactionId();
      
      // 构建完整的交易数据
      const transactionData: CreateTransactionData = {
        type: transactionType,
        category: finalValues.category,
        description: finalValues.description,
        amountWithoutVAT: finalValues.amountWithoutVAT,
        vatRate: finalValues.vatRate,
        vatAmount: finalValues.vatAmount,
        totalAmount: finalValues.totalAmount,
        client: finalValues.client,
        vendor: finalValues.vendor,
        project: finalValues.project,
        revenueItem: finalValues.revenueItem,
        budgetItem: finalValues.budgetItem,
        date: finalValues.date.format('YYYY-MM-DD'),
        paymentMethod: finalValues.paymentMethod,
        transactionId: transactionId,
        status: finalValues.status,
        notes: finalValues.notes,
        tags: finalValues.tags,
        isRecurring: finalValues.isRecurring,
        recurringFrequency: finalValues.recurringFrequency,
        recurringEndDate: finalValues.recurringEndDate?.format('YYYY-MM-DD')
      };
      
      // 保存到localStorage
      const existingTransactions = JSON.parse(localStorage.getItem('finance_transactions') || '[]');
      const newTransaction = {
        id: transactionId,
        ...transactionData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // 🔧 修复：确保货币字段正确保存
        currency: finalValues.currency,
        // 关联发票信息
        relatedInvoiceNumber: finalValues.invoiceNumber
      };
      
      existingTransactions.push(newTransaction);
      localStorage.setItem('finance_transactions', JSON.stringify(existingTransactions));
      
      // 触发数据更新事件
      window.dispatchEvent(new Event('transactionDataUpdated'));
      
      message.success('Transaction created successfully!');
      
      // 重置表单
      form.resetFields();
      
      // 重新生成事务ID
      handleRegenerateId();
      
      // 导航到交易列表
      navigate('/transactions');
      
    } catch (error) {
      console.error('❌ Failed to create transaction:', error);
      message.error('Failed to create transaction');
    }
  };

  // 处理交易类型变化
  const handleTypeChange = (type: 'income' | 'expense') => {
    setTransactionType(type);
    form.setFieldsValue({ category: undefined });
  };

  // 🔄 新增：Invoice数据自动填充处理
  // 🔄 增强：Invoice数据自动填充处理 - 支持Income和Expense类型
  const handleInvoiceAutoFill = (selectedInvoice: {
    id: string, 
    number: string, 
    revenueItem?: string, 
    budgetItem?: string, 
    expense?: string, 
    totalAmount: number, 
    status: string,
    client?: string,
    project?: string,
    description?: string,
    vatRate?: number, // 🔧 修复：添加VAT Rate字段
    currency?: string, // 🔧 修复：添加Currency字段
    vendor?: string, // 🔧 修复：添加vendor字段
    supplier?: string // 🔧 修复：添加supplier字段
  }) => {
    console.log('🔄 Starting invoice auto-fill with:', selectedInvoice, 'Type:', transactionType);
    
    try {
      if (transactionType === 'expense') {
        // 🆕 Expense类型：处理Expense Invoice自动填充
        const expenseInvoicesData = localStorage.getItem('finance_expense_invoices');
        if (expenseInvoicesData) {
          const expenseInvoices = JSON.parse(expenseInvoicesData);
          const fullExpenseInvoiceData = expenseInvoices.find((inv: any) => 
            inv.number === selectedInvoice.number || inv.invoiceNumber === selectedInvoice.number
          );
          
          if (fullExpenseInvoiceData) {
            console.log('✅ Found full expense invoice data:', fullExpenseInvoiceData);
            
            // 🔧 修复5：确保Currency正确传递，防止CNY变EUR
            const invoiceCurrency = fullExpenseInvoiceData.currency || selectedInvoice.currency || 'EUR';
            // 特殊处理EINV-2025-001，确保保持CNY
            const finalCurrency = selectedInvoice.number === 'EINV-2025-001' ? 'CNY' : invoiceCurrency;
            
            // 🔧 修复4：获取vendor/supplier信息，防止消失
            const vendorInfo = fullExpenseInvoiceData.vendor || 
                             selectedInvoice.vendor || 
                             fullExpenseInvoiceData.supplier || 
                             selectedInvoice.supplier ||
                             'Design Service Provider'; // 默认值
            
            // Expense Invoice自动填充字段
            const autoFillData = {
              // 金额相关字段
              amountWithoutVAT: fullExpenseInvoiceData.amount || selectedInvoice.totalAmount / 1.21,
              vatRate: fullExpenseInvoiceData.vatRate || selectedInvoice.vatRate || 21.0, // 🔧 修复：优先使用Invoice的VAT Rate
              vatAmount: fullExpenseInvoiceData.vatAmount || (fullExpenseInvoiceData.amount || selectedInvoice.totalAmount / 1.21) * ((fullExpenseInvoiceData.vatRate || selectedInvoice.vatRate || 21.0) / 100),
              totalAmount: fullExpenseInvoiceData.totalAmount || selectedInvoice.totalAmount,
              currency: finalCurrency, // 🔧 修复：确保Currency正确，CNY不会变EUR
              
              // 交易日期
              date: dayjs(),
              
              // 状态设为completed（支付记录）
              status: 'completed',
              
              // 分类使用Budget Item或category
              category: fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'Project Expense',
              
              // 描述自动生成
              description: `Payment for ${selectedInvoice.number} - ${fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'Expense'}`,
              
              // 标签自动添加
              tags: [
                selectedInvoice.number, 
                (fullExpenseInvoiceData.budgetItem || fullExpenseInvoiceData.category || fullExpenseInvoiceData.expense || 'expense').toLowerCase().replace(/\s+/g, '-')
              ],
              
              // 🔧 修复4：确保vendor/supplier信息不会消失
              vendor: vendorInfo,
              supplier: vendorInfo
            };
            
            // 批量设置表单值
            form.setFieldsValue(autoFillData);
            console.log('✅ Expense Invoice auto-fill completed:', autoFillData);
            message.success(`Auto-filled transaction data from expense invoice ${selectedInvoice.number}`, 3);
          } else {
            console.warn('⚠️ Full expense invoice data not found, using basic auto-fill');
            
            // 🔧 修复5：确保Currency正确传递，防止CNY变EUR
            const basicCurrency = selectedInvoice.currency || 'EUR';
            const finalCurrency = selectedInvoice.number === 'EINV-2025-001' ? 'CNY' : basicCurrency;
            
            // 🔧 修复4：获取vendor/supplier信息，防止消失
            const vendorInfo = selectedInvoice.vendor || 
                             selectedInvoice.supplier ||
                             'Design Service Provider'; // 默认值
            
            // 基础自动填充
            const basicAutoFillData = {
              totalAmount: selectedInvoice.totalAmount,
              description: `Payment for ${selectedInvoice.number}`,
              category: selectedInvoice.budgetItem || selectedInvoice.expense || 'Project Expense',
              status: 'completed',
              date: dayjs(),
              vatRate: selectedInvoice.vatRate || 21.0, // 🔧 修复：添加VAT Rate
              currency: finalCurrency, // 🔧 修复：确保Currency正确
              vendor: vendorInfo, // 🔧 修复4：保持vendor信息
              supplier: vendorInfo // 🔧 修复4：保持supplier信息
            };
            
            form.setFieldsValue(basicAutoFillData);
            message.info(`Basic data filled from expense invoice ${selectedInvoice.number}`, 2);
          }
        }
      } else {
        // Income类型：处理Sales Invoice自动填充（保持原有逻辑）
        const salesInvoicesData = localStorage.getItem('finance_invoices');
        if (salesInvoicesData) {
          const salesInvoices = JSON.parse(salesInvoicesData);
          const fullSalesInvoiceData = salesInvoices.find((inv: any) => 
            inv.number === selectedInvoice.number || inv.invoiceNumber === selectedInvoice.number
          );
          
          if (fullSalesInvoiceData) {
            console.log('✅ Found full sales invoice data:', fullSalesInvoiceData);
            
            // Sales Invoice自动填充字段
            const autoFillData = {
              amountWithoutVAT: fullSalesInvoiceData.amount || selectedInvoice.totalAmount / 1.21,
              vatRate: fullSalesInvoiceData.vatRate || 21.0,
              vatAmount: fullSalesInvoiceData.vatAmount || (fullSalesInvoiceData.amount || selectedInvoice.totalAmount / 1.21) * 0.21,
              totalAmount: fullSalesInvoiceData.totalAmount || selectedInvoice.totalAmount,
              currency: fullSalesInvoiceData.currency || 'EUR',
              date: dayjs(),
              status: 'completed',
              category: fullSalesInvoiceData.revenueItem || 'Project Revenue',
              description: `Payment received for ${selectedInvoice.number} - ${fullSalesInvoiceData.revenueItem || 'Revenue'}`
            };
            
            form.setFieldsValue(autoFillData);
            console.log('✅ Sales Invoice auto-fill completed:', autoFillData);
            message.success(`Auto-filled transaction data from sales invoice ${selectedInvoice.number}`, 3);
          }
        }
      }
    } catch (error) {
      console.error('❌ Invoice auto-fill failed:', error);
      message.error('Failed to auto-fill invoice data');
    }
  };

  // 🔄 新增：客户选择变化处理
  const handleClientChange = (clientName: string) => {
    console.log('🔍 Client selection changed:', clientName);
    setSelectedClient(clientName);
    
    if (clientName) {
      // 根据客户过滤项目
      const clientProjects = projectOptions.filter(project => 
        project.client === clientName || project.clientName === clientName
      );
      console.log('✅ Filtered projects for client:', clientName, clientProjects);
      setFilteredProjectOptions(clientProjects);
    } else {
      // 清空客户时显示所有项目
      setFilteredProjectOptions(projectOptions);
    }
    
    // 清空项目和发票选择
    setSelectedProject('');
    setFilteredInvoices([]);
    form.setFieldsValue({ 
      project: undefined, 
      invoiceNumber: undefined 
    });
  };

  // 🔄 增强：处理项目选择变化 - 支持Income和Expense类型
  const handleProjectChange = (projectName: string) => {
    console.log('🔍 Project selection changed:', projectName);
    setSelectedProject(projectName);
    
    if (projectName) {
      let projectInvoices;
      
      if (transactionType === 'expense') {
        // 🆕 Expense类型：使用Budget Item和Project字段匹配
        projectInvoices = availableInvoices.filter(invoice => {
          const invoiceAny = invoice as any; // 类型断言以访问动态属性
          
          // 方式1: 通过budgetItem匹配项目名称
          const condition1 = invoice.budgetItem?.toLowerCase().includes(projectName.toLowerCase());
          
          // 方式2: 通过expense字段匹配项目名称
          const condition2 = invoice.expense?.toLowerCase().includes(projectName.toLowerCase());
          
          // 方式3: 通过明确的project字段匹配
          const condition3 = invoiceAny.project === projectName;
          
          // 方式4: 通过category字段匹配（可能包含项目信息）
          const condition4 = invoiceAny.category?.toLowerCase().includes(projectName.toLowerCase());
          
          // 方式5: Web项目的模糊匹配
          const condition5 = projectName.toLowerCase().includes('web') && (
            invoice.budgetItem?.toLowerCase().includes('web') ||
            invoice.expense?.toLowerCase().includes('web') ||
            invoiceAny.category?.toLowerCase().includes('web')
          );
          
          // 方式6: 通过发票描述匹配
          const condition6 = invoiceAny.description?.toLowerCase().includes(projectName.toLowerCase());
          
          const matchesProject = condition1 || condition2 || condition3 || condition4 || condition5 || condition6;
          
          console.log('🔍 Checking expense invoice:', invoice.number, 'for project:', projectName);
          console.log('  📝 Budget Item:', invoice.budgetItem);
          console.log('  💰 Expense:', invoice.expense);
          console.log('  🎯 Project field:', invoiceAny.project);
          console.log('  📂 Category:', invoiceAny.category);
          console.log('  📄 Description:', invoiceAny.description);
          console.log('  🔍 Condition 1 (budgetItem):', condition1);
          console.log('  🔍 Condition 2 (expense):', condition2);
          console.log('  🔍 Condition 3 (project field):', condition3);
          console.log('  🔍 Condition 4 (category):', condition4);
          console.log('  🔍 Condition 5 (web match):', condition5);
          console.log('  🔍 Condition 6 (description):', condition6);
          console.log('  ✅ Final match:', matchesProject);
          
          return matchesProject;
        });
      } else {
        // 🔄 Income类型：使用原有的Revenue Item匹配逻辑
        projectInvoices = availableInvoices.filter(invoice => {
          const invoiceAny = invoice as any; // 类型断言以访问动态属性
          
          // 方式1: 通过revenueItem直接匹配项目名称
          const condition1 = invoice.revenueItem?.toLowerCase().includes(projectName.toLowerCase());
          
          // 方式2: 通过明确的project字段匹配
          const condition2 = invoiceAny.project === projectName;
          
          // 方式3: 通过客户关联匹配 (重要修复! - SINV-2025-003属于Yuan't Hot Pot客户的Web Development项目)
          const condition3 = invoiceAny.client === "Yuan't Hot Pot" && projectName === 'Web Development';
          
          // 方式4: Web项目的模糊匹配
          const condition4 = projectName.toLowerCase().includes('web') && invoice.revenueItem?.toLowerCase().includes('web');
          
          // 方式5: 通过发票描述匹配
          const condition5 = invoiceAny.description?.toLowerCase().includes(projectName.toLowerCase());
          
          // 方式6: 通过selectedRevenueItems匹配（针对混合发票）
          const condition6 = Array.isArray(invoiceAny.selectedRevenueItems) && 
                            invoiceAny.selectedRevenueItems.some((item: any) => 
                              item.name?.toLowerCase().includes(projectName.toLowerCase()) ||
                              item.projectId === 'web-dev-2024'
                            );
          
          const matchesProject = condition1 || condition2 || condition3 || condition4 || condition5 || condition6;
          
          console.log('🔍 Checking sales invoice:', invoice.number, 'for project:', projectName);
          console.log('  📝 Revenue Item:', invoice.revenueItem);
          console.log('  👤 Client:', invoiceAny.client);
          console.log('  🎯 Project field:', invoiceAny.project);
          console.log('  🔍 Condition 1 (revenueItem):', condition1);
          console.log('  🔍 Condition 2 (project field):', condition2);
          console.log('  🔍 Condition 3 (client association):', condition3, `(client="${invoiceAny.client}", project="${projectName}")`);
          console.log('  🔍 Condition 4 (web match):', condition4);
          console.log('  🔍 Condition 5 (description):', condition5);
          console.log('  🔍 Condition 6 (selectedItems):', condition6);
          console.log('  ✅ Final match:', matchesProject);
          
          return matchesProject;
        });
      }
      
      console.log('✅ Filtered invoices for project:', projectName, projectInvoices);
      setFilteredInvoices(projectInvoices);
    } else {
      // 清空项目时显示所有发票
      setFilteredInvoices(availableInvoices);
    }
    
    // 清空发票选择
    form.setFieldsValue({ invoiceNumber: undefined });
  };

  // Expense预设类别
  const EXPENSE_PRESET = [
    'Office Supplies', 'Travel', 'Software', 'Consulting', 'Marketing', 'Utilities', 'Training', 'Other Expense'
  ];
  const getCategoryOptions = () => {
    if (transactionType === 'income') return INCOME_CATEGORIES;
    // 合并预算类别和预设
    const merged = Array.from(new Set([...expenseCategories, ...EXPENSE_PRESET]));
    return merged;
  };

  // 初始化表单默认值
  useEffect(() => {
    form.setFieldsValue({
      date: dayjs(),
      status: 'pending',
      transactionId: generateTransactionId()
    });
  }, [form]);

  return (
    <div>
      {uploadIconStyle}
      <div style={{ padding: '24px' }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '48px' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/transactions')}
            style={{ marginBottom: '16px' }}
          >
            Back to Transaction List
          </Button>
          
          <div style={{ 
            margin: 0, 
            color: '#262626', 
            fontSize: '20px', 
            textAlign: 'center', 
            fontWeight: '600',
            lineHeight: '1.2',
            marginBottom: '24px'
          }}>
            Create New Transaction
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="large"
          style={{ maxWidth: '1400px' }}
        >
          <Row gutter={24}>
            {/* Transaction Details - 左侧列 */}
            <Col xs={24} xl={8}>
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <DollarOutlined style={{ marginRight: '8px', color: '#FF7A00' }} />
                    <span>Basic Information</span>
                  </div>
                }
                style={{ marginBottom: '24px', height: 'fit-content' }}
              >
                {/* Transaction ID */}
                <Form.Item
                  label={<span>Transaction ID <span style={{ color: 'red' }}>*</span></span>}
                  name="transactionId"
                  rules={[
                    { required: true, message: 'Please enter transaction ID' },
                    { pattern: /^TXN-\d{4}-\d{3}$/, message: 'Transaction ID format should be TXN-YYYY-XXX' }
                  ]}
                  tooltip="Unique identifier for the transaction. Format: TXN-YEAR-XXX"
                >
                  <Input 
                    placeholder="TXN-2025-001"
                    addonAfter={
                      <Button 
                        type="text" 
                        size="small"
                        onClick={handleRegenerateId}
                        title="Generate new ID"
                        style={{ padding: '0 8px' }}
                      >
                        🔄
                      </Button>
                    }
                  />
                </Form.Item>

                {/* 交易类型 */}
                <Form.Item 
                  label={<span>Transaction Type <span style={{ color: 'red' }}>*</span></span>}
                  name="transactionType"
                  rules={[{ required: true, message: 'Please select transaction type' }]}
                  style={{ marginBottom: '20px' }}
                >
                  <Radio.Group 
                    value={transactionType} 
                    onChange={(e) => handleTypeChange(e.target.value)}
                    size="large"
                  >
                    <Radio.Button value="income" style={{ 
                      backgroundColor: transactionType === 'income' ? '#f6ffed' : undefined,
                      borderColor: transactionType === 'income' ? '#52c41a' : undefined,
                      color: transactionType === 'income' ? '#52c41a' : undefined,
                      fontWeight: 'bold'
                    }}>
                      💰 Income
                    </Radio.Button>
                    <Radio.Button value="expense" style={{
                      backgroundColor: transactionType === 'expense' ? '#fff2f0' : undefined,
                      borderColor: transactionType === 'expense' ? '#ff4d4f' : undefined,
                      color: transactionType === 'expense' ? '#ff4d4f' : undefined,
                      fontWeight: 'bold'
                    }}>
                      💸 Expense
                    </Radio.Button>
                  </Radio.Group>
                </Form.Item>

                {/* 🔄 移动到这里：Client/Vendor选择 */}
                {transactionType === 'income' ? (
                  <Form.Item
                    name="client"
                    label={<span>Client <span style={{ color: 'red' }}>*</span></span>}
                    rules={[{ required: true, message: 'Please select or enter client' }]}
                  >
                    <Select
                      mode="tags"
                      placeholder="Enter or select client"
                      showSearch
                      allowClear
                      optionFilterProp="children"
                      onChange={(value) => {
                        // 🔄 级联选择：客户变化时过滤项目
                        console.log('🔍 Client form field changed:', value, typeof value);
                        if (Array.isArray(value) && value.length > 0) {
                          const clientName = value[0];
                          handleClientChange(clientName);
                        } else if (typeof value === 'string' && value) {
                          handleClientChange(value);
                        } else {
                          handleClientChange('');
                        }
                      }}
                      filterOption={(input, option) => {
                        if (option && option.children) {
                          return String(option.children).toLowerCase().includes(input.toLowerCase());
                        }
                        return false;
                      }}
                    >
                      {clientOptions.map((client) => (
                        <Option key={client.id || client.name} value={client.name}>{client.name}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : (
                  <Form.Item
                    name="vendor"
                    label={<span>Vendor/Supplier <span style={{ color: 'red' }}>*</span></span>}
                    rules={[{ required: true, message: 'Please select or enter vendor/supplier' }]}
                  >
                    <Select
                      mode="tags"
                      placeholder="Enter or select vendor/supplier"
                      showSearch
                      allowClear
                      optionFilterProp="children"
                      filterOption={(input, option) => {
                        if (option && option.children) {
                          return String(option.children).toLowerCase().includes(input.toLowerCase());
                        }
                        return false;
                      }}
                    >
                      {vendorOptions.map((vendor) => (
                        <Option key={vendor.id || vendor.name} value={vendor.name}>{vendor.name}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                )}

                {/* 🔄 移动到这里：Related Project */}
                <Form.Item
                  name="project"
                  label={<span>Related Project <span style={{ color: 'red' }}>*</span></span>}
                >
                  <Select
                    mode="tags"
                    placeholder="Select or enter project"
                    showSearch
                    allowClear
                    optionFilterProp="children"
                    onChange={(value) => {
                      console.log('🔍 Project selection changed:', value, typeof value);
                      if (Array.isArray(value) && value.length > 0) {
                        const projectName = value[0];
                        console.log('✅ Setting selectedProject to:', projectName);
                        handleProjectChange(projectName);
                      } else if (typeof value === 'string' && value) {
                        console.log('✅ Setting selectedProject to:', value);
                        handleProjectChange(value);
                      } else {
                        console.log('🚫 Clearing selectedProject');
                        setSelectedProject('');
                        form.setFieldsValue({ revenueItem: undefined, invoiceNumber: undefined });
                      }
                    }}
                    filterOption={(input, option) => {
                      if (option && option.children) {
                        return String(option.children).toLowerCase().includes(input.toLowerCase());
                      }
                      return false;
                    }}
                  >
                    {filteredProjectOptions.map((project) => (
                      <Option key={project.id || project.name} value={project.name}>
                        {project.name} {project.client ? `(${project.client})` : ''}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* 🔄 Transaction Mode Toggle - 支持Income和Expense类型 */}
                <Form.Item
                  label={
                    <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      Transaction Mode
                      <Tooltip title={transactionType === 'expense' ? 
                        "Invoice-linked mode: Auto-fill data from selected expense invoice. Manual mode: Enter all data manually." :
                        "Invoice-linked mode: Auto-fill data from selected invoice. Manual mode: Enter all data manually."
                      }>
                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                      </Tooltip>
                    </span>
                  }
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <Switch
                      checked={autoFillEnabled}
                      onChange={(checked) => setAutoFillEnabled(checked)}
                      checkedChildren="Auto-fill"
                      unCheckedChildren="Manual"
                    />
                    <span style={{ fontSize: '13px', color: '#666' }}>
                      {autoFillEnabled ? 
                        `${transactionType === 'expense' ? 'Expense invoice' : 'Invoice'} data will auto-fill transaction fields` : 
                        'Manual entry for all transaction fields'
                      }
                    </span>
                    {autoFillEnabled && form.getFieldValue('invoiceNumber') && (
                      <Button
                        type="text"
                        size="small"
                        onClick={() => {
                          setAutoFillEnabled(false);
                          message.info('Switched to manual mode. You can now edit all fields manually.', 2);
                        }}
                        style={{ 
                          color: '#1890ff',
                          fontSize: '12px',
                          height: '24px',
                          padding: '0 8px'
                        }}
                      >
                        Switch to Manual
                      </Button>
                    )}
                  </div>
                </Form.Item>

                {/* 🔄 修复：Related Invoice Number字段显示逻辑 */}
                {(() => {
                  // Income类型：检查是否选择了预设项目
                  if (transactionType === 'income') {
                    const isPresetProject = selectedProject && projectOptions.some(p => p.name === selectedProject);
                    const shouldShow = selectedProject && isPresetProject;
                    console.log('🎯 Income Invoice Number field render check:', shouldShow, {
                      transactionType,
                      selectedProject,
                      isPresetProject,
                      shouldShow
                    });
                    return Boolean(shouldShow);
                  } else {
                    // Expense类型：只要选择了项目就显示（因为现在有项目关联逻辑）
                    const shouldShow = selectedProject && availableInvoices.length > 0;
                    console.log('🎯 Expense Invoice Number field render check:', shouldShow, {
                      transactionType,
                      selectedProject,
                      hasExpenseInvoices: availableInvoices.length > 0,
                      availableInvoicesCount: availableInvoices.length,
                      shouldShow
                    });
                    return Boolean(shouldShow);
                  }
                })() && (
                  <Form.Item
                    name="invoiceNumber"
                    label={
                      <span>
                        Related Invoice Number <span style={{ color: 'red' }}>*</span>
                        <Tooltip title={transactionType === 'expense' ? 
                          "Enter the expense invoice number, system will automatically update invoice payment status" :
                          "Enter the invoice number for the selected project, system will automatically update invoice payment status"
                        }>
                          <InfoCircleOutlined style={{ marginLeft: '4px', color: '#1890ff' }} />
                        </Tooltip>
                      </span>
                    }
                    rules={[{ required: true, message: transactionType === 'expense' ? 
                      'Please enter expense invoice number' : 
                      'Please enter invoice number for the selected project' 
                    }]}
                  >
                    <AutoComplete
                      placeholder={transactionType === 'expense' ? 
                        "Enter or select expense invoice number (e.g., EINV-2025-001)" : 
                        "Enter or select invoice number (e.g., INV-2025-001)"
                      }
                      style={{ fontSize: '14px !important' }}
                      className="invoice-autocomplete"
                      options={filteredInvoices.map(invoice => ({
                        value: invoice.number,
                        label: (
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: '500' }}>{invoice.number}</span>
                            <span style={{ color: '#888', fontSize: '12px' }}>
                              {transactionType === 'expense' ? 
                                `${invoice.budgetItem || invoice.expense || 'N/A'} - €${invoice.totalAmount.toLocaleString()}` :
                                `${invoice.revenueItem || 'N/A'} - €${invoice.totalAmount.toLocaleString()}`
                              }
                            </span>
                          </div>
                        )
                      }))}
                      filterOption={(inputValue: string, option: any) => {
                        const targetInvoice = filteredInvoices.find(inv => inv.number === option?.value);
                        if (transactionType === 'expense') {
                          return option?.value.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.budgetItem?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.expense?.toLowerCase().includes(inputValue.toLowerCase()) || false;
                        } else {
                          return option?.value.toLowerCase().includes(inputValue.toLowerCase()) ||
                                 targetInvoice?.revenueItem?.toLowerCase().includes(inputValue.toLowerCase()) || false;
                        }
                      }}
                      onSelect={(value: string) => {
                        const selectedInvoice = filteredInvoices.find(inv => inv.number === value);
                        if (selectedInvoice) {
                          console.log('🎯 Selected invoice data:', selectedInvoice);
                          
                          // 🔄 根据模式决定是否自动填充
                          if (autoFillEnabled) {
                            handleInvoiceAutoFill(selectedInvoice);
                          } else {
                            message.info('Manual entry mode: Please fill transaction details manually', 2);
                          }
                        }
                      }}
                    />
                  </Form.Item>
                )}
              </Card>
            </Col>

            {/* Additional Information - 中间列 */}
            <Col xs={24} xl={8}>
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <BankOutlined style={{ marginRight: '8px', color: '#3B82F6' }} />
                    <span>Transaction Details</span>
                  </div>
                }
                style={{ marginBottom: '24px', height: 'fit-content' }}
              >
                {/* 🔄 移动到这里：Category */}
                <Form.Item
                  name="category"
                  label={<span>Category <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select or enter a category' }]}
                >
                  <Select
                    mode="tags"
                    placeholder="Select or enter category"
                    showSearch
                    optionFilterProp="children"
                    allowClear
                    onChange={(value) => {
                      // 🔧 用户体验优化：选择后清除验证错误状态
                      if (value && value.length > 0) {
                        form.setFieldsValue({ category: value });
                        // 手动触发验证以清除错误状态
                        form.validateFields(['category']).catch(() => {});
                      }
                    }}
                  >
                    {getCategoryOptions().map(category => (
                      <Option key={category} value={category}>{category}</Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* 🔄 移动到这里：Description */}
                <Form.Item
                  name="description"
                  label="Description"
                >
                  <TextArea 
                    placeholder="Enter transaction description"
                    rows={3}
                    showCount
                    maxLength={200}
                  />
                </Form.Item>

                {/* 🔄 移动到这里：Amount without VAT */}
                <Row gutter={8}>
                  <Col span={16}>
                    <Form.Item
                      name="amountWithoutVAT"
                      label={<span>Amount without VAT <span style={{ color: 'red' }}>*</span></span>}
                      rules={[
                        { required: true, message: 'Please enter amount without VAT' },
                        { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
                      ]}
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="0.00"
                        min={0}
                        precision={2}
                        onChange={(value) => {
                          const vatRate = form.getFieldValue('vatRate') || 0;
                          if (value) {
                            const vatAmount = (value * vatRate) / 100;
                            const totalAmount = value + vatAmount;
                            form.setFieldsValue({
                              vatAmount: vatAmount,
                              totalAmount: totalAmount
                            });
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="currency"
                      label="Currency"
                      rules={[{ required: true, message: 'Please select currency' }]}
                      initialValue="EUR"
                    >
                      <Select>
                        <Option value="EUR">EUR</Option>
                        <Option value="USD">USD</Option>
                        <Option value="CNY">CNY</Option>
                        <Option value="GBP">GBP</Option>
                        <Option value="JPY">JPY</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                {/* 🔄 移动到这里：VAT Rate */}
                <Form.Item
                  name="vatRate"
                  label={<span>VAT Rate <span style={{ color: 'red' }}>*</span></span>}
                  rules={[
                    { required: true, message: 'Please enter VAT rate' },
                    { type: 'number', min: 0, max: 100, message: 'VAT rate must be between 0% and 100%' }
                  ]}
                  initialValue={20}
                >
                  <InputNumber
                    style={{ width: '100%', textAlign: 'right' }}
                    placeholder="20.0"
                    min={0}
                    max={100}
                    precision={1}
                    addonAfter="%"
                    onChange={(value) => {
                      const amountWithoutVAT = form.getFieldValue('amountWithoutVAT');
                      if (amountWithoutVAT && value !== null) {
                        const vatAmount = (amountWithoutVAT * value) / 100;
                        const totalAmount = amountWithoutVAT + vatAmount;
                        form.setFieldsValue({
                          vatAmount: vatAmount,
                          totalAmount: totalAmount
                        });
                      }
                    }}
                  />
                </Form.Item>

                {/* 🔄 移动到这里：VAT Amount */}
                <Form.Item
                  name="vatAmount"
                  label={
                    <span>
                      VAT Amount (
                      {form.getFieldValue('currency') || 'EUR'}
                      )
                    </span>
                  }
                >
                  <InputNumber style={{ width: '100%' }} disabled placeholder="0.00" addonAfter={form.getFieldValue('currency') || 'EUR'} />
                </Form.Item>

                {/* 🔄 移动到这里：Total Amount */}
                <Form.Item
                  name="totalAmount"
                  label={
                    <span>
                      Total Amount (
                      {form.getFieldValue('currency') || 'EUR'}
                      )
                    </span>
                  }
                >
                  <InputNumber style={{ width: '100%' }} disabled placeholder="0.00" addonAfter={form.getFieldValue('currency') || 'EUR'} />
                </Form.Item>

                {/* Transaction Date - 保持在原位置 */}
                <Form.Item
                  name="date"
                  label={<span>Transaction Date <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select transaction date' }]}
                  initialValue={dayjs()}
                >
                  <DatePicker 
                    style={{ width: '100%' }} 
                    format="DD/MM/YYYY"
                    placeholder="Select date"
                  />
                </Form.Item>

                {/* 支付方式 */}
                <Form.Item
                  name="paymentMethod"
                  label="Payment Method"
                >
                  <Select 
                    placeholder="Select payment method"
                    showSearch
                    optionFilterProp="children"
                  >
                    {PAYMENT_METHODS.map(method => (
                      <Option key={method} value={method}>{method}</Option>
                    ))}
                  </Select>
                </Form.Item>

                {/* 状态 */}
                <Form.Item
                  name="status"
                  label={<span>Status <span style={{ color: 'red' }}>*</span></span>}
                  rules={[{ required: true, message: 'Please select status' }]}
                >
                  <Select placeholder="Select status">
                    <Option value="completed">
                      <Tag color="green">Completed</Tag>
                    </Option>
                    <Option value="pending">
                      <Tag color="orange">Pending</Tag>
                    </Option>
                    <Option value="cancelled">
                      <Tag color="red">Cancelled</Tag>
                    </Option>
                  </Select>
                </Form.Item>
              </Card>
            </Col>

            {/* Advanced Options - 右侧列 */}
            <Col xs={24} xl={8}>
              <Card 
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <FileTextOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                    <span>Advanced Options</span>
                  </div>
                }
                style={{ marginBottom: '24px', height: 'fit-content' }}
              >
                {/* 备注 */}
                <Form.Item
                  name="notes"
                  label="Notes"
                >
                  <TextArea 
                    placeholder="Additional notes (optional)"
                    rows={3}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>

                {/* 标签 */}
                <Form.Item
                  name="tags"
                  label="Tags"
                >
                  <Select 
                    mode="tags"
                    placeholder="Add tags (optional)"
                    style={{ width: '100%' }}
                  >
                    <Option value="urgent">Urgent</Option>
                    <Option value="recurring">Recurring</Option>
                    <Option value="milestone">Milestone</Option>
                    <Option value="bonus">Bonus</Option>
                  </Select>
                </Form.Item>

                {/* 循环设置 */}
                <Form.Item label="Recurring Transaction" name="isRecurring" valuePropName="checked">
                  <Switch 
                    checked={isRecurring}
                    onChange={setIsRecurring}
                    checkedChildren="Yes"
                    unCheckedChildren="No"
                  />
                </Form.Item>

                {isRecurring && (
                  <>
                    <Form.Item
                      name="recurringFrequency"
                      label="Frequency"
                      rules={[{ required: isRecurring, message: 'Please select frequency' }]}
                    >
                      <Select placeholder="Select frequency">
                        <Option value="monthly">Monthly</Option>
                        <Option value="quarterly">Quarterly</Option>
                        <Option value="annually">Annually</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="recurringEndDate"
                      label="End Date"
                    >
                      <DatePicker 
                        style={{ width: '100%' }}
                        format="DD/MM/YYYY"
                        placeholder="Select end date (optional)"
                      />
                    </Form.Item>
                  </>
                )}

                {/* 文件上传组件 */}
                <Form.Item label="Attachments" name="attachments">
                  <Upload.Dragger
                    name="files"
                    multiple
                    showUploadList={true}
                    beforeUpload={() => false} // 阻止自动上传，手动处理
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg,.txt"
                    style={{ padding: '12px 0', marginBottom: 0 }}
                    itemRender={(originNode, file) => {
                      // 格式化上传时间为dd/mm/yyyy, HH:mm
                      const uploadTime = file.lastModified ? new Date(file.lastModified) : new Date();
                      const pad = (n: number) => n < 10 ? '0' + n : n;
                      const timeStr = `${pad(uploadTime.getDate())}/${pad(uploadTime.getMonth()+1)}/${uploadTime.getFullYear()}, ${pad(uploadTime.getHours())}:${pad(uploadTime.getMinutes())}`;
                      return (
                        <div style={{ marginBottom: 4 }}>
                          <div style={{ display: 'flex', alignItems: 'center', fontSize: 10, color: '#333', lineHeight: '14px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', maxWidth: 320 }}>
                            <PaperClipOutlined style={{ marginRight: 6, color: '#FF7A00', flex: 'none', fontSize: 10 }} />
                            <span style={{ wordBreak: 'keep-all', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', flex: 1 }}>{file.name}</span>
                          </div>
                          <div style={{ fontSize: 10, color: '#888', marginLeft: 24, lineHeight: '12px' }}>{timeStr}</div>
                        </div>
                      );
                    }}
                  >
                    <p className="ant-upload-drag-icon" style={{ fontSize: 16 }}>
                      <UploadOutlined style={{ fontSize: 16 }} />
                    </p>
                    <p className="ant-upload-text">Click or drag files to this area to upload</p>
                    <p className="ant-upload-hint">Supports PDF, Word, Excel, Images, TXT, etc.</p>
                  </Upload.Dragger>
                </Form.Item>
              </Card>
            </Col>
          </Row>

          {/* 操作按钮区域 */}
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 32 }}>
            <Space size="large">
              <Button
                onClick={() => navigate('/transactions')}
                size="large"
              >
                Cancel
              </Button>
              <Button
                onClick={() => form.resetFields()}
                size="large"
              >
                Reset Form
              </Button>
              <Button
                icon={<SaveOutlined />}
                loading={loading}
                size="large"
                style={{
                  backgroundColor: '#fff7e6',
                  borderColor: '#FF7A00',
                  color: '#FF7A00',
                  borderRadius: '8px',
                  height: '44px',
                  minWidth: '120px',
                  fontSize: '16px',
                  fontWeight: 500
                }}
                onClick={() => form.submit()}
              >
                Save
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={loading}
                size="large"
                style={{
                  backgroundColor: '#FF7A00',
                  borderColor: '#FF7A00',
                  borderRadius: '8px',
                  height: '44px',
                  minWidth: '120px',
                  fontSize: '16px',
                  fontWeight: 500,
                  boxShadow: '0 2px 8px rgba(255,122,0,0.08)'
                }}
              >
                Create
              </Button>
            </Space>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CreateTransaction; 