import React, { useState } from 'react';
import {
  Form,
  Switch,
  Button,
  Typography,
  Card,
  Space,
  Divider,
  Alert,
  Select,
  InputNumber,
  message
} from 'antd';
import {
  SettingOutlined,
  SecurityScanOutlined,
  TeamOutlined,
  MailOutlined
} from '@ant-design/icons';
import { Project } from '../../types';

interface TeamSettingsProps {
  project: Project;
  onSettingsUpdated: () => void;
}

const { Title, Text } = Typography;
const { Option } = Select;

const TeamSettings: React.FC<TeamSettingsProps> = ({
  project,
  onSettingsUpdated
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const [settings, setSettings] = useState({
    // 团队访问设置
    allowPublicJoin: false,
    requireApprovalForJoin: true,
    allowGuestAccess: false,
    
    // 邀请设置
    invitationExpiryDays: 7,
    allowMemberInvites: false,
    maxTeamSize: 50,
    
    // 安全设置
    requireTwoFactor: false,
    enforcePasswordPolicy: true,
    sessionTimeout: 24,
    
    // 通知设置
    notifyOnMemberJoin: true,
    notifyOnMemberLeave: true,
    notifyOnRoleChange: true,
    weeklyDigest: true
  });

  const handleSave = async () => {
    setLoading(true);
    try {
      // 这里应该调用 API 保存设置
      // await teamService.updateTeamSettings(project.id, settings);
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSettingsUpdated();
      message.success('Team settings updated successfully');
    } catch (error) {
      message.error('Failed to update team settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={4}>
          <SettingOutlined /> Team Settings
        </Title>
        <Text type="secondary">
          Configure team access, security, and collaboration settings for your project.
        </Text>
      </div>

      <Form form={form} layout="vertical">
        {/* 团队访问设置 */}
        <Card title={<><TeamOutlined /> Access & Joining</>} style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong>Allow Public Join</Text>
                  <br />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    Anyone with the project link can request to join
                  </Text>
                </div>
                <Switch
                  checked={settings.allowPublicJoin}
                  onChange={(checked) => handleSettingChange('allowPublicJoin', checked)}
                />
              </div>
              {settings.allowPublicJoin && (
                <Alert
                  message="Public joining is enabled. Make sure your project doesn't contain sensitive information."
                  type="warning"
                  showIcon
                  style={{ marginTop: 8 }}
                />
              )}
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Require Approval for Join Requests</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Team admins must approve all join requests
                </Text>
              </div>
              <Switch
                checked={settings.requireApprovalForJoin}
                onChange={(checked) => handleSettingChange('requireApprovalForJoin', checked)}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Allow Guest Access</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Enable temporary access for external collaborators
                </Text>
              </div>
              <Switch
                checked={settings.allowGuestAccess}
                onChange={(checked) => handleSettingChange('allowGuestAccess', checked)}
              />
            </div>

            <div>
              <Text strong>Maximum Team Size</Text>
              <br />
              <InputNumber
                min={1}
                max={1000}
                value={settings.maxTeamSize}
                onChange={(value) => handleSettingChange('maxTeamSize', value)}
                style={{ width: 120, marginTop: 8 }}
              />
              <Text type="secondary" style={{ marginLeft: 8 }}>members</Text>
            </div>
          </Space>
        </Card>

        {/* 邀请设置 */}
        <Card title={<><MailOutlined /> Invitation Settings</>} style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div>
              <Text strong>Invitation Expiry</Text>
              <br />
              <Select
                value={settings.invitationExpiryDays}
                onChange={(value) => handleSettingChange('invitationExpiryDays', value)}
                style={{ width: 200, marginTop: 8 }}
              >
                <Option value={1}>1 day</Option>
                <Option value={3}>3 days</Option>
                <Option value={7}>7 days</Option>
                <Option value={14}>14 days</Option>
                <Option value={30}>30 days</Option>
              </Select>
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Allow Members to Send Invites</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Regular members can invite new team members
                </Text>
              </div>
              <Switch
                checked={settings.allowMemberInvites}
                onChange={(checked) => handleSettingChange('allowMemberInvites', checked)}
              />
            </div>
          </Space>
        </Card>

        {/* 安全设置 */}
        <Card title={<><SecurityScanOutlined /> Security Settings</>} style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Require Two-Factor Authentication</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  All team members must enable 2FA
                </Text>
              </div>
              <Switch
                checked={settings.requireTwoFactor}
                onChange={(checked) => handleSettingChange('requireTwoFactor', checked)}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Enforce Strong Password Policy</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Require complex passwords for all members
                </Text>
              </div>
              <Switch
                checked={settings.enforcePasswordPolicy}
                onChange={(checked) => handleSettingChange('enforcePasswordPolicy', checked)}
              />
            </div>

            <div>
              <Text strong>Session Timeout</Text>
              <br />
              <Select
                value={settings.sessionTimeout}
                onChange={(value) => handleSettingChange('sessionTimeout', value)}
                style={{ width: 200, marginTop: 8 }}
              >
                <Option value={1}>1 hour</Option>
                <Option value={8}>8 hours</Option>
                <Option value={24}>24 hours</Option>
                <Option value={168}>1 week</Option>
                <Option value={720}>30 days</Option>
              </Select>
            </div>
          </Space>
        </Card>

        {/* 通知设置 */}
        <Card title="📧 Notification Settings" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Notify on Member Join</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Send notifications when new members join
                </Text>
              </div>
              <Switch
                checked={settings.notifyOnMemberJoin}
                onChange={(checked) => handleSettingChange('notifyOnMemberJoin', checked)}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Notify on Member Leave</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Send notifications when members leave
                </Text>
              </div>
              <Switch
                checked={settings.notifyOnMemberLeave}
                onChange={(checked) => handleSettingChange('notifyOnMemberLeave', checked)}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Notify on Role Changes</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Send notifications when member roles change
                </Text>
              </div>
              <Switch
                checked={settings.notifyOnRoleChange}
                onChange={(checked) => handleSettingChange('notifyOnRoleChange', checked)}
              />
            </div>

            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>Weekly Team Digest</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Send weekly summary of team activities
                </Text>
              </div>
              <Switch
                checked={settings.weeklyDigest}
                onChange={(checked) => handleSettingChange('weeklyDigest', checked)}
              />
            </div>
          </Space>
        </Card>

        <Divider />

        <div style={{ textAlign: 'right' }}>
          <Button
            type="primary"
            loading={loading}
            onClick={handleSave}
            icon={<SettingOutlined />}
            size="large"
          >
            Save Team Settings
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TeamSettings; 